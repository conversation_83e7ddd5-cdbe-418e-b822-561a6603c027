from dataclasses import dataclass
from dataclasses_json import dataclass_json
from PIL import Image
import fitz
from ultralytics import <PERSON>OL<PERSON>
from ultralytics.engine.results import Results
from enum import IntEnum

from core import logger

MODEL_DIR = "parser/yolov12l-doclaynet.pt"
MODEL_IOU = 0.3
BATCH_SIZE = 4
DPI = 200

OVERLAP_AREA_THRESHOLD = 0.25

model = YOLO(MODEL_DIR, task="detect")

class Label(IntEnum):
  CAPTION = 0
  FOOTNOTE = 1
  FORMULA = 2
  LIST_ITEM = 3
  PAGE_FOOTER = 4
  PAGE_HEADER = 5
  PICTURE = 6
  SECTION_HEADER = 7
  TABLE = 8
  TEXT = 9
  TITLE = 10

@dataclass_json
@dataclass
class LayoutBox:
  x0: float
  y0: float
  x1: float
  y1: float
  label: Label
  confidence: float
  
  def overlap_area(self, other: "LayoutBox"):
    max_x0 = max(self.x0, other.x0)
    max_y0 = max(self.y0, other.y0)
    min_x1 = min(self.x1, other.x1)
    min_y1 = min(self.y1, other.y1)
    return max(0.0, min_x1 - max_x0) * max(0.0, min_y1 - max_y0)
  
  @property
  def area(self):
    return (self.x1 - self.x0) * (self.y1 - self.y0)

def _resolve_overlapping_boxes(boxes: list[LayoutBox]) -> list[LayoutBox]:
  sorted_boxes = sorted(boxes, key=lambda x: x.confidence * x.area, reverse=True)
  selected_boxes = []
  for box in sorted_boxes:
    keep_box = True
    for oth_box in selected_boxes:
      if box.overlap_area(oth_box) / min(box.area, oth_box.area) > OVERLAP_AREA_THRESHOLD:
        keep_box = False
        break
    if keep_box:
      selected_boxes.append(box)
  return selected_boxes

@dataclass_json
@dataclass
class LayoutPage:
  layout_boxes: list[LayoutBox]
  model_output: Results
  @staticmethod
  def from_model_output(output: Results) -> "LayoutPage":
    boxes = []
    for label_num, box, confidence in zip(output.boxes.cls, output.boxes.xyxyn, output.boxes.conf): # type: ignore
      x0, y0, x1, y1 = box.numpy()
      label = Label(label_num.item())
      boxes.append(LayoutBox(float(x0), float(y0), float(x1), float(y1), label, float(confidence)))
    boxes = _resolve_overlapping_boxes(boxes)
    return LayoutPage(boxes, output)

@dataclass_json
@dataclass
class LayoutPrediction:
  layout_pages: list[LayoutPage]

def _pixmap_to_image(pixmap: fitz.Pixmap) -> Image.Image:
  image = Image.frombytes('RGB', (pixmap.width, pixmap.height), pixmap.samples)
  return image

def predict_layout(document: fitz.Document) -> LayoutPrediction:
  logger.info("Converting document to images (page count: %d)", document.page_count)
  page_images = [_pixmap_to_image(page.get_pixmap(dpi=DPI)) for page in document.pages()]
  
  logger.info("Predicting page layout")
  page_results = []
  for i in range(0, len(page_images), BATCH_SIZE):
    images = page_images[i:i+BATCH_SIZE]
    model_output = model.predict(images, verbose=False, device="mps", iou=MODEL_IOU)
    model_output = [_.to("cpu") for _ in model_output]
    batch_result = [LayoutPage.from_model_output(_) for _ in model_output]
    for page_num, page in enumerate(batch_result, i):
      logger.info(f"Page {page_num}: {page.model_output.verbose()}")
    page_results.extend(batch_result)
    
  return LayoutPrediction(page_results)
