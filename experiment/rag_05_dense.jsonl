{"question_id": "cdb59f3c15dd8d951c169a9f2fae03e000215bae", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "Yes, the cross-linguistic analysis includes both French and Spanish. The zero-shot cross-lingual evaluation involved models trained on English (Newsela-En) and tested on Spanish (Newsela-Es) and French (Vikidia-Fr) datasets."}
{"question_id": "cf26cc0cd1000ad63bfba19b5159a20efba34b18", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "The parameters of the BLOSUM matrix are not estimated by the model; rather, the BLOSUM matrix is given and used as a fixed substitution matrix. The model pre-computes weighted averaged BLOSUM vectors from the multiple sequence alignment once, which are then processed into BLOSUM embeddings by a neural network to provide position-specific information. Thus, the BLOSUM matrix itself is a fixed input and not subject to parameter estimation within the model."}
{"question_id": "7ed6482c76ae428708b4675495718652adfed68d", "paper_id": "nlpeer/COLING2020/679", "generation": "Yes, six of the nine annotators who participated in the evaluation were native English speakers."}
{"question_id": "e76615760f7625a5b984cb961f5d948ab2407178", "paper_id": "nlpeer/PeerRead-ACL2017/193", "generation": "In the UCCA scheme, a node (or unit) corresponds either to a terminal (a single token) or to several terminals (not necessarily contiguous) viewed as a single entity. Thus, a node can represent a single token (terminal) or a non-terminal unit comprising multiple tokens. The scheme uses non-terminal nodes to represent units comprising more than one word, motivated by constructions with no clear head, such as coordination structures, multi-word expressions, and prepositional phrases. Therefore, a node is either a terminal or a non-terminal, but not both simultaneously."}
{"question_id": "0081780bb3b2f8ca303dd426ce4ddfd89c13e80d", "paper_id": "nlpeer/ARR-22/0f595b4bd968ef5daacc88c4edb581bf7f683af30b6874ba157c6d2282aabead577041409b949924e4bc5ef54c543d85a10e962be8e70304dea65e1b18441bdb", "generation": "The average duration reported in Table 1 serves to indicate the average time taken by workers to complete a Human Intelligence Task (HIT) and the average time taken to assess dialogues. This information helps to characterize the efficiency and time investment required from workers during the human evaluation process."}
{"question_id": "6a2461eebef2649b0832b8d91cb8ed490edbf2d3", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "The provided information does not explicitly state the reason why Ordinary Least Squares (OLS) was chosen for the regression-based systems instead of Support Vector Regression (SVR). However, it is indicated that the regression models used non-contextual word-level embeddings as input into an OLS model, serving as a baseline for regression. This suggests that OLS was employed as a straightforward, standard linear regression approach to establish baseline performance for comparison with more complex models such as the neural regressor (regBERT). There is no mention of SVR being used or compared in the regression experiments."}
{"question_id": "5f4c9dea82aa176c2e42cd2c59ff3da0fce4a367", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "Yes, the pairwise distances of the latent representations can be compared to the distances in a properly fitted phylogenetic tree. A quantitative analysis comparing the Euclidean distances between the latent representations of the leaves with the corresponding branch lengths in the phylogenetic tree shows a strong correlation. Specifically, for the Draupnir model, the correlation coefficient is 0.91 and the Spearman correlation coefficient is 0.94, indicating that the latent space distances closely reflect the phylogenetic distances. This demonstrates that the latent representations effectively capture the evolutionary relationships encoded in the tree."}
{"question_id": "7088d27d671a415164b81660a60173ea7602d968", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "A bidirectional gated recurrent unit (GRU) decoder is used for sequences of the same length to model the sequential dependencies along the alignment length while incorporating both the latent vector representing each sequence and position-specific amino acid preferences. At each position in the sequence alignment, the GRU receives as input a concatenated vector consisting of the sequence-specific latent vector and a BLOSUM embedding that encodes amino acid preferences at that position. The GRU processes this input across the fixed-length sequence to produce hidden states that are then mapped to logit vectors specifying the probabilities of the possible characters at each position. This architecture allows the model to capture complex position-dependent patterns and correlations in the sequence data despite the uniform sequence length, leveraging the recurrent structure of the GRU to integrate information across positions effectively."}
{"question_id": "058da91fd7bfe9ecd3163d9d8877e5116cf5cdf6", "paper_id": "openreview/ICLR-2022-conf/C03Ajc-NS5W", "generation": "The evidence for the success of the generative model in synthesizing new molecules is demonstrated in the targeted molecule discovery experiments, where the G-SphereNet model outperforms the G-SchNet model in generating molecular geometries with desirable quantum properties. Specifically, G-SphereNet achieves higher validity rates and produces more molecular geometries with good properties, such as low HOMO-LUMO gaps and high isotropic polarizability. Both models use the same pretraining and fine-tuning pipeline, indicating that the superior performance of G-SphereNet reflects its stronger capability to search and generate valid 3D molecular geometries with targeted quantum properties. This is further supported by ablation studies showing improved validity when using combined local and global features, incorporating distance, angle, and torsion information, and employing a Sigmoid method for focal atom selection."}
{"question_id": "465b94530bc99598bbefb742da70cf99236b86d9", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "Classification-based models were not used for the zero-shot experiments in Tables 6 and 7 because the zero-shot cross-lingual scenario requires ranking texts from a target language without explicit training on that language. The pairwise ranking approach, such as the Neural Pairwise Ranking Model (NPRM) with mBERT, is better suited for this task as it directly models relative readability between text pairs and performs well in cross-lingual transfer settings. In contrast, classification models treat reading levels as discrete class labels and rely on training data from the target language for accurate classification, which is not available in zero-shot scenarios. Thus, the pairwise ranking model was preferred to establish strong baseline performance for new languages without target language training."}
{"question_id": "a36f298f8941bf93ad6bdc2ef8db6471e6ca4156", "paper_id": "nlpeer/PeerRead-ACL2017/193", "generation": "The UCCA transition scheme offers capabilities that address structural properties not fully supported by existing AMR and SDP parsers. Specifically, UCCA parsing supports general directed acyclic graph (DAG) parsing, including full reentrancy and discontinuity, and employs non-terminal nodes to represent multi-word units and constructions without clear heads, such as coordination. In contrast, existing transition-based AMR parsers are limited to a subset of reentrancies and discontinuities and cannot produce arbitrary DAG parses, partly because they may remove nodes before their parents are predicted. SDP parsers, while addressing a wide range of semantic phenomena and supporting discontinuous units and reentrancy, rely on bilexical dependencies requiring head selection for every relation, which can be problematic in constructions lacking clear heads.\n\nTherefore, the UCCA transition scheme, by enabling general DAG parsing and the use of non-terminal nodes, could potentially improve the performance of existing algorithms for AMR and SDP by overcoming their limitations in handling reentrancy, discontinuity, and coordination structures. However, the UCCA scheme differs in its semantic distinctions and abstraction from syntax, which may require adaptation for direct application to AMR and SDP tasks."}
{"question_id": "d256c384aa446ef6ba7d69269df08e3dbbdb2db9", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "Constructing the phylogenetic tree required as input for the ancestral sequence reconstruction algorithm involves inferring the tree topology, the labels of the tree\u2019s edges, and the composition of the ancestral sequences. This process is a central concern of the field of phylogenetics and typically relies on methods based on heuristics, such as maximum parsimony, or probabilistic evolutionary models. The tree is represented as a binary tree with known extant sequences as leaf nodes and unknown ancestral sequences as internal nodes, with edges labeled by positive real numbers representing time differences or amounts of change. Thus, the construction of the phylogenetic tree is a non-trivial task that requires specialized computational methods and models to accurately represent evolutionary relationships."}
{"question_id": "e2ee4a1059cbb3c736b7b00cd902bbbd428423e8", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The authors have not considered formulating the Darpa dataset as a dynamic graph for network intrusion detection. While they mention the use of graph neural networks (GNNs) for intrusion detection in general [29], there is no indication that the Darpa dataset specifically has been modeled as a dynamic graph in their work. The discussion focuses on various datasets and methods, but the Darpa dataset is not explicitly addressed in the context of dynamic graph formulation."}
{"question_id": "b8c9effb94fbab2db127d7262041fecdb031c1dd", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "Yes, the same preprocessing step of converting numerical features to categorical ones through binning is applied to all methods for a fair comparison. Specifically, three float features\u2014connection duration, number of source bytes, and number of destination bytes\u2014are converted into categorical bins. However, experiments were also conducted without this preprocessing, and it was observed that performance varies depending on the method and split, with no clear superiority of one feature set over the other across all methods."}
{"question_id": "6ceebb857d2674bd38452e8bc30a3d280f2ce647", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "The lower performance on Vikidia-Fr compared to Newsela-Es can be attributed to several factors: \n\n1. Language difference: Vikidia-Fr is in French, whereas Newsela-Es is in Spanish.\n2. Domain diversity: Newsela-Es contains content from the same domain as Newsela-En, while Vikidia-Fr has more diverse content.\n3. Number of reading levels: Vikidia-Fr has only two reading levels, which leads to ranking metrics penalizing incorrect rankings more heavily. An incorrect ranking in Vikidia-Fr results in a completely reversed list, causing low SRC and KTCC scores. In contrast, Newsela-Es has more reading levels, making SRC and KTCC more forgiving of ranking errors.\n\nThus, the combination of language differences, domain diversity, and fewer reading levels in Vikidia-Fr contributes to its lower performance relative to Newsela-Es."}
{"question_id": "de0f53c58cedd98fd958715bb1a2f5a3d24e829d", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The source and destination IP addresses and port numbers were discarded because the dataset preprocessing focused on enabling a language-modelling approach through discretization of numerical features. Although these additional features (source and destination IP addresses and port numbers) might be useful for models designed to analyze connections between nodes in the system (e.g., graph-based models), they were not included in the main feature set used for the benchmark. The preprocessing retained all conventional features but applied discretization to continuous values, while the additional features were excluded to maintain the focus on features suitable for the intended modeling approach."}
{"question_id": "264d5ee47c97b02b5a597533254b78c97cb41f8b", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "There is no information in the provided chunks regarding the DARPA dataset or any limitations related to it that would prevent it from being a graph dataset. The available content discusses the Kyoto-2006+ dataset and other network intrusion detection datasets but does not mention DARPA or its characteristics in relation to graph data."}
{"question_id": "ee956c6a1b9b9808fc77d7d1f8f82237123f5000", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "\"Edit boundaries might be unusual\" refers to the fact that the start and end points of edits in the automatic annotation process are determined algorithmically rather than manually. Since the edit extraction relies on alignment algorithms and merging rules to identify the spans of text that have been changed between the original and corrected sentences, the resulting edit spans may not always correspond precisely to human intuition or manual annotation standards. Consequently, the boundaries of these automatically detected edits may appear atypical or unconventional to human evaluators, even though the error type classification focuses on the appropriateness of the error type assigned to the edit regardless of these boundary placements."}
{"question_id": "****************************************", "paper_id": "openreview/ICLR-2023-conf/-CoNloheTs", "generation": "Yes, the proposed algorithm can be used to recover real neural networks. It provides a polynomial time and query complexity method for exact reconstruction of two-layer neural networks with any number of hidden neurons under mild general position assumptions. Additionally, it extends to exact reconstruction of three-layer neural networks under mild general position assumptions, with the conditions that the number of first layer neurons is smaller than the input dimension and the second layer has non-zero partial derivatives, which is valid for most networks with more second layer neurons than first layer neurons. The algorithm works by identifying points where the network's linearity breaks and extracting neurons by recovering the affine transformations near these points. It also includes a novel approach to distinguish neurons belonging to the first or second layer, enabling reconstruction of networks with general depth-two architectures and three-layer networks in polynomial time. The method is applicable to networks with any finite width and does not require independence of weight matrix rows, making it suitable for real neural networks."}
{"question_id": "96a32bff80b5928198a99a4fc2c2e24cd1a982dd", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The magnitude of the noise in the data is represented by the parameter \u03c3, which is the noisy level for all the samples. Specifically, the input signal for each sample is generated as \\( x | y = k \\sim \\mathcal{N}(\\mu_k, \\sigma^2 I) \\), where \u03c3 controls the variance of the Gaussian noise added to the input features."}
{"question_id": "815b242c673ecb7aea2ccec4f6c83ab1191a9124", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "A Multi-Layer Perceptron (MLP) cannot be used to decode the aligned sequences instead of a Gated Recurrent Unit (GRU) decoder because the decoding process requires modeling sequential dependencies along the alignment length. The GRU, as a recurrent neural network, processes input sequences position by position, capturing contextual information and dependencies across sequence positions. Specifically, the GRU decoder takes as input at each position a concatenated vector of the latent representation of the sequence and the BLOSUM embedding of the amino acid preferences at that position, and produces likelihood parameters for each character in the sequence. This sequential modeling capability is essential for accurately reconstructing protein sequences from latent representations. In contrast, an MLP lacks the recurrent structure necessary to capture such positional dependencies and sequence context, making it unsuitable for decoding aligned biological sequences in this framework."}
{"question_id": "a3bcdc5b71130202e27b2a0b4a8485392efedec5", "paper_id": "openreview/ICLR-2023-conf/rLguqxYvYHB", "generation": "The provided information does not explicitly discuss challenges specifically related to estimating the feature map \\(\\phi_A\\) when treatments are discrete. However, it notes that in the binary treatment case \\(A = \\{0,1\\}\\), the average treatment effect (ATE) is typically defined as the expectation of the difference of potential outcomes, and the proposed method defines ATE as the expectation of the potential outcome \\(E[Y(a)]\\), which is a primary target in continuous treatment cases.\n\nThe algorithm for back-door adjustment models the conditional expectation \\(g(a,x)\\) as \\(w^\\top (\\phi_A(a) \\otimes \\phi_X(x))\\), where \\(\\phi_A\\) and \\(\\phi_X\\) are feature maps represented by neural networks. This tensor product form explicitly separates the treatment features from the covariate features, which is advantageous especially when \\(X\\) is much higher dimensional than \\(A\\). It is noted that concatenating both as a single input tends to downplay the information in \\(A\\).\n\nNo specific difficulties or challenges are mentioned regarding the estimation of \\(\\phi_A\\) in the discrete treatment setting. The challenges discussed in the context of high-dimensional treatments relate primarily to continuous or complex high-dimensional treatments, where estimating the Riesz representer \\(\\alpha\\) becomes difficult and error bounds grow exponentially with dimension.\n\nTherefore, there is no direct evidence that estimating \\(\\phi_A\\) poses particular challenges when treatments are discrete."}
{"question_id": "962be5303d07e7707c9750241730986cfeb7d754", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "The significance test was performed using the CoNLL-2014 Shared Task results, specifically the True Positive (TP), False Positive (FP), and False Negative (FN) counts for various teams and error types. These counts include detailed error annotations such as Missing, Replacement, and Unnecessary edits, as well as finer-grained error types (e.g., ADJ, ADV, DET, NOUN, VERB) for each team\u2019s system outputs. The data used for the test is summarized in Tables 10 and 11, which provide the TP, FP, and FN counts for each error type and team, enabling the evaluation and comparison of grammatical error correction system performance."}
{"question_id": "1d2837da9c9a557b18715b0482bea0532e3ac176", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The performance of the Transformer model (BERT for Anomalies) is lower in the IID split than in the NEAR split because, within the IID split, the anomalies (outliers) are modeled quite poorly by the language model. This results in a slightly lower IID performance compared to the NEAR split. Specifically, parts of the IID split contain outliers that are not well captured by the model, which degrades its anomaly detection capability. In contrast, in the NEAR split, the model is better able to model the outliers, leading to improved performance. This phenomenon is illustrated by the monthly evaluation where the inliers' performance gradually degrades over time, and the outliers in the IID split are poorly modeled, explaining the relatively lower performance of BERT on IID compared to NEAR."}
{"question_id": "8f2072e6213f44471d3294973c9cfdd790bc7259", "paper_id": "openreview/ICLR-2022-conf/_X90SIKbHa", "generation": "The proposed methods, specifically ST-AM, MST-AM, and RST-AM, differ from Newton and standard quasi-Newton methods such as BFGS, L-BFGS, Chord, and Levenberg\u2013Marquardt in several key aspects:\n\n1. **Memory Efficiency and Historical Information Storage**  \n   - Unlike full-memory quasi-Newton methods (e.g., BFGS) that require storing many historical iterations to form secant equations, MST-AM only needs to store two corrected historical iterations. This is achieved through careful incorporation of historical information via orthogonalization, which in ideal cases (strongly convex quadratic optimization or SPD linear systems) is equivalent to full-memory Anderson Mixing (AM) without loss of historical information.  \n   - Limited-memory quasi-Newton methods like L-BFGS reduce memory by discarding the oldest iteration but may lose local superlinear convergence properties and are sensitive to the choice of historical length, which is problem-dependent and not known a priori. MST-AM avoids this sensitivity.\n\n2. **Matrix Access and Computational Cost**  \n   - ST-AM does not require direct access to the matrix (e.g., Hessian) and only needs residuals, making it appealing when explicit matrix access is difficult or costly. This contrasts with Newton methods and some quasi-Newton methods that rely on Hessian or Hessian approximations.  \n   - ST-AM can be directly extended to unconstrained optimization where gradients are available but Hessians are expensive to compute.\n\n3. **Algorithmic Flexibility and Black-Box Acceleration**  \n   - ST-AM is flexible and can accelerate any black-box iterative solver by manipulating residuals without line search or trust-region techniques, which are commonly used in Newton and quasi-Newton methods.\n\n4. **Convergence and Theoretical Properties**  \n   - MST-AM retains theoretical properties comparable to full-memory AM and can achieve local superlinear convergence in nonlinear equations.  \n   - RST-AM, an extension for stochastic optimization, has theoretical guarantees in both deterministic and stochastic settings and recovers fast convergence rates in quadratic optimization without requiring step size tuning, unlike momentum methods or adaptive learning rate methods.\n\n5. **Performance in Experiments**  \n   - Experiments comparing MST-AM with gradient descent, conjugate residual, BFGS, Broyden\u2019s method, and full-memory AM show that MST-AM is competitive and can outperform or match these methods in solving nonlinear problems and root-finding tasks.\n\nIn summary, the proposed methods offer a memory-efficient, flexible, and theoretically sound alternative to Newton and standard quasi-Newton methods, with advantages in scenarios where Hessian computation is costly, memory is limited, or stochastic optimization is involved."}
{"question_id": "f3d892c65c8fc7edb3624cad07d6ef39115402eb", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "A new set of error categories was designed to achieve a compromise between informativeness and practicality. The new framework allows for flexible evaluation at different levels of granularity by prefixing error types with indicators of edit operations (Missing, Replacement, Unnecessary), enabling detailed observations of system performance. Unlike previous tagsets, it avoids overly narrow distinctions that would require creating numerous new categories and complex rules, which would be impractical. For example, the framework labels certain edits as tense errors rather than modal errors to maintain clarity and manageability, as differentiating narrowly between such cases would complicate the scheme. This design facilitates automatic annotation, standardization across datasets, and detailed error type evaluation that was not possible with earlier tagsets."}
{"question_id": "67b6a78d6cea6ff4cd6a6cdd262aaf4e4bfea275", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "For the hard sample in Figure 3, the learning path of the model's output probability q initially moves towards the unknown true label distribution p* before eventually veering off and converging towards the one-hot encoding e_y. This results in a \"zig-zag\" trajectory where the model first approaches p* but then shifts towards the one-hot label. The early stopping point of training is closer to p* than the final converged point near e_y, indicating that during one-hot training, the neural network can spontaneously refine the initially \"bad\" one-hot label towards the more accurate p*. Thus, p* represents a more accurate or true label distribution that differs from the hard one-hot label, and the model's output initially aligns more closely with p* before ultimately memorizing the one-hot label."}
{"question_id": "0c4afb8ced370f2f67477fe4617ff846513cfb6d", "paper_id": "openreview/NeurIPS-2022-dabt/8lQDn9zTQlW", "generation": "Table 2 presents zero-shot language model evaluation results, specifically showing the rouge1_fmeasure metric for various models across different biomedical datasets. The values include the mean, standard error (SE), minimum (Min), and maximum (Max) rouge1_fmeasure scores for each model-dataset pair. Models evaluated include SciFive-Base, SciFive-Large, GPT-Neo-1.3B, GPT-2, GPT-J-6B, T0_3B, T5 v1.1-xxl, T0, T0+, T0++, GPT-NeoX-20B, and GPT-3. The datasets include bigbio_blurb_bc5chem, bigbio_blurb_bc5disease, bigbio_blurb_ncbi_disease, bigbio_blurb_bc2gm, and bigbio_blurb_jnlpba."}
{"question_id": "7869279cfc2dc07fcd82704dc07789afa6de5c82", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "The issue with the point estimate (PE) oracle itself is fundamental and not resolved by regularization. The PE approach is sensitive to the sparse distribution of neural response data combined with few repeats per stimulus, which leads to overconfident and thus inaccurate estimation of the gold standard (GS) parameters. This results in the paradoxical outcome where the Null model outperforms the GS model for the majority of neurons when using the PE approach. Attempts to improve the GS model through other methods such as maximum a posteriori (MAP) estimation or regularization did not outperform the posterior predictive (Bayesian) GS model. The Bayesian approach, which treats parameters probabilistically rather than as point estimates, consistently yields better likelihood performance, especially in regimes with few repeats, demonstrating that the problem lies with the PE oracle itself rather than a lack of regularization."}
{"question_id": "3b4dcf624027feff21ac63b6e451169e1ca6bf2a", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The proposed criterion for quantifying generalization performance is the average L2 distance between the supervision signal \\( p_{\\text{tar}} \\) and the ground truth distribution \\( p^* \\) over the input samples, expressed as \\( \\mathbb{E}_x \\left[ \\| p_{\\text{tar}}(x) - p^*(x) \\|_2 \\right] \\). Smaller values of this average L2 distance correspond to better generalization performance. This is supported by a theoretical bound showing that the variance of the risk estimate is dominated by a term proportional to the squared L2 distance between \\( p_{\\text{tar}} \\) and \\( p^* \\) when the sample size \\( N \\) is large. Empirical experiments on a synthetic Gaussian dataset further confirm that smaller L2 distances lead to higher test accuracy and better calibration, as measured by expected calibration error (ECE). Thus, the quality of the supervision signal, and consequently the generalization performance, can be roughly measured by its L2 distance to the ground truth distribution."}
{"question_id": "49887aceab5099bc8a45f1f01aa437f760c289a5", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "The 90% Normalized Information Gain (NInGa) achieved by the mixture model on the real data example indicates a high level of performance for that specific dataset, corresponding to a likelihood of 7.00 bits per image and neuron. Additional analyses were performed on multiple datasets to demonstrate how NInGa facilitates model comparison across different datasets, suggesting that the metric is suitable for evaluating and comparing model performance beyond the single dataset. However, the text does not explicitly state that the 90% NInGa performance itself generalizes directly to other datasets, only that NInGa as a metric enables consistent model comparison across datasets. Therefore, while the 90% NInGa reflects strong performance on the evaluated dataset, its indicative value for other datasets depends on further evaluation using NInGa on those datasets."}
{"question_id": "924a054e5ec561c4d58306dfd312782d7b4f70ca", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "Students are supervised using \"one-hot\" labels, which are label vectors with a value of 1 for the correct class and 0 for all other classes. However, alternative supervisory signals can be used to improve performance, such as distributions of human annotations, label smoothing (which mixes one-hot labels with a uniform distribution), and knowledge distillation (KD). Knowledge distillation provides \"soft\" teacher outputs rather than hard labels, offering sample-specific refined supervision. In particular, a well-trained teacher network can provide supervisory signals closer to the true categorical distribution \\( p^*(y|x) \\), especially by averaging the teacher\u2019s predictions over training, as in the proposed Filter-KD method. This approach refines \"bad\" labels\u2014those that are far from the true distribution\u2014by leveraging the teacher\u2019s learning trajectory to yield better supervision than the original one-hot labels."}
{"question_id": "fffbbdd88b4cdc0b98de790921df08f7be1eed7d", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The zig-zag learning path behavior is representative and prevalent across the dataset. This is quantitatively verified by defining a zig-zag score, which measures the extent to which the prediction path deviates toward classes other than the true label during training. Analysis on toy datasets shows a positive correlation between base difficulty and the zig-zag score, indicating that samples with higher base difficulty exhibit more zig-zagging paths. Furthermore, in the CIFAR10 dataset with 1000 flipped (wrong) labels, these samples demonstrate significantly higher zig-zag scores compared to the average, confirming that the zig-zag pattern is more pronounced in difficult or mislabeled samples. Randomly selected samples from each class in CIFAR10 also visually confirm the presence of this zig-zag learning path pattern. Thus, the zig-zag pattern is a widespread phenomenon across the dataset, especially prominent in samples with higher difficulty or label noise."}
{"question_id": "c322ea8d24002e229a330243b37f745a157a764e", "paper_id": "egu/esd/14-185-2023", "generation": "The reference numbers for the delta to the DIC pool, as mentioned in Table 1, can be verified in Weaver et al. (2001, Sect. 3e), which details the participation of the MOS_DIC tracer in the inorganic ocean carbon cycle and the air\u2013sea gas exchange process in the UVic ESCM."}
{"question_id": "01163085d0c4776005e14d8621ce2bbdd3cc1c13", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The Filter-KD model outperforms models trained using label smoothing (OHT KD) and knowledge distillation with the optimum temperature (ESKD) in terms of both accuracy and calibration. Quantitative results on CIFAR10 and CIFAR100 datasets show that Filter-KD achieves higher accuracy and lower expected calibration error (ECE) than both OHT KD and ESKD. For example, on CIFAR100, Filter-KD attains an accuracy of 80.09% compared to 78.07% for OHT KD and 78.83% for ESKD, and an ECE of 0.029 compared to 0.053 and 0.067, respectively. Similarly, on the TinyImageNet dataset under various noise ratios, Filter-KD consistently achieves higher accuracy than both OHT and ESKD. Additionally, in teacher-to-student distillation scenarios on CIFAR100, Filter-KD yields better student performance than ESKD and the baseline student model. These results indicate that the refined supervisory signal provided by Filter-KD leads to improved generalization performance over label smoothing and conventional knowledge distillation with optimized temperature."}
{"question_id": "3493acb3c91a1415959829136fe3e250966fc8f0", "paper_id": "openreview/NeurIPS-2022-dabt/8lQDn9zTQlW", "generation": "The prompt design for biomedical natural language processing (BioNLP) tasks, as implemented in the B IG B IO framework, primarily adapts general domain prompt templates to the biomedical context. Specifically, biomedical prompts are largely based on general domain templates used to tune models like T0, with the biomedical text simply populated into these existing templates. This approach results in prompts that are more \"in-distribution\" relative to the model's pretraining, rather than completely novel prompt designs. For named entity recognition (NER) tasks in BioNLP, prompts are formulated as text translation tasks, where the output is a list of all entities found in the input text, differing from typical classification or generation prompts in general NLP.\n\nIn contrast, general NLP tasks often use prompt templates that are directly designed for the specific classification or generation task without necessarily adapting from another domain. The biomedical prompt design does not involve iterative prompt tuning but relies on existing prompt templates from resources like PromptSource, reflecting the original classification tasks. This contrasts with some general NLP prompting approaches that may include iterative prompt tuning or automated prompt learning to optimize performance.\n\nThus, the key differences are that BioNLP prompt design tends to reuse and adapt general domain prompt templates with biomedical text inputs, formulates NER tasks as entity list generation (text translation), and does not perform iterative prompt tuning, whereas general NLP tasks may use more task-specific prompt designs and tuning strategies."}
{"question_id": "06dc05622c2bee5d4a078d2ef7542457179ac90b", "paper_id": "nlpeer/ARR-22/6d1b7e47f044e29d515db7e4762ace8e3f089fe88c2a9de074e8d71f0f78da6169be5756ec9320b5354d2f0ebef3352fedbf5dd82012a7eafde0b0dfbfc311df", "generation": "The baseline model achieves significantly better performance on ACE 2004 and ACE 2005 compared to Yu et al. (2020) because it uses a strong architecture consisting of RoBERTa-base (for English datasets) or BERT-base-wwm (for Chinese datasets) combined with a BiLSTM layer and a biaffine decoder, which provides robust contextualized representations and effective span classification. This configuration, although relatively simple and free of additional auxiliary embeddings, is well-suited for handling the nested entity structures prevalent in ACE 2004 and ACE 2005, leading to higher precision, recall, and F1 scores than Yu et al. (2020).\n\nIn contrast, on OntoNotes 5 and CoNLL 2003, which are flat NER corpora, the baseline model achieves on-par or slightly inferior performance relative to Yu et al. (2020). This suggests that the baseline's architecture and training setup are particularly advantageous for nested NER tasks like ACE 2004 and ACE 2005, but yield comparable results to prior state-of-the-art methods on flat NER tasks. The difference in entity structure complexity between datasets likely accounts for the variation in relative performance."}
{"question_id": "4d8419e9aeb2f3d606bca8774d3618d08b70c41f", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "Improving the upper bound for the Information Gain evaluation metric, specifically Normalized Information Gain (NInGa), provides several practical benefits:\n\n1. **Interpretable Performance Scale:** It enables placing model performance on a bounded and interpretable scale by defining meaningful lower and upper bounds. This allows for a clear assessment of how close a model is to the best possible performance achievable given the data.\n\n2. **Robustness to Data Challenges:** A robust upper bound estimator, such as the Bayesian oracle based on posterior predictive distributions, is more data-efficient and reliable, especially in the presence of sparse responses, low signal-to-noise ratios, and limited repeated trials. This robustness mitigates failures seen in naive point estimate approaches.\n\n3. **Improved Model Evaluation:** With a reliable upper bound, the evaluation metric can accurately reflect the quality of likelihood-based neural encoding models, facilitating the identification of which parameters of the response distribution are well predicted and which require improvement.\n\n4. **Model Comparison Across Datasets:** The bounded scale provided by improved upper bound estimates allows for meaningful comparison of models trained on different datasets that may have varying levels of achievable performance.\n\n5. **Guidance for Model Development:** By quantifying the gap between the model and the upper bound, researchers can identify specific aspects of the model to improve, such as better prediction of certain distribution parameters, thereby guiding future model enhancements.\n\n6. **Avoidance of Misleading Results:** A well-estimated upper bound prevents pathological cases where the evaluation metric might yield values greater than one, which, while not mathematically impossible, are undesirable and indicate estimation issues.\n\nOverall, improving the upper bound enhances the interpretability, reliability, and utility of the Information Gain metric in evaluating and comparing neural encoding models."}
{"question_id": "e7e5b24e35bd512176a8587170677228842e2e24", "paper_id": "egu/esd/14-185-2023", "generation": "No, coastal seaweeds, which generally have a very low surface-to-volume ratio, are unlikely to be competitive in iron uptake against the mostly small and specialized open ocean phytoplankton that have a high surface-to-volume ratio, especially in iron-limited areas. The modeling study does not consider iron limitation for macroalgae growth, noting that iron is a micronutrient needed in low quantities and that iron limitation on macroalgae is not widely discussed. However, the referenced literature (Suzuki et al., 1995) acknowledges iron utilization by brown macroalgae but does not suggest they are competitive in iron uptake compared to phytoplankton adapted to low-iron environments. Therefore, due to their lower surface-to-volume ratio and the specialized adaptations of open ocean phytoplankton, coastal seaweeds would not be expected to compete effectively for iron in iron-limited open ocean regions."}
{"question_id": "a48eb6eab4e9448324227205ae04b8d47a5b181e", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "In addition to accuracy (ACC) and expected calibration error (ECE), no other specific criteria for defining generalization are explicitly mentioned in the provided content. The focus is on accuracy as a measure of predictive performance and ECE as a measure of how well the predicted confidence represents the true correctness likelihood."}
{"question_id": "fcf91acb3ff79184eb4af002b876fec65732620c", "paper_id": "egu/esd/14-185-2023", "generation": "The partial pressure of carbon dioxide (pCO2) related to the dissolved inorganic carbon from remineralized macroalgal biomass (MOS_DIC) was calculated by tracking MOS_DIC as a tracer within the UVic Earth System Climate Model (UVic ESCM). MOS_DIC participates in the inorganic ocean carbon cycle and, upon reaching the ocean surface, its outgassing follows the air\u2013sea gas exchange process implemented in the UVic ESCM as described by Weaver et al. (2001, Sect. 3e). This air\u2013sea exchange flux calculation allows for the determination of the location and quantity of MOS_DIC outgassing, which is directly related to the partial pressure of CO2 at the ocean surface."}
{"question_id": "31314c6ad7630579c350af928493caac9c563dbb", "paper_id": "nlpeer/ARR-22/0f8bc751a0472564732a976cd152643d92d5393c2e77c8234f386a801883d09ff9540378b3dab5834544a246c22f292d5b1c4d370219943c9a19a1626a8f8781", "generation": "The minimum and maximum threshold values are \\( \\tau_{\\min} = 0.0005 \\) and \\( \\tau_{\\max} = 0.995 \\), respectively."}
{"question_id": "27a8c35fcd38d0a141fb5248ad93038196553dfb", "paper_id": "egu/esd/14-185-2023", "generation": "The authors do not explicitly state that iron is sourced from the platform when considering the feasibility of coastal seaweeds competing for iron against open ocean phytoplankton. The introduction mentions simulated macroalgae cultivated on offshore floating platforms moored to the seabed, but there is no indication that these platforms provide iron. Instead, the study focuses on nutrient utilization and climate change implications, with macroalgae grown at 5 m depth and subsequently sunk to the seafloor. The references include studies on iron requirements of brown macroalgae and competition in the northern Japan Sea, but the modeling study itself does not assume iron sourcing from the platform for macroalgae growth."}
{"question_id": "36e40e97993a08a2c5e50bfc69c991334be39e6e", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The noise heads are discarded at test time because the model is trained to separate each audio source using the query heads, and the noise heads are only used during training to handle noisy data by allowing an optimal permutation to minimize the loss. After training, only the query heads are used for inference to specify the target sound by an input query without requiring any post-selection process. This approach differs from models like MixIT, as it enables direct use of the query heads for sound separation at test time while the noise heads serve solely as a training mechanism to improve noise invariance."}
{"question_id": "92394e14628bdc9941b0581b43b20ab42dbdd3fd", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "No, the expected label does not always match the most probable label given the noisy data. The analysis of learning paths on real tasks shows that during training with noisy or corrupted labels, the model's predictions initially move towards the unknown true label distribution (p*), which can differ from the provided noisy label. However, eventually, the model tends to memorize the incorrect noisy labels. This is evidenced by experiments where labels were randomly flipped: early in training, the model's predictions partially recover the true original labels rather than the noisy ones, but with continued training, the model ultimately memorizes the noisy labels. Thus, the expected label (true underlying distribution) and the most probable label (noisy observed label) can differ, especially in the presence of label noise."}
{"question_id": "c83f53bbc1390bf3f6a15aa58e1c559cf391a507", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The task described in Section 4.1 of the paper is text-queried sound separation using noisy unlabeled videos. The method involves learning to separate target sounds specified by input text queries without requiring labeled data. The approach employs noise invariant training (NIT) with a regularization term to handle noisy data in the wild, allowing the model to separate sounds corresponding to the query while managing noise through noise heads. The model uses a weighted loss combining noise invariant loss and regularization, and it is trained and evaluated by monitoring metrics such as training/validation losses, test signal-to-distortion ratio (SDR), and noise head activations."}
{"question_id": "c67443bf273772ac2d4297564f839c0a0229e6eb", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The greyed out portion of Figure 3 represents the noise heads in the CLIPSep-NIT model. These noise heads are used to predict noisy target sounds and are regularized by the noise regularization level \u03b3 to control the extent to which signals are assigned to noise. During evaluation, the noise heads are discarded, but keeping them can lead to higher signal-to-distortion ratio (SDR) performance."}
{"question_id": "cd020940c9b12a598dae5fc4fde1d63c2d88d88d", "paper_id": "egu/esd/14-185-2023", "generation": "The provided information does not explicitly address the potential for longer permanence of CO2 storage through sediment carbonate dissolution caused by high respiratory dissolved inorganic carbon (DIC) at the ocean bottom. However, it does discuss the fate of sunken macroalgal biomass and its remineralization, which consumes oxygen and creates benthic oxygen minimum zones (OMZs). When macroalgal biomass does not undergo microbial remineralization, the captured CO2 can be permanently stored without leakage, thereby increasing the carbon dioxide removal (CDR) potential. The impact of remineralization on benthic OMZs and oxygen distributions is noted, but there is no direct mention of sediment carbonate dissolution or its effect on CO2 storage permanence in the sediment. Therefore, the potential for longer permanence of CO2 storage via sediment carbonate dissolution due to high respiratory DIC at the bottom has not been explicitly considered or analyzed in the study."}
{"question_id": "0c64726cf3b593196fd8f350d0f2c2d4aba98d1e", "paper_id": "nlpeer/ARR-22/7e701552b7cfaad6239d3d5dfcd9da1852a03a66907c789fe65d368f1c2845e723adcecd7f7008849eef13c7ca467be73de53747c3376a6bb46d1911ed1b6409", "generation": "ELECTRA's binary classification head (RTE) labels each token in the input sequence as either a \"good\" gap or a \"non-gap.\" All gaps in the training data are replaced with the first intended target answer and labeled positive (\"good\"), while the remaining tokens are labeled negative (\"non-gap\")."}
{"question_id": "9ee8e92c0faa3a3d17cff68d75d28be3dca8e8c4", "paper_id": "openreview/ICLR-2022-conf/hcQHRHKfN_", "generation": "The provided chunks do not contain any information regarding the parameter \\(\\delta\\) or its impact on the results of Table 1. Therefore, no conclusion can be drawn about the effect of adjusting \\(\\delta\\) on the results of Table 1 from the given content."}
{"question_id": "c5b7931f3e58dd10d67e388fcd5680c37e267022", "paper_id": "nlpeer/ARR-22/0f8bc751a0472564732a976cd152643d92d5393c2e77c8234f386a801883d09ff9540378b3dab5834544a246c22f292d5b1c4d370219943c9a19a1626a8f8781", "generation": "The threshold values are selected to ensure that the distribution of class labels approximates a ratio of about 1:10, with the distituent class being the majority. This choice is motivated by the skewness of random spans, which are more likely to represent distituents, and by linguistic considerations that distituents must necessarily outnumber constituents. Specifically, the lower and upper bounds of the threshold are chosen to reflect this distributional skewness, providing a crude estimate that accounts for larger sentence lengths in the dataset. For self-training experiments, the thresholds \u03c4_min and \u03c4_max are set to 0.0005 and 0.995, respectively, to treat outside strings satisfying these bounds as gold-standard outside of constituents and distituents."}
{"question_id": "9824d5fa73a188c99c7d977a3dda3d2b24856f9d", "paper_id": "openreview/ICLR-2023-conf/_01dDd3f78", "generation": "The results of the experiments on the myocardial infarction complications dataset regarding mortality risk have been compared and evaluated against existing medical literature describing how each complication affects the risk of death. The alignment between the concept gradient (CG) scores and the severity of mortality risk described in the medical literature supports the validity of the interpretations. This comparison serves as a form of expert review, as the interpretations are benchmarked against established medical knowledge. However, there is no explicit statement indicating that a medical expert directly reviewed the experimental results; rather, the evaluation relies on consistency with published medical findings."}
{"question_id": "a81ef48de406906c5a847928da2bc47079136f55", "paper_id": "nlpeer/COLING2020/1681", "generation": "The number of distinct phrases used for phrase addition in the adversarial attack generation is not explicitly specified. The text states that a range of simple phrases were tried but only presents results for appending the phrase \"it is true\" and mentions that other phrases are left out for simplicity. Therefore, the exact number of distinct phrases used is not provided."}
{"question_id": "bf41e9f2b170cb8e1801812167b945e8f56aa8cb", "paper_id": "openreview/ICLR-2022-conf/hcQHRHKfN_", "generation": "The results in Table 1 demonstrate that without intrinsic rewards, the Reward-Switching Policy Optimization (RSPO) method may slow down convergence, fail to discover non-trivial local optima due to insufficient exploration, or become stuck during exploration because of low data efficiency. This highlights the critical role of intrinsic rewards in facilitating effective exploration and enabling RSPO to discover diverse and high-quality strategies."}
{"question_id": "ba25580bbc4ec4f20348cefaf968e1cdea408642", "paper_id": "openreview/ICLR-2023-conf/XrMWUuEevr", "generation": "In the domain shift experiment described in Section 5.3, the 8 positive and 8 negative examples per task were randomly selected molecules from the training set of the Tox21 dataset. These examples formed the support set used for few-shot learning evaluation. The positive examples correspond to active molecules for a given task, while the negative examples correspond to inactive molecules for that task."}
{"question_id": "b9d3ed7981d9f1e47fea48aaf2b4037fe7b25658", "paper_id": "openreview/ICLR-2023-conf/_01dDd3f78", "generation": "The purpose of finetuning the same network for the concept model g is to ensure that g is similar to the target model f in both architecture and weights, which leads to better attribution performance. By initializing g with the pretrained weights of f and finetuning it to predict concepts, the final converged solution of g remains close to f. This similarity ensures that g utilizes the input representation in a manner aligned with f, resulting in more accurate and meaningful concept gradients. Additionally, freezing certain layers during finetuning can further constrain g to maintain this alignment. This approach helps to reduce misalignment in gradients between g and f, thereby improving the reliability of concept importance attribution."}
{"question_id": "0258b0f39ec3f7316f9d299a25a7cd36274e9631", "paper_id": "openreview/ICLR-2023-conf/_01dDd3f78", "generation": "No, the concepts were not ranked according to the TCAV_Q score. Instead, the study used Concept Gradients (CG) as the method for concept importance attribution, which relaxes the linear assumption of TCAV and models concepts with non-linear functions. The evaluation compared CG with CAV/TCAV methods, showing that CG consistently outperforms TCAV in both local and global concept importance attribution. The ranking of concepts was based on the importance scores derived from CG rather than the TCAV_Q score."}
{"question_id": "1aa55ba3f2f47947ad4df928f35cfc4c7d7035ec", "paper_id": "nlpeer/ARR-22/21dd1d5e232c7abf421302a95ef2c9ef44c7bad069da7ca4129a8cf1bfbd6f660afee123144ebcbd8da698aed212e2ca1151a13bca7aaef50ce59d27d23aa83a", "generation": "The focus of this paper is on intent classification and entity/slot recognition within task-oriented cross-lingual natural language understanding (XNLU)."}
{"question_id": "834016a31e50565175511dcdf3d75a1be44b532c", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The paper defines difficulty, referred to as \"base difficulty,\" as a property of samples that correlates with the shape of their learning path during training. Samples with higher base difficulty exhibit a more pronounced \"zig-zag\" learning path, where the network's predicted distribution q_t initially moves toward an unknown true label distribution p* before eventually converging to the one-hot training label e_y. This zig-zag pattern is quantitatively summarized by a \"zig-zag score,\" which measures how much the predicted distribution deviates toward classes other than the training label during training. Specifically, the zig-zag score is defined as the highest integral over training time of the predicted probability q_t for any class other than the labeled class y:\n\n\\[\n\\text{zig-zag score} = \\max_{i \\in \\{1, \\ldots, K\\} \\setminus \\{y\\}} \\sum_{t=1}^T q_t^i(x_n)\n\\]\n\nwhere \\(q_t^i(x_n)\\) is the predicted probability of class \\(i\\) for sample \\(x_n\\) at training step \\(t\\), and \\(K\\) is the number of classes. A higher zig-zag score indicates a more difficult sample, as the learning path deviates more significantly toward classes other than the training label, reflecting ambiguity or label noise. This definition of difficulty is supported by empirical correlations between base difficulty and zig-zag score across toy datasets and real data experiments."}
{"question_id": "44279226e8c3ea5497ac4c43754e3e890e0183fb", "paper_id": "openreview/ICLR-2023-conf/XrMWUuEevr", "generation": "The best way to compare the performance of different models or architectures is to conduct multiple training reruns and evaluate them on multiple support set samplings for each task. Performance metrics such as AUC and \u2206 AUC-PR should be reported as means with standard deviations across these reruns and support sets to capture variation due to tasks, support sets, and training randomness. Statistical significance of performance differences between models should be assessed using paired tests such as the Wilcoxon rank sum test. Additionally, task-wise model comparisons can be visualized using scatterplots where each point corresponds to a task, and the number of tasks on which one model outperforms another can be tested for significance using binomial tests. This approach ensures a robust and statistically sound comparison of model performance across tasks and experimental conditions."}
{"question_id": "1a42a5af41f66bb6428c643d96fd05eba81ce635", "paper_id": "nlpeer/PeerRead-CONLL2016/166", "generation": "The proposed named entity recognition (NER) model relies heavily on wiki\ufb01er features derived from grounding words and phrases to Wikipedia entries, specifically using FreeBase types and Wikipedia categories obtained from the top candidate titles returned by the wiki\ufb01er. The model uses a multilingual Wikipedia dump to generate these features and applies them to all n-grams in the text. Gazetteers for each language are also created from Wikipedia titles containing relevant FreeBase types.\n\nThere is no indication in the described methodology or experiments that the model explicitly accounts for named entities that are not listed on Wikipedia. The approach depends on the presence of entities in Wikipedia to generate features and gazetteers. Therefore, named entities absent from Wikipedia would not be directly represented or accounted for by the wiki\ufb01er features or the gazetteers derived from Wikipedia titles."}
{"question_id": "5678b6bf40f5958402473fd66a08dc836eaa98a7", "paper_id": "nlpeer/COLING2020/1681", "generation": "The coreference resolution pipeline depicted in Table 1 is not universally accepted in the field. It represents an idealized, rule-based approach resembling the multi-pass sieve method implemented in Stanford CoreNLP\u2019s Coref-Annotator. However, current neural machine translation (NMT) models are unable to decouple the individual steps of this pipeline, indicating that it is more of a conceptual framework rather than a universally adopted standard in coreference resolution."}
{"question_id": "8fc13b01107e614b030a2c7dbc65aa19d0363778", "paper_id": "nlpeer/ARR-22/21dd1d5e232c7abf421302a95ef2c9ef44c7bad069da7ca4129a8cf1bfbd6f660afee123144ebcbd8da698aed212e2ca1151a13bca7aaef50ce59d27d23aa83a", "generation": "The authors do not explicitly state that they ran their experiments multiple times with different random initializations to confirm the results. However, they conducted a two-tailed z-test for the difference of proportions to ensure statistical significance of their most effective method compared to the previous state-of-the-art, with significance at p < 0.01 for all datasets. This indicates a robust comparison but does not confirm multiple runs with different random seeds."}
{"question_id": "b97a690598219404cca037101ff05ec348b5e525", "paper_id": "openreview/NeurIPS-2022-dabt/heBKnuV42O", "generation": "There is no indication of a plan to open-source the proprietary medical knowledge base or the telemedicine software. The dataset released under the CC-BY license is synthesized using the proprietary medical knowledge base and a commercial rule-based automatic diagnosis system, but only the dataset itself is made publicly available. The knowledge base and the rule-based AD system remain proprietary and are provided by Dialogue Health Technologies Inc."}
{"question_id": "9c04f85fb5baad69d0ae21c1c2c07abc0422bd55", "paper_id": "nlpeer/ARR-22/013b9bf63a6f68fd0c3ecc36f8cbe2ad5bc92ea3bfe5a9f6c15eb056ecc4f858718410182c3765b2dc2695ae29ba08fb5dea5fc495faf2bbb77205bc3f765fcd", "generation": "To ensure a more diverse cross-cultural representation when building an NLP model, the following concrete steps can be taken:\n\n1. **Data Collection and Curation**:  \n   - Use data sources that are appropriate for the target culture of downstream NLP applications to mitigate cultural bias.  \n   - Curate large, general-purpose datasets to be as unbiased as possible and carefully document them.  \n   - Recruit geo-diverse annotators from different regions and cultures to provide both data and annotations, as exemplified by initiatives like MasakhaNER for African languages.  \n   - Ensure datasets represent diverse sources and multiple perspectives in terms of annotations, including human-generated annotations from various cultural backgrounds.\n\n2. **Data Sampling and Balancing**:  \n   - Apply exponential smoothing or more uniform sampling of languages to reduce skewness in data distribution, improving performance for low-resource languages without excessively harming high-resource languages.  \n   - Extend sampling strategies beyond languages to explicitly target cultural diversity within languages, addressing disparities in data sources and cultural representation.\n\n3. **Model Training Techniques**:  \n   - Employ methodologies that optimize for group fairness, such as Group Distributionally Robust Optimization (group DRO) and Worst-Case-Aware Curriculum Learning, to improve worst-case performance across languages or cultural groups.  \n   - Use training approaches that account for model updates for different groups to reduce representation disparity and cultural biases.  \n   - Derive fair models from biased data by incorporating fairness-aware learning methods that require access to protected demographic attributes, which partially reflect culture.\n\n4. **Granularity and Definition of Cultural Groups**:  \n   - Carefully define the level of granularity at which cultural groups are represented and annotated in the data, balancing between individual-level representation (which may raise privacy concerns) and large, culturally diverse groups treated as homogeneous.\n\n5. **Community Involvement and Scalability**:  \n   - Foster diverse and open research communities dedicated to manual annotation and dataset creation for underrepresented languages and cultures, facilitating scalability and broader cultural coverage.\n\nThese steps collectively aim to balance generalization of universally common knowledge with equal representation of minority cultures, thereby mitigating cultural homogenization and biases in NLP models."}
{"question_id": "587b8f363bb9be4e82b38b70f74608f844559b6f", "paper_id": "nlpeer/COLING2020/1681", "generation": "The data augmentation strategy, termed Antecedent-free augmentation (afa), involves identifying sentences where a coreferential pronoun \"it\" refers to an antecedent not present in the current or previous sentence. For these candidate sentences, two new training examples are created by modifying the gender of the German translation of \"it\" to masculine (\"er\") and feminine (\"sie\"), while keeping the source sentence unchanged. This augmentation aims to break the strong prior bias towards the neuter pronoun (\"es\") in translation and improve coreference resolution capabilities of the model. The pronoun case is determined using a dependency parser to ensure grammatical correctness, particularly because masculine pronouns differ in nominative and accusative cases. The model is fine-tuned on a dataset consisting of both the original candidates and the augmented samples. This approach leads to improved performance on coreferential pronoun translation without degrading general translation quality."}
{"question_id": "70418ac3cb9f40b039a74031b89324e2b891ccf5", "paper_id": "nlpeer/PeerRead-ACL2017/699", "generation": "The word embeddings are randomly initialized with a uniform distribution in the range [-0.1, 0.1]."}
{"question_id": "d49df57b22ec381fed263033d6a02678f16a18c1", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "The width of the model, defined by the number of units per layer, does not improve performance when the prior functions are the same for all units. Specifically, increasing the number of units in the Bayesian neural network (BNN) prior used in VIP from a smaller size to 200 units per layer does not lead to better results. This indicates that simply increasing model width without changing the prior functions does not enhance predictive performance."}
{"question_id": "b879c4d1344942fd8fab9fbe8fc495f4ae67c0b0", "paper_id": "openreview/ICLR-2023-conf/VA1YpcNr7ul", "generation": "The provided chunks do not contain any explicit definition or statement regarding the nature or domain of the parameter \\(\\omega\\) in Definition 1.1. There is no information specifying whether \\(\\omega\\) is an element of \\(\\mathbb{R}\\) or any other set. Therefore, it cannot be confirmed from the given text that \\(\\omega \\in \\mathbb{R}\\) in Definition 1.1."}
{"question_id": "10dbff5874380289cdab832a0eecab1cc3c34117", "paper_id": "openreview/ICLR-2023-conf/VA1YpcNr7ul", "generation": "The proposed algorithm DASHA is related to momentum variance reduction methods (MVR), which are in the family of variance reduction techniques similar to Stochastic Variance Reduced Gradient (SVRG). Specifically, DASHA is inspired by MARINA and MVR methods (Cutkosky & Orabona, 2019; Tran-Dinh et al., 2021; Liu et al., 2020). In the stochastic setting, DASHA employs an MVR-like strategy for variance reduction. Unlike MARINA, DASHA never sends uncompressed vectors and reduces variance from the oracle and the compressor separately, improving theoretical convergence rates. Thus, DASHA incorporates variance reduction techniques related to SVRG and its variants, particularly through the use of momentum variance reduction strategies."}
{"question_id": "428b48f2d5cfea8890c3fe80599575b25565a976", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "Using only one mixture component (R = 1) during training simplifies the computation by propagating a single sample through the network, which reduces computational cost. In contrast, using 100 mixture components (R = 100) during testing involves propagating 100 Monte Carlo samples through the network to approximate the predictive distribution as a Gaussian mixture. This results in a more flexible and accurate approximation of the predictive distribution at test time compared to the single-component approximation used during training."}
{"question_id": "81b292ac9640d75024251269649ea71272b1710c", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "When both Deep Gaussian Processes (DGP) and Deep Variational Implicit Processes (DVIP) use the same Gaussian Process (GP) priors, the tradeoff between runtime and performance is as follows:\n\nDVIP achieves similar predictive performance to DGP but at a lower computational cost and faster training time. This is because DVIP uses a linear GP approximation that does not require specifying inducing points, whereas DGP relies on sparse GPs with inducing points, which are more computationally expensive. Sampling approximately from the GP prior in VIP (and thus in DVIP) is costly, limiting the addition of extra layers, but overall DVIP remains faster than DGP.\n\nIn summary, DVIP with GP priors provides comparable or slightly better results than DGP while being more computationally efficient and faster to train."}
{"question_id": "96525f380b4694ec387b707fa87e78c972a12b4c", "paper_id": "openreview/NeurIPS-2022-dabt/FPgCB_Z_0O", "generation": "Training on the DARTset dataset demonstrates significant effectiveness in improving hand pose estimation and mesh reconstruction performance, particularly when combined with real-world datasets such as FreiHAND. Cross-dataset evaluations using two mesh reconstruction methods, CMR and METRO, show that mixed training on both FreiHAND and DARTset leads to improved accuracy on the FreiHAND test set. For example, the CMR model trained on mixed data improved PA-MPVPE by 8.9% on FreiHAND compared to training on FreiHAND alone. This indicates that DARTset complements challenging real-world datasets by providing a wider and more continuous hand pose distribution, which enhances generalizability.\n\nHowever, a domain gap exists between DARTset and FreiHAND, primarily due to differences in textures, accessories, and hand pose distributions. DARTset has a larger and more diverse pose distribution than FreiHAND, which benefits FreiHAND performance when mixed but does not similarly boost DARTset performance. This is reflected in the cross-dataset results where training on mixed data improves FreiHAND test results more than DARTset test results.\n\nOverall, DARTset's large-scale synthetic data with diverse poses, textures, and accessories, combined with its ability to be mixed with real-world data, effectively enhances model generalization and performance in hand pose estimation and mesh reconstruction tasks."}
{"question_id": "92c772c75354552e709f16f3e3b15a31e395f1cf", "paper_id": "openreview/NeurIPS-2022-dabt/heBKnuV42O", "generation": "The rationale for altering the data distribution despite the knowledge base (KB) being compiled from medical papers is to address limitations inherent in the original incidence and prevalence rates and to ensure a balanced dataset for patient generation. Specifically, some diseases have incidence rates exceeding 100%, which would lead to a highly imbalanced dataset dominated by a few pathologies. To prevent this, incidence rates are capped at a maximum of 100%. Conversely, diseases with extremely low incidence rates would be underrepresented, so a minimum cap of 10% is applied to increase their representation. This adjustment of rates between 10% and 100% creates a more balanced dataset, facilitating the generation of a diverse and representative set of synthetic patients for training and evaluation purposes."}
{"question_id": "89fb9729921ad950b90987550b32f9ede60c8a8c", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "Yes, the Deep Gaussian Process (DGP) with RBF kernels failed to capture the data trend during the interpolation experiment on the Mauna Loa CO2 dataset. This failure led to mean reversion and over-estimation of the prediction uncertainty. In contrast, the Deep Variational Implicit Process (DVIP) model was able to capture the data trend effectively in the missing gaps, producing very smooth prior samples."}
{"question_id": "65df6e41f1c8c77eec8b264ef0a3dcd104abb9dc", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The higher average out-degree of normal users compared to fraudsters is because fraudsters tend to fill fewer emergency contacts in general. This behavior aligns with their primary purpose of defrauding the platform, as providing more emergency contacts is not beneficial for committing fraud. Normal users, in contrast, are more likely to fill out a greater number of emergency contacts, reflecting more genuine social connections."}
{"question_id": "e0385760ba4f37eeba3376cf4dd4ed0727cedb81", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "The rationale for choosing the prior for the model is to use an implicit process (IP) prior that can represent complex distributions over functions, such as those defined by Bayesian neural networks (BNNs). The IP prior is defined by a generative process involving a noise source and a function \\( g_\\theta(x, z) \\), which allows straightforward sampling of function values. However, the prior and posterior distributions under an IP are generally intractable for point-wise density estimation, except in the Gaussian process (GP) case.\n\nTo address this, the IP prior is approximated by a Gaussian process (GP) whose mean and covariance functions are empirically estimated from samples drawn from the IP. This GP approximation retains dependence on the IP prior parameters \\(\\theta\\), enabling prior adaptation to observed data. The GP is further approximated by a linear model \\( f(x) = \\phi(x)^T a + m_\\star(x) \\) with Gaussian-distributed coefficients \\( a \\sim \\mathcal{N}(0, I) \\), which reduces computational complexity from \\(O(N^3)\\) to a more scalable form suitable for mini-batch training.\n\nMaximizing the variational objective with respect to the prior parameters \\(\\theta\\) allows the model to adapt the prior to the observed data, which is a key factor when considering IP priors. Thus, the prior is chosen to combine the expressiveness of implicit processes with the tractability and scalability of Gaussian process approximations, enabling effective Bayesian inference and prior adaptation."}
{"question_id": "c3790b0e0925f080c8d91160ae348f35ac367993", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "Word overlap was used instead of an exact match when comparing F1(s\u2217_{j,1}, s_{j,1}) to determine whether two entities are the same because the predicted entity may mention the same entity as the gold answer but with slightly different names. Using word-level F1 allows for partial matches and avoids false positives in detecting invalid questions, as entities can be considered equal as long as their first mentions have some word overlap. This approach accounts for variations in entity naming that exact match would not tolerate."}
{"question_id": "721388955b97db097e963c4b221fe9af9e2da4ae", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "When the absolute accuracy numbers are higher than the F1 scores, it indicates that human evaluation reports higher correctness of model answers compared to automatic evaluation metrics. This discrepancy arises because automatic evaluations rely on a fixed set of gold answers that cannot capture all possible correct answers to open-ended or multi-answer questions, leading to lower F1 scores. In contrast, human annotators can assess the correctness of answers more flexibly and comprehensively, resulting in higher accuracy measurements."}
{"question_id": "60c2bf23190bf1120b8652501ff951bae6f3e046", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The rationale for comparing 2-hop homophily (with background nodes as intermediate nodes, denoted as \"xBx\" or with target nodes as intermediate nodes, \"xTx\") to 1-hop homophily (direct connections, \"xx\") in Figure 3(d) is to investigate the role of background nodes in the connectivity and semantic relationships within the graph. The comparison reveals that 2-hop connections involving background nodes as intermediates have a higher homophily ratio than direct 1-hop connections. This indicates that background nodes, despite being difficult to separate by node features, contribute to stronger class-insensitive edge homophily in indirect relationships. Therefore, analyzing 2-hop homophily helps to demonstrate the importance of background nodes in maintaining network connectivity and enhancing the semantic coherence of node relationships, which is valuable for improving fraud detection performance in the DGraph dataset."}
{"question_id": "9dfb86a40b292918a304254d03b72b6fee37e740", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The target knowledge base (KB) size used for MedMentions in the experiments is 2.3 million entities."}
{"question_id": "8a7925cf9978728b68e7bc89204643a94468964a", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The combination of layer normalization and dropout improves overall performance in reinforcement learning, particularly in complex environments such as Ant and Humanoid. The primary benefit of using layer normalization with dropout is that layer normalization suppresses the learning instability caused by dropout. Dropout alone destabilizes the learning of Q-functions, leading to significant oscillations in Q-function loss and gradient variance. Layer normalization effectively reduces these oscillations, stabilizing the learning process. This stabilization enables better Q-value propagation, which consequently improves overall performance. Additionally, the variance re-scaling component of layer normalization is important for this synergistic effect. Other normalization methods, such as batch normalization, do not provide similar benefits, and layer normalization without variance re-scaling is less effective and leads to unstable learning."}
{"question_id": "9073f9407118eaf0b34170000a57846d672b4a5d", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "The four conversational question answering (CQA) models were selected based on having different model architectures and training strategies."}
{"question_id": "b509625d37c9da4c7585e00370dc166be8e9be88", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "No, the model does not need to compute \\(\\psi(m_i, e)\\) for all entities in the target knowledge base during inference. Instead, the inference procedure constructs a graph \\(G\\) by adding edges from each mention \\(m\\) to its \\(k\\)-nearest neighbor mentions and to its nearest entity, where the nearest entity is determined by minimizing the dissimilarity function involving \\(\\psi(e, m)\\). This approach limits the computation to only the nearest entity for each mention rather than all entities in the knowledge base."}
{"question_id": "3356f072c76c12c8ebc215b5bd495d5ccbea0126", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The training procedure involves constructing a graph over mentions and entities within a mini-batch and performing constrained clustering to form clusters that model mention coreference relationships. The mini-batch size used during training is 128, which is chosen to balance gradient stability and computational feasibility. The clustering and arborescence-based training explicitly model mention-mention coreference by ensuring connectivity and minimizing dissimilarity between coreferent mentions and entities within the batch.\n\nHowever, there is no explicit statement indicating that the mentions within a training or inference mini-batch are restricted to or sampled from the same document. The procedure focuses on modeling coreference across mentions and entities in the batch, which may span multiple documents, as the approach is designed for cross-document coreference and entity linking. The use of FAISS for nearest-neighbor search during graph construction at both training and inference further suggests that mentions are embedded and linked across documents rather than confined to a single document.\n\nTherefore, the batches do not necessarily contain mentions exclusively from the same document but are constructed to leverage coreferent mentions across documents as much as possible through the graph-based clustering and arborescence modeling."}
{"question_id": "672a51e77d6a34b48ad5a93aa1a37139d71e3c72", "paper_id": "nlpeer/ARR-22/1370310ca3e0e0d092a2e8b58f90b0d692331caa6e3ce5ca6dacbef926f2b01ff49c81282f32c554fa66c04979a125b7548c9692c2091bb2673642102d85412d", "generation": "The rationale for analyzing the self-attention functions in the last layer of the BERT model is that fine-tuning BERT does not significantly change the correlations to human attention across layers, and attention flow filtering tends to select paths of minimal value at each layer, resulting in little change in correlation throughout the network. Additionally, selecting the highest correlating head from the last attention layer generally produces weaker correlations than using attention flow, indicating that raw attention weights, particularly from the last layer, do not reliably correspond to token relevance. Therefore, focusing on the last layer's self-attention functions provides a consistent and interpretable point of comparison for evaluating alignment with human attention patterns."}
{"question_id": "abf4bcae7809ff5b01e8cf7fdb201caa7b8421ac", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The computation time referred to is the process time per update loop, specifically the time required for executing the overall loop of the reinforcement learning methods (e.g., lines 3\u201310 in Algorithm 2), including the Q-function updates (e.g., lines 4\u20139 in Algorithm 2). REDQ requires process times in the range of 2200\u20132300 milliseconds per update, whereas SAC requires process times in the 800\u2013900 milliseconds range. This indicates that REDQ runs more than two times slower than SAC in terms of the process time per update loop."}
{"question_id": "8d69a05246c31778897996bc35b60061f15554f3", "paper_id": "nlpeer/COLING2020/1550", "generation": "The results presented in the paper are primarily task- and dataset-specific. The study focuses on stance detection as a multi-class classification problem related to Fake News Detection, using the FNC-1 and the extended FNC-1 ARC data sets. These data sets have particular characteristics, such as heavily skewed class distributions and differences in document types (news articles versus user posts), which influence model performance and hyperparameter preferences. The paper explicitly states that while the findings provide valuable insights and starting points for further experiments in related fields, they do not claim that these results are directly transferable or generalizable to other Fake News related data sets employing different tasks or label sets."}
{"question_id": "2074c1cd08c7d4b134ac01c5ee57f13765a4cc47", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "Layer normalization offers distinct advantages over other regularization schemes for improving the performance of dropout in deep reinforcement learning, particularly in high update-to-data (UTD) ratio settings. Unlike batch normalization, which does not significantly improve average return or reduce estimation bias and leads to unstable Q-function learning, layer normalization effectively stabilizes the learning process when combined with dropout. This stabilization is achieved by suppressing the oscillations in Q-function loss and the variance of the gradient with respect to Q-function parameters, which are otherwise exacerbated by dropout alone. \n\nFurthermore, layer normalization's effectiveness is partly attributed to its variance re-scaling component, which is critical for its synergistic effect with dropout. Variants of layer normalization without variance re-scaling fail to improve performance and result in unstable Q-function learning. Group normalization, which shares a similar mechanism with layer normalization, also works well when combined with dropout, but layer normalization remains the preferred method due to its demonstrated superior stabilization and performance enhancement.\n\nIn summary, layer normalization uniquely stabilizes the learning instability caused by dropout, enabling better Q-value propagation and consequently improving overall performance in complex reinforcement learning environments, an effect not matched by batch normalization or layer normalization variants lacking variance re-scaling."}
{"question_id": "4bb993f44c76628b67f41da43c78aa82b50cbc19", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The full name of the algorithm DUVN is \"Double Uncertainty Value Network.\""}
{"question_id": "5227809e5dbd6a7ef588b7a84fc243e6cd0eed8d", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "The provided evidence chunks do not contain any information regarding the compensation rate for the annotators hired for the Mechanical Turk crowdsourcing work."}
{"question_id": "f567015ed8777554298ac8d5b511b255c317d3da", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "Dropout on its own does not work effectively for the proposed method because, without additional engineering to stabilize learning, the use of dropout in Q-functions leads to instability. Specifically, previous studies that introduced dropout to single Q-functions for policy evaluation did not employ stabilization techniques, whereas DroQ incorporates layer normalization to stabilize the learning of dropout Q-functions. Furthermore, in high update-to-data (UTD) ratio settings, which increase estimation bias and present a more challenging environment, using dropout alone is insufficient. The combination of dropout with layer normalization is necessary to achieve improved performance and stability in these high UTD settings."}
{"question_id": "7b375e548c69cd6c0b0d75953da0021adb9e2a7e", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The process times per update for SAC are in the range of approximately 835 to 870 milliseconds, while for REDQ they are in the range of approximately 2269 to 2340 milliseconds. This indicates that REDQ requires about 2.5 to 3 times longer computation time per update compared to SAC."}
{"question_id": "52e2f447dc9bcf3dc208f99a89d4b59da4004923", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The embedding matrix for relative positions (W_p) is randomly initialized."}
{"question_id": "720c06898bbd974bce657e8eefac71ea8641b762", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "Position embeddings are generated by table lookup from a trainable parameter matrix \\( W_p \\in \\mathbb{R}^{d_p \\times (s_{\\max} \\times 2 - 1)} \\), where \\( d_p \\) is the embedding size and \\( s_{\\max} \\) is the maximum sentence length in the dataset. Each relative position of a word with respect to the event mention is assigned a distinct embedding vector from this matrix, analogous to how word embeddings are obtained from a word embedding matrix \\( W_w \\). This means that for each word position \\( i \\) in the sentence, the relative position embedding \\( v_i^p \\) is retrieved by indexing into \\( W_p \\), similar to the lookup process used for word embeddings."}
{"question_id": "1cd2ab406e0d21ae6597092cde6d7a2fca652f82", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The provided information does not explicitly address whether dynamic nodes specifically need to be included in the data for the dynamic graph approach to be effective. However, it emphasizes that temporal dynamics of graphs are commonly present in real-world scenarios and are ignored by most current Graph Anomaly Detection (GAD) datasets, which is a limitation that DGraph aims to overcome. DGraph is described as a real-world dynamic graph dataset in the financial domain, implying that incorporating temporal dynamics (which would include dynamic nodes and edges) is important for realistic and effective GAD research. Therefore, including dynamic nodes as part of temporal dynamics is implied to be necessary for the dynamic graph approach to capture real-world graph evolution and improve anomaly detection performance."}
{"question_id": "c3b651600b60b22f2a4c805aeb87745aff3c0c84", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The full name of the proposed method \"REDQ\" mentioned in the paper is \"Randomized Ensembled Double Q-learning.\""}
{"question_id": "45a145511dd96e98d18e5ac09f454b95ceee5a38", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The left and right neighbors of the trigger word are used by concatenating their word embeddings with the sentential features of the event mention. Specifically, the sentential features vector \\( v_{sent} \\in \\mathbb{R}^{2 \\times d_c} \\), which is derived from max-pooling over convolutional features on the sentence parts to the left and right of the trigger, is concatenated with the word embeddings of the trigger word and its immediate left and right neighbors. This concatenation results in a combined vector \\( v_{sent+lex} \\in \\mathbb{R}^{2 \\times d_c + 3 \\times d_w} \\). This combined vector incorporates lexical semantics from the trigger and its neighbors, which are strong indicators for coreference. The concatenated vector is then processed by an additional hidden layer to generate the final event-mention representation \\( v_e \\)."}
{"question_id": "4a12daa058e224f39629de8997d5de7c8b0c2d3c", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The complete graph is pruned during training to enforce specific constraints that ensure the formation of valid clusters for positive sampling. These constraints require that each cluster contains at most one entity, that the dissimilarity between connected nodes does not exceed a threshold \u03bb, and that every node in a cluster is reachable from every other node. Pruning removes edges with weights above the dissimilarity threshold and edges that violate these constraints, thereby preventing clusters from containing multiple entities or disconnected nodes. This pruning process results in a partitioned graph where each mention node has only one incoming edge from either an entity or a mention, minimizing the dissimilarity function between coreferent mentions and entities. Using the complete graph without pruning would violate these constraints, leading to invalid clusters and impairing the optimization of the parametric encoder models. Thus, pruning is essential to maintain cluster validity and to optimize the dissimilarity function effectively during training."}
{"question_id": "8b0ad0aeb866b9064a6bd71b1559fe85d81a612b", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The event-mention representation \\( v_e \\) serves as a fixed-size vector encoding of an individual event mention, capturing both sentential semantics and lexical-level features of the event trigger and its context. It is generated by processing the sentence containing the event mention through convolutional and max-pooling layers to extract sentence-level features, which are then concatenated with embeddings of the trigger word and its immediate neighbors. This combined vector is further transformed by a hidden layer with a tanh activation to produce \\( v_e \\). The resulting representation \\( v_e \\) is used as the input for subsequent event-linking decisions, enabling the model to determine coreference relations between event mentions."}
{"question_id": "dfa7d8d8808a8928555e5e665068db90d3261334", "paper_id": "openreview/ICLR-2022-conf/fQTlgI2qZqE", "generation": "Yes, it is valid to assume that finite evaluations of one arm are sufficient to obtain an accurate reward. This is stated as Assumption 1 in the proposed interaction detection method, which explicitly assumes that a finite number m of evaluations of one arm suffice to obtain an accurate estimate of its reward."}
{"question_id": "a74c71ff53a5ff84cacb938350996a66ceb0ae12", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "In Phase-I training, the proposal network is trained using the loss function specified in Equation 2. The training employs the SGD optimizer with an initial learning rate of 0.01 and a batch size of 16. The training duration is 8 epochs. The proposal network is trained on geometric cues extracted from the RGB input, such as depth and normal maps, using the base class bounding box annotations. The architecture used for the proposal network is OLN, which is built on Faster RCNN but replaces classification heads with objectness score prediction heads."}
{"question_id": "5ab449ca87a0bb86111a5e4e594ac959ec7d0e86", "paper_id": "nlpeer/PeerRead-CONLL2016/142", "generation": "Post-editing rates were chosen over prediction (H)TER for intrinsic uncertainty evaluation because post-editing time is a more comprehensive measure of post-editing effort. It captures not only technical effort, as measured by HTER, but also cognitive effort. Additionally, post-editing time is more directly applicable in real translation environments, where uncertainty estimates are useful, as it relates directly to productivity measures."}
{"question_id": "b248a530072224a71459f4ce7aa708f9990067c2", "paper_id": "openreview/ICLR-2023-conf/KDhFkA6MQsW", "generation": "The parameter \\(\\mu\\) is set to either \\(0.001\\) or \\(0.01\\) in the experiments. Specifically, for the cubic regularization problem and the quartic function experiments, \\(\\mu\\) is commonly set to \\(0.001\\) for ZO-Perturbed-AGD and to \\(0.001\\) or \\(0.01\\) for ZO-Perturbed-AGD-ANCF, depending on the dimension and experiment configuration."}
{"question_id": "d753561800b2ad04d3d8262519328d014142d717", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The rationale for choosing to remove tokens at the 4th, 7th, and 10th layers in DeiT-S and DeiT-B models (and similarly at the 5th, 9th, and 13th layers in LV-ViT-S) is to incorporate the token identification module at intermediate depths of the Vision Transformer (ViT) network. This approach allows the model to gradually reduce the number of image tokens as the network goes deeper, thereby decreasing computational cost while preserving attentive tokens that contribute most to classification accuracy. The token identification module uses the attentiveness of the [CLS] token to other tokens to identify and preserve important tokens, while inattentive tokens are fused or removed. Incorporating token reorganization at these specific layers balances computational efficiency and model performance without requiring substantial modifications to the original ViT architecture or training strategies."}
{"question_id": "888ba5daeae0d5b3d5120c824c8f61abd5b77ee3", "paper_id": "openreview/ICLR-2022-conf/IfNu7Dr-3fQ", "generation": "The difference in error between the KT+ and standard thinning (ST) methods for the Hinch posterior using the IMQ kernel (\u03bd = 0.5) across varying coreset sizes is explained by KT+'s ability to uniformly improve the maximum mean discrepancy (MMD) error and integration error compared to ST. KT+ achieves significantly smaller integration errors for functions both inside the reproducing kernel Hilbert space (RKHS), such as k(X\u2032, \u00b7), and outside the RKHS, including the first and second moments and the benchmark continuous integrand family (CIF) function, in nearly every setting. This improvement occurs despite neither kernel (IMQ or Laplace) admitting a square-root kernel with fast-decaying tails, which typically facilitates better error decay. Empirically, KT+ shows better MMD and integration error decay rates than ST, as demonstrated by the higher MMD values for KT+ at comparable coreset sizes (e.g., KT+: n=0.50 vs. ST: n=0.46 for MMD k(in, out)) and consistently improved integration errors across multiple test functions. This indicates that KT+ provides a more effective thinning strategy that yields more accurate approximations of the target distribution for a given coreset size than standard thinning."}
{"question_id": "3f32444ad6371e1401f9047615faeed1a6572e73", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The difference between the results in Table 1 and Table 2(a) lies in the specific models and experimental settings reported:\n\n- Table 1 presents results of EViT on DeiT-S, comparing two variants: EViT without inattentive token fusion and EViT with inattentive token fusion. It shows Top-1 and Top-5 accuracy, throughput (images/s), and MACs for different keep rates, with accuracy values averaged over three independent trials and standard deviations reported.\n\n- Table 2(a) reports results of EViT on DeiT-S as well, but includes additional details such as varying image sizes alongside keep rates. It compares the baseline DeiT-S (keep rate 1.0, image size 224) with EViT at different keep rates and image sizes, showing Top-1 and Top-5 accuracy, throughput, and MACs. The results indicate performance changes when training or finetuning on higher resolution images.\n\nIn summary, Table 1 focuses on comparing two EViT variants at fixed image size (224) across keep rates with statistical averaging, while Table 2(a) extends the analysis by including the effect of increased image resolution on EViT-DeiT-S performance across keep rates."}
{"question_id": "4191cd3e5bd482f3d097b689c9857bf3d727f98b", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The depth and normal maps are combined for training the proposal network in Phase-I by training two separate object proposal networks independently on each geometric cue (depth and normal maps). The pseudo boxes extracted from these two networks are then merged into a single pseudo box pool for Phase-II training. The merging process involves filtering out overlapping boxes: if the Intersection over Union (IoU) of two pseudo boxes is larger than 0.5, they are considered overlapping, and the box with the lower objectness score is filtered out. This method of ensembling pseudo labels from separately trained networks is empirically shown to be slightly better than stacking the two geometric cues together as input to a single proposal network in Phase-I."}
{"question_id": "1b3c40fd196db55e9ffea18c2b7d9ffe988c5ad2", "paper_id": "openreview/ICLR-2022-conf/IfNu7Dr-3fQ", "generation": "The kernel \\( k_{\\text{split}} \\) serves as an auxiliary kernel used in the first step of the generalized kernel thinning algorithm (Alg. 1). Specifically, \\( k_{\\text{split}} \\) is employed in the KT-SPLIT procedure to divide the input point sequence \\( S_{\\text{in}} = (x_i)_{i=1}^n \\) into \\( 2^m \\) candidate coresets, each of size approximately \\( \\lfloor n / 2^m \\rfloor \\), using non-uniform randomness. The kernel \\( k_{\\text{split}} \\) governs the splitting process by defining the kernel matrix and associated reproducing kernel Hilbert space (RKHS) \\( \\mathcal{H}_{\\text{split}} \\), which is used to measure integration error and control the quality of the candidate coresets.\n\nThe choice of \\( k_{\\text{split}} \\) affects the theoretical guarantees of the KT-SPLIT step. For example, when \\( k_{\\text{split}} \\) is the square-root kernel \\( k_{\\text{rt}}^{(2)} \\) of the target kernel \\( k \\), the generalized kernel thinning recovers the original ROOT KT algorithm. More generally, \\( k_{\\text{split}} \\) influences the sub-Gaussian concentration properties of the random function representing the difference between the input distribution and the candidate coreset distribution in the RKHS \\( \\mathcal{H}_{\\text{split}} \\). This allows the derivation of probabilistic bounds on the integration error for functions in \\( \\mathcal{H}_{\\text{split}} \\) and, via perturbations, for functions outside this space.\n\nIn summary, \\( k_{\\text{split}} \\) is a critical component that defines the geometry and randomness of the splitting step, enabling the construction of candidate coresets with provable integration error guarantees and facilitating the subsequent refinement step (KT-SWAP) that minimizes the maximum mean discrepancy (MMD) with respect to the target kernel \\( k \\)."}
{"question_id": "a4439f559ec40c32bb7edf1ee7fa3a854ed2b883", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Combining RGB with geometric cues such as depth and normals does not improve performance for smaller classes. When RGB is stacked with geometric cues to train the proposal network, the model tends to rely more on RGB, which is a stronger signal in closed-world setups, leading to inferior performance in open-world detection. Specifically, RGB-based object proposal networks favor smaller detection boxes, which may correspond to small objects or just textures and parts of larger objects, potentially harming detection quality. The method \"GOOD-All,\" which merges pseudo boxes from RGB with those from geometric cues, shows no performance gains or even worsens performance on benchmarks including smaller classes (e.g., VOC to ADE20K). In contrast, models using geometric cues alone (GOOD-Both) outperform those incorporating RGB for novel object detection, including smaller objects. Thus, combining RGB with depth and normals does not improve and may degrade performance for smaller classes."}
{"question_id": "2e474adb8f0c5bd3285e43db4bfb774e7cd5b7a5", "paper_id": "nlpeer/ARR-22/78d7b31f3d19cf16bcc71aa109ca56b923d9082a2919fd8008060641daf4d5ae37d70daea5bb36f4aa83c9c49ad8b44e597aa41960348f3a0abe0abb529f9aea", "generation": "The proposed generated knowledge prompting method relies on eliciting knowledge statements from a language model using few-shot demonstrations and then integrating this knowledge by prompting an inference model to make predictions. The method is designed to be flexible and does not require task-specific supervision or access to structured knowledge bases. While the paper emphasizes the effectiveness of the method on large-scale, state-of-the-art pretrained models (e.g., T5-11b and its variants), it does not explicitly discuss the performance or effectiveness when using smaller language models as the knowledge source.\n\nHowever, the method's core mechanism\u2014generating knowledge statements via few-shot prompting and integrating them at inference time without finetuning\u2014suggests that it could be applied with any language model capable of producing relevant knowledge statements. The quality and flexibility of the generated knowledge are crucial for performance, implying that smaller models might generate less accurate or less relevant knowledge, potentially reducing effectiveness.\n\nIn summary, while the method is conceptually applicable to smaller language models, its effectiveness likely depends on the quality of knowledge generated by the model. The paper does not provide direct empirical evidence on the use of smaller models as the knowledge source, so the effectiveness in such cases remains unverified but potentially diminished compared to large-scale models."}
{"question_id": "5739894b5714e42337f53319a265bb28e2f6e18d", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Yes, incorporating RGB images together with depth and normal images in the first step (Phase-I) leads to worse performance. When RGB is stacked with geometric cues to train the proposal network, the model tends to rely more heavily on RGB due to its stronger signal in closed-world setups, which causes it to underutilize geometric cues. This reliance on RGB prevents the model from effectively discovering novel objects, which is crucial for open-world object detection. Empirical results show that stacking RGB with geometric cues results in inferior performance across multiple benchmarks compared to using geometric cues alone. Additionally, an alternative method that merges pseudo boxes from RGB-trained proposals with those from geometric cues (termed \"GOOD-All\") either yields no performance gains or worsens performance, particularly on benchmarks like VOC to ADE20K. This degradation is attributed to RGB-based proposals favoring smaller detection boxes, which can harm detection of larger objects and overall performance. Therefore, using RGB images in addition to depth and normal images in the first step leads to inferior open-world object detection performance."}
{"question_id": "c8e25c77b2ec42c4f94bc044959aa372dd3f9638", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The design of the proposed method is not arbitrary for all layers of a given ViT model; rather, the token identification module is incorporated into specific fixed layers. For DeiT-S and DeiT-B models with 12 layers, the module is incorporated into the 4th, 7th, and 10th layers. For LV-ViT-S with 16 layers, it is incorporated into the 5th, 9th, and 13th layers. This indicates a deliberate selection of certain intermediate layers for the token identification process rather than applying it arbitrarily across all layers."}
{"question_id": "ecbcef296be692e081bf8e60e3d63dcbf2090dbf", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "No, a language expert was not explicitly consulted to verify the accuracy of the translations of English time expressions into other languages. The discussion notes that the automatic translation of time expressions and templates from English to other languages may introduce some errors, indicating that the translations were done automatically rather than verified by language experts."}
{"question_id": "f21290e4ef8f0ead710c484502ad8e103217da11", "paper_id": "openreview/NeurIPS-2022-dabt/76w7bsdViZf", "generation": "Yes, the authors have evaluated additional pretrained models including DenseNet161 and VGG16 on ImageNet. These models were assessed without finetuning as well as with finetuning of a final linear layer atop fixed feature encoders. The evaluation included classification on Hard ImageNet and RIVAL20 datasets, both derived from ImageNet validation splits. The results showed that these models exhibited higher accuracy under ablation, lower Relative Foreground Sensitivity (RFS) scores, and lower saliency alignment on Hard ImageNet compared to RIVAL20, indicating challenges in classifying Hard ImageNet objects without relying on spurious cues."}
{"question_id": "8e2cb1c95dffd133cc91ab3123074a0853c829fb", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "The authors were motivated to experiment with their model on the CNN/DailyMail dataset because it is a widely used benchmark for extractive text summarization, with established original splits for training, validation, and testing. The dataset's characteristics, such as an average of 40.31 sentences and 31.2 paragraphs per document, make it suitable for evaluating hierarchical structure information in summarization models. Additionally, the authors aimed to investigate the effectiveness of various hierarchical position encoding methods and model architectures on this dataset, as well as to conduct ablation studies to optimize model settings such as the number of Transformer layers and token position embedding initialization strategies."}
{"question_id": "496d254bdc722d815fb98ad3903cdc34df700fce", "paper_id": "openreview/ICLR-2023-conf/KDhFkA6MQsW", "generation": "The complexity term \\( t = \\frac{\\delta_f}{\\epsilon^{1.75}} \\log d \\) does not match the complexity in Theorem 2 because the analysis in the proof of the main results of Algorithm 1 involves more refined bounds on the Hamiltonian decrease and gradient norms that depend on additional parameters and constants, such as the Lipschitz constants, Hessian smoothness \\(\\rho\\), dimension \\(d\\), and perturbation scale \\(\\mu\\). \n\nSpecifically, the proofs show that the Hamiltonian decrease per iteration and the control of gradient norms require careful balancing of terms involving powers of \\(\\epsilon\\), \\(\\rho\\), and \\(d\\), with \\(\\mu\\) chosen as \\(\\tilde{O}(\\epsilon^{5/8} d^{1/4})\\) or \\(\\tilde{O}(\\epsilon^{1/2} d^{1/4})\\) in different steps. The resulting complexity bounds incorporate these dependencies and yield rates that differ from a simple \\(\\epsilon^{-1.75}\\) scaling.\n\nMoreover, the lemmas (e.g., Lemma 23 and Lemma 18) and the Hamiltonian decrease arguments show that the decrease in energy \\(E_t\\) is bounded by terms involving \\(\\epsilon^2\\) and other fractional powers of \\(\\epsilon\\), combined with dimension-dependent factors, which collectively produce a complexity that does not reduce to the form \\(\\frac{\\delta_f}{\\epsilon^{1.75}} \\log d\\).\n\nThus, the mismatch arises because the complexity in Theorem 2 accounts for the intricate interplay of gradient norms, Hessian smoothness, perturbation scales, and dimension, leading to a more nuanced complexity expression than the stated \\( t = \\frac{\\delta_f}{\\epsilon^{1.75}} \\log d \\)."}
{"question_id": "888f26be8b81ccb011c79b043940eec47d9b414e", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The same network architecture, OLN (Kim et al., 2021), is used for both depth and normal by training separate object proposal networks on the predicted depth maps and normal maps, respectively. These networks are trained independently on their respective geometric cue inputs to discover novel unannotated objects. For the combined approach (GOOD-Both), the pseudo boxes generated by the two separately trained networks (one on depth and one on normal maps) are merged by filtering out overlapping boxes based on their Intersection over Union (IoU) and objectness scores. This approach leverages the same OLN architecture but applies it separately to each type of geometric cue."}
{"question_id": "68cdad6fbee4b667c3a6b10e5761a4ccf660e32b", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "The data does not directly show how the location of a country within its time zone affects the results. However, the paper discusses that the grounding of time expressions to times may vary within languages spoken across multiple countries or regions due to differences such as time zones. For example, Portuguese speakers in Brazil and Portugal may perceive time expressions differently because of their different time zones. Additionally, the paper notes that the average night start times roughly match average sunset times in the capitals of countries but do not align with sunrise times, indicating some geographic and temporal variation. The authors also mention that finer-grained analysis correlating sunrise and sunset times in specific locations within countries with workers' indicated times would be interesting but was not performed due to limited data. Thus, while the study acknowledges potential effects of geographic location within time zones, it does not provide explicit data quantifying these effects."}
{"question_id": "cf66689ffef1b230b7bab23901a8bf528a8e97f8", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "The paper incorporates section titles into the [BOS] (beginning of sentence) representation by generating section title embeddings (STE) using the same pre-trained Transformer language model (TLM) involved in the summarization model. The tokenized section title is fed into the TLM, and the last hidden states at each token position are summed to produce a single embedding representing the section title. This section title embedding is then injected into the [BOS] token embedding of each sentence within the corresponding section, thereby providing hierarchical structure information to the sentence representation. Additionally, the paper proposes a classified section title embedding (classified STE) approach, where typical section title classes are predefined, and intra-class section titles are replaced by the embedding of their corresponding class. If a section title does not belong to any class or belongs to multiple classes, the original STE is used. This method allows the model to encode semantic similarities among related section titles and incorporate this information into the [BOS] token embeddings for extractive summarization."}
{"question_id": "691280cb66aae7b9fee2d8ac0937e5f970437b43", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "Yes, human evaluation results for the extracted summaries are demonstrated in Appendix A.6 of the paper, providing a more intuitive understanding of the superiority of the proposed system."}
{"question_id": "986801b0e009ad637f5bc4d62c8af27a2580f7b9", "paper_id": "nlpeer/ARR-22/3282bdf968d7f87ae69c4ae7fdd35d4e9a46fa83f56d8bee0296c16dcf23b23cb1e4f20f510c7d01fb7be2309c08dd29cc240b54bdcbc3d66969041b5c84077a", "generation": "The degree of formality transfer in Hindi is effectively controlled and maintained by the proposed models, particularly DIFFUR - MLT and DIFFUR - INDIC, which achieve the best overall performance in style accuracy (r-ACC, a-ACC), semantic similarity (SIM), and aggregate metrics (r-AGG, a-AGG). These models demonstrate a good trade-off between preserving semantic content and accurately transferring formality, as indicated by their high style accuracy and semantic similarity scores (Table 2).\n\nThe dataset used for evaluation includes 1000 sentence pairs in Hindi with formality and semantic similarity annotations, created by style transferring held-out Samanantar sentences using the UR - INDIC + BT model. This dataset allows for precise measurement of formality transfer quality.\n\nAlthough the specific use of Persian or Sanskrit lexical forms in Hindi during formality transfer is not explicitly quantified in the provided data, the methodology involves native speakers and professional linguists to ensure understanding and correct application of formality, which in Hindi often correlates with the use of Persian or Sanskrit-derived vocabulary. The high style accuracy and semantic similarity scores suggest that the models successfully maintain the linguistic nuances associated with formality, including the appropriate use of Persian/Sanskrit forms, during transfer.\n\nFurthermore, the ablation studies show that removing components related to paraphrase generation or vector differences significantly reduces performance, indicating that these components contribute to maintaining the stylistic and lexical characteristics of formal Hindi, which would include Persian and Sanskrit forms.\n\nIn summary, the degree of formality in Hindi is well preserved and controllable in the transfer process, with models effectively maintaining the stylistic features characteristic of formal Hindi, implicitly including the use of Persian and Sanskrit lexical forms."}
{"question_id": "953feae01ae0b8d2066fd035c079f0a5dd581aaf", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Label smoothing during the head probing (HP) stage, referred to as lsHP, can improve feature adaptation and is particularly effective when the pretrained features are already strong and the HP training accuracy converges very quickly (above 90%). In such cases, lsHP reserves energy for subsequent feature adaptation, leading to better adaptation without changing HP-train-accuracy. However, lsHP does not always improve performance. When the HP-train-accuracy is low, the assumption that the model\u2019s prediction converges to the labels no longer holds, and lsHP may not bring enhancement. Additionally, if label smoothing is applied in both the HP and finetuning (FT) stages with the same smoothing factor, the reserved energy disappears, resulting in performance similar to the baseline without smoothing. Moreover, using label smoothing with opposite energy settings can cause larger adaptation but worse generalization due to features adapting in conflicting directions. Therefore, label smoothing does not universally improve the hyperparameter-fine tuning procedure; its effectiveness depends on the initial feature quality, the HP-train-accuracy, and the relative smoothing factors used in HP and FT."}
{"question_id": "dc3fd256c5702edb18e7a21a01836945f7bc0b17", "paper_id": "openreview/ICLR-2023-conf/WlbG820mRH-", "generation": "The paper restricts its analysis to Message Passing Neural Networks (MPNN) where the aggregation functions compute a simple sum of their inputs, and the combination and readout functions are represented by neural networks with ReLU activation only. These limitations imply that the formal verification results\u2014specifically the impossibility of verifying output reachability and adversarial robustness for graph-classifier MPNNs on unbounded input spaces, and the possibility of verification for node-classifier MPNNs on bounded-degree graphs\u2014only extend to GNN models that are at least as expressive as the considered MPNNs with these specific functional forms.\n\nIf the aggregation, combination, or readout functions deviate significantly from these assumptions\u2014for example, by using different aggregation schemes, non-piecewise-linear activations, or architectures outside the spatial-based MPNN framework\u2014the applicability of the results becomes uncertain. The results do not directly transfer to more expressive models such as DropGNN, which exceed the expressiveness of the considered MPNNs. Consequently, the limitations on these components constrain the generalizability of the formal verification results, and extending these findings to other GNN models requires further investigation, particularly for models with different expressiveness or architectural designs."}
{"question_id": "28241e220ebb3458ef6d5eaf6829f8563161a7c1", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Mild adaptation, characterized by a reasonably large value of the hyperparameter \u03c4, results in the feature representation \\( z_T \\) being stretched (i.e., an increase in \\(\\| z_T \\|_2\\)) in a direction similar to the original features \\( z_0 \\), with little change in the cosine similarity between \\( z_T \\) and \\( z_0 \\). This effect makes originally overlapped features more separable without significantly altering the manifold of the features. Such behavior is desirable in most transfer learning scenarios because it enhances feature separability while preserving the underlying feature structure."}
{"question_id": "e0bf6addede2ca2d1e76eac67027e72e3ff385f5", "paper_id": "nlpeer/ARR-22/3cfcbbad78a71ca6cf4a5b2bbaee2a7ad8a1de295cf9f4103408dfeaf38a0de01b2b8d23cb94e0ef0b5f76a1947815f0335a7077bdea03ea4a29f6a2bff432cc", "generation": "Hanja is considered an extinct language rather than merely a script because it represents a distinct linguistic system used in ancient Korea, characterized by its own lexical, semantic, and syntactic features that differ significantly from modern Korean and modern Chinese. Although Hanja is based on ancient Chinese characters, it evolved to incorporate the basic syntax of classical Chinese mixed with colloquial Korean elements, resulting in a unique language form used primarily in formal records during the Joseon dynasty. This linguistic system is no longer in active use, and understanding historical documents written in Hanja requires specialized language models. Thus, Hanja is treated as a dead or extinct language rather than just a writing system."}
{"question_id": "c12d6fb12967631fd985c4b437ee77cd1d2a8e2e", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Yes, when combining multiple modalities for the model, filtering is applied to merge the pseudo boxes generated by object proposal networks trained separately on different modalities. Specifically, if the Intersection over Union (IoU) of two pseudo boxes is greater than 0.5, they are considered overlapping, and the box with the lower objectness score is filtered out. This filtering process ensures that overlapping pseudo boxes from different modalities are resolved by retaining the more confident detection."}
{"question_id": "9b256b585691520864c3cf7d1b8cfb8f863d6663", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Yes, the results of the study suggest several implications beyond the general empirical impression. The study provides a detailed analysis of how the choice and design of the task head influence the adaptation of pretrained features during finetuning and consequently affect downstream performance. By decomposing the learning dynamics of the features, the study identifies key factors\u2014energy and direction terms\u2014that correlate strongly with accuracy at the beginning of finetuning. It reveals a non-trivial quadratic trend in feature adaptation metrics such as the norm of the fine-tuned features and their inner product with the initial features, which is analytically proven in an overparameterized linear model and experimentally verified in practical settings.\n\nFurthermore, the study shows that appropriate control of feature adaptation\u2014through early stopping during head probing, label smoothing, or manipulating the task head capacity\u2014can improve downstream task performance, especially when pretrained features are not perfectly aligned with the downstream task. It also highlights that different downstream datasets and pretraining conditions may require different degrees of feature adaptation, with some benefiting from preserving earlier pretrained layers and others from reinitializing later layers.\n\nOverall, the study advances understanding by providing a theoretical and empirical framework to analyze feature adaptation beyond simple distance metrics, offering new perspectives on how to prepare task heads for finetuning and suggesting that tailored adaptation strategies can yield better transfer learning outcomes."}
{"question_id": "ffe260fb92f4c53395118a567f59d32fd365c351", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The degree of separability when adapting a model to a task is influenced primarily by the quality and suitability of the pretrained features relative to the downstream task. Specifically:\n\n1. **Pretrained Feature Quality:** If the pretrained features are near optimal for the downstream task, only tiny adaptation is needed, resulting in minimal changes to feature separability. Conversely, if the pretrained features are far from optimal, strong adaptation is required, which can significantly alter feature separability.\n\n2. **Task Head Complexity and Capacity:** Using a more complex task head, such as a two-layer MLP instead of a single linear layer, increases training accuracy after head probing and reduces the need for feature adaptation. However, this does not necessarily improve downstream performance or separability.\n\n3. **Backbone Layer Reinitialization:** Treating some of the later layers of the backbone as part of the task head (i.e., reinitializing them) affects separability. Preserving earlier layers of the pretrained model tends to be beneficial when pretrained features are good, as these layers capture useful features. When pretrained features are poor, discarding later layers and increasing the task head size can improve adaptation and separability.\n\n4. **Dataset Characteristics:** Different downstream datasets may require different degrees of adaptation. For example, datasets with simpler features (e.g., black-and-white lines) may benefit from larger task heads and more extensive adaptation, while datasets with richer pretrained features require less adaptation.\n\n5. **Energy and Direction Terms in Feature Adaptation:** The adaptation of features is linked to the energy and direction terms of the feature gradients during finetuning. These terms influence how features move in the representation space, affecting separability.\n\nIn summary, the degree of separability during adaptation depends on the interplay between pretrained feature quality, task head design and capacity, the extent of backbone reinitialization, downstream dataset characteristics, and the dynamics of feature gradient energy and direction during finetuning."}
{"question_id": "e7ac4283196f09643f681e08d6093e9313983cf1", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "Yes, the translation of the template \u201cthe <morning> starts at <9.00>\u201d has been adapted to account for languages with grammatical gender. For example, in Italian, morning (mattina) is feminine whereas afternoon (pomeriggio) is masculine, which results in variation in the determiner used\u2014\u201cla <time_exp>\u201d for feminine and \u201cil <time_exp>\u201d for masculine."}
{"question_id": "789b3799040a63d59e93a029bab4459c7ff3aa2c", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The phrase \"information exchanges between the two parts of the network\" in Fig. 1 refers to the interaction between the backbone feature extractor \\( f(x; B) \\) and the task head \\( g(z; v) \\) during finetuning. Specifically, it describes how the representation vector \\( z = f(x; B) \\) changes as the network parameters are updated with training samples using gradient descent. This interaction is characterized by the empirical neural tangent kernel (NTK) of the backbone, the gradient of the task head prediction with respect to the representation vector, and the difference between the one-hot label vector and the predicted probability vector. The change in \\( z \\) can be decomposed into terms representing slow changes, direction, and energy, which together capture how updates in the task head parameters influence the backbone features and vice versa. Thus, \"information exchanges\" denote the dynamic feedback loop where updates in one part (head or backbone) affect the other through gradients and kernel interactions during finetuning."}
{"question_id": "08c2ff08d58f88bfead47fc3783d34333d02f023", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The rationale for choosing a label-smoothing coefficient of 0.9 (\u03b7_HP = 0.9) during head probing (HP) is to reserve a certain amount of \"energy\" for subsequent feature adaptation in finetuning. Specifically, by setting the labels during HP as a mixture \u03b7_HP e_y + (1 \u2212 \u03b7_HP) u, where u is a uniform K-class categorical distribution, the HP stage maintains at least (1 \u2212 \u03b7_HP) * \u2225e_y \u2212 u\u2225\u00b2 energy for feature adaptation, even as the number of HP epochs \u03c4 approaches infinity. This reserved energy prevents the model's prediction p_0 from fully converging to the hard labels, allowing for a milder and more effective adaptation of features during finetuning.\n\nChoosing \u03b7_HP = 0.9 means that 10% of the label distribution is smoothed uniformly, which is particularly helpful when the HP training accuracy converges very quickly to above 90%, indicating that the pretrained features are already strong. This mild smoothing ensures that the features continue to adapt during finetuning without losing the benefits of the pretrained representations.\n\nFurthermore, experiments show that if label smoothing is applied both during HP and finetuning with the same coefficient (\u03b7_HP = \u03b7_FT = 0.9), the reserved energy disappears because the labels in both phases become identical, resulting in similar performance to the baseline without smoothing. Therefore, to ensure correct directional adaptation, it is recommended to set \u03b7_HP \u2264 \u03b7_FT when using label smoothing in both phases.\n\nIn summary, a label-smoothing coefficient of 0.9 is chosen to balance preserving pretrained feature quality and enabling effective feature adaptation by reserving sufficient energy during head probing."}
{"question_id": "fced44b64830046f3b8766306510296277091b7a", "paper_id": "nlpeer/ARR-22/3282bdf968d7f87ae69c4ae7fdd35d4e9a46fa83f56d8bee0296c16dcf23b23cb1e4f20f510c7d01fb7be2309c08dd29cc240b54bdcbc3d66969041b5c84077a", "generation": "The provided information does not include specific details or analysis regarding the degree of formality or the use of Persian/Sanskrit forms for the other Indic languages studied (Bengali, Kannada, Telugu, Gujarati) beyond Hindi. The evaluation and experimental results focus primarily on automatic metrics for formality transfer performance across these languages, but do not explicitly describe the linguistic characteristics such as the degree of formality or the influence of Persian/Sanskrit lexical forms in these languages. Therefore, no direct information is available on the degree of formality or the use of Persian/Sanskrit forms for the other languages studied."}
{"question_id": "f29ff7d6be64035f374fe6b3fc470453591154e9", "paper_id": "nlpeer/COLING2020/1570", "generation": "Yes, the annotated mistakes will be released upon the release of ManyNames v2. The dataset ManyNames v2 includes only consistent response sets by excluding names that do not meet criteria for naming the same object and adequacy. However, to facilitate different uses and analyses, the raw verification annotations, which include the inadequacy types and thus the annotated mistakes, will be publicly released in addition to the consistent response sets."}
{"question_id": "9ff146fb1145a7e6cd038252a41b96f5c6ac0494", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "Yes, there are examples of synthetic and ground-truth (real) data used to compare model performance. Specifically, in the training-from-scratch setting on the CIFAR-100 dataset, a synthetic dataset of 50,000 images (500 images per class) was generated to train a ResNet-50 model, achieving a top-1 accuracy of 28.74% on the CIFAR-100 test set. This performance was compared to that of a pre-trained CLIP model trained on real-world data, which performed significantly better. Additionally, it was found that training with only 9,500 real images (95 images per category) achieved comparable performance to training with 50,000 synthetic images, indicating that synthetic data are less data-efficient and effective than real data for downstream classification tasks. This comparison illustrates the performance gap between synthetic and ground-truth data in image recognition tasks."}
{"question_id": "65ca807b7bfc58200ae0e5c46fcec1e31096cbf5", "paper_id": "openreview/ICLR-2022-conf/fQTlgI2qZqE", "generation": "Yes, the performance boost of the ParaACE model observed in the selected datasets is expected to generalize to other datasets as well. The ParaACE model demonstrates improved predictive performance and significant model compression across various synthetic and real-world datasets, including Elevators, Parkinsons, Skillcraft, Bike sharing, Cal housing, Higgs Boson, Spambase, and Diabetes. For most datasets tested, the detected interactions help improve predictive accuracy while substantially reducing model size. Additionally, ParaACE is shown to be sample efficient, surpassing over-parameterized neural networks trained with substantially more data. These consistent results across diverse datasets suggest that the performance gains induced by interaction detection with ParaACE are likely to be observed in other datasets beyond those explicitly tested."}
{"question_id": "726e56359927a7fc14be9019ea69e50929fa9e67", "paper_id": "nlpeer/ARR-22/dd0a83852cb35c8d0ac0c40da6b7ebeb385f9c7e4816ef51f43abd6910a9adacc0daf460dfda4e56f37fa542a749961a78ddbbe74af0326f41e5cd38e867b121", "generation": "The use of predicted cues instead of gold cue information for scope resolution results in a performance drop. This is evidenced by the reported scope resolution results in Table 3, where the scores are lower compared to cue detection results, indicating that errors in cue detection propagate and negatively affect scope resolution accuracy. The discussion highlights that improvements in cue detection, such as those achieved by CueNB, help alleviate errors in scope resolution by better identifying unique cues and correctly delimiting scope boundaries. Therefore, accurate cue detection is critical for effective scope resolution, and using predicted cues rather than gold cues introduces additional challenges that reduce scope resolution performance."}
{"question_id": "9f2536e9ca279c79b121794af57c2ad02d6b13c8", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "The use of synthetic data does not improve the performance of a model when training from scratch with a large amount of ground-truth data available. Synthetic data deliver inferior performance and are much less data-efficient compared to real data in this setting. For example, training a ResNet-50 model from scratch on 50k synthetic images for CIFAR-100 achieves only 28.74% top-1 accuracy, which is significantly lower than models pre-trained on real data. Moreover, it requires around five times more synthetic data to match the performance achieved with real data, and increasing the amount of synthetic data beyond a certain point does not yield further performance gains.\n\nHowever, synthetic data show promising results in large-scale model pre-training for transfer learning, where they can deliver comparable or even superior performance to ImageNet pre-training, especially when used with ViT-based backbones and self-supervised pre-training methods. In these pre-training scenarios, synthetic data can complement or enhance real data, but this improvement is observed primarily in transfer learning contexts rather than direct training from scratch with abundant real data.\n\nIn summary, synthetic data alone do not improve model performance over large amounts of ground-truth data in direct training scenarios, but they are effective and beneficial in large-scale pre-training and transfer learning settings."}
{"question_id": "6ed4842f06973b3b3b83a068d590e3a5421678f8", "paper_id": "openreview/ICLR-2023-conf/T2Ncx_PN2K", "generation": "The RNN-Transducer (RNN-T) architecture described includes a speech module \\( M_S \\) that converts speech frames to vectors, which can be implemented using either a Transformer or a recurrent network such as an LSTM. The text mentions that the speech module typically uses a Transformer or recurrent network to produce these vectors, implying that both architectures are viable options within the RNN-T framework. However, the provided information does not explicitly compare the performance or advantages of using a Transformer-based acoustic model over an LSTM-based one, nor does it provide empirical evidence or theoretical justification that one would improve the model architecture relative to the other.\n\nTherefore, while a Transformer-based acoustic model is a possible choice within the RNN-T architecture, there is no direct evidence in the text to conclude that it would improve the model architecture compared to an LSTM-based acoustic model."}
{"question_id": "94d3d227741b9bf8258649cb3567dc790b9dca07", "paper_id": "nlpeer/ARR-22/91d9e763eb1470028002e5c97689ada4b74f30be17291a14458f7542c387a9f2a7516e60f4022d887bdbd2165aa8cce6d566db380e5a82af677917c48efc2446", "generation": "The authors use regularization specifically for label embeddings to address the anisotropy problem and to promote uniformity in the label representation space, preventing model degeneration. The label embedding regularizer (LER) encourages label representations to be dispersed uniformly on the unit hypersphere, which enhances the discriminative power and stability of the label embeddings. This is achieved by minimizing the exponential mean of the cosine similarity between all pairs of label embeddings, thereby ensuring that label representations do not collapse or cluster too closely.\n\nIn contrast, instance embeddings are aligned to their corresponding label embeddings through the instance-centered contrastive loss, which directly encourages instance representations to be close to their label representations in the semantic space. This alignment inherently provides supervision and structure to the instance embeddings, reducing the need for an additional regularization term. Thus, the regularization is applied to label embeddings to maintain their uniformity and prevent degeneration, while instance embeddings are sufficiently guided by the contrastive alignment with labels."}
{"question_id": "ca87a914265cffe46bfb63e2e24a3568efbc7888", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Yes, there are cases where the 'end' histogram (i.e., the model's prediction after head probing for \u03c4 epochs, denoted as p\u2080) does not match the label vector e_y when using different head types for hyperparameter tuning (HP). Specifically, when using a linear head with small \u03c4 (large energy), the features adapt more unpredictably, and the head may not converge fully to match e_y, resulting in a mismatch between p\u2080 and e_y. Increasing the head capacity, such as using a 2-layer MLP head, can improve the HP training accuracy and bring p\u2080 closer to e_y, especially when the linear head plateaus at lower accuracy levels. However, this also slows down convergence and prolongs the initial chaotic phase of feature adaptation.\n\nAdditionally, label smoothing during HP can deliberately prevent p\u2080 from converging exactly to e_y by mixing e_y with a uniform distribution, thus reserving energy for feature adaptation and ensuring that p\u2080 differs from e_y even as \u03c4 \u2192 \u221e. This technique (lsHP) is useful when a mild adaptation is desired despite high HP training accuracy.\n\nTherefore, the mismatch between the 'end' histogram and e_y depends on the choice of head type, the duration of HP (\u03c4), and label manipulation strategies such as label smoothing."}
{"question_id": "14f24eacc79985de8d643389b87e35ceb5209775", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "The search cost of the proposed \u039b-DARTS method on the DARTS search space and CIFAR-10 dataset is 0.8 GPU days on a GTX 1080 Ti GPU. This cost is twice that of the first-order DARTS (0.4 GPU days) due to two extra forward-backward passes required for estimating the gradients of the regularization term. However, it is 20% less than the second-order DARTS (1.0 GPU days) and about 40% less than SDARTS-ADV (approximately 1.3 GPU days). By using forward or backward finite difference approximations in an interleaved fashion, the cost can be further reduced to around 0.6 GPU days, which is only 50% more than the original first-order DARTS and comparable to DrNAS with progressive search."}
{"question_id": "5af1f02badf7c044e04f7544f4881486216b9f42", "paper_id": "openreview/ICLR-2023-conf/OTbRTIY4YS", "generation": "The new metric, Concept Purity, is introduced because the original metric proposed in Magister et al. (2021) for evaluating concept representations relied on graph edit distance, which is computationally expensive. To address this, Concept Purity is adapted to exploit the annotation of local explanations, specifically using the typology of the motif represented by the local explanation. This adaptation allows for a more efficient and practical evaluation of how well the embedding clusters local explanations without incurring the high computational cost associated with graph edit distance."}
{"question_id": "a7d741be648d514c67c1a0468a78782b19c6d11c", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Label smoothing during head probing (lsHP) is effective in scenarios where the pretrained features are already strong and the standard head probing (HP) training accuracy converges very quickly to above 90%. In such cases, lsHP helps reserve energy for subsequent feature adaptation, leading to improved adaptation without changing HP-train-accuracy. However, when the HP-train-accuracy is low, the assumption that the model's prediction converges to the labels no longer holds, and lsHP does not necessarily bring improvement. Additionally, if label smoothing is applied in both finetuning and head probing with equal smoothing factors (\u03b7_HP = \u03b7_FT), the reserved energy disappears, resulting in performance similar to the baseline without smoothing. Using label smoothing with opposite energy settings can cause larger feature adaptation but worse generalization due to features adapting in conflicting directions. Therefore, label smoothing does not always improve performance and can degrade it when the HP-train-accuracy is low or when smoothing is improperly applied in both phases without ensuring \u03b7_HP \u2264 \u03b7_FT."}
{"question_id": "99d5ca18b41cac7092cd7ca7cf0888b8a29a3018", "paper_id": "openreview/ICLR-2023-conf/OM7doLjQbOQ", "generation": "The new automated image transformation strategy, referred to as ILA-DA (Intermediate Level Attack with Data Augmentation), is designed to improve the transferability of intermediate-level attacks by incorporating data augmentation techniques. The related works section describes that previous transfer-based attacks, including MI-FGSM and DIM, have successfully incorporated data augmentation methods such as momentum, random resizing, zero-padding, and other input transformations to enhance transferability.\n\nGiven that ILA-DA builds upon the concept of integrating data augmentation into attacks to improve transferability, it is consistent with the approach used in MI-FGSM and DIM. The visualization and experimental sections show that ILA-DA is applied in conjunction with I-FGSM and intermediate-level attacks, suggesting that the augmentation strategy is compatible with iterative gradient-based methods.\n\nTherefore, the automated image transformation strategy introduced by ILA-DA can be applied to other transfer-based methods such as MI-FGSM and DIM to enhance their transferability."}
{"question_id": "87b3ef59cb4832acb7306d8368503784307b1adc", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The provided information does not explicitly discuss or compare the advantages of proposal-based object detectors relative to DETR or other similar object detectors. The text focuses on the architecture choices within the GOOD framework, comparing proposal-based detectors (such as OLN built on Faster R-CNN) and proposal-free detectors (such as FCOS), but does not include any direct comparison or advantages of proposal-based detectors over DETR or related models. Therefore, no specific advantages of proposal-based object detectors compared to DETR and similar detectors can be derived from the given content."}
{"question_id": "6f797e6284c2b0ebd83dc98348c33626ac517dbb", "paper_id": "openreview/NeurIPS-2022-dabt/Zx5qJzNesn0", "generation": "The benchmark locations differ not only in weather but also in soil profile characteristics. Specifically, the observations used in the reinforcement learning environment include soil profile nitrate-N (PROF SOIL NO3), soil profile ammonium-N (PROF SOIL NO4), and the sum of microbial biomass N and stabilized soil organic N pools (ORG SOIL N). Additionally, for crop planning experiments, further soil-related observations such as gross N mineralization, gross N immobilization, net N mineralization, nitrification of ammonium, nitrous oxide emissions from nitrification and denitrification, ammonia volatilization, and denitrification of nitrate are considered. These soil profile variables indicate that soil nutrient dynamics and nitrogen cycling processes vary across locations in the benchmark, representing differences beyond weather conditions."}
{"question_id": "0eb6095e3dbae2dd6e1abc90265e56378f49fa1a", "paper_id": "openreview/ICLR-2022-conf/ZTsoE8G3GG", "generation": "No, when using Algorithm 2 with larger vocabulary sizes, individual atoms are not added one-by-one to the partial graph. Instead, the model either adds an entire motif in one step or generates atoms and bonds one-by-one only when not using motifs. The motifs are atom-disjoint and attaching a motif to a partial graph requires adding only a single bond, so there is no need to add individual atoms separately within the motif-based generation process."}
{"question_id": "f8b91940d2ce9e4e0df966f18d724e12b5aac0e5", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "In a fully-supervised setting where models are trained from scratch using synthetic data, synthetic data deliver inferior performance compared to real data and are much less data-efficient. For example, training a ResNet-50 model from random initialization on a synthetic CIFAR-100 dataset of 50,000 images (500 per class) achieves only 28.74% top-1 accuracy on the CIFAR-100 test set, which is substantially lower than the performance of a pre-trained CLIP model. To match this performance using real data, approximately five times less real data (9,500 images, or 95 per category) are required, indicating that synthetic data are less effective and require a larger volume to achieve comparable results. Increasing the amount of synthetic data beyond this scale does not yield further performance gains. The lower effectiveness of synthetic data in this setting is attributed to limitations in data quality and diversity compared to real-world data. \n\nHowever, in large-scale pre-training scenarios, synthetic data can be effective for supervised pre-training, delivering performance close to or even surpassing that of ImageNet-1K real data pre-training when sufficient data amount and diversity are provided. Synthetic data pre-training benefits from increased label space and data scale, and when combined with initialization from real data pre-trained weights, it can further improve downstream task performance. \n\nIn summary, synthetic data in fully-supervised training from scratch are currently less effective and less data-efficient than real data for image recognition, but synthetic data show promise and effectiveness in large-scale supervised pre-training for transfer learning."}
{"question_id": "36fdc759d8b028d2f3c0c5cb9e8c26b5744962d0", "paper_id": "openreview/ICLR-2023-conf/OM7doLjQbOQ", "generation": "ILA-DA consistently outperforms other transferable attack methods, including LinBP and the CTM family, across different choices of intermediate layers. Experimental results show that ILA-DA achieves strictly higher attack success rates than ILA (the baseline intermediate-level attack) on all tested intermediate layers for models such as ResNet50, VGG19, and Inception V3. The performance of ILA-DA is robust to the selection of the intermediate layer near the default layer proposed by Huang et al. (2019), exhibiting an inverted U-curve pattern that indicates low sensitivity to layer choice.\n\nFurthermore, when compared to state-of-the-art transfer-based attacks such as MI-CT-FGSM and NI-CT-FGSM (members of the CTM family), as well as LinBP, ILA-DA demonstrates superior attack success rates on both undefended and defended models. For example, on defended models including adversarially trained Inception V3 variants and top defenses from the NIPS 2017 competition, ILA-DA significantly improves attack success rates over these baselines. This improvement is attributed to the extensive data augmentations used in ILA-DA, which enable the attack to exploit gradients that better generalize across different architectures.\n\nIn summary, ILA-DA not only surpasses the original ILA method across various intermediate layers but also outperforms other advanced transferable attacks such as LinBP and the CTM family in terms of attack success rate and robustness to layer selection."}
{"question_id": "ba8b96d10b44463b1ec163db65a29b6145f8729a", "paper_id": "openreview/ICLR-2023-conf/ySCL-NG_I3", "generation": "The learning curves for the HMR model on the rigid protein docking task, as shown in Figure H.2, illustrate the training dynamics over 80 epochs. The curves track multiple performance metrics including total loss, F-score, AUC (Area Under the Curve), and average precision (AP) on both training and validation splits.\n\n- **Total Loss:** The total loss decreases steadily throughout the training process, indicating effective optimization and convergence of the model parameters.\n\n- **F-score:** The F-score improves progressively, reflecting enhanced balance between precision and recall in the binding site prediction task as training proceeds.\n\n- **AUC:** The AUC metric shows a consistent upward trend, demonstrating increasing discriminative ability of the model to distinguish binding sites from non-binding sites.\n\n- **Average Precision (AP):** The AP metric also increases over epochs, with the model achieving its best validation AP score near the end of training. This metric is used as the criterion for selecting the final model for testing on the DB5.5 dataset.\n\nOverall, the learning curves indicate stable and effective training dynamics, with continuous improvement in both loss minimization and predictive performance metrics. The model does not exhibit signs of overfitting, as validation metrics improve alongside training metrics, supporting the robustness of the learned molecular representations for rigid protein docking."}
{"question_id": "f18d6c2ac81007f7b42cab31234075307c85ee0a", "paper_id": "nlpeer/ARR-22/6262c349a3980c6850e252b3a44391b1b1318c26fbf29be55c61c0a32b8b626e5581c98234d1ecb410674c00ebcf3ba2adfdce41c2b2d32211721372a8ed1bc0", "generation": "Yes, the training languages used for the non-baseline models in the cross-lingual experiments include German. Specifically, the models were fine-tuned on a randomly chosen 30-minute subset from the Karlsson dataset, which contains German speech. Additionally, the pretraining data of the low-resource model includes Russian speech, but the fine-tuning data for the cross-lingual experiments involves German, indicating overlap with German during fine-tuning."}
{"question_id": "c0701214e646badb3bbb44e4985982c8e9e65fc7", "paper_id": "nlpeer/ARR-22/83dc885bef279cbb7a3b5b173494428b6cd043d34527016519250477d274fd1793fa0bddd2bd4bbb3edfa9709ddc85e90825a0554bfa8eceb8cb34d813c06c53", "generation": "To ensure the accuracy of the confidence network in neural machine translation (NMT), the proposed approach involves learning an unsupervised confidence estimate jointly with the training of the NMT model. Confidence is defined as the number of hints the NMT model requires to make a correct prediction, with more hints indicating lower confidence. The model is allowed to request hints to improve translation accuracy at the cost of a slight penalty. The level of confidence is then approximated by counting the number of hints used. This method enables the confidence estimate to better reflect predictive uncertainty compared to raw softmax probabilities, which are known to be miscalibrated and often overconfident in errors. The learned confidence estimate has been demonstrated to achieve high accuracy on extensive sentence- and word-level quality estimation tasks. Furthermore, analytical results verify that this confidence estimate can correctly assess underlying risk in real-world scenarios, such as detecting noisy samples and out-of-distribution data. This joint learning framework and the hint-based mechanism constitute the key measures taken to ensure the accuracy of the confidence network."}
{"question_id": "e57f2a5a860c3aa8c1e0f8ca5a3375dd735d463c", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "The performance drop after 100 epochs in Figure 4 (c) is attributed to the issue of performance collapse caused by low layer alignment and vanishing gradients in DARTS. Specifically, the optimal architectures for different layers become vastly different due to skip-connections dominating deeper layers and convolutions dominating shallower layers, leading to gradient magnitude disparities and suboptimal architecture selection. This results in a consistent decline in the discovered architecture's performance over extended search epochs. However, \u039b-DARTS mitigates this collapse by harmonizing operation selection among cells, achieving convergence around 100 epochs with only a negligible performance drop thereafter."}
{"question_id": "46357f5d8816d410e6100ea03a5fde2f576ae270", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "The extracted patterns are representative traffic speed patterns derived from historical data, specifically average daily patterns consisting of 288 speed data points per vertex, corresponding to 24 hours with 5-minute intervals. These daily patterns are sliced into segments using a given window size, resulting in a set of traffic patterns. The patterns exhibit high correlation between leading and trailing segments, even over short-term periods, and the initial extracted pattern set shows a biased distribution with many similar patterns (class imbalance). To address this, clustering-based undersampling using cosine similarity is applied, grouping patterns with similarity above a threshold into clusters and selecting cluster centers as representative patterns. This process yields a balanced and representative pattern set used as keys for memory access in the forecasting model. The patterns effectively capture repeating traffic behaviors and enable accurate matching and prediction of future traffic speeds, including abrupt changes and complex dynamic conditions."}
{"question_id": "7a4e6842b9fed6c17b9fc508c5e7f7bdc1614d7c", "paper_id": "openreview/ICLR-2023-conf/ySCL-NG_I3", "generation": "When designing network architectures for molecular representation learning, several specific guidelines and strategies are emphasized:\n\n1. **Equivariance Considerations**: Traditional geometric deep learning (GDL) approaches require equivariant networks to ensure molecular representations transform appropriately under rotation and translation. However, enforcing equivariance can limit network expressive power. Therefore, it is desirable to develop representations that properly encode 3D molecular structure while bypassing the equivariance requirement.\n\n2. **Multi-Resolution Representation**: Molecular representations should capture features at various resolutions to accommodate different tasks. For example, high-level holistic features are needed for molecular property prediction, while fine-grained features are necessary for describing specific molecular interactions such as protein binding interfaces. Designing efficient multi-resolution message passing mechanisms is ideal, especially for encoding molecules of distinct sizes, including macromolecules with tens of thousands of atoms.\n\n3. **Use of Molecular Surface Representations**: Instead of relying solely on Euclidean space representations, leveraging the molecular surface as a high-level representation of molecular shape is advantageous. The molecular surface encodes key information about intermolecular interactions, including shape complementarity and chemical interactions, making it an ideal candidate for molecular representation learning.\n\n4. **Avoiding Deep or Large Neighborhoods for Long-Range Communication**: In Euclidean space, long-range communication between distant atoms is typically achieved by stacking deep layers or increasing neighborhood radius, which can hinder effective representation of large molecules. Residue-level graph representations and multi-resolution approaches help mitigate this issue.\n\nThese strategies collectively aim to improve the expressive power, efficiency, and applicability of molecular representation networks across diverse molecular sizes and tasks."}
{"question_id": "030389c23b9697a71ca59dec3bcab088d7f20ced", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "No, equation 1 is a summation from j = 1 to k, not from j = 0 to k."}
{"question_id": "9246fb2439ec9512f9298b927660f030736765c0", "paper_id": "openreview/ICLR-2023-conf/T2Ncx_PN2K", "generation": "The training process for the proposed model TOLSTOI is not truly end-to-end. Although the RNN-Transducer (RNN-T) architecture itself is trained end-to-end on paired speech and text data, the adaptation approach involves a separate imputation model that generates speech module output vectors (h vectors) from text alone. This imputation model is trained independently to produce these intermediate speech representations from text, rather than generating raw audio. Subsequently, the pretrained RNN-T model is fine-tuned on a proxy parallel dataset created by pairing target domain text with imputed h vectors. During this fine-tuning, only the language module and joint network parameters are updated, while the speech module parameters remain fixed. Thus, the overall adaptation procedure involves modular training steps rather than a single end-to-end training process."}
{"question_id": "67314f99bdc98da9611efbf0de1f4660e36f629c", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "The sampling method used to extract patterns with a time window involves first computing an average daily traffic speed pattern consisting of 288 data points per vertex, corresponding to 24 hours with 5-minute intervals. Then, the daily patterns are sliced into segments using a given window size \\( T' \\), resulting in multiple traffic patterns. This slicing process creates the initial pattern set \\( P \\), where the number of patterns is \\( N \\times \\lfloor 288 / T' \\rfloor \\), with \\( N \\) being the number of vertices. To address class imbalance caused by many similar patterns, clustering-based undersampling is applied using cosine similarity. Patterns with cosine similarity above a threshold \\( \\delta \\) are grouped into the same cluster, and the cluster centers are used as representative patterns for memory access."}
{"question_id": "3f6d76b052a19c42fdc0f3fa7a03e99d76e053d5", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "The navigation tasks require the semantic layout of unseen regions in order to be effectively solved. The proposed approach actively learns to predict (hallucinate) semantics in unobserved areas of the map, including both objects and scene structures, and leverages the uncertainty over these predictions to plan paths toward the target. This capability to infer semantic information beyond the agent\u2019s current field of view enables the agent to encode spatial associations and semantic priors that guide navigation in novel environments. Traditional methods that rely solely on observed areas or accurate 3D metric maps do not capture the uncertainty or semantic context of unobserved spaces, which is crucial for target-driven navigation tasks. Thus, incorporating semantic layout predictions of unseen regions is essential for solving object goal navigation in unknown environments."}
{"question_id": "cbd5e6e55ec199de5569c76823febc8a19d28e5e", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "Yes, the NAS method \u039b-DARTS can be trained on a large-scale dataset such as ImageNet. The search was performed using the PC-DARTS setting on ImageNet with a search model consisting of 8 layers (6 normal and 2 reduction cells) and 16 initial channels. The method utilized a batch size of 384 and a learning rate of 0.5, which was linearly decayed to zero. Evaluation was conducted with a model of 14 layers and 48 initial channels for 250 epochs. The results demonstrated improved top-1 and top-5 accuracy compared to baseline methods, including DARTS, \u03b2-DARTS, and DrNAS, indicating the effectiveness of \u039b-DARTS on large-scale datasets."}
{"question_id": "d539111f12ea7af828c1637c34c8e6fcb06f589a", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "The framework for estimating epistemic uncertainty using the variance of ensembles is described in the context of the model outputting a probability distribution over semantic map predictions, i.e., logits. The model f is constructed as an ensemble of segmentation models with different random initializations, and epistemic uncertainty is approximated from the variance between the outputs of these models in the ensemble. This variance is computed over the probability outputs (logits) of the classifier.\n\nThere is no explicit discussion or evidence in the provided text about applying this ensemble variance framework directly in the semantic feature space rather than in the space of logits. The method relies on interpreting the model output as a likelihood function over parameters and averaging predictions to approximate the true probability distribution, which naturally aligns with operating in the output (logit) space.\n\nTherefore, while the ensemble variance approach is effective for uncertainty estimation in the logit space, the text does not provide evidence or analysis supporting its direct application or equal effectiveness in the semantic feature space. The framework as described is specifically tailored to the probabilistic outputs (logits) of the model rather than intermediate semantic features."}
{"question_id": "d59bc31fea9ec1c2594f0ed7813ed2d9348abc75", "paper_id": "nlpeer/COLING2020/939", "generation": "The task described in the paper focuses specifically on hypernym prediction, which involves identifying the correct direct hypernym (target node) in a taxonomy for a given synset (source node) based on its embedding. This task is formulated as finding the correct point to attach a new synset to the existing WordNet taxonomy, predicting direct hypernym relations rather than indirect ones.\n\nIn contrast, taxonomy induction refers to the broader task of constructing or extending an entire taxonomy, which involves organizing terms into a hierarchical structure of is-a relations. Taxonomy induction encompasses the creation or expansion of the taxonomy as a whole, rather than predicting a single direct hypernym for a given synset.\n\nThus, the key difference is that hypernym prediction is a focused relation prediction task targeting the direct hypernym of a single synset within an existing taxonomy, whereas taxonomy induction involves the broader construction or extension of the taxonomy structure itself."}
{"question_id": "b9dc0dacfa9d5676e09c6d6308a65cb2885392cd", "paper_id": "nlpeer/ARR-22/24714d27941075cbad95c02db3ac730df71d355f85f5d247a62c9453ea29004b15cfb386663c82ce55ba17d652253064f4ff70f21dd5c08d8e39986ab22c45ce", "generation": "The results of the Montreal Forced Aligner, when applied to singing recordings for obtaining alignments to lyrics, have not been explicitly stated as manually checked for accuracy. The ground-truth pitch alignment is derived from the alignments to lyrics obtained by the Montreal Forced Aligner, leveraging the shared lyrics in paired data from PopBuTFy, but there is no mention of manual verification of these alignments in the provided information."}
{"question_id": "05fe45ed14b202a953eb1a25a0c4552d1d2ff42d", "paper_id": "openreview/ICLR-2022-conf/k7efTb0un9z", "generation": "Yes, the proposed method can be applied to simpler models such as multilayer perceptrons (MLPs). This is demonstrated by the evaluation of training a 3-layer MLP with a hidden size of 128 on the Fashion MNIST dataset, where the method (GNS) achieved the best test accuracy of 89.8%, outperforming other optimizers and schedulers."}
{"question_id": "d8ac040e919b01e19818a6416896dd66bd58e69d", "paper_id": "nlpeer/COLING2020/939", "generation": "The success of the path-based model, specifically the hypo2path model, can be attributed to formulating hypernym prediction as a sequence generation task where taxonomy paths in WordNet are generated. This approach leverages the structural information encoded in WordNet paths, allowing the model to capture hierarchical relationships more effectively than direct hypernym prediction methods. The hypo2path model, implemented as a sequence-to-sequence architecture with Luong attention and trained to generate full taxonomy paths, demonstrated improved performance by utilizing the rich path information rather than relying solely on direct hypernym links. Additionally, the use of an encoder-decoder framework with LSTM units and attention mechanisms facilitated effective encoding and decoding of these paths, contributing to the model's state-of-the-art performance."}
{"question_id": "7f3ceaefa9722ecb3ce14f4c48d0191a6893f607", "paper_id": "nlpeer/F1000-22/11-222", "generation": "The lupeol and pristimerin used in this study were obtained as plant-derived extracts from Cayman Chemicals (MI, USA)."}
{"question_id": "0d68ad6ddb3ddfccd1c2d71ae7fc8a724843e891", "paper_id": "nlpeer/F1000-22/10-72", "generation": "The econometric models used to write equations (2) through (10) are based on dynamic panel data estimation techniques, specifically employing the system Generalized Method of Moments (system-GMM) estimator. This approach addresses issues of endogeneity, omitted variable bias, and unobserved country-specific effects by using internal instruments derived from lagged values of dependent and independent variables. The system-GMM estimator is particularly suitable for models with lagged dependent variables and potential simultaneity between foreign direct investment (FDI) inflows and domestic investment (DI), as it controls for correlation between regressors and error terms and accounts for weak exogeneity assumptions.\n\nThe economic theory underlying these equations involves the dynamic relationship between FDI and DI, considering the levels of development of countries (developing, economies in transition, and developed countries). The model incorporates lagged effects of DI and FDI, growth rates (GR), and time trends (TIME), as well as interaction terms that capture differential effects of FDI on DI across country groups. The classification of countries by development level reflects macroeconomic conditions influencing the FDI-DI relationship, consistent with theories that FDI impacts domestic investment differently depending on economic development status.\n\nEquations (3), (4), and (5) represent the short-run effects of FDI on DI for developing countries, economies in transition, and developed countries, respectively, by combining estimated coefficients corresponding to these groups. Equation (6) formulates the null hypothesis for the combined short-run effects across all three country groups.\n\nIn summary, the models are dynamic panel data regressions estimated via system-GMM, grounded in economic theories of FDI's heterogeneous impact on domestic investment conditioned by country development levels and temporal dynamics."}
{"question_id": "280960bc073f24e47cd5b63da7388c21eb12d9be", "paper_id": "openreview/ICLR-2023-conf/pWVASryOyFw", "generation": "The CNN classifier was selected for the sentiment analysis experiments to ensure a fair comparison with prior work by Liu et al. (2019), as the CNN classifier was pre-trained for two epochs following their methodology."}
{"question_id": "ae75b890d30e2879b6a6571bbc634ee4e6157e30", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "Measuring the performance of a method using mean average precision at gold-k (MAP@k_g) serves to characterize the effectiveness of entity set expansion (ESE) methods in scenarios where coverage of large concept sizes is important, such as knowledge base population. This metric adapts to different concept sizes and provides an estimate of recall, which is crucial for real-world settings with commonly large entity sets. Unlike evaluation at a fixed small k (e.g., k=20), MAP@k_g includes more instances of multifaceted, vague, and non-named entities that are typically present in user-generated text but may be ignored in smaller k evaluations. Therefore, MAP@k_g acts as a complementary metric designed to stress test ESE methods, helping practitioners assess the suitability and robustness of methods for practical applications involving comprehensive entity coverage."}
{"question_id": "9d42f16a53845fd4e37c5676307657425faf1538", "paper_id": "nlpeer/ARR-22/841d64f4eb3d5e3d6ea89751ea19897bb8424acf856c9dd1574ef7d2803ff7d155df1b9cc201df8b4fc8c1984f6ca8bdfaf94036960348047e8da35b0003ae34", "generation": "Yes, it is feasible to generate a lattice graph for a large dataset. The method uses hypothesis recombination combined with best-first search to compactly encode a large number of generation candidates in a lattice structure. The number of nodes in the lattice is bounded by the number of node expansions during beam search, ensuring that the lattice can be stored compactly and does not require excessive memory. The wall clock time for generating lattices is manageable, typically between 1 and 10 seconds for summarization tasks, and the algorithm can be parallelized across examples to improve efficiency. Experiments conducted on large datasets, including 100,000 translations or summaries sampled from validation sets, demonstrate that the lattices successfully encode large numbers of high-quality and diverse generation options, confirming the scalability of the approach."}
{"question_id": "fd9af00fe3f20196d71e9e364f55c157d4cd2cd3", "paper_id": "nlpeer/COLING2020/939", "generation": "To ensure that paths were not shared between the training and test sets, the validation and test sets were filtered to include only hyponym queries that were unseen anywhere in the full taxonomy paths of the training data. Additionally, hyponyms appearing as hyponyms in the training set were excluded from the test and validation sets to prevent models from merely copying. This filtering created a subset, denoted as WN18RR-hp, which guaranteed that all evaluated queries were new to both path-based and non-path models."}
{"question_id": "23c1d98a22e68ab8a92b7b1cd2fee83fa79e9a86", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The optimal depth of a ranked list of entities returned by an entity search engine approach should correspond to the actual concept size, which varies and can be much larger or smaller than commonly used fixed cutoffs such as 10 to 50 entities. For example, in the Tripadvisor and Wiki datasets, the median concept sizes are 121 and 205 entities, respectively. Therefore, limiting evaluation or retrieval to a small fixed set (e.g., fewer than 50 entities) may not sufficiently represent the full set of relevant entities, especially for multifaceted, vague, and non-named entities prevalent in user-generated text. Hence, the ranked list depth should be adaptive to the concept size to ensure comprehensive retrieval of relevant entities."}
{"question_id": "e111e75817d67b6fbeec06d5ba117b3419bf2f0f", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The initial value being discussed in Section 3.2 is the initialized task head parameter denoted as \\( v_0 \\). This parameter serves as the starting point for finetuning and influences the adaptation of features learned from the pretraining stage to the downstream task. The analysis focuses on how \\( v_0 \\) affects the change in representations and the behavior of certain key terms, such as \\( \\operatorname{tr}(B_0^\\top B_t) \\) and \\( \\operatorname{tr}(B_t^\\top B_t) \\), where \\( B_0 \\) is fixed and \\( B_t \\) depends on \\( v_0 \\). The goal is to find a good initialization \\( v_0 \\) that leads to better downstream performance by enabling better feature adaptation."}
{"question_id": "e26fc7a2455a2acb2de4d608a7ca7bf1c8fb62a1", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "For a neural module to be \"in charge of solving one specific subproblem\" means that each module m_i is responsible for addressing a distinct subproblem F_i within the overall task. There is a one-to-one and onto mapping between subproblems and modules, such that each module specializes in solving its assigned subproblem. This specialization entails that the module receives only the subset of the state components relevant to its subproblem and outputs the solution to that subproblem. Consequently, all tasks requiring the solution to subproblem F_i share the same module m_i. The modular architecture constructs the overall policy by chaining these modules in sequence, with each module focusing solely on its designated subproblem without needing to process or pass through extraneous information related to other subproblems."}
{"question_id": "f2564b011de1ce166a9e4410c3857b5a9c2496ce", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The significant performance difference between CGExpan and LM-Base can be explained by their differing behaviors on concepts with characteristics typical of user-generated text. While CGExpan and LM-Base have comparable performance on well-formed concepts (e.g., \"company\" in the Jobs benchmark), LM-Base outperforms CGExpan on concepts involving entities with user-generated text characteristics (e.g., \"seating arrangement\"). CGExpan tends to retrieve entities that co-occur frequently with the concept name, which may not be effective for diverse or multifaceted entities common in user-generated text. In contrast, LM-Base, which uses simpler prompting of language models with Hearst patterns without additional mechanisms like concept name guidance or iterative expansion, is less penalized by the diverse contexts of entities in user-generated text. This leads to LM-Base showing smaller drops in performance compared to CGExpan on user-generated text benchmarks, where the latter exhibits larger performance declines due to the challenging characteristics of such text."}
{"question_id": "be0cd13d8445fb87a73943d5acf2e5089a02876c", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "The optimization in Equation (1) for active training selects locations \\( l_j \\) from the map to maximize epistemic uncertainty as a proxy for information gain. The map region considered is an \\( h \\times w \\) egocentric local region over which the model estimates the semantic map \\( \\hat{m}_t \\). This local region is egocentric, with the agent at the center, and the semantic map predictor produces predictions registered to a global map during navigation.\n\nFor goal selection at test time, Equation (2) uses an upper confidence bound policy to select goals from any map region the agent has either observed or hallucinated. This indicates that the optimization for goal selection is performed over both observed and hallucinated regions of the map, not limited strictly to observed locations.\n\nTherefore, the optimization does not happen solely over the geocentric map locations observed so far but includes both observed and hallucinated map regions."}
{"question_id": "a3566edd083568caf0264186c9b8e1658c31e561", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The rationale behind the design of the different modules is to assign each neural module \\( m_i \\) the responsibility of solving one specific subproblem \\( F_i \\), establishing a one-to-one and onto mapping from subproblems to modules. This modular decomposition allows all tasks requiring the same subproblem \\( F_i \\) to share the corresponding module \\( m_i \\). The architecture assumes that the state representation can be factorized into module-specific components, such that each module receives only the subset of the state relevant to its subproblem and outputs only the solution to that subproblem. This design avoids the brittle dependencies and cascading effects inherent in a pure chaining architecture, where the entire input passes sequentially through all modules. By decomposing the input and passing task components separately to distinct modules, each module focuses solely on its assigned subproblem, facilitating better generalization to unseen task combinations and reducing the complexity of module interactions. This modular structure also supports lifelong reinforcement learning by enabling flexible adaptation and reuse of modules across tasks while minimizing interference and catastrophic forgetting."}
{"question_id": "c0ddf26bae180b57c24cd4b90e7a0da4a0676425", "paper_id": "openreview/ICLR-2022-conf/KTPuIsx4pmo", "generation": "A real-world pushing task involves using a robotic arm, specifically a UR5 robot equipped with an RGB camera, to push a designated target object to a specified target area, such as a pink region. The task requires the robot to identify and manipulate the correct object among distractors and successfully push it within the target zone. The task is evaluated by testing the robot's ability to push novel objects, with success defined as positioning the target object within the center of the target area. This task is more complex than simulated pushing due to factors such as observation and action noise, misaligned paired data, and differing perspectives between human demonstrations and robot observations. Variants of the task include pushing the target object forward by a certain distance (e.g., more than 5 cm) amid distractors, which is considered an easier version. The real-world pushing task is used to assess the performance of meta-imitation learning methods by measuring success rates and analyzing failure modes related to object identification and control."}
{"question_id": "3611098cfd2590d775531ef564d87617713fe8bf", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The rationale for using entity set expansion (ESE) methods on user-generated text is that user-generated text data is abundant and largely unlabeled, making it a valuable resource for mining entities necessary for downstream natural language processing applications such as semantic search and question answering. However, user-generated text differs significantly from well-curated text in characteristics such as having a higher prevalence of multifaceted entities (entities belonging to multiple concepts), non-named entities (common noun phrases rather than proper names), and vague entities (entities with subjective or unclear concept labels). These distinctive characteristics, along with the scarcity of training data in new domains, necessitate the use of low-resource ESE methods that can expand a small seed set of entities with limited supervision. This enables effective entity extraction from user-generated text, which is essential for enabling NLP applications over these largely unlabeled and diverse data sources."}
{"question_id": "1740b93cc1257022895a050e975d38feebe0f904", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "Yes, the activity graph is an abstraction introduced in the MOMA-LRG dataset that serves as a single universal representation of human activities encompassing multiple levels of granularity. It captures hierarchical and compositional aspects of activities across three levels: activity, sub-activity, and atomic action. This abstraction enables evaluation and parsing of activities at different granularities, from coarse activity class labels to temporally localized sub-activities and fine-grained atomic actions involving entities and their interactions. The activity graph formulation allows for a unified task of hierarchical video understanding and supports evaluation of models on all these levels simultaneously."}
{"question_id": "be6ee11df60dadea667438571e3ed15560c3cb04", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The MOMA-LRG dataset differs from the MOMA dataset in several key aspects. First, MOMA-LRG introduces a new abstraction of human activity through activity graphs that serve as a single universal representation encompassing video understanding at the activity, sub-activity, and atomic action levels, whereas MOMA only addressed the atomic level. Second, MOMA-LRG contains an order of magnitude more annotations and includes longer videos from a greater variety of scenes. Third, MOMA-LRG introduces a new annotation schema that is easily convertible from natural language to graphical annotations, enabling few-shot learning capabilities. Finally, MOMA-LRG is motivated by the limitations of video-language models (VLMs) and provides a new framework (GraphVLM) to evaluate VLMs on complex activity parsing, which was not a focus of the original MOMA dataset."}
{"question_id": "8952c8598f43e3e36131d56d62db44fded0352d3", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The improved performance of the LM-Base model compared to the CGExpan model on user-generated text datasets is primarily due to the following factors:\n\n1. **Handling of Multifaceted and Vague Entities:** CGExpan employs a scoring mechanism that selects one positive concept and multiple negative concepts to rank candidate entities. This approach penalizes entities that are multifaceted (belonging to multiple concepts) or vague (mentioned in diverse contexts), which are common characteristics in user-generated text. In contrast, LM-Base does not implement such restrictive mechanisms, allowing it to better accommodate entities with diverse and ambiguous contexts.\n\n2. **Use of Contextual Representations Without Overly Restrictive Filtering:** LM-Base leverages language model (LM) probing with Hearst patterns to score entity candidates without additional mechanisms like concept name guidance or iterative expansion used in CGExpan. This simpler approach avoids disregarding noisy or ambiguous context features, which are prevalent in user-generated text, thereby improving ranking quality.\n\n3. **Robustness to Diverse Contexts:** CGExpan tends to retrieve entities based on frequent co-occurrence with the concept, which may not capture the varied and informal nature of user-generated text. LM-Base\u2019s reliance on LM-based contextual representations enables it to better capture the multifaceted and diverse contexts of entities in user-generated content.\n\n4. **Avoidance of Penalizing Ambiguous Contexts:** SOTA methods like CGExpan are designed to avoid selecting ambiguous contexts to improve precision on well-curated datasets. However, this design choice leads to suboptimal performance on user-generated text, where ambiguity and vagueness are inherent. LM-Base\u2019s simpler design does not penalize such ambiguity, resulting in better performance.\n\nIn summary, LM-Base\u2019s simpler, less restrictive approach to entity ranking, which embraces the multifaceted and vague nature of user-generated text, contributes to its superior performance over CGExpan on these datasets."}
{"question_id": "bf76bbb77fabe1a9105b55efcd591d179958b2c6", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "Optimizing the reward with respect to the possible combinations of modules means searching over different ways to combine a set of pre-trained neural modules to identify the combination that yields the highest reward for the current task. This involves evaluating the performance of each candidate module combination by executing the corresponding policy and measuring the reward obtained. The goal is to discover which subset or configuration of modules, when composed together, best solves the task, thereby enabling efficient reuse of existing knowledge. This process is typically performed during an online training phase where the shared modules remain fixed, allowing the agent to select the most relevant modules without altering their parameters initially. Subsequently, exploration and adaptation can be performed on a copy of the selected modules to refine performance while preventing catastrophic forgetting."}
{"question_id": "e5d8459c3ebc7cdeb1a56ddced28a7467921a917", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The videos in the MOMA-LRG dataset were collected from YouTube."}
{"question_id": "c0f788c455af0bee35fb5b9cc36af6a5ec6aaf3f", "paper_id": "nlpeer/ARR-22/212dff6380418cd7c050ec757b31e6b5b65bbe922aa30ffbd5950fe7a04ca737b7c7b3d706f1cd1502d7932b61d2b7c079924793e45e437526230c1e9c0626ed", "generation": "Additional fine-tuning of CLIP was not included in the experiments because fine-tuning the last few layers of CLIP with a classification head consistently resulted in worse performance compared to using extracted features for classification with SVM. This outcome is attributed to the relatively small size of the labeled dataset, which is insufficient for effective fine-tuning of CLIP for the claim detection task."}
{"question_id": "06b380902968cd38bbb66c2a75d9372c2f039f2f", "paper_id": "openreview/NeurIPS-2022-dabt/in7XC5RcjEn", "generation": "A graph with an average diameter in the range of 10-50 indicates a sufficiently large graph size, which is critical for the presence of long-range interactions (LRIs). Larger graph sizes increase the likelihood that local message passing GNNs (MP-GNNs) will encounter information bottlenecks or oversquashing, as the node receptive field grows exponentially with the number of layers. This bottleneck limits the ability of local MP-GNNs to capture distant information, thus necessitating models that explicitly model LRIs, such as fully-connected or Transformer-based GNNs.\n\nSpecifically, datasets with small graph sizes and small problem radius r (e.g., ZINC with 9-37 nodes and small diameter) do not require long-range signal propagation, and local MP-GNNs perform well without bottlenecks. In contrast, graphs with larger diameters (e.g., average diameter around 10-50 as seen in the Peptides-func and Peptides-struct datasets) imply a larger problem radius and longer-range dependencies. This larger diameter increases the need for models capable of capturing global structural information and distant node interactions, which are not effectively handled by local MP-GNNs alone.\n\nTherefore, an average diameter of 10-50 serves as an indicator that the graph is large enough to exhibit long-range relationships, making it a suitable candidate for benchmarking models designed to capture LRIs."}
{"question_id": "0dcfbcf7b77777639a682294aaf99c3fff25cd20", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The authors ensure that the natural language sentences produced from the \"ground truth\" activity graphs accurately describe the scene by converting all levels of the MOMA-LRG activity graph hierarchy into natural language through a graph-to-language module. This module represents each class name at different hierarchy levels with appropriate linguistic forms: at the activity level, class names are used as nouns or prompted phrases (e.g., \"A video of [CLS_NAME]\"); at the sub-activity level, class names are expressed as descriptions in the present continuous tense in a narration style; and at the atomic action level, predicates are tagged with [src] and [trg] templates to form grammatically correct present continuous sentences. For example, a predicate such as \"touching\" is represented as \"[src] touching [trg]\", which, given entities [src]=person and [trg]=table, produces the sentence \"A person is touching the table.\" This structured and templated conversion ensures that the generated natural language sentences are semantically precise and grammatically correct, thereby accurately describing the scene depicted by the ground truth activity graphs."}
{"question_id": "ff310f12cf0c134c9763ec3389c106e6c16dc65c", "paper_id": "openreview/ICLR-2022-conf/ZTsoE8G3GG", "generation": "The metric \"score\" in Table 2 is a task-specific real number in the range [0, 1], where higher values indicate better performance, reflecting how well the generated molecules match a target molecular profile. The \"quality\" metric measures the proportion of generated molecules that pass predefined quality filters, which are the same as those used by Brown et al. (2019). In scaffold-constrained optimization tasks, molecules that do not contain the required scaffold receive a total score of 0, as scaffold presence is treated as a binary component in the scoring function aggregated via the geometric mean."}
{"question_id": "b6cb81cf492f5369fa4051c1d2e90b05b0aa9247", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The modules were partitioned according to distinct subproblems, with each module responsible for solving one specific subproblem \\(F_i\\). The state input was decomposed into module-specific components, such that each module received only the subset of the state relevant to its assigned subproblem. For example, in the discrete 2-D tasks, the architecture processed the static object first, then the target object, and finally the agent dynamics, with separate modules for each: a static object module, a target object module, and an agent module. Each module consumed input channels corresponding to its task component and processed them through dedicated neural network blocks. The outputs of earlier modules were incorporated into subsequent modules via preprocessing networks and concatenation, ensuring that each module focused solely on its relevant component without interference from distractor features. This modular decomposition allowed for specialized processing and facilitated generalization to unseen task combinations."}
{"question_id": "a37fa0862f28bd4005c14092d19b86f84c30c983", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The criteria for selecting videos and classes in the study included ensuring that each activity class in the taxonomy was gender-neutral, culturally inclusive, and friendly toward people from different socioeconomic backgrounds. The video selection was conducted by a diverse team of researchers from different ethnicities and genders who examined and discussed each video to ensure diversity and the absence of offensive content. Additionally, keywords from multiple languages, including but not limited to English, Chinese, French, and Japanese, were used to search for videos to enhance diversification, robustness, and generalization of the models while significantly reducing potential bias in the dataset."}
{"question_id": "e774f0bb72932f381463769b74f98a8f360db732", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "GraphVLM is a framework designed to evaluate video-language models (VLMs) on the task of activity parsing, which involves generating activity graphs as a unified representation of human activities. Activity graphs, introduced as a key abstraction in MOMA-LRG, are dynamic graphs that capture hierarchical and compositional aspects of activities at three levels: activity, sub-activity, and atomic action. GraphVLM incorporates a video stream, a text stream, and shared video tokenizers across all three levels of the MOMA hierarchy to parse these activity graphs from videos. It also employs a transfer learning approach based on knowledge distillation, enabling flexible and lightweight adaptation of VLMs for fine-grained, actor-centric activity recognition. Thus, GraphVLM operationalizes the activity graph abstraction by providing a model architecture and evaluation framework that leverages the structured graphical representation of activities introduced in MOMA-LRG, facilitating few-shot learning and improved interpretability in complex human activity understanding."}
{"question_id": "fd627c54f792bc91ab0db4a51e8311a23d8af60d", "paper_id": "nlpeer/ARR-22/82520f1d31572d488cfeae02712db642750fda4063e5eab8a771a000f6e17e1397ab18af73e5362e4d660b3dcc78b2260e121fdefc4c1664448e1c30066d5cb9", "generation": "For prompts 7 and 8, which did not have trait-specific scores in the original ASAP AEG dataset, the trait-specific scores used in the experiments were obtained from the trait-specific scores provided by Mathias and Bhattacharyya (2018a)."}
{"question_id": "62968052606a9490b4add4170997a2c69f19d4ec", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "Yes, the dataset is planned to be licensed under a Creative Commons license, specifically the Attribution 4.0 International (CC BY 4.0) license."}
{"question_id": "507cdeff564fe9a3d5fe258fe00eef340d135d9b", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The ethical considerations taken into account when selecting the data for the MOMA-LRG dataset include the following protocols: (1) Taxonomy selection was carefully conducted to ensure that each activity class is gender-neutral, culturally inclusive, and respectful toward people from different socioeconomic backgrounds. (2) Video selection was performed by a diverse team of researchers from different ethnicities and genders who examined and discussed each video to ensure diversity and the absence of offensive content. Additionally, keywords from multiple languages, including English, Chinese, French, and Japanese, were used to search for videos to enhance diversity and reduce potential bias in the dataset."}
{"question_id": "9d285bc752521120d3b45a5b35069f1365c8f603", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "Set expansion approaches are used to construct a dictionary from customer reviews to achieve high coverage of relevant entities corresponding to specific concepts of interest, such as amenities or room features, in an unlabeled review corpus. Since customer reviews are typically unlabeled and contain a large variety of entities\u2014including non-named entities and multifaceted or vague entities\u2014set expansion methods enable the discovery of additional entities beyond a small initial seed set provided by experts. This facilitates semantic search features that help users explore relevant reviews by highlighting mentions of different entities related to queried concepts. The approach is particularly useful in low-resource settings where labeled data or external knowledge bases are limited, and it leverages contextual features and distributed representations extracted directly from the corpus to rank candidate entities for expansion."}
{"question_id": "6159c6e153be58a55c17f3cda104c7ebdd581acc", "paper_id": "openreview/ICLR-2023-conf/8efJYMBrNb", "generation": "The rationale for using the CS-error metric instead of SPS metrics lies in the strictness and precision of the column score (CS) definition. The CS metric counts the number of alignment columns in the inferred alignment that are identical to the \u201ctrue\u201d alignment, requiring both the coordinates and the characters of each column to match exactly. For example, if a position in one sequence is aligned to a different position in the other sequence, even if the characters match, it is not considered a correct alignment column. This strict matching criterion ensures a precise quantification of alignment accuracy. The CS-error is defined as 1.0 minus the normalized CS-score, reflecting the level of disagreement between the inferred and true multiple sequence alignments (MSAs). This approach provides a direct and normalized measure of alignment error, emphasizing exact column correspondence rather than partial or pairwise similarity measures that SPS metrics might capture."}
{"question_id": "5c090b48e2d8b39f413f602a716b92676b7e7ba7", "paper_id": "openreview/ICLR-2023-conf/8efJYMBrNb", "generation": "The provided information does not explicitly mention whether the baseline aligners (ClustalW, DIALIGN, MAFFT, T-Coffee, PRANK, MUSCLE) have adjustable parameters that could be tuned to improve their performance. The discussion focuses on comparing the accuracy of BetaAlign to these state-of-the-art alignment algorithms and highlights differences in their performance across datasets, but it does not address parameter adjustability or optimization for the baseline methods."}
{"question_id": "5219ee2947eb66850d9df883d32c6549b914d086", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The system contains a number of modules equal to the number of subproblems that must be solved, denoted as \\( d_{\\max} \\). For example, in the described robotic tasks, \\( d_{\\max} = 3 \\) modules correspond to the subproblems of (1) grasping an object, (2) avoiding an obstacle, and (3) controlling a robot arm. Experimental results also mention configurations with 3, 4, 5, and 6 modules, with 4 modules being the original (correct) number for certain 2-D discrete tasks. Thus, the number of modules is task-dependent but typically matches the number of distinct subproblems to be addressed."}
{"question_id": "1b206d1d36f66f3d336a33e34858567e8a593ab0", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The system employs a modular neural policy architecture designed for compositional reinforcement learning tasks. The architecture decomposes the input state into distinct task components, each processed by a dedicated neural module specialized for a specific subproblem. For the discrete 2-D tasks, the architecture consists of three types of modules arranged in a fixed graph structure: static object modules, target object modules, and agent modules.\n\n- **Static object modules** process the channels corresponding to static objects through convolutional blocks with ReLU activations and max pooling, producing a representation of the static environment.\n\n- **Target object modules** preprocess the target object channel similarly, then concatenate their output with the static object module's output and pass this combined representation through further convolutional layers.\n\n- **Agent modules** preprocess the agent channel, concatenate it with the target object module's output, and then pass this through separate multi-layer perceptrons (MLPs) for the actor and critic networks, each with a single hidden layer of 64 units and tanh activation.\n\nThe overall architecture for each task is constructed by combining one module of each type in sequence, reflecting the compositional nature of the task. This design contrasts with a simple chained architecture where the entire input is passed sequentially through modules; instead, the input is decomposed and fed separately to each module, allowing modules to focus solely on their relevant subproblem. This modular decomposition reduces brittle dependencies and improves generalization to unseen task combinations.\n\nThe architecture is illustrated in Figure D.3 of the paper and is empirically validated to outperform simpler chained modular architectures."}
{"question_id": "c68719740a590d79c10dfd14e5cb4e1f51938871", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "The low performance of the \"description\" answer type in Table 5 is attributed to the difficulty in predicting the structure of description answers. This answer class was also identified by the annotators as the most challenging, indicating that the complexity and variability in the description-type answers make them harder for the model to accurately predict."}
{"question_id": "f5c277093cecafb8c4d4588dfbae6b5279b14a79", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "The re-annotation process described was applied to the original MultiSpanQA dataset, where all data were (re-)annotated using the Brat annotation tool with three annotators following a category-specific annotation guide. However, the expanded dataset was created by adding single-span question\u2013answer pairs and unanswerable instances through random sampling from the NQ dataset and applying the same preprocessing, but there is no mention of performing re-annotation specifically for the expanded dataset. Therefore, re-annotation was performed for the original multi-span dataset but not explicitly for the expanded dataset."}
{"question_id": "e1369f11b53bb858522bacf4bf2e9d8448dc1ef5", "paper_id": "openreview/ICLR-2022-conf/EnwCZixjSh", "generation": "It is important for the metric to be insensitive to the number of layers when evaluating rank correlations because metrics computed using different GIN (Graph Isomorphism Network) configurations, including variations in the number of layers, should yield consistent and comparable performance. Insensitivity to the number of layers ensures that the evaluation metric's rank correlation results are stable and not unduly influenced by architectural choices of the underlying neural network. This stability allows for objective comparison across experiments and model configurations, as indicated by the observation that metrics computed using a pretrained GIN are nearly indistinguishable from those using a random GIN across rank correlation experiments, despite differences in network depth."}
{"question_id": "42aec356c91c672d813f540951f0b79d9f57705f", "paper_id": "openreview/ICLR-2022-conf/oiZJwC_fyS", "generation": "DeepNN is a deep neural network architecture with layers of size 28 \u00d7 28, 512, 256, 128, and 10. It is used to evaluate the performance of compression methods by applying compression on all hidden layers from the input to the output."}
{"question_id": "3318142bc7bd1401191fcc4a9712243c0df0f1df", "paper_id": "openreview/NeurIPS-2022-dabt/HYELrdRdJI", "generation": "High inter-class view similarity refers to the phenomenon where different objects appear similar or identical in specific views. This similarity causes humans to be uncertain about the correct class labels for these views, which are thus designated as uninformative views. Because these uninformative views do not provide clear discriminative information, the class labels assigned to them are uncertain or ambiguous. This uncertainty leads to inconsistencies between the one-hot class labels (used as ground truth) and human judgment, resulting in multi-view label noise. In other words, the label noise arises because the ground truth labels do not fully capture the ambiguity present in the uninformative views caused by high inter-class similarity."}
{"question_id": "19e742075a73b79f8d1593d22060ea47535a485a", "paper_id": "openreview/ICLR-2022-conf/8uz0EWPQIMu", "generation": "Figure 3a illustrates the process of evaluating neuron rankings by probing. Specifically, it shows how a language model creates a word representation (e.g., for the word \"was\"), which is then fed into a neuron-ranking method to rank neurons according to their importance for a particular attribute (e.g., tense). The top-k ranked neurons are subsequently used as input to a probe trained to predict that attribute. This figure serves to demonstrate the methodology for assessing the quality of neuron rankings in terms of their relevance to specific linguistic attributes."}
{"question_id": "8c6b063b9a5318af6557db02c0c7dbc93f8939be", "paper_id": "openreview/NeurIPS-2022-dabt/HYELrdRdJI", "generation": "The 16k real captured views are sampled and combined to construct the 9k multi-view sets. Specifically, for each object, multiple views are sampled\u2014both informative and uninformative\u2014and then combined into multi-view sets containing two to six views. The multi-view sets are formed by grouping these sampled views such that each set contains at least one informative view (in training) or exactly one informative view (in validation and test), ensuring diversity and coverage of the object's overall appearance. Thus, the 9k multi-view sets are composed by aggregating the 16k individual real captured views through a systematic sampling and combination process."}
{"question_id": "4b00407f8b0e58aafdaee12f078de8b313872f04", "paper_id": "openreview/ICLR-2022-conf/8uz0EWPQIMu", "generation": "Yes, the proposed alternate method differs from the original linear model by Dalvi et al. (2019) in how neurons are ranked after training the probe. The original algorithm distributes neurons equally among labels, assigning the same number of neurons to each label regardless of the actual importance of neurons for that label. This can result in non-important neurons being ranked higher than important ones. In contrast, the alternate method computes the mean absolute value of the weights associated with each neuron and ranks neurons by this value from highest to lowest. This approach does not enforce equal distribution among labels and empirically provides better results, especially for large label sets."}
{"question_id": "b49d17a4084f479d1c1f3e920a863be370968f66", "paper_id": "nlpeer/ARR-22/3cbf91f8e2f7e62be51109688c84a2b424e6018ee3c976fbe55e5331bc9ab2f4c46491d006087aed3be35a3370c945c100bd718e2c1131de19eac3dbe93326bc", "generation": "The per-triple templates were developed manually by one of the authors using only the input data, without relying on human reference texts. The templates are single-sentence, mostly clear-cut verbalizations of the predicates, created to introduce in-domain knowledge about the semantics of individual predicates. The approach involved hand-crafting a single template for each predicate, as exemplified by templates such as \"<s> plays <o>\" for the predicate \"instrument.\" The templates were designed to be general and unambiguous, although some minor disfluencies resulting from template filling were acknowledged. The decision to create templates manually was motivated by the noisiness of training data examples and the unavailability of such data in a zero-shot setup, which precluded extracting templates automatically from training data."}
{"question_id": "b0767779541047ab4deb8c71f900288615ddd5a7", "paper_id": "openreview/ICLR-2022-conf/5hLP5JY9S2d", "generation": "The Vision Transformer (ViT) model demonstrates better generalization to the open-set scenario on ImageNet compared to other methods primarily due to its architectural characteristics and training behavior. Despite having a large number of parameters (86 million) and possessing few inductive biases\u2014specifically, it does not use convolutions\u2014ViT does not overfit its representation to the training classes. This lack of overfitting allows ViT to maintain strong performance in identifying semantic novelty in open-set recognition tasks. The superior open-set performance of ViT supports findings on the benefits of purely attention-based vision models, which include improved generalization capabilities. Additionally, ViT's strong closed-set performance correlates positively with its open-set performance, further contributing to its effectiveness in open-set recognition on ImageNet."}
{"question_id": "5ac9e91c8d313af9a0771c5e3e46e95d2a7c2315", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "The new evaluation metric proposed in the paper is the partial match precision, recall, and F1 score for multi-span question answering. This metric measures the overlap between predicted answer spans and ground truth answer spans by treating each span as a string and computing the longest common substring (LCS) between each predicted span and each ground truth span. The partial retrieved score (precision component) is defined as the length of the LCS divided by the length of the predicted span, and the partial relevant score (recall component) is the length of the LCS divided by the length of the ground truth span. Precision and recall are then computed by taking the maximum partial scores across all span pairs, and the F1 score is calculated as the harmonic mean of these precision and recall values. This metric allows for a more fine-grained evaluation of multi-span answers beyond exact match."}
{"question_id": "7cd7d4d944b16a2603aaaf3ebb5628edd813a346", "paper_id": "nlpeer/ARR-22/3cbf91f8e2f7e62be51109688c84a2b424e6018ee3c976fbe55e5331bc9ab2f4c46491d006087aed3be35a3370c945c100bd718e2c1131de19eac3dbe93326bc", "generation": "The criteria for delimiter placement in the sentence aggregation model is based on whether neighboring facts should be mentioned together in a single sentence or separately. Specifically, the model outputs a sequence of sentence delimiters \u03b4_i \u2208 {0, 1} for the ordered sequence of facts F^o. A delimiter \u03b4_i = 0 indicates that the neighboring facts should be aggregated and their corresponding sentences fused into one sentence. Conversely, \u03b4_i = 1 indicates that the neighboring facts should be mentioned separately, i.e., the neighboring sentences should not be fused. The model is trained using synthesized sentences where \u03b4_i = 0 corresponds to pairs of sentences originally aggregated (resulting from splitting a single sentence), and \u03b4_i = 1 otherwise."}
{"question_id": "f117dce3beae4a1fc909bbadebcc235634d017c0", "paper_id": "openreview/NeurIPS-2022-dabt/7w-a8PYPlP", "generation": "The seismic forward modeling applied absorbing boundary conditions."}
{"question_id": "86b7bff4eb8f5701bb87715221c22a2db29eaae1", "paper_id": "openreview/ICLR-2022-conf/sPIFuucA3F", "generation": "Algorithm 1 is not explicitly provided in the given chunks. However, the proof section for Theorem 4.1 references Algorithm 1 and states that at each iteration t, the data tuple \\((x_t, a_t, r_t)\\) is retrieved from the offline dataset \\(D_t = \\{(x_\\tau, a_\\tau, r_\\tau)\\}_{1 \\leq \\tau \\leq t}\\). It also notes that the policy \\(\\hat{\\pi}_t\\) returned by Algorithm 1 is measurable with respect to \\(D_{t-1}\\), implying that the data tuple at time \\(t\\) is accessed sequentially from the dataset.\n\nTherefore, the method used to retrieve the data tuple in Algorithm 1, line 4, is to sequentially access the \\(t\\)-th data tuple \\((x_t, a_t, r_t)\\) from the offline dataset \\(D_n\\)."}
{"question_id": "eb715a337474694a5b2fa3212f3936f2979ff998", "paper_id": "openreview/ICLR-2023-conf/kDEL91Dufpa", "generation": "Yes, evidence that different contrastive learning methods can be represented using Definition 3.2 is provided in the proof of Proposition C.3. It shows that methods such as Barlow Twins, VICReg, and TCR are dimension-contrastive methods. Specifically, the proof analyzes the criteria of DCL-sq and DCL-abs, which are sample-contrastive methods, and demonstrates that their loss functions can be decomposed into an invariance term and a repulsive term expressed as a LogSumExp or max operator over similarities in the Gram matrix. This repulsive force leads to a diagonal Gram matrix, which aligns with the goal of the sample-contrastive criterion. Furthermore, the rewriting of VICReg\u2019s criterion as a variation of sample-contrastive learning with added regularization and variance loss normalization (as shown in Section 3) highlights the close relationship and equivalence between sample-contrastive and dimension-contrastive methods. This establishes that various contrastive learning methods can be formulated within the framework defined by Definition 3.2."}
{"question_id": "61588ca196125738d21c2d191ecac13249af297d", "paper_id": "openreview/ICLR-2022-conf/oiZJwC_fyS", "generation": "Yes, the algorithm can be tested on larger networks. Experiments conducted on larger datasets such as CIFAR using CIFAR-VGG and an altered version of AlexNet adapted for CIFAR demonstrate that the Neural Path K-means method retains good performance. In most cases, it achieves slightly better accuracy and lower deviation than baseline pruning methods, although its performance deteriorates when keeping almost zero weights. This indicates that the algorithm is applicable and effective for compressing larger neural network architectures."}
{"question_id": "cd38abc68b46b12d953fddc8838eb77978963fca", "paper_id": "openreview/ICLR-2023-conf/kDEL91Dufpa", "generation": "In Proposition 3.1, SimCLR and DCL are defined as self-supervised learning criteria that rely on cosine similarities between normalized embeddings. Specifically, considering an infinite number of available negative samples, optimizing SimCLR and DCL\u2019s criteria leads to embeddings where, for negative pairs \\((x, x^-)\\) in \\(\\mathbb{R}^M\\), the expected dot product is zero and the variance of the dot product is \\(1/M\\):\n\n\\[\n\\mathbb{E}[x^T x^-] = 0 \\quad \\text{and} \\quad \\mathrm{Var}[x^T x^-] = \\frac{1}{M}.\n\\]\n\nSimCLR and DCL aim at making negative pairs of embeddings opposite in terms of cosine similarity, which is a stronger condition than orthogonality targeted by the contrastive criterion \\(L_c\\). Both methods use cosine similarity as the similarity measure rather than squared or absolute values of cosine similarity. This leads to embeddings that are uniformly distributed on the \\(M\\)-dimensional hypersphere as the number of negative samples approaches infinity."}
{"question_id": "ec3f80fbff718abc4b5fae665cfdc994570329fb", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "No quantitative evaluation of the experiment on Ego4D is reported. The model was applied to Ego4D for transfer detection without finetuning, using user-provided category names, and the results are presented qualitatively through visualizations of detected novel categories in indoor and grocery store scenes."}
{"question_id": "601d6dade2b1d6724ae69aafc64a71bafd79062e", "paper_id": "openreview/NeurIPS-2022-dabt/7w-a8PYPlP", "generation": "Yes, the published code includes the algorithms used to generate the data. The data generation pipeline is described in the supplemental material, detailing the synthesis of velocity maps from three different priors (mathematical representations, natural images, and geological reservoirs) and the subsequent generation of seismic data via forward modeling. The code and related information are available on the Github repository (https://github.com/lanl/openfwi), which supports reproducibility and includes the data generation process."}
{"question_id": "1eafcdeb90458c749f5b2e6dcdaaa06a4ba58abd", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "The phrase \"but train the detector head with \\( r(\\cdot) \\) online in a single stage\" refers to the training procedure of the F-VLM model where the detector head is the only trainable component, while the backbone vision and language model (VLM) remains frozen. Specifically, \\( r(\\cdot) \\) denotes the detection region embeddings produced by the detector head. Training \"online in a single stage\" means that the detector head is trained directly and simultaneously on the frozen backbone features without requiring multiple separate training phases or stages. This approach contrasts with multi-stage training pipelines and leverages the frozen backbone's locality-sensitive features to enable accurate detection by updating only the detector head parameters during training."}
{"question_id": "fcd1a1d599ae695d923bfabe4f62e5e457ca2de1", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "Yes, the time requirements for training the surrogate model significantly outweigh the benefits in terms of speeding up the evaluation of the dynamic system during inference. The highest computational demand originates from the ML training time, which is orders of magnitude larger than the inference time. For example, in the reported experiments, training times for the Fourier Neural Operator (FNO) surrogate model range from thousands to over a hundred thousand seconds depending on the problem and resolution, whereas the inference times are fractions of a second. Once trained, the surrogate model can predict solutions multiple orders of magnitude more efficiently than classical numerical PDE solvers, enabling rapid evaluation. However, the initial training cost is substantial and dominates the overall computational expense."}
{"question_id": "5420a636705116e4e99d17572011f028d54a72b2", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "Prior work related to this paper has primarily used classical statistical error measures such as the root-mean-squared-error (RMSE) over the whole domain and, in some cases, PDE-motivated variants such as the RMSE of the gradient. Measures based on properties of the underlying physical systems have been lacking in these prior benchmarks."}
{"question_id": "a8466adf7868015b87e7447c11f576b29d121012", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The phrase \"environment's health\" refers to the overall well-being and condition of the natural environment, which is closely related to human health aspects. It implies maintaining the environment in a state that supports ecological balance and reduces environmental hazards, thereby promoting positive outcomes for both the ecosystem and human populations. Recycling is adopted as a measure to preserve this health by mitigating environmental degradation and promoting sustainable resource use."}
{"question_id": "3e6d53b8861714d6727e6f1a924eb2046baac6a7", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "When novel categories with large vocabularies are added to the set of candidate categories, the accuracy of the model on open-vocabulary detection (novel categories) is not compromised. Increasing the feature pyramid capacity, which enhances the representation learned upon the frozen backbone features, benefits standard detection on base categories significantly without reducing performance on novel categories. Specifically, larger feature pyramids improve base category average precision (AP_c, AP_f) while maintaining or slightly improving novel category average precision (AP_r). Additionally, score fusion strategies that combine detection and vision-language model (VLM) scores are important for optimal performance, as neither detection nor VLM scores alone yield the best results. Overall, the model demonstrates strong scaling properties and steady improvement in performance as the vocabulary size increases, with no detrimental effect on novel category detection accuracy."}
{"question_id": "c908b12fc3ea26161680a836fc0ee29b02fd4e96", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "Region classification for novel categories with a large vocabulary of class names not present in the image is performed by leveraging text embeddings of both base and novel categories within a shared embedding space. Specifically, the system replaces the standard fixed K-way classifier with a text-embedding region classifier that computes cosine similarity between the region embedding and the text embeddings of all categories, including novel ones. At inference time, the text embeddings are expanded to include novel categories, enabling open-vocabulary detection without retraining.\n\nThe region embedding is obtained by applying a region pooling operation on the cropped backbone features corresponding to the region proposal. The classification scores for each region are computed as the cosine similarity between the region embedding and the text embeddings of the categories, followed by a softmax with a learnable or fixed temperature to scale the logits. This approach allows the model to generalize to novel categories near the base categories in the embedding space.\n\nFurthermore, the final detection scores combine the detection head scores and the vision-language model (VLM) scores via a geometric mean, weighted differently for base and novel categories, to reflect both recognition and localization quality. This design supports open-vocabulary recognition at the region level, even when novel categories are not present in the training images."}
{"question_id": "8397ceb9d91201c9f2eb30de3c87e1e8243a827d", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The conceptual model presented in Figure 2 illustrates the flow of the online gamified learning (EGL) activity designed to motivate youths' recycling intention. It conceptualizes that youths' motivation to recycle is driven by both intrinsic motivation (such as satisfaction, happiness, or enjoyment) and extrinsic motivation (such as monetary rewards, promotion, or punishment) evoked through a gameful experience. This gameful experience, characterized by competitive and challenging game elements, not only motivates the youths themselves but also socially influences their family and friends to engage in recycling. Thus, the model integrates gameful experience, intrinsic motivation, extrinsic motivation, and social influence as key factors that collectively enhance recycling intention among youths and their social circles."}
{"question_id": "****************************************", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "The accuracy of the surrogate model directly impacts the accuracy of the downstream task, particularly in inverse problem setups where the surrogate is used to estimate unknown initial conditions or parameters. Since the surrogate approximates the forward propagator of the PDE, any prediction error in the surrogate propagates to the downstream task, leading to inaccuracies in the inferred quantities. For example, in inverse problems, minimizing the prediction error of the surrogate over a time horizon is used to identify initial conditions; thus, a more accurate surrogate yields a more precise estimation of these conditions. Conversely, surrogate models with higher errors, such as those affected by phenomena like Gibbs oscillations in Fourier-based methods or limitations in handling low diffusion coefficients, result in poorer performance in downstream tasks. Therefore, the fidelity of the surrogate model is critical for reliable and accurate solutions in subsequent inference or control tasks."}
{"question_id": "dd594c4d0897a3c1259ae3d2d2d23859fbb96f04", "paper_id": "nlpeer/F1000-22/10-637", "generation": "The purpose of the saliva sample was to validate the smoking status of participants."}
{"question_id": "174eff40340ae0616a2328f75efd8cf8431b3150", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The conceptual model in Figure 2 was developed through a qualitative thematic analysis of focus group discussions (FGDs) following Braun and Clarke's six-phase approach. Initially, the transcriptions were reviewed and annotated to familiarize with the data, followed by coding to ensure reliability. Thirty-seven codes were identified and then sorted into thirteen categories, which were further refined into themes based on repeated patterns and similarities. These initial themes were reviewed and refined to ensure alignment with the research questions, resulting in four main themes: gameful experience, intrinsic motivation, extrinsic motivation, and social influence. These themes represent the key factors influencing recycling intention and were used to verify and enrich the conceptual framework, creating a progressive construct that reflects participants' insights and responses. This process culminated in the verified conceptual framework depicted in Figure 2, which categorizes the factors influencing youths' recycling intention initiated by gamified learning."}
{"question_id": "127423c37403d9d9d34a21c17bfb33234b5f944a", "paper_id": "openreview/ICLR-2023-conf/tYIMtogyee", "generation": "Yes, the authors used a validation set to tune the hyperparameters on the QM9 dataset. Specifically, they mention that hyperparameters, including the noise scale for denoising during pre-training and fine-tuning, were tuned on the HOMO target and then kept fixed for all other targets. The QM9 dataset was split into training, validation, and test sets, with 10,000 examples allocated for validation, which implies that the validation set was used for hyperparameter tuning."}
{"question_id": "ce87b952cfde98f1de69d4c860537a4d3989c67a", "paper_id": "nlpeer/F1000-22/11-9", "generation": "The 30,229 SARS-CoV-2 genome sequences used in the study were obtained from the GISAID database, with a date range from 2020-01-01 to 2021-03-21. The geographical distribution of these sequences is summarized in a table deposited in Figshare (https://doi.org/10.6084/m9.figshare.19721716.v1). The sequences were aligned to the reference genome NC_045512.2, and inappropriate sequences with base calling errors, unresolved nucleotides (\"N\"), or gaps were omitted. The study extracted 11 different coding sequences from these strains according to their genomic positions in the reference strain.\n\nRegarding genetic nomenclature and variants of concern (VOC), the frequency percentage of the top 10 nonsynonymous mutations in the primary lineages associated with past and present VOCs was obtained from COVID CG (COVID CG, RRID:SCR_022266) and included in Table 3 of the study. This information provides insight into the distribution of key mutations across different SARS-CoV-2 lineages relevant to VOCs."}
{"question_id": "73faf71e5e746272235608cd46a8fda2b309ef70", "paper_id": "openreview/ICLR-2023-conf/tYIMtogyee", "generation": "The authors refer to tuning \"approximately\" 5 values on the QM9 dataset in the context of selecting optimal noise scales for denoising during pre-training and fine-tuning. Specifically, they performed hyperparameter tuning over a discrete set of noise values {0.005, 0.01, 0.02, 0.05, 0.1} to determine the best noise level for the HOMO energy target. This tuning process involved evaluating these roughly five candidate noise values to identify the most effective one, which was then fixed for all other targets in QM9."}
{"question_id": "94974352b0e42eb1b459e4b85aea1ca6ddb9b713", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "Yes, the robust region proposed in this paper explicitly includes physical distortions encountered in real-world cases, specifically geometric transformations such as rotations and translations. The paper defines adversarial examples robust to geometric perturbations as regions in the geometric parameter space where all transformed images within these parameter bounds are classified consistently. The method can handle complex compositions of simple geometric transformations, and the robust regions are constructed in the low-dimensional geometric parameter space corresponding to these physical distortions.\n\nRegarding frequency, the paper demonstrates that such robust regions are commonly achievable in practice. It notes that empirical robustness is often non-uniform across an example, with significant parts being less robust, especially near the original input image. In contrast, the provably robust adversarial examples constructed by their method do not exhibit this non-uniformity and provide reliable robustness to intensity changes and geometric transformations. The experiments show that the method can generate robust regions against geometric changes with 3 to 4 geometric parameters, indicating practical applicability to real-world physical distortions."}
{"question_id": "1fd3a7fbc973d1042fee519c269028acdbb1ccec", "paper_id": "nlpeer/COLING2020/341", "generation": "The antecedent is included in the sum in formula #6 because the property being measured is the relative frequency of a construction involving one head and one dependent, considering both possible word orders. Specifically, the formula for the single-link property counts occurrences of the construction with the head preceding the dependent (e.g., NOUN ADJ with amod) as well as the dependent preceding the head (e.g., ADJ NOUN with amod). The relative frequency is computed as the count of the construction in one order divided by the total count of the construction in both orders combined. This approach captures the proportion of the construction occurring in a particular order relative to all occurrences of that construction, rather than expressing a directional implication such as \"verb-nsubj:noun-obj:noun \u21d2 nsubj:noun-verb-obj:noun.\" Thus, the antecedent is included in the denominator sum to represent the total frequency of the construction in both directions, enabling a normalized measure of word order preference."}
{"question_id": "273390ef5c449b8574f4d7b6e46f332e7152a15d", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "The intuition and motivation behind the head-to-tail method in section 3.2.3 is to model latent dependency structures in sentences by capturing dependency relations between words, where the presence of a certain word (head) can be predictive of another word (tail). This reflects the natural language property that words often have dependency relations, such as a verb cooccurring with its subject. By pretraining language models on data generated with such head-to-tail dependency pairs, the models are hypothesized to acquire an inductive bias toward identifying relations between tokens in the input. This inductive bias is considered important for processing natural language effectively, as it encourages the model to learn structural dependencies that are characteristic of natural language syntax and semantics."}
{"question_id": "df9756e054d7db2937ebc51e1ed8477345e57387", "paper_id": "nlpeer/COLING2020/341", "generation": "The logical atom \"nsubj:noun-verb-obj:noun\" represents a double-link property extracted from Universal Dependencies treebanks, specifically the relative frequency of a syntactic construction involving one head and two dependents. In this case, it denotes the frequency of occurrences where a verb (head) has a nominal subject (nsubj:noun) and a nominal object (obj:noun) simultaneously. This atom quantifies how often the configuration of a verb with a noun as its subject and a noun as its object appears in a given language corpus."}
{"question_id": "ff4b45b2af4e13f58512fc1783fc12dd129feb6f", "paper_id": "11-917-2023", "generation": "Yes, the model incorporates wave erosion through the calculation of wave energy and refraction based on Snell\u2019s law, using hydrodynamic boundary conditions derived from observed significant wave height and direction data. The model accounts for both normal sea state conditions and storm impacts by including mean wave parameters and maximum significant wave height from storm events at a specified occurrence rate. Additionally, the model addresses subaerial erosion with user-defined weathering rates for each sediment class, which indirectly relates to erosion processes affecting coastal limestone facies."}
{"question_id": "77d9dbb3a3af5156b369e66924d4bcf14f794893", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The criteria used to evaluate \"provability\" in the paper is based on the output of a verifier \\( V \\) applied to a candidate adversarial region \\( U_i \\). Specifically, the verifier \\( V \\) returns a value \\( e_i \\) that is non-negative if and only if the region \\( U_i \\) is provably robust adversarial. The algorithm iteratively refines the region and uses the verifier to certify robustness; a region is considered provably robust if the verifier certifies it by returning a non-negative value. This ensures that the entire connected input region \\( U_i \\) is guaranteed to be adversarial under the considered perturbations."}
{"question_id": "46780f3f8ab86a46782f68b7ca66e5e1438afc01", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "An image's adversarial distance radius \\( R' \\) was considered \"too big\" if it was more than 33% larger than the certified radius \\( R \\) of the smoothed classifier \\( g \\). Images with such radii were excluded from the analysis to avoid trivial attacks where the image is attackable for most classes on \\( g \\). This exclusion criterion was applied after heuristically selecting \\( R' \\) by searching for the smallest adversarial distance on the base network \\( f \\) where at least 10% of 500 attacks succeeded."}
{"question_id": "fc938634e35cc53e7b6cb50564929eabb0fc7afe", "paper_id": "11-917-2023", "generation": "\"Production versus depth curves\" refer to the non-linear relationship between the growth rate of carbonate producers (such as different coral species) and water depth. These curves quantify how carbonate production rates vary as a function of depth, reflecting the influence of environmental factors like water temperature, turbidity, wave energy, and water depth on coral growth. In the Dionisos forward stratigraphic model, each carbonate sediment class is assigned a user-defined production-versus-depth curve that represents this relationship, allowing the model to simulate carbonate production rates at different depths. These curves are derived from literature sources and provide growth rates for various carbonate facies, such as tabular corals, encrusting corals, carbonate sand, and carbonate mud, typically expressed in meters per million years (m Myr\u207b\u00b9)."}
{"question_id": "e7b7d480aa1076d06dccb8bcb2b7f2b1fd0f9c87", "paper_id": "11-917-2023", "generation": "The model presented in the study has spatial limitations and is not easily applied universally to other geographical areas without further adaptation. While it provides valuable insights into the sensitivity of the Last Interglacial (LIG) geological record, its application is currently limited to specific settings, such as the stable Pleistocene coastlines modeled here. The study suggests that additional investigation is warranted to analyze more glacial isostatic adjustment (GIA) and tectonically diverse LIG coral-dominated coastlines. Therefore, applying the model to other regions, especially those with different GIA and tectonic characteristics, requires further testing and refinement."}
{"question_id": "bc6f50621da1a65a6e46211a4f48751a6da35304", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The paper defines the \"adversarial L2 ball\" in the context of randomized smoothing and adversarial attack strength as follows:\n\nAn adversarial attack \\(\\tilde{x}\\) on a smoothed classifier \\(g\\) is characterized by an adversarial distance \\(R_{\\text{adv}}\\), which is the radius of an \\(L_2\\) ball around \\(\\tilde{x}\\) that is certified to have the same adversarial class as \\(\\tilde{x}\\) on the smoothed network \\(g\\). Formally, this means that all points within this \\(L_2\\) ball centered at \\(\\tilde{x}\\) share the same adversarial classification \\(y_t \\neq f(x)\\) as \\(\\tilde{x}\\).\n\nThis definition implies that the adversarial \\(L_2\\) ball is a region around the adversarial example \\(\\tilde{x}\\) where the smoothed classifier \\(g\\) consistently predicts the adversarial class \\(y_t\\), rather than the original class of \\(x\\). The key point is that the ball is centered at the adversarial example \\(\\tilde{x}\\), not at the original input \\(x\\). Therefore, every sample within this ball has the same classification as \\(\\tilde{x}\\), which is the adversarial class, and not the original class of \\(x\\).\n\nThis contrasts with the usual expectation that an adversarial ball around the original input \\(x\\) contains points with different classifications from \\(x\\). Instead, here the ball is defined around the adversarial example \\(\\tilde{x}\\), ensuring that the smoothed classifier's prediction is stable and consistently adversarial within that neighborhood. The radius \\(R_{\\text{adv}}\\) thus measures the strength of the adversarial attack by quantifying the size of the region around \\(\\tilde{x}\\) where the adversarial classification holds."}
{"question_id": "d7f32782ecbb103a3971b1e3a918576549b44723", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The motivation for building provably robust adversarial examples arises from the limitations of existing adversarial attack methods, which typically produce only single concrete adversarial examples whose effects can often be mitigated by existing defenses. Provably robust adversarial examples address this by consolidating individual adversarial points into large connected input regions that are guaranteed to contain only adversarial examples. This guarantees robustness to a wide set of real-world perturbations, such as pixel intensity changes and geometric transformations, providing stronger and more reliable adversarial attacks. Unlike prior approaches that rely on empirical verification without formal guarantees, provably robust adversarial examples enable formal certification of adversarial robustness, making them significantly more effective against state-of-the-art defenses, including those based on randomized smoothing."}
{"question_id": "7e53c05206cc77e9d6e3b28338d1c85336543244", "paper_id": "egu/esd/14-81-2023", "generation": "The authors determine the accuracy of the CMIP6 climate models in simulating the processes by including a large number of models and multiple members per model to cover a wide range of physically plausible climate responses. They use multiple members per model to account for within-model internal variability and multiple models to account for across-model variability and uncertainty. Additionally, they utilize long piControl simulations (450-year control runs for 10 out of 13 models, totaling 4500 years of unforced data) to sample a representative distribution of unforced trends. This approach increases confidence in assessing whether forced trends lie outside the likely range of unforced trends, thereby evaluating the models' ability to simulate climate processes accurately."}
{"question_id": "41338acb67bcf333b2817c15b8b74290fb3d9327", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "The provided information does not mention any downstream tasks other than dependency parsing being tested using an encoder transferred from language 1 to language 2 without any parameter updates. Specifically, the experiments evaluate pretrained encoders on dependency parsing with the English EWT dataset, but there is no reference to testing on natural language inference, question answering, semantic role labeling, or other downstream tasks."}
{"question_id": "70ee52925bfa4ed21c8798964ec74fcdd0f9845f", "paper_id": "11-917-2023", "generation": "No, the subaerial erosion rate is not equivalent to the maximum weathering rate. The maximum subaerial weathering rate is a user-defined parameter that approximates the highest possible weathering rate under subaerial exposure conditions, influenced by environmental factors such as precipitation and groundwater chemistry. The subaerial erosion rate solved by the Dionisos model incorporates this maximum weathering rate but also accounts for limitations such as the speed of dune migration, which can reduce exposure time and thus limit actual erosion. For example, a lower maximum subaerial weathering rate of 100 m Myr\u207b\u00b9 was employed in the model to avoid overestimation, compared to higher rates cited in other studies (e.g., 250 m Myr\u207b\u00b9). Therefore, the subaerial erosion rate is constrained by, but not strictly equal to, the maximum weathering rate."}
{"question_id": "4006c4e45f822e15f9ccc27df354f3bcd8298509", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "Using alignment for video preprocessing, as opposed to using a fixed cropping window, can introduce temporal inconsistency in the processed video frames. This is because the alignment process involves detecting landmarks for each frame independently, which may vary over time. To mitigate this temporal inconsistency, a low-pass Gaussian filter is applied to smooth the estimated keypoints before warping the images. Additionally, the alignment procedure includes realigning images in the depth direction to force keypoints (e.g., the nose) to a consistent point in the world coordinate system defined by a parametric face model (3DMM). This alignment approach facilitates the training of 3D image GANs by providing more consistent spatial references across frames, but it requires smoothing steps to reduce temporal artifacts that would not be present if a fixed cropping window were used."}
{"question_id": "7c6f77a64467e8275e381a36386d66650b13e832", "paper_id": "nlpeer/F1000-22/11-404", "generation": "The term \"aging effect of smiling\" (AES) specifically refers to the phenomenon that smiling faces are estimated to be older than neutral faces in direct age estimations. This effect is attributed to wrinkles around the eyes caused by smiling. In contrast, when participants retrospectively estimate the mean age of a group of faces, the smiling face group is estimated to be younger than the neutral face group; however, this retrospective estimation is considered a different effect and is not encompassed by the term AES."}
{"question_id": "a8d6ed01ff1866040e47a7082ff97ea95a6edd03", "paper_id": "11-917-2023", "generation": "Yes, there is a scientific rationale for the Greenland Ice Sheet (GrIS) to start melting before the Antarctic Ice Sheet (AIS) beyond merely creating two distinct peaks in a model. The G2A5 scenario described separates the contributions of GrIS and AIS melt, with GrIS melting first and contributing 2 m to sea level rise over 126\u2013124 ka, followed by AIS melting later at 118 ka contributing 5 m. This scenario reflects possible discrepancies in the stability of the two ice sheets and variable hemisphere-specific climate fluctuations. Such differences in timing are consistent with interpretations of fossil reef sequences and climate variability that suggest asynchronous melting driven by distinct regional climate forcings affecting the Northern and Southern Hemispheres differently."}
{"question_id": "aab380aaa605fffff2d765c9cb058cfc03ee1729", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The rationale for only keeping scenarios that cause collisions with at least two algorithms is to ensure that the selected testing scenarios have high transferability across different autonomous driving (AD) algorithms and exhibit high risk levels. This selection criterion improves both the effectiveness and efficiency of AD evaluation by focusing on scenarios that are safety-critical and capable of inducing collisions in multiple AD algorithms, thereby providing a more robust and generalizable assessment of AD system safety."}
{"question_id": "9be6e9b8ef3f3db6f29bafd74243978f78f2f657", "paper_id": "11-917-2023", "generation": "The phrase \"actual exposed MIS 5e facies are lacking\" refers to the observation that, although preserved exposed fossil MIS 5e reef facies are present at Lembetabe, the corresponding MIS 5e sequences are sometimes absent or underrepresented in the synthetic well logs produced by the Dionisos model. This discrepancy arises because multi-meter sections of lithology deposited during the Last Interglacial (LIG, MIS 5e) are removed in the intervening millennia between the LIG and the modern period due to erosion and sediment transport processes within the model. Consequently, there is a \"preservation bias\" in the model output, where the timing and amount of LIG reef facies deposition vary significantly among scenarios, and only the Full scenario preserves LIG reef facies above modern sea level. This indicates that the model may underestimate the preservation of MIS 5e facies compared to what is observed in the field."}
{"question_id": "f87855d105235224a2584b0e0716b794ef647705", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "Yes, the authors took specific steps to address jittering caused by alignment in the processed videos. They noted that the single-frame face alignment technique introduces temporal inconsistency, which can manifest as jittering. To mitigate this, they applied a low-pass Gaussian filter to smooth the estimated facial keypoints before warping the images. Additionally, after estimating the camera pose for each video frame using deep face reconstruction, they again applied the low-pass Gaussian filter to smooth the camera pose results, further reducing temporal inconsistency and jittering in the videos."}
{"question_id": "d88d8f911add3ead1f649741f7c22700199f9094", "paper_id": "nlpeer/ARR-22/ed81d66dc55496061fd4c97530be7b966d2e734e8435092b2c0c6764f54ea145e62f67555e480d996aebb93772e0de2877ccaad504e0f2228a1395c68d157eda", "generation": "The provided chunks do not contain any explicit information regarding the public availability of the code and dataset associated with the paper. Therefore, it cannot be determined from the given content whether the code and dataset will be made publicly available."}
{"question_id": "eeba4b725b3f8ad526cddce373ff444a591594c9", "paper_id": "nlpeer/ARR-22/df4051328ee57291cf600f10ef67af6872fcef0deb3ff7323b89142b68beb16ea1fbe09c44be8ccca24fe30d67a1e6dbfb715a77384c30fbeb37c362c25c743c", "generation": "The difference in performance between the DICTA test set and the new test set, particularly the difference between the character accuracy (CHA) and word accuracy (WOR) scores, can be explained by the nature of the errors and the characteristics of the test sets. On the DICTA test set, lexicalized systems like DICTA achieve higher word accuracy (WOR) scores (e.g., 94.11%) compared to unlexicalized systems like NAKDIMON (e.g., 89.75%), while character accuracy (CHA) scores are closer (e.g., DICTA 96.77% vs. NAKDIMON 96.37%). This suggests that lexicalized systems benefit from word-level dictionary data, which helps reduce word-level errors, especially on out-of-vocabulary (OOV) words.\n\nIn contrast, on the new test set, NAKDIMON outperforms Morfix and approaches DICTA's performance on character-level metrics but shows lower word-level accuracy. The new test set likely contains more OOV words or words not seen in training, which challenges lexicalized systems that rely on dictionary data. Additionally, vocalization-agnostic dotting errors contribute substantially to word-level errors, as indicated by the higher proportion of word-level errors attributable to vocalization differences (18.43% for NAKDIMON on the new test set). This discrepancy between CHA and WOR scores reflects that while many characters are correctly diacritized, errors in a few characters can cause entire words to be counted as incorrect, lowering WOR more than CHA.\n\nFurthermore, the DICTA test set evaluation involved some minimal adaptations due to encoding constraints (e.g., not distinguishing between kamatz katan and kamatz gadol), which may simplify the task and improve scores. The new test set, being novel and possibly more challenging, reveals a larger gap between character-level and word-level accuracy due to these factors."}
{"question_id": "42b9bcc5c85c3d4087d4f57791f953fa732fc625", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The rationale for selecting the scenarios used in the paper is to ensure that the testing scenarios have high safety-criticality and transferability across different autonomous driving (AD) algorithms. Initially, a large set of scenarios is generated using multiple scenario generation algorithms applied to diverse driving routes. These raw scenarios are then tested on four AD algorithms, and only those scenarios that cause collisions in at least two algorithms are selected. This selection process prioritizes scenarios with high collision rates and risk levels, thereby improving the effectiveness and efficiency of safety evaluation. The selected scenarios demonstrate high transferability across AD algorithms and induce more safety-critical conditions, as evidenced by increased collision rates and improved metrics such as overall score and selection rate."}
{"question_id": "2a76dc6fa246d4afcfe9aea7050a47aa3949d755", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "No, the camera pose is not concatenated with the latent code in the same way as the EG3D method. Instead, the PV3D model conditions the generator on pre-estimated camera pose sequences separately from the latent codes. The latent code is decoupled into appearance and motion components, and the camera pose at each timestep is used to condition the generator to model head rotation by rotating the camera for observation rather than deforming the 3D scene. This approach alleviates motion ambiguities and improves multi-view consistency and temporal coherence. The camera pose is incorporated as a conditioning input for each frame independently, rather than being concatenated directly with the latent code as in EG3D."}
{"question_id": "****************************************", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "The authors noted that the single-frame face alignment technique used in preprocessing introduces temporal inconsistency, which can be interpreted as jittering in the processed videos. To mitigate this, they applied a low-pass Gaussian filter to smooth the estimated keypoints before warping the images and also smoothed the estimated camera poses with the same filtering approach. Despite these measures, the preprocessing pipeline still involves some temporal inconsistency due to the nature of the alignment and pose estimation steps."}
{"question_id": "9dbf9a9e3f0cc960065255b48616ad3b79759600", "paper_id": "openreview/ICLR-2023-conf/tyZ1ChGZIKO", "generation": "The MDSF module splits the input features along the channel dimension into multiple groups, and each group is processed by a separate decoupler branch with a distinct filter size (kernel size). This multi-branch design provides various local receptive fields for frequency decomposition. Specifically, the features are divided equally among the channel dimension into groups, and each group is assigned a decoupler with a different kernel size to dynamically decompose the feature maps into low- and high-frequency components. The kernel sizes of these decouplers correspond to the different branches in the multi-branch structure, enabling diverse frequency separation. The exact kernel sizes for each branch are chosen to provide varied receptive fields, but the specific sizes are not explicitly stated in the text. The number of groups (and thus the number of decoupler branches with different kernel sizes) is a design choice discussed and optimized in the ablation studies, with 8 groups selected for better performance."}
{"question_id": "5b14dc7213f8e7181d9bf848cef4fb79a7b1ad10", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The performance of scenario generation algorithms is evaluated using the following metrics: collision rate (CR), overall score (OS), and overall selection rate (SR). Collision rate measures the frequency of collisions induced by the scenarios. Overall score reflects the safety-critical scenario generation capability, with lower values indicating more effective generation of challenging scenarios. Selection rate evaluates the transferability of the generated scenarios across different autonomous driving (AD) algorithms, indicating how well scenarios cause collisions in multiple AD agents."}
{"question_id": "6d0c861407de2db08718ca55a383b59284a8e223", "paper_id": "nlpeer/PeerRead-CONLL2016/129", "generation": "The number of selected in-domain sentences for each machine translation system is empirically determined based on experimental results on a separate validation set."}
{"question_id": "9373d254f956bcbffe53a9ba10531f5102ecdb83", "paper_id": "openreview/ICLR-2023-conf/dSYoPjM5J_W", "generation": "The authors justify their claim that gradient-based attacks are responsible for the distribution shift between training and testing data by demonstrating that these attacks predominantly modify the graph structure around training nodes rather than uniformly across the entire graph. Empirical evidence shows that adversarial perturbations generated by gradient-based methods, such as MetaAttack and PGD, are concentrated mainly in the Train-Train and Train-Test regions of the adjacency matrix, with minimal changes in the Test-Test region. This uneven distribution of perturbations effectively increases the distribution shift between training and testing nodes.\n\nFurthermore, the choice of surrogate loss function influences the attack tendency: losses computed on training nodes (L_train) lead to perturbations focused on training nodes, while losses computed on testing nodes (L_self) result in perturbations around testing nodes. MetaAttack uniquely adapts its gradient computation via meta-gradients, allowing it to adjust the location of adversarial edges adaptively to maximize distribution shift. Theoretical analysis confirms that perturbations around training nodes enlarge the distribution shift effectively, which underpins the superior effectiveness of gradient-based attacks compared to heuristic methods.\n\nThus, the gradient-based attack's ability to strategically modify edges near training nodes increases the distribution shift between training and testing data, which is the fundamental mechanism driving the attack's effectiveness."}
{"question_id": "6610ad96e462f49d6d8f20fee0cdc6dd8a70175a", "paper_id": "egu/esd/14-81-2023", "generation": "If ridge regression (RR)-based fingerprint construction is not applied, the detection method would not benefit from the optimization of the signal-to-noise ratio (SNR) across models. The RR method improves generalizability by producing a regression coefficient pattern that optimally projects simulated spatiotemporal precipitation patterns onto a one-dimensional detection space, thereby enhancing the SNR. Without RR, observations would be projected directly onto the signal pattern without this optimization step, potentially resulting in a lower SNR and less robust detection of the forced response. Thus, the absence of RR-based fingerprint construction would reduce the ability to isolate the forced climate signal from internal variability and model disagreement effectively."}
{"question_id": "9505c56639fea265e46601d12575e9d9715b9e7a", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "The variational approximation of the latent variable \\( c \\) given the query and support sets is modeled as  \n\\[\nq_{\\zeta}(c \\mid D_s^j \\cup x_q^{0:T}),\n\\]  \nwhere \\( D_s^j \\) is the support set and \\( x_q^{0:T} \\) is the query series. This approximation shares the same meta set-embedding model as the prior \\( p_{\\zeta}(c \\mid D_s^j) \\) but is conditioned on the augmented set formed by combining the support set with the query series. The meta-model parameterized by \\(\\zeta\\) encodes the support set and query series through a feed-forward embedding network, aggregating embeddings of individual sequences via averaging, and parameterizes the approximate posterior as a Gaussian distribution with mean \\(\\mu_c\\) and variance \\(\\sigma_c^2\\)."}
{"question_id": "de2dc4d1f8b898e5b34a256294729fe7b46f6fda", "paper_id": "openreview/ICLR-2023-conf/ZrEbzL9eQ3W", "generation": "The board games studied use the following win conditions:\n\n- Connect Four: Players win by connecting four of their tokens in a line.\n- Pentago: Players win by connecting a line of five tokens, with each turn ending with a rotation of one of the four quadrants of the board.\n- Oware: The text does not specify a particular win condition but describes the game as having unbounded game length and a different input representation; no explicit win condition is detailed in the provided information."}
{"question_id": "587b947b50e65e3caa8174633245ab39edbdb0f0", "paper_id": "11-33-2023", "generation": "The authors did not conduct analyses using detrended data to isolate the effect of the temperature increase. Their transient model simulations from 1951 to 2019 directly incorporate the observed increase in air temperatures over this period to analyze the evolution of ground temperatures and permafrost thaw. The methodology involves performing a spin-up using the coldest 10-year period to establish stable initial conditions, followed by transient runs driven by the actual warming trend, without mention of detrending or removing the temperature increase signal to isolate its effect."}
{"question_id": "6c9381de277251ad2ce40cd39b94a872cbc4126e", "paper_id": "egu/esd/14-81-2023", "generation": "The authors' conclusion about the accuracy of the CMIP6 climate models in simulating the processes is not explicitly stated in terms of agreement between observed data and the models' predictions regarding residual variability. The methodology section indicates that multiple models and ensemble members are used to capture a wide range of physically plausible climate responses and to account for both within-model internal variability and across-model variability and uncertainty. Long unforced control simulations are employed to sample representative distributions of unforced trends, thereby increasing confidence in assessing whether forced trends lie outside the likely range of unforced variability. However, no direct statement is made about the accuracy of the models based on residual variability agreement with observations."}
{"question_id": "7110b14e5ab532a6273415a059f6808204376ee6", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "The sequential neural process (SNP) setting is not included as a comparison because SNPs are originally designed for supervised learning of a regression function over time rather than for forecasting. The work extends SNP to realize a meta-version of the sequential latent variable model (SLVM) formulation as a counterpart to be compared with the presented meta-SLVM, implying that the original SNP setting does not directly align with the forecasting task addressed in the study."}
{"question_id": "cbb83b653ecc965d0b930f4f016e4ff93c485696", "paper_id": "openreview/ICLR-2023-conf/HnSceSzlfrY", "generation": "RPM-Random performs poorly in pure cooperation scenarios because it samples policies without using ranks, leading to unstable performance and large variance in evaluation returns, as shown in Pure Coordination (PC) 1-3. The absence of ranking in policy sampling prevents RPM from effectively coordinating agents in pure cooperation tasks. In contrast, in the Prisoner's Dilemma (PD) scenarios, although RPM-Random still underperforms compared to ranked sampling, the impact is less severe, indicating that the value of the ranking interval \u03c8 and the use of ranks are more critical for stable and improved performance in pure cooperation than in Prisoner's Dilemma. Thus, the poor performance of RPM-Random in pure cooperation arises from the lack of rank-based policy sampling, which is essential for stable coordination in these tasks."}
{"question_id": "f2e744ebd60bf15d94cd1b9a5cdc7db9f0c4ad93", "paper_id": "openreview/ICLR-2023-conf/ZrEbzL9eQ3W", "generation": "The playing strength of agents is evaluated by restricting them using a fixed number of MCTS simulations (steps) per move rather than by time limits. Specifically, all agents use the same number of MCTS simulations per move at test time, equal to the number used during training, which is 300 MCTS steps. This approach allows tailoring inference-time compute by scaling the number of MCTS steps inversely with forward-pass compute costs, but the evaluation itself is based on a fixed MCTS iteration count rather than a time limit."}
{"question_id": "836969164a688341782ffa72b87f1348ba1ee4ac", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "A total of 27 3D models were used in the pre-collection stage of the data collection process. This comprised 3 replicas each for 9 distinct types of surface discontinuities identified around the targeted urban areas."}
{"question_id": "83065c1670e7bbe6476efd1959f21480a4f3bf69", "paper_id": "nlpeer/ARR-22/eeda0f4ff6c7aef2647481ec15f8ade5a9b32ec32b643e6918a0b8d0ffa1ba44a3f2b9171ba4ba44d5c0faf3199e3bc5f0f740ee31cf48cb966493a96f35ca71", "generation": "In this work, \"knowledge\" is defined as the outcome of assimilating information through learning, encompassing the body of facts, principles, theories, and practices related to a field of work or study. Knowledge components correspond to hard skills, such as acquiring proficiency in a programming language like Python.\n\n\"Skill\" refers to the ability to apply knowledge and use know-how to complete tasks and solve problems. It represents the practical application of knowledge components to specific tasks.\n\n\"Attitude\" corresponds to soft skills and is considered part of skill components. Attitudes involve the ability to use knowledge, skills, and personal, social, and/or methodological abilities in work or study situations, encompassing professional and personal development.\n\nThus, hard skills are typically knowledge components, skills are the application of these knowledge components, and soft skills are referred to as attitudes, which are integrated within skill components."}
{"question_id": "b16ae6d142599eafe257ac83cdf206be914a7a7e", "paper_id": "openreview/ICLR-2023-conf/tyZ1ChGZIKO", "generation": "The Multi-branch Dynamic Selective Frequency module (MDSF) and the Multi-branch Compact Selective Frequency module (MCSF) are concatenated in Figure 1 to leverage their complementary strengths in frequency selection and receptive field coverage. MDSF dynamically decouples feature maps into different frequency components using learnable, theoretically proven filters and applies channel-wise attention to select the most informative frequency parts for reconstruction. It employs multiple branches with varied filter sizes to capture diverse frequency information. In contrast, MCSF efficiently enlarges the receptive field through two branches (global and window-based) using multi-scale average pooling operations, enabling it to handle large-scale degradation blurs with fewer parameters and lower complexity, though it does not perform convolution-based frequency decoupling and modulation like MDSF.\n\nBy concatenating MDSF and MCSF, the network combines the dynamic, fine-grained frequency selection and modulation capabilities of MDSF with the large receptive field and computational efficiency of MCSF. This integration allows the selective frequency network (SFNet) to effectively capture and emphasize useful frequency components across multiple scales and contexts, improving image restoration performance while maintaining manageable computational complexity."}
{"question_id": "f5fe5047a045ce5a97066fd72458d8951c846342", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "A total of 27 3D replicas were used in the survey. These consisted of 3 replicas each for 9 distinct types of surface discontinuity, resulting in 3 \u00d7 9 = 27 replicas."}
{"question_id": "d096c58eea777208cfd4ba272dac018b8a808d6c", "paper_id": "openreview/NeurIPS-2022-dabt/TscdNx8udf5", "generation": "The simulation environments were selected to cover a wide range of manufacturing processes, including beer fermentation, atropine production, penicillin manufacturing, monoclonal antibodies production, and a continuous stirred tank simulation. These environments were chosen to facilitate research in biological and pharmaceutical manufacturing process control by providing high-fidelity simulations that reflect real-world industrial dynamics. The environments are built on published dynamic models and validated with industrial data to ensure alignment with actual factory conditions. This selection enables testing and benchmarking of reinforcement learning algorithms in controlled, safe settings that mimic complex, stochastic manufacturing processes, addressing the lack of high-fidelity simulations and standard APIs for such applications."}
{"question_id": "2f75586071f2de4ab14810c7f2bd7f7b4e143fb6", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "Popular meta-learning frameworks like MAML and Probabilistic MAML are not considered in the experiments because applying optimization-based meta-learning methods such as MAML to sequential latent variable models (SLVMs) encounters challenges related to stability and convergence. Specifically, issues such as vanishing gradients over the complex computation graph of SLVMs make the extension of MAML to these models non-trivial, as noted in the literature and observed during attempts to apply MAML to DKF and GRU-res models."}
{"question_id": "916923a8909ab06632f575b7f36db3ac70642419", "paper_id": "egu/esd/14-1261-2023", "generation": "The molecular diffusivity \u03ba_m is given as 1.4 \u00d7 10^(-7) m\u00b2/s. The wave-induced diffusivity \u03ba_v varies depending on wave parameters and can be significantly larger than \u03ba_m. For example, Table 1 shows dimensionless values of the ratio \u03ba_v(0)/\u03ba_m ranging from 0.36 up to 128.9 for different wave steepness and depth parameters. This indicates that the wave-induced diffusivity \u03ba_v can be several orders of magnitude greater than the molecular diffusivity \u03ba_m, demonstrating that wave-induced mixing is much more efficient than molecular diffusion under the studied conditions."}
{"question_id": "911fa2b76fba7e5ae58fb8322c5940c42acbd848", "paper_id": "openreview/ICLR-2023-conf/xYlJRpzZtsY", "generation": "The proposed taxonomy of generic reasoning errors is comprehensive in that it covers a broad spectrum of reasoning errors encountered in step-by-step language problem solving. It includes nine error types centered on the overall reasoning chain quality, such as consistency with the context and commonsense reasoning, as well as fine-grained errors marking inconsistencies within individual reasoning steps, their contribution to the final decision, and logical inference or fluency issues. The taxonomy addresses both chain-level coarse-grained evaluations (overall quality, coherence, consistency within the explanation and with the context) and step-level fine-grained evaluations (consistency with previous steps, novelty and supportiveness of information, factuality, and logical inference). This is evidenced by the detailed error categories listed in Table 10, which include grammar, factuality, hallucination, redundancy, repetition, missing steps, coherency, commonsense, and arithmetic errors. These categories are used both for constructing diagnostic datasets with perturbed errors and for human annotation of model-generated reasoning chains, demonstrating their applicability and coverage. Furthermore, the taxonomy is designed to be generic enough to work on natural language rationales and to consider alignment with the input context and generated explanation, supporting its comprehensiveness."}
{"question_id": "e232c66d9986ff1ac6f532437bd94f0b71a44ca5", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "The dataset was collected from ten different locations where a high frequency of targeted surface discontinuities was found, including Damansara Perdana, Kota Damansara, Bandar Utama, Up-Town, Damansara Jaya, Bandar Sunway (PJS 7, PJS 9, PJS 10), Brickfields, and Pudu. The sampling method was judgmental sampling, a non-probability approach that prioritized areas with more samples based on the likelihood of encountering surface discontinuities, considering factors such as new versus older urban developments and input from blind and low vision (BLV) volunteers. This approach aimed to capture a representative variety of surface discontinuities relevant to blind navigation in urban environments. Therefore, the selection of these ten locations was intentional to encompass diverse urban surface conditions, suggesting that these ten locations are sufficient to represent the variety of surfaces in the targeted urban environments for the purpose of the dataset."}
{"question_id": "27642536b6affc6438fc0e5a5b2ce6b2d5208309", "paper_id": "egu/esd/14-1261-2023", "generation": "The study applies to both nearshore and offshore environments. The numerical model is designed to cover a range of water depths, including shallow-water and deep-water conditions, as indicated by the range of the nondimensional parameter kh from 0.5 to 2.0. This range allows direct comparison with previous work and includes conditions relevant to nearshore (surfzone) dynamics as well as offshore conditions. Furthermore, the model can be modified to simulate pseudo-random ocean waves in a large periodic domain to analyze wave-induced vertical mixing processes in offshore environments. However, deep-water cases with kh > \u03c0 were omitted in this study due to complete mixing effects within the selected time frame."}
{"question_id": "876d1ffd9379695a117eb81936e7bb2b0ffb1e9d", "paper_id": "openreview/NeurIPS-2022-dabt/TscdNx8udf5", "generation": "The baseline algorithms, including PID, MPC, and EMPC, were tuned by varying either the weights or the horizon, or both, until closed-loop trajectories with little to no overshoot and fast response times were obtained."}
{"question_id": "1818a27b73310c09fb736d5e044187417bda0dbc", "paper_id": "nlpeer/ARR-22/eeda0f4ff6c7aef2647481ec15f8ade5a9b32ec32b643e6918a0b8d0ffa1ba44a3f2b9171ba4ba44d5c0faf3199e3bc5f0f740ee31cf48cb966493a96f35ca71", "generation": "The experts who annotated the dataset have expertise in the domain of the dataset, specifically in the job posting (JP) domain, rather than in linguistics. This is indicated by the description of the annotation process involving domain experts and the emphasis on domain-adaptive pre-training for skill extraction from job postings."}
{"question_id": "3546db32608ccae0b45d96e051b10a8967437d6f", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "Only one single person was employed to wear the wearable sensor and capture all the image samples for the survey."}
{"question_id": "064bd1dff89282732ddcf6c71a98975792d8b3d4", "paper_id": "openreview/ICLR-2023-conf/6xXtM8bFFJ", "generation": "The two-time-scale algorithm, specifically simSGDA-RR and altSGDA-RR, can be extended to the mini-batch setting by partitioning the dataset of n components into mini-batches of size b \u2265 1, where b divides n (i.e., n = bq for some integer q \u2265 1). In each epoch k, a random permutation \u03c3_k of the indices [n] is uniformly sampled, and the components are divided into q mutually disjoint mini-batches:\n\n\\[\nB_{k,t} := \\{ \\sigma_k(j) : b(t-1) < j \\leq bt, j \\in [n] \\}, \\quad t \\in [q].\n\\]\n\nThe updates for the variables \\(x\\) and \\(y\\) are performed over these mini-batches as follows:\n\n- For the primal variable \\(x\\), at iteration \\(t\\) within epoch \\(k\\),\n\n\\[\nx_{k,t} = x_{k,t-1} - \\frac{\\alpha}{b} \\sum_{i \\in B_{k,t}} \\nabla_1 f_i(x_{k,t-1}; y_{k,t-1}).\n\\]\n\n- For the dual variable \\(y\\), two update schemes are considered:\n\n  - Simultaneous update (simSGDA-RR):\n\n  \\[\n  y_{k,t} = y_{k,t-1} + \\frac{\\beta}{b} \\sum_{i \\in B_{k,t}} \\nabla_2 f_i(x_{k,t-1}; y_{k,t-1}).\n  \\]\n\n  - Alternating update (altSGDA-RR):\n\n  \\[\n  y_{k,t} = y_{k,t-1} + \\frac{\\beta}{b} \\sum_{i \\in B_{k,t}} \\nabla_2 f_i(x_{k,t}; y_{k,t-1}).\n  \\]\n\nAfter completing all q mini-batch updates in epoch k, the epoch variables are updated as\n\n\\[\n(x_{k+1,0}, y_{k+1,0}) = (x_{k,q}, y_{k,q}).\n\\]\n\nThis mini-batch extension preserves the without-replacement sampling structure by ensuring that all mini-batches within an epoch are mutually disjoint, which differs from standard mini-batch sampling with replacement. The analysis assumes Lipschitz continuity of the mini-batch gradient oracles and aggregates updates over the epoch to facilitate convergence proofs. Step sizes \\(\\alpha\\) and \\(\\beta\\) are chosen appropriately to maintain convergence guarantees under this mini-batch scheme."}
{"question_id": "add8f70fda4a981fbac7e3f41f938eeecd3ccd4d", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The first kind of rule in Section 4 is called \"Markovian\" because the calculation of the next state probability depends only on the current state probability, without considering previously visited edges. This means that the random walk performed under Markovian constraints satisfies the Markov property, where the next step is determined solely by the present state, making the process memoryless with respect to the path history."}
{"question_id": "cf91a671c7d2248b716cf143ff64e032fed4681e", "paper_id": "nlpeer/ARR-22/27046f6633d116b03e48eb94976921b46a86a976ddd2199ef76c90820a95e0d85406daa5ebc9d7b12359157d20f3bd0025e1cbc2c126bb2506f5e83e93f435b5", "generation": "The layer normalization approximation incurs the most significant performance reduction among the approximation components in THE-X. Specifically, it causes an average performance drop of 1.08% on the GLUE benchmark and is identified as the primary source of performance degradation. In token-level tasks such as Named Entity Recognition (NER), the layer norm approximation also leads to the largest performance reduction, with an F1 score decrease of approximately 1.9%. The approximation error from layer normalization tends to accumulate as the transformer model stacks more layers, further impacting performance. Overall, while THE-X maintains good performance across tasks, the layer norm approximation remains the critical factor contributing to performance loss."}
{"question_id": "aae1c73c7b0de5fb88e34c245782e2ecb4dcb24d", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "The dataset is not balanced across different classes. Both up-steps and down-steps have slightly higher frequency than the other classes, indicating a minor class imbalance typical of the urban areas sampled."}
{"question_id": "130985a6f0c94e81204c5a5014faa6017dc2a328", "paper_id": "egu/esd/14-1261-2023", "generation": "The problem under investigation pertains to genuinely nonlinear wave dynamics rather than being limited to a weakly nonlinear context. The study extends previous analyses based on weakly nonlinear theory by employing a fully nonlinear wavemaker model that admits arbitrary-order nonlinear terms without an upper limit. This fully nonlinear model captures higher-order wave components, amplitude dispersion, nonlinear wave\u2013wave interactions, and solitary wave propagation, which go beyond the applicability of weakly nonlinear approaches that retain only second-order terms. The fully nonlinear solution provides a more accurate estimation of the phase-averaged wave velocity field and wave-induced mixing processes, especially for waves of higher steepness where strong nonlinearities significantly affect subsurface mixing."}
{"question_id": "d250649b5c73368021f92321b3d59f4c1d3c762f", "paper_id": "openreview/ICLR-2022-conf/iulEMLYh1uR", "generation": "The msec/example values in Figures 1 and 2 were measured on a V100 GPU."}
{"question_id": "006b4d78ff2835159d4e1f745a3f9c4f41fe8351", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The Markovian assumption in equation (6) is valid under the condition that the calculation of the next state probability depends only on the current state probability, without consideration of previously visited edges. In the described framework, the random walk is performed under Markovian constraints that include body predicates \\( P_i \\) and temporal relations \\( TR_{i,l+1} \\) between the query interval and each body interval. These constraints are encoded in the matrix operators \\( M_{i,CM_i} \\), which represent adjacency matrices filtered by the current step's predicate and temporal relation constraints.\n\nSince the current random walk step is determined by a fixed relation type and a query interval (starting time) that does not change during the walk, the transition probabilities depend solely on the current entity and the fixed constraints at step \\( i \\). This satisfies the Markov property, where the next state depends only on the current state and not on the path history.\n\nNon-Markovian constraints, which involve pairwise temporal relations between body intervals, are handled separately by filtering the completed walks after the Markovian random walk process. Thus, the Markovian assumption applies strictly to the stepwise random walk process described in equation (6), where the current step's transition depends only on the current state and fixed constraints, including the given relation type and starting time.\n\nTherefore, the Markovian assumption in equation (6) is valid given that the current random walk step is determined by a fixed relation type and starting time that remain constant throughout the random walk."}
{"question_id": "cfbd6962220a29ddfda999443b628e02ebd2d79b", "paper_id": "11-849-2023", "generation": "The provided information does not explicitly address the effect of grid orientation or the presence of more north-facing slopes in the Elwha area compared to the Quinault area on solar radiation absorption and glacier distribution. However, it is noted that the flowline glacier model used in the study captures variability along the direction of steepest precipitation gradients but neglects variability across the valleys, which may be important for spatially variable drivers of ablation such as topographic shading. This implies that aspect-related melting, which would include the influence of slope orientation on solar radiation absorption, was not explicitly accounted for in the model. Therefore, while slope orientation could theoretically influence solar radiation absorption and thus glacier distribution, this factor was not directly incorporated or analyzed in the modeling results presented."}
{"question_id": "95d0f3eec5444caddab3df7e45aa31db81cabef8", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The operator \\( M_i,CM_i \\in \\{0,1\\}^{|E| \\times |E|} \\) represents the adjacency matrix under Markovian constraints for step \\( i \\) in a constrained random walk on a knowledge graph. Specifically, for entities \\( e_x, e_y \\in E \\), the entry \\( (M_i,CM_i)_{x,y} \\) is defined as the maximum over all facts \\( F \\in F_{y,x} \\) of the product of filtering operators \\( f_{P_i}(F) \\) and \\( f_{TR_{i,l+1}}(F, (e_s, r, ?, I)) \\), where \\( f_{P_i} \\) and \\( f_{TR_{i,l+1}} \\) are filtering operators for the predicate and temporal relation constraints, respectively. The entries are set to 1 if the constraints are satisfied, and 0 otherwise. This operator thus encodes the allowable transitions between entities at step \\( i \\) under the given Markovian constraints, enabling the identification of all paths between entity pairs that satisfy these constraints during the random walk process."}
{"question_id": "5c790742a803e76547d117cf4a77434d1737b5b1", "paper_id": "11-849-2023", "generation": "Besides temperature and precipitation, several other factors could influence the distribution of glaciers in a given area. These include:\n\n1. **Precipitation Type:** The form of precipitation (snow versus rain) affects glacier mass balance, as only snow contributes to accumulation. Rainfall, especially at lower elevations and during warmer seasons, does not add to glacier mass and can reduce accumulation.\n\n2. **Cloudiness:** Cloud cover modulates the amount of solar radiation reaching the glacier surface, thereby influencing melt rates. Spatial patterns in cloudiness, often linked to orographic effects, can create differences in melting efficiency across regions.\n\n3. **Humidity:** Variations in humidity can affect glacier mass balance by influencing sublimation rates and the lapse rate. Lower humidity, often found in leeward valleys due to descending airflows, can increase the lapse rate and potentially enhance melting.\n\n4. **Topographic Factors:** Valley geometry can induce feedback mechanisms such as ice elevation feedback, where glacier thickening in narrowing valleys leads to rapid glacier advance. Topographic shading also affects spatial variability in ablation by altering solar radiation exposure.\n\n5. **Melt Efficiency:** Differences in the efficiency of melting, potentially driven by local climatic or topographic conditions, can significantly limit or enhance glacier growth and extent.\n\nThese factors interact with temperature and precipitation to shape the spatial distribution and dynamics of glaciers within mountainous regions."}
{"question_id": "e7cb5933a3df86f543cb36cb77b3f41cd7ad4021", "paper_id": "nlpeer/COLING2020/1367", "generation": "The provided evidence chunks do not explicitly discuss the advantages of rule-based approaches over statistical or neural methods when working with small corpora for Indigenous languages. The information focuses primarily on automatic speech recognition (ASR) experiments with polysynthetic Indigenous languages, challenges related to out-of-vocabulary rates, sub-word modeling units, and the development of predictive text using word-level unigrams due to sparse data. There is no direct comparison or mention of rule-based approaches versus statistical or neural methods in the context of small corpora for Indigenous languages. Therefore, no specific advantages of rule-based approaches can be stated from the given information."}
{"question_id": "317ee5566b85c3b36699add3f268020579e8b718", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The drawbacks of using statistical learning methods such as StreamLearner and TLogic include:\n\n1. Inflexibility in temporal constraints learning: TLogic builds temporal constraints on timestamps rather than intervals, and these constraints are fixed during learning, which impairs its ability to learn temporal constraints effectively.\n\n2. Independent rule learning: These methods estimate rule confidence by counting the number of rule groundings and body groundings independently, ignoring interactions between different rules derived from the same positive example. This can lead to inaccurate confidence estimation where the confidence of some rules might be enhanced or diminished due to interactions with other rules.\n\n3. Inability to handle similarity between different rules: Statistical methods do not account for the similarity between rules. For example, a reliable rule may imply a high confidence for a similar rule with slightly different temporal patterns, but statistical methods may assign low confidence to such infrequent rules.\n\n4. Limited applicability to interval-based temporal knowledge graphs (tKGs): These methods primarily operate on timestamp-based data and their performance on interval-based tKGs, which have more complex temporal relations, is not demonstrated and likely impaired.\n\n5. Restricted random walk strategies: StreamLearner and TLogic perform random walks in a very restricted manner, such as requiring all body atoms in extended rules to have the same timestamp (StreamLearner) or using sampling strategies that impair model performance by limiting successful long-distance random walks (TLogic).\n\n6. Lack of interaction modeling and flexibility: The statistical counting approach does not model the interactions between rules or allow for flexible temporal pattern learning, limiting the quality and interpretability of learned temporal logical rules."}
{"question_id": "09c36735b520089d4936e6966157cb10f8f1ed0e", "paper_id": "openreview/ICLR-2022-conf/iulEMLYh1uR", "generation": "Yes, when scaling the depth (number of layers) or width (hidden dimension) of the Vision Transformer (ViT) models, all other hyperparameters are kept fixed based on the default values given by the referenced papers."}
{"question_id": "85afe8245083d99893657bc1eeadbcefa12dbf59", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "\"1 dB in PSNR\" refers to an improvement of one decibel in the Peak Signal-to-Noise Ratio, which is a common metric used to measure the quality of image compression. An increase of 1 dB in PSNR indicates a higher fidelity or better quality of the compressed image relative to the original, reflecting a reduction in distortion at a given bitrate."}
{"question_id": "08ee038d964c18feafe50974403477b69a786d82", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The variables and functions in equations (8) to (13) have the following dimensions:\n\n- The parameters \\( \\mu_{\\text{pair}}, \\sigma_{\\text{pair}}, \\lambda_{\\text{pair}} \\) belong to \\( \\mathbb{R}^{|R| \\times |R|} \\), where \\(|R|\\) is the number of relations. These parameters are used in the temporal feature modeling functions \\( F_{c,s} \\), \\( F_{c,\\bar{s}} \\), and \\( SWC(e_s, e_c) \\).\n\n- The scoring functions \\( \\varphi_{\\text{pair}, i}(e_c; h_{\\text{pair}, i}, w_{\\text{pair}, i}, b_{\\text{pair}, i}) \\) for \\( i = 1, 2, 3 \\) have parameters \\( h_{\\text{pair}, i}, w_{\\text{pair}, i}, b_{\\text{pair}, i} \\in \\mathbb{R}^{|R| \\times |R|} \\).\n\n- The temporal feature \\( h_{\\text{pair}, i} \\) is based on the parameters \\( \\{ \\mu_{\\text{pair}, i}, \\sigma_{\\text{pair}, i} \\} \\) or \\( \\lambda_{\\text{pair}, i} \\) with input \\( x = |t_s - (T_s)^i_{r'}| \\).\n\n- The Recurrence feature parameters \\( p_{\\text{rec}, 1}, p_{\\text{rec}, 2} \\in \\mathbb{R}^{|R|} \\), and the corresponding scoring functions \\( \\varphi_{\\text{rec}, i}(e_c; h_{\\text{rec}, i}, w_{\\text{rec}, i}, b_{\\text{rec}, i}) \\) have parameters \\( h_{\\text{rec}, i}, w_{\\text{rec}, i}, b_{\\text{rec}, i} \\in \\mathbb{R}^{|R|} \\) for \\( i = 1, 2 \\).\n\n- The TemporalOrder feature parameters \\( p_{\\text{order}, i} \\in \\mathbb{R}^{|R| \\times |R|} \\) and scoring functions \\( \\varphi_{\\text{order}, i}(e_c; h_{\\text{order}, i}, w_{\\text{order}, i}, b_{\\text{order}, i}) \\) have parameters \\( h_{\\text{order}, i}, w_{\\text{order}, i}, b_{\\text{order}, i} \\in \\mathbb{R}^{|R| \\times |R|} \\) for \\( i = 1, 2, 3 \\).\n\n- The Duration feature parameters \\( \\mu_d, \\sigma_d \\in \\mathbb{R}^{|R|} \\).\n\n- The combined temporal feature modeling scores \\( \\varphi_{\\text{tfm}, i}(e_c) \\) for \\( i = 1, 2, 3 \\) are weighted sums of the respective Recurrence, TemporalOrder, and RelationPairInterval scoring functions, with learnable weights \\( \\gamma \\geq 0 \\) summing to 1 within each part.\n\nIn summary, the dimensions of variables and functions correspond to the number of relations \\(|R|\\) or pairs of relations \\(|R| \\times |R|\\), depending on whether the feature models single relations (Recurrence, Duration) or pairs of relations (RelationPairInterval, TemporalOrder)."}
{"question_id": "****************************************", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "Yes, the proposed algorithms can be applied to other classes of probability distributions beyond the Gaussian distribution and the banana-shaped source. The experiments include applications to various sources such as particle physics data and speech data, as indicated by the rate-distortion performance plots and visualizations in the experiments section. This demonstrates the generality of the approach to different types of data distributions."}
{"question_id": "d5b246ec5a8edcc34e88a24fb9bfd7d313572647", "paper_id": "openreview/ICLR-2022-conf/pjqqxepwoMy", "generation": "The target of return \\( v^{tar}_t \\) in equation (1) can be estimated by any value learning algorithm such as TD(0), Peng\u2019s Q(\u03bb), or other methods based on Bellman equations. It can also be obtained using Monte-Carlo return if available. In the implementation described, double Q-learning with dueling architecture is employed to compute \\( v^{tar}_t \\) due to its effectiveness and simplicity."}
{"question_id": "7603a58573eeaabda0e22ca42e407dd44c83bd3e", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "Action frames are syntactic and semantic structures associated with verbs that represent the core components of an action, typically including the subject, verb, direct object, and prepositional object. These components are mapped to semantic roles such as agent (subject), theme (direct object), and goal (prepositional object). For verbs involving prepositional phrases, separate frames are created for each preposition based on corpus statistics. Action frames capture the different contexts or relations in which a verb can occur, reflecting the multiple frame relations a verb may have according to frame semantics. They serve as the basis for modeling physical implications and entailments of verbs by considering pairwise argument relations within each frame."}
{"question_id": "33ac8263606098fca8bcdc4746cd3f4235387b26", "paper_id": "openreview/ICLR-2022-conf/0jP2n0YFmKG", "generation": "The new method, Graph Parallelism, demonstrates superior performance compared to existing distributed training methods such as data parallelism and pipeline (model) parallelism. Specifically, when combined with 32-way data parallelism, Graph Parallelism achieves a scaling efficiency of 47% on 256 GPUs for the largest model, which is promising for training extremely large models on hundreds of GPUs. In contrast, pure data parallel training with 32 GPUs attains a scaling efficiency of 75% for the smallest model, indicating that Graph Parallelism maintains reasonable efficiency even at much larger scales.\n\nFurthermore, Graph Parallelism outperforms pipeline parallelism for the considered models. This is attributed to better load balancing across GPUs since Graph Parallelism evenly distributes nodes, edges, and triplets of a given batch, whereas pipeline parallelism suffers from load imbalance due to varying graph sizes. Although pipeline parallelism might have advantages for very deep GNNs due to communication overhead characteristics, the empirical results show that Graph Parallelism achieves higher sustained computational throughput, with the largest model sustaining 3.5 PetaFLOPs on 256 V100 GPUs.\n\nAdditionally, combining Graph Parallelism with pipeline parallelism is possible and these methods are complementary, potentially enabling further performance improvements.\n\nIn summary, Graph Parallelism runs faster and scales more efficiently than existing distributed training methods such as data and pipeline parallelism for the large GNN models considered."}
{"question_id": "6de0c620431f72ce5a6331d7dde1b8df91e24936", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "The criteria for determining which knowledge dimensions to annotate for each frame involve collecting human judgments on the general relationship that the arguments of the frame exhibit with respect to all considered knowledge dimensions (such as size, weight, strength, rigidity, and speed). Crowd workers are presented with a frame template (e.g., \"x threw y\") and asked to rate the relationship between the frame arguments along each knowledge dimension by selecting one of four options: (1) x > a y, (2) x < a y, (3) x \u2243 a y, or (4) no general relation. Frame/attribute combinations that receive less than two-thirds agreement among workers or are selected by at least two-thirds of workers as having no general relation are removed. This filtering ensures that only frames with reliable and meaningful annotations across the knowledge dimensions are retained for further use."}
{"question_id": "53c8c64ae66712791ba2a355e4cc97a262c61acc", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "The provided text does not explicitly address the definition or treatment of the rate-distortion (R-D) function for probability distributions where the expectation is undefined, such as the Cauchy distribution. The standard definition of the R-D function given in the text assumes the existence of expectations with respect to the source distribution \\( P_X \\), specifically the expected distortion and mutual information terms, which are defined with respect to the joint distribution \\( P_X Q_{Y|X} \\).\n\nFormally, the R-D function is defined as\n\n\\[\nR(D) = \\inf_{Q_{Y|X} : \\mathbb{E}[\\rho(X,Y)] \\leq D} I(X;Y),\n\\]\n\nwhere the expectation \\(\\mathbb{E}[\\rho(X,Y)]\\) and the mutual information \\(I(X;Y)\\) are taken with respect to the joint distribution \\(P_X Q_{Y|X}\\). This definition presupposes that these expectations exist and are finite.\n\nFor distributions like the Cauchy distribution, where the expectation of certain functions (including moments) may be undefined or infinite, the standard formulation of the R-D function as given cannot be directly applied because the expected distortion \\(\\mathbb{E}[\\rho(X,Y)]\\) may not be well-defined.\n\nThe text does not provide an alternative or generalized definition of the R-D function for such cases. Therefore, within the scope of the provided information, the R-D function is defined only for probability distributions where the relevant expectations exist and can be approximated by sample averages."}
{"question_id": "92b04f60c27edb89dcdc8dcd575bcb9872f0e307", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "Yes, the incorrectly-classified actions and objects exhibit ambiguity for humans. The error analysis highlights cases where different crowd workers provided conflicting examples for the same frame, indicating underspecified or polysemous verb senses. For instance, in the example of \"PERSON stopped the fly with {the jar / a swatter},\" some workers judged the fly as lighter than the jar or swatter, while others gave examples like \"PERSON stopped their car with the brake,\" where the car is heavier than the brake. This reflects complex underlying physics and ambiguous frame specifications that humans interpret differently. Another example involves polysemy in the phrase \"She caught the runner in first,\" where the correct interpretation is that she is faster than the runner, but crowd workers selected a different sense involving catching a baseball, reversing the relative speed relation. These examples demonstrate that the ambiguity in verb senses and frame specifications leads to inconsistent human judgments, contributing to model misclassifications."}
{"question_id": "8f0ed4f134911b593527e3793459b6d55faf5923", "paper_id": "openreview/NeurIPS-2022-dabt/-VyJim9UBxQ", "generation": "The copyright for the images used in the paper is held by the Reddit users who originally posted the photos. The dataset complies with the Reddit User Agreement and Reddit API terms of use, which state that user photos, text, and videos (\"User Content\") are owned by the users and not by Reddit. The dataset creators do not modify the original content and provide tools to access the data while respecting the rights of the content owners."}
{"question_id": "748b3e8ce8fe5bb6fa899f962211e41d18c30cae", "paper_id": "openreview/NeurIPS-2022-dabt/wPEXGTzZJt", "generation": "The bounds presented in Table 1 correspond to the performance limits established by baseline models in the evaluation of visual search models. The lower bound is defined by the uniform and center bias models, which represent minimal predictive performance: the uniform model assumes fixations are uniformly and independently distributed over the image, while the center bias model accounts for the human tendency to fixate near the center of images using a Gaussian Kernel Density Estimate (GKDE) derived from fixation data. The upper bound is given by the Gold Standard model (GS), which predicts fixations of each participant based on a GKDE over all fixations from other participants on the same image, representing the best achievable prediction given human data variability. These bounds serve to contextualize the performance of evaluated models by providing a baseline range from minimal to near-optimal prediction accuracy."}
{"question_id": "3b1176248b0cfc5fb6e1786bb4007f98aa2ac210", "paper_id": "openreview/NeurIPS-2022-dabt/UrAYT2QwOX8", "generation": "The first and second disparities are induced by controlling the generative model sampling process, depending on the group and label, respectively."}
{"question_id": "413aa7a24c99874e0aae31b569348cc6c4e39b14", "paper_id": "nlpeer/F1000-22/10-170", "generation": "Recommendations based on the views expressed on coordination, preparation, and decision-makers include the following:\n\n1. Increase budgetary allocation and improve efficiency within the health system to address preexisting constraints such as budget shortages, low-quality services, high out-of-pocket payments, and an unregulated private sector.\n\n2. Implement targeted policy approaches to decentralize health system management, enabling tactful decentralization and coordination among different relevant ministries and government departments at district and sub-district levels.\n\n3. Engage public health professionals and relevant experts\u2014including infectious disease epidemiologists, health policy and systems experts, medical anthropologists, health economists, health communication experts, laboratory scientists, and clinicians\u2014in scientific decision-making processes to ensure a science-based professional response to pandemics.\n\n4. Establish a dedicated public health career track within the health sector to strengthen pandemic preparedness and management.\n\n5. Enhance multisectoral collaboration and coordination by involving not only government departments but also religious leaders, cultural activists, the for-profit private sector, non-governmental organizations, political parties, community groups, and individuals.\n\n6. Ensure transparency in data and decision-making, provide correct and contextually appropriate information through trusted community messengers, and tailor communication strategies using scientifically oriented social and behavior change communication experts.\n\n7. Improve regulation and accountability in the health sector by curbing corruption, enforcing punitive actions against wrongdoers, dissolving syndicates, and regulating the private sector for cost and quality.\n\n8. Avoid decisions driven by non-scientific considerations, such as terminating lockdowns for political or cultural events despite ongoing outbreaks, by prioritizing expert public health advice.\n\n9. Coordinate actions among different sectors to prevent contradictory policies that negatively impact vulnerable populations, such as the lack of coordination between trade organizations, police, and health departments affecting garment workers.\n\nThese recommendations aim to address the identified issues of incoordination, inadequate pandemic preparedness, mismanagement, and poor regulation to improve the overall health system response."}
{"question_id": "b4885c9ebb178f8daa578cef0c857a1de41d8d54", "paper_id": "nlpeer/ARR-22/43e8458544e18304f6aa2c1aededf974877f088dd2ccd7d53ef71eb59e54567c0572f715ecdcbaf04c39206950c3f5628198e61ebc5ec09fa1ba0a559102f923", "generation": "The location of NLP conferences significantly influences the diversity of participation. Conferences held in different locations encourage local researchers to attend, thereby increasing participation diversity. Analysis shows that researchers tend to travel shorter distances than would be expected if venues were chosen randomly, resulting in a higher number of short trips and fewer long trips. This pattern supports the hypothesis that hosting events in varied locations promotes local attendance and enhances diversity.\n\nFurthermore, the diversity of participation generally increases with travel distance, but some conferences achieve higher diversity at lower travel distances, such as LREC events. European conferences have historically been the most diverse, though Asian events are increasingly catching up. Over time, the mean participation diversity of events organized within continents has shown an increasing trend.\n\nIn summary, distributing conferences across different geographic locations fosters greater participation diversity by facilitating attendance from nearby researchers, balancing travel distance and inclusivity."}
{"question_id": "c44a73f20a295acb499abf61c0f0b96e4080d9ba", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "Yes, there is a universal quantifier \\(\\forall \\phi \\in \\Phi\\) before equation (3.5) in Definition 3.2 (Transferability). The definition states that a representation \\(\\phi \\in \\Phi\\) is \\((\\rho, C_{\\rho})\\)-transferable if there exist \\(\\rho > 0\\) and \\(C_{\\rho} > 0\\) such that for any \\(\\bar{\\phi}_{\\omega} \\in \\bar{\\Phi}_{\\omega}\\), the inequality\n\n\\[\nL^{\\star}_0(\\phi) - L^{\\star}_0(\\bar{\\phi}_{\\omega}) \\leq C_{\\rho} \\left( \\sum_{t=1}^T \\omega_t [L^{\\star}_t(\\phi) - L^{\\star}_t(\\bar{\\phi}_{\\omega})] \\right)^{1/\\rho}\n\\]\n\nholds. This implies the statement applies for all \\(\\phi \\in \\Phi\\)."}
{"question_id": "0544f3fb3619a15d1f6076086707d91cea93b334", "paper_id": "openreview/NeurIPS-2022-dabt/wPEXGTzZJt", "generation": "The authors selected the four datasets because, to their knowledge, these are the only publicly available datasets specifically designed for the task of visual search in natural scenes. Each dataset contains curated search images, target objects, and anonymized participants' scanpaths, which are essential for evaluating visual search models. The datasets differ in characteristics such as the number of participants, image content, presence of distractors, color, and target size, providing a range of conditions for evaluation. This selection allows for a common framework and unified criteria to fairly compare models despite variations in task design and dataset properties. The authors also emphasize the importance of testing models on a diverse collection of datasets with comparable measures and suggest that future datasets could be added to the benchmark under the established preprocessing and evaluation conditions."}
{"question_id": "caa94f24704e76df0ceac395ee650a45f7a174e1", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The most popular and advanced methods in cross-task learning are pre-training and joint training. Pre-training involves first training the representation part of the model on source data and then fine-tuning the entire model on the target data. Joint training simultaneously uses both source and target data to train a shared representation model along with task-specific functions for both source and target tasks. These paradigms are commonly employed with models such as BERT, where the representation model is shared and task-specific functions are implemented as last-layer classifiers. Additionally, weighted training algorithms like Target-Aware Weighted Training (TAWT) have been proposed to enhance these paradigms by adaptively assigning trainable weights to source tasks or samples, thereby improving sample efficiency and performance in cross-task learning."}
{"question_id": "ddbf5cd6168ece49006281d9def64503e3610f0f", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The provided chunks do not contain any explicit information or discussion regarding the nature of \\(\\phi\\) as a vector or any assumption labeled as \"assumption b.\" The proofs and statements focus on bounding losses, empirical processes, and transferability assumptions related to functions \\(\\phi\\), \\(\\bar{\\phi}\\), and learned weights \\(\\omega\\), but do not specify the structural form or dimensionality of \\(\\phi\\).\n\nTherefore, there is no evidence in the given text to conclude whether \\(\\phi\\) must be a vector under any assumption, including \"assumption b.\""}
{"question_id": "9776571072ba250ab654c2a326bd48a527e61213", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The proposed framework, Meta-EGN, is designed as a general unsupervised learning framework for combinatorial optimization (CO) problems. It leverages meta-learning, specifically model-agnostic meta learning (MAML), to learn a good initialization for neural networks that can adapt quickly to individual problem instances. This approach improves performance on CO tasks such as the maximum independent set problem on large-scale random regular graphs and demonstrates good generalization across different data scales and distributions.\n\nWhile the framework is primarily applied to CO problems, the meta-learning-based training pipeline is general in nature and aims to optimize neural networks towards instance-wise good solutions rather than averaged performance over distributions. This suggests that the framework could potentially be extended to other CO problems beyond those explicitly studied, including problems without closed-form objectives, such as circuit design, as inspired by related work on proxy objectives.\n\nHowever, the framework is specifically tailored to unsupervised learning for CO and has not been reported to be applied to tasks outside this domain. The meta-learning approach used is model-agnostic and has been applied in other fields (e.g., vision tasks), but the current work is the first to apply meta-learning to unsupervised learning for CO. Therefore, while the framework is extensible within the domain of combinatorial optimization, its extension to other application domains or tasks beyond CO has not been demonstrated in this work."}
{"question_id": "1874144ac78d10d99982bc7f6446545ac56a4805", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "A fine-grained model analysis was conducted by sampling approximately 1,000 cue-image pairs from instances created with 10-12 candidates. Three authors categorized these instances into six association types: (a) visually salient, (b) visually non-salient, (c) concept related, (d) activity, (e) counting, and (f) colors. The model CLIP ViT-B/32 was evaluated on these categories, and accuracy per category was reported. The analysis revealed that model performance was highest for visually salient and colors categories, degraded for concept related and activity categories, and was much worse for visually non-salient and counting categories. This indicates that models struggle particularly with visually non-salient associations, suggesting a lack of common sense reasoning capabilities. The annotated data from this analysis was released for future research."}
{"question_id": "530a4fe0ab0d3f801dab10132e0e5791d29d2051", "paper_id": "nlpeer/ARR-22/12ea2851de6445bf343d642c6ccc8640bf7a6c28b5f20ba123d6d58f02790c7c7f8ab696e6a6b4e45bfad28bd4bc0419bdcf6358693404dd0c14d202b291503b", "generation": "The results in Table 1 with decoder downsampling are obtained without the two-step decoding method. This is indicated by the statement that employing the two-step decoder matches the decoding speed of subword models but results in much worse overall translation quality, implying that the reported translation quality in Table 1 corresponds to models without the two-step decoder."}
{"question_id": "c5bafca1e41f1bfed6bf6063b321e6bb5102b171", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The poorer performance of the CLIP-ViL model compared to other models is attributed to its use of the image-text matching (ITM) pre-training objective, whereas models like X-VLM and ViLT are fine-tuned specifically for image-text retrieval tasks. CLIP-ViL's ITM objective is less effective for the association task in WinoGAViL, while CLIP uses a different contrastive pre-training objective that is more suitable for this task."}
{"question_id": "207945518935728931a4b020daa416e8fc8f1cda", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The relaxations in Table 2 follow the training loss design and relaxation approach established in prior work, specifically referencing Karalias & Loukas (2020) and Wang et al. (2022). The detailed derivation of the loss function relaxation for the three problems (MC, MVC, and MIS) is based on these existing methodologies rather than being newly derived in this paper."}
{"question_id": "fe2486dcff37fd819cb07981aa6a1e026c1f52e5", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The proposed method, Target-Aware Weighted Training (TAWT), improves upon state-of-the-art (SOTA) cross-task learning methods by adaptively assigning trainable weights to source tasks or samples in a theoretically principled manner. Unlike previous approaches that assume a single shared representation among tasks or rely on heuristic weighting strategies, TAWT minimizes a representation-based task distance between source and target tasks, which provides a finer-grained and more informative measure of task relatedness. This approach enhances sample efficiency in both pre-training and joint training paradigms, leading to an average absolute performance improvement of 3.1% over BERT, a leading baseline model. Furthermore, TAWT is the first weighted algorithm for cross-task learning with theoretical guarantees, and it functions as an easy-to-use plugin without significant computational overhead or hyperparameter tuning. Thus, TAWT offers both theoretical and empirical advantages over existing advanced methods in cross-task learning."}
{"question_id": "26c5a6b5fdd3b1d0b2d97c1550126b503f144eed", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The chance performance reported in Table 4 is 15%."}
{"question_id": "a9dce4895de5aac10ec1c0b1da92de90c47582f9", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The wall clock training time to convergence for the EGN and Meta-EGN models on various datasets is reported in Table 9. The training times (in minutes:seconds) are as follows:\n\n- MC: EGN 46:50, Meta-EGN 101:55\n- MVC: EGN 104:37, Meta-EGN 210:04\n- MIS: EGN 282:57, Meta-EGN 609:47\n- Twitter: EGN 100:58, Meta-EGN 276:38\n- RB200: EGN 83:27, Meta-EGN 168:25\n- RB500: EGN 128:39, Meta-EGN 282:15\n- RRGs: EGN 733:02, Meta-EGN 1088:55\n\nThus, the EGN models were pretrained for times ranging approximately from 46 minutes to 12 hours, depending on the dataset, while Meta-EGN models took roughly two to three times longer, ranging from about 1 hour 40 minutes to over 18 hours."}
{"question_id": "8192e96a224224e5fc15e03019d0ac65253d1492", "paper_id": "nlpeer/F1000-22/10-838", "generation": "The provided chunks do not contain any information regarding a \u201crange\u201d parameter or its purpose. Therefore, no answer can be given based on the available content."}
{"question_id": "1be49494b9a1c964df99b3dabe0af0bfdc970713", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The methodology described focuses on unsupervised learning for combinatorial optimization (CO) problems through meta learning, specifically using a model-agnostic meta learning (MAML) framework to learn good initializations for optimization on individual problem instances. Each training instance is treated as a pseudo-new task, and the model is trained to quickly adapt to each instance via fine-tuning, aiming for instance-wise optimality rather than averaged performance.\n\nWhile the approach shares conceptual similarities with multi-task learning\u2014such as learning from multiple tasks (instances) to improve generalization and adaptation\u2014the methodology is explicitly designed for fast adaptation to new instances of CO problems rather than simultaneous learning across multiple distinct tasks. The meta learning framework is used to optimize initialization for quick fine-tuning on each instance, which can be viewed as a form of task adaptation.\n\nTherefore, the methodology can be interpreted as applicable to a form of multi-task learning where each task corresponds to optimization over a single CO instance. However, it is specialized for unsupervised learning in CO and emphasizes rapid adaptation to new instances rather than joint training on multiple tasks without adaptation. The work is noted as the first to apply meta learning to unsupervised learning for CO, indicating a novel use case rather than a general multi-task learning framework."}
{"question_id": "21f851f7058a46d8f9904d493b82811edf3aa8f3", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The provided content does not mention or discuss any connections between the proposed Target-Aware Weighted Training (TAWT) algorithm and existing research on learning data weighting using multi-arm bandits. The paper focuses on a theoretically principled weighted training approach for cross-task learning, where adaptive weights on source tasks or samples are determined by minimizing a representation-based task distance. The weighting strategy is contrasted with common schemes such as importance sampling in domain adaptation, but no reference or comparison to multi-arm bandit methods for data weighting is made. Therefore, no explicit connections to multi-arm bandit approaches are established in this work."}
{"question_id": "b79e294382978a8a9cebe595211529bcb653e0f2", "paper_id": "nlpeer/COLING2020/1886", "generation": "The topics for n-grams are chosen by assigning each n-gram to a predefined list of topics based on its content. For example, n-grams such as \"missile\" and \"army\" are assigned to the topic \"War.\" The assignment process involves categorizing n-grams into topics, proper names, hashtags, and function words. The importance of each topic for the two target classes (fake and real news) is then defined by summing the relevance values (R_v and F_v) of the n-grams included in that topic. To focus on n-grams relevant to one of the target classes, the difference between R_v and F_v is computed for each n-gram, and only those with a difference larger than the mean plus one standard deviation (\u03bc + \u03c3) are retained."}
{"question_id": "8f304b893f44b97b98ba8df1ea43ce0fcb657b87", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The expression in question appears in the context of bounding differences of loss functions weighted by \u03c9_t and normalized by n_t, specifically terms of the form\n\n\\[\n\\sum_{t=1}^T \\omega_t \\left[ L_t(\\phi, f_t) - \\ell(f_t \\circ \\phi(x_{t,i}), y_{t,i}) + \\ell(\\bar{f}^\\omega_t \\circ \\bar{\\phi}^\\omega(x_{t,i}), y_{t,i}) + L_t(\\bar{\\phi}^\\omega, \\bar{f}^\\omega_t) \\right].\n\\]\n\nFrom the provided proofs, the loss function \\(\\ell\\) is bounded in the interval \\([0,1]\\). This boundedness ensures that differences of the form\n\n\\[\nL_t(\\phi, f_t) - \\ell(f_t \\circ \\phi(x_{t,i}), y_{t,i}) + \\ell(\\bar{f}^\\omega_t \\circ \\bar{\\phi}^\\omega(x_{t,i}), y_{t,i}) + L_t(\\bar{\\phi}^\\omega, \\bar{f}^\\omega_t)\n\\]\n\nare themselves bounded and controlled. Moreover, the weights \\(\\omega_t\\) and sample sizes \\(n_t\\) are positive quantities by definition (weights and counts of samples cannot be negative).\n\nBecause each term in the sum is weighted by a positive \\(\\omega_t\\) and normalized by positive \\(n_t\\), and the loss differences are bounded, the entire sum is non-negative or at least bounded below by zero in the context of the inequalities used.\n\nThis non-negativity or boundedness from below justifies taking powers of the sum to the \\(1/p\\) power, as the expression inside the power is effectively positive or non-negative. In particular, the transferability assumption and the neighborhood bounds used in the proof rely on this positivity to apply H\u00f6lder-type inequalities and take fractional powers.\n\nIn summary, the positivity or non-negativity of the sum arises from:\n\n1. The boundedness of the loss function \\(\\ell\\) in \\([0,1]\\).\n2. The positivity of the weights \\(\\omega_t\\) and sample sizes \\(n_t\\).\n3. The structure of the terms as differences of losses and empirical risks, which are controlled and bounded.\n\nTherefore, it is valid to take the power \\(1/p\\) of the sum because the sum is guaranteed to be non-negative under these conditions."}
{"question_id": "3684e20e39c14e7d52c12084515e178aa0789584", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "Associations that are solvable by AI should be kept in the framework to reduce dependence on a specific model and to maintain the naturalness and quality of the data. The selection of final test instances is based solely on the \"solvable-by-humans\" score, without excluding instances that AI models can solve. This approach prevents the dataset from becoming biased toward only associations that fool the AI, ensuring that the data remains representative and challenging in a balanced manner. It also supports the dynamic nature of the benchmark as the field advances and models improve."}
{"question_id": "04e9ce2f786d7603574645eafe3bfe6e1a603190", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The choice of mirror descent in the proposed method is not strictly necessary. The update rule (2.8) uses mirror descent as a canonical generalization of Euclidean gradient descent to gradient descent on the probability simplex, which is appropriate for updating the weights \u03c9 on the simplex. However, other optimization methods, such as projected gradient descent, can also be used in place of mirror descent. Thus, mirror descent is chosen for its canonical properties but is not the only possible optimization method for the weight updates."}
{"question_id": "da72c5aada0a4b421a7919d5ef2739ddb658ae5e", "paper_id": "nlpeer/ARR-22/12ea2851de6445bf343d642c6ccc8640bf7a6c28b5f20ba123d6d58f02790c7c7f8ab696e6a6b4e45bfad28bd4bc0419bdcf6358693404dd0c14d202b291503b", "generation": "The results in Table 2 were generated using an average and standard deviation calculated from 20 evaluations."}
{"question_id": "a7ad87d54def516b43292de78c758cf6107320f7", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "Yes, the proposed method can be implemented using projected gradient methods instead of mirror descent. The update rule (2.8) employs mirror descent as a canonical generalization of Euclidean gradient descent on the probability simplex, but other optimization methods, such as projected gradient descent, can also be used for updating the weights."}
{"question_id": "9baa6e51bac226403b63b8bef97ea58737e9f14c", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The increased difficulty of the 10 & 12 task compared to the 5 & 6 task arises primarily from the larger number of candidates, which increases the complexity and challenge for models but does not significantly affect human performance. Models experience a significant performance drop with the increased number of candidates, indicating that they struggle to handle the higher difficulty level, whereas humans remain robust and maintain similar accuracy across both tasks. Additionally, training improves model performance more noticeably in the 10 & 12 candidate cases, suggesting that the model can better exploit supervised data when the task is harder and the random chance of success is lower. This implies that the 10 & 12 task requires more sophisticated reasoning and discrimination among a larger set of distractors, which models find challenging but humans can manage effectively."}
{"question_id": "bc784ef2841eb98841522b97ab75da7a7106b99c", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The term \"fine-tuning timing for classical solver\" does not explicitly appear in the provided chunks. However, the context of \"fine-tuning\" in the experiments refers to a one-step parameter adaptation process applied to the Meta-EGN model during testing, where the model parameters are updated using the gradient of the loss on the given test instance before rounding the solution. This fine-tuning step is distinct from classical solvers like Gurobi9.5, which do not involve parameter adaptation but rather solve the optimization problem within a specified time limit.\n\nIn the tables, \"fine-tune\" or \"f-t\" denotes the application of this one-step fine-tuning procedure to the Meta-EGN model, improving solution quality or approximation rate. The timing values reported for classical solvers such as Gurobi9.5 correspond to the solver's runtime limits (e.g., \u2264 0.25s, \u2264 0.50s, \u2264 1.00s, \u2264 2.00s) set for solving the problem instances, not to any fine-tuning process.\n\nTherefore, \"fine-tuning timing for classical solver\" is not a defined concept in the given context. Instead, timing for classical solvers refers to the fixed computational time budget allocated for solving the optimization problem, whereas fine-tuning timing pertains to the additional parameter update step applied only to learned models like Meta-EGN during testing."}
{"question_id": "d3ce17368ff699857360c15972ad48cb235350b8", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "Setting the dimension of the word vectors and discourse vectors to 10 empirically results in the entire token distribution approximating the Zipfian distribution because this dimensionality balances the representation capacity of the vectors such that the conditional probability distribution over tokens, defined by \\( p(w|s) \\propto \\exp(\\vec{c}_s \\cdot \\vec{v}_w) \\), naturally exhibits a frequency-rank relationship similar to Zipf's law. In other words, a 10-dimensional embedding space allows the log-linear model to generate token cooccurrence patterns and unigram distributions that reflect the power-law decay characteristic of natural language word frequencies."}
{"question_id": "69d95e16f2f308754136f8e2245592ca5497ff5f", "paper_id": "openreview/ICLR-2023-conf/xYlJRpzZtsY", "generation": "In the ROSCOE training process, the model is trained on triplets consisting of reference reasoning steps, positive hypothesis reasoning steps, and hard-negative hypothesis reasoning steps. Specifically, the pairs used are:\n\n- Positive pairs: the context and reference reasoning steps (denoted as (s, r))\n- Hard-negative pairs: the context and perturbed reference steps (denoted as (s, h))\n\nThese pairs are used in a contrastive learning framework to minimize the cross-entropy objective with in-batch negatives, encouraging the model to embed positive pairs as similar and hard-negative pairs as dissimilar."}
{"question_id": "d07dca8f8e126c43dacdaf145ec4103ef25400f5", "paper_id": "nlpeer/PeerRead-ACL2017/561", "generation": "Not having access to pre-trained embeddings, specifically the pre-trained context embeddings from bidirectional language models (LM embeddings), would significantly reduce the performance of the sequence tagging method. The LM embeddings, learned from large unlabeled corpora, provide rich context-sensitive representations that capture both semantic and syntactic roles of words in context. These embeddings improve performance by supplying composition functions learned from much larger data than the labeled training set alone.\n\nExperiments show that removing the task-specific sequence RNN and relying solely on fixed LM embeddings results in a substantial drop in performance (F1 score of 88.17), well below the baseline model that uses task-specific RNNs trained on labeled data. This indicates that the RNNs trained on labeled data encode essential information not captured by LM embeddings alone.\n\nFurthermore, the addition of LM embeddings yields the most benefit when labeled training data is limited, improving F1 scores by over 3% when only 1% of the training data is used, compared to a smaller improvement with full training data. Without pre-trained LM embeddings, the model would lack this semi-supervised advantage and rely solely on limited labeled data, resulting in lower accuracy.\n\nIn summary, the absence of pre-trained embeddings would lead to a marked decrease in model performance due to the loss of rich, context-sensitive representations learned from large unlabeled corpora, and the inability to leverage semi-supervised learning benefits."}
{"question_id": "ebd7cf5f3adbc674bd5ce88a563f3fea990873a9", "paper_id": "openreview/NeurIPS-2022-dabt/UrAYT2QwOX8", "generation": "The rationale for training on the first six months of data and validating the models on the last two months is to leverage a temporal split that reflects common practice in the fraud domain. This approach is used because more recent data tends to be more faithful to the data\u2019s distribution when models are deployed in production. Thus, training on earlier months and testing on later months ensures that the model evaluation is realistic and aligned with the dynamic nature of fraud patterns over time."}
