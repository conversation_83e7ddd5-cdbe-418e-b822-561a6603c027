async function get_response(paper_id, question) {
  response = await fetch("https://notebooklm.google.com/_/LabsTailwindUi/data/google.internal.labs.tailwind.orchestration.v1.LabsTailwindOrchestrationService/GenerateFreeFormStreamed?bl=boq_labs-tailwind-frontend_20250701.09_p0&f.sid=-3334882304020344180&hl=en&_reqid=2715297&rt=c", {
    "headers": {
      "accept": "*/*",
      "accept-language": "en-US,en;q=0.9,vi;q=0.8",
      "content-type": "application/x-www-form-urlencoded;charset=UTF-8",
      "priority": "u=1, i",
      "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
      "sec-ch-ua-arch": "\"arm\"",
      "sec-ch-ua-bitness": "\"64\"",
      "sec-ch-ua-form-factors": "\"Desktop\"",
      "sec-ch-ua-full-version": "\"137.0.7151.104\"",
      "sec-ch-ua-full-version-list": "\"Google Chrome\";v=\"137.0.7151.104\", \"Chromium\";v=\"137.0.7151.104\", \"Not/A)Brand\";v=\"********\"",
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-model": "\"\"",
      "sec-ch-ua-platform": "\"macOS\"",
      "sec-ch-ua-platform-version": "\"15.5.0\"",
      "sec-ch-ua-wow64": "?0",
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "same-origin",
      "x-client-data": "CIa2yQEIpbbJAQipncoBCNv1ygEIkqHLAQiRo8sBCIagzQEI/aXOAQiS7c4BCKXyzgEIo/XOAQ==",
      "x-same-domain": "1"
    },
    "referrer": "https://notebooklm.google.com/",
    "referrerPolicy": "origin",
    "body": `f.req=%5Bnull%2C%22%5B%5B%5B%5B%5C%22${paper_id}%5C%22%5D%5D%5D%2C%5C%22${question}%5C%22%2Cnull%2C%5B2%5D%5D%22%5D&at=AJpMio3_ndRpBMezz6ogAZ22Df02%3A1751836496015&`,
    "method": "POST",
    "mode": "cors",
    "credentials": "include"
  }).then(x => x.text())
  return response
}

function saveToFile(data, filename) {
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

// [(question_id, paper_id, question_text)]
input = <INPUT/>
output = []

counter = 0
for (d of input) {
  question_id = d[0]
  paper_id = d[1]
  question_text = d[2]

  response = await get_response(paper_id, question_text)
  output.push([question_id, paper_id, question_text, response])

  await new Promise(resolve => setTimeout(resolve, Math.floor(Math.random() * 2000) + 1000));
  console.log(`Processed question ${question_id} for paper ${paper_id} (${counter + 1}/${input.length})`);
  counter++;
}

saveToFile(output, 'output.json');