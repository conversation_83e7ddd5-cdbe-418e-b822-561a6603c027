from pathlib import Path
import json

from core import logging
from data import data_store

logger = logging.get_logger()

PAPER_DIR = Path("experiment/data/papers")

SCRIPT_TEMPLATE_PATH = Path("experiment/template.js")
SCRIPT_INPUT_PLACEHOLDER = "<INPUT/>"
GENERATED_SCRIPT_PATH = Path("experiment/run_notebooklm.js")

def normalized_paper_id(paper_id: str) -> str:
  return paper_id.replace("/", "_")

def copy_paper_pdfs():
  paper_ids = data_store.get_free_from_available_paper_ids()
  for paper_id in paper_ids:
    pdf_path = data_store.get_pdf_path(paper_id)
    target_path = PAPER_DIR / f"{normalized_paper_id(paper_id)}.pdf"
    
    if not target_path.exists():
      logger.info(f"Copying PDF for {paper_id}")
      target_path.parent.mkdir(parents=True, exist_ok=True)
      target_path.write_bytes(pdf_path.read_bytes())
    else:
      logger.info(f"PDF for {paper_id} already exists, skipping copy")

def get_remote_paper_id_mapping(file_path) -> dict:
  mapping = {}
  data = json.load(open(file_path, 'r'))
  data = json.loads(data[0][2])
  data = data[0][0][1] # or [0][1]
  for d in data:
    remote_paper_id = d[0][0]
    paper_id_norm = d[1][:-4] # Remove the .pdf extension
    mapping[paper_id_norm] = remote_paper_id
  return mapping

if __name__ == "__main__":
  # Copy PDFs to the experiment directory for upload
  copy_paper_pdfs()
  logger.info("All PDFs copied")

  # Upload PDFs to notebooklm, then get the remote paper IDs
  remote_id_mapping = get_remote_paper_id_mapping("experiment/remote_papers_info_raw.json")
  
  # Get the QA data
  qa_data = data_store.get_qa_data()
  qa_data = qa_data[qa_data["answer_free_form"].notnull()]
  
  # Create input for the script
  input = []
  for qa in qa_data.to_dict(orient="records"):
    question_id = qa["question_id"]
    paper_id = qa["paper_id"]
    remote_paper_id = remote_id_mapping.get(normalized_paper_id(paper_id), None)
    question = qa["question"]
    assert(remote_paper_id is not None), f"Remote paper ID not found for {paper_id}"
    input.append((question_id, remote_paper_id, question))
  
  # Generate script
  template = SCRIPT_TEMPLATE_PATH.read_text()
  template = template.replace(SCRIPT_INPUT_PLACEHOLDER, json.dumps(input, indent=2))
  GENERATED_SCRIPT_PATH.write_text(template)
  logger.info(f"Generated script saved to {GENERATED_SCRIPT_PATH}")
