async function get_response(paper_id, question) {
  response = await fetch("https://notebooklm.google.com/_/LabsTailwindUi/data/google.internal.labs.tailwind.orchestration.v1.LabsTailwindOrchestrationService/GenerateFreeFormStreamed?bl=boq_labs-tailwind-frontend_20250701.09_p0&f.sid=-3334882304020344180&hl=en&_reqid=2715297&rt=c", {
    "headers": {
      "accept": "*/*",
      "accept-language": "en-US,en;q=0.9,vi;q=0.8",
      "content-type": "application/x-www-form-urlencoded;charset=UTF-8",
      "priority": "u=1, i",
      "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
      "sec-ch-ua-arch": "\"arm\"",
      "sec-ch-ua-bitness": "\"64\"",
      "sec-ch-ua-form-factors": "\"Desktop\"",
      "sec-ch-ua-full-version": "\"137.0.7151.104\"",
      "sec-ch-ua-full-version-list": "\"Google Chrome\";v=\"137.0.7151.104\", \"Chromium\";v=\"137.0.7151.104\", \"Not/A)Brand\";v=\"********\"",
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-model": "\"\"",
      "sec-ch-ua-platform": "\"macOS\"",
      "sec-ch-ua-platform-version": "\"15.5.0\"",
      "sec-ch-ua-wow64": "?0",
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "same-origin",
      "x-client-data": "CIa2yQEIpbbJAQipncoBCNv1ygEIkqHLAQiRo8sBCIagzQEI/aXOAQiS7c4BCKXyzgEIo/XOAQ==",
      "x-same-domain": "1"
    },
    "referrer": "https://notebooklm.google.com/",
    "referrerPolicy": "origin",
    "body": `f.req=%5Bnull%2C%22%5B%5B%5B%5B%5C%22${paper_id}%5C%22%5D%5D%5D%2C%5C%22${question}%5C%22%2Cnull%2C%5B2%5D%5D%22%5D&at=AJpMio3_ndRpBMezz6ogAZ22Df02%3A1751836496015&`,
    "method": "POST",
    "mode": "cors",
    "credentials": "include"
  }).then(x => x.text())
  return response
}

function saveToFile(data, filename) {
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

// [(question_id, paper_id, question_text)]
input = [
  [
    "8f2072e6213f44471d3294973c9cfdd790bc7259",
    "b0045e84-960f-484f-83ce-c947c594ad26",
    "How does the proposed method compare to Newton and standard quasi-Newton methods such as BFGS, L-BFGS, Chord, and Levenberg\u2013Marquardt?"
  ],
  [
    "7ed6482c76ae428708b4675495718652adfed68d",
    "c8f4834a-bed5-447f-9230-a48f86af3946",
    "Are the annotators of the test sets native English speakers?"
  ],
  [
    "a3bcdc5b71130202e27b2a0b4a8485392efedec5",
    "79bd221c-ef5e-467a-a4f5-9f92a641393c",
    "Does estimating the \\phi_a function pose any challenges when the treatments are discrete?"
  ],
  [
    "6a2461eebef2649b0832b8d91cb8ed490edbf2d3",
    "a5d9987b-93b7-4d0a-91ea-58a6cf05558f",
    "Why was Ordinary Least Squares (OLS) used for the regression-based systems instead of Support Vector Regression (SVR)?"
  ],
  [
    "cdb59f3c15dd8d951c169a9f2fae03e000215bae",
    "a5d9987b-93b7-4d0a-91ea-58a6cf05558f",
    "Does the cross-linguistic analysis include French and Spanish?"
  ],
  [
    "465b94530bc99598bbefb742da70cf99236b86d9",
    "a5d9987b-93b7-4d0a-91ea-58a6cf05558f",
    "Why were classification-based models not used for the zero shot experiments in Tables 6 and 7?"
  ],
  [
    "6ceebb857d2674bd38452e8bc30a3d280f2ce647",
    "a5d9987b-93b7-4d0a-91ea-58a6cf05558f",
    "What is the reason for the lower performance on Vikidia-FR compared to Newsela-ES?"
  ],
  [
    "a36f298f8941bf93ad6bdc2ef8db6471e6ca4156",
    "f5245a6c-acef-4e4f-bda3-82f59986be8f",
    "Would the use of the UCCA transition scheme improve the performance of existing algorithms for the meaning representations AMR and SDP?"
  ],
  [
    "e76615760f7625a5b984cb961f5d948ab2407178",
    "f5245a6c-acef-4e4f-bda3-82f59986be8f",
    "Do you mean that a node in the UCCA scheme can be either a terminal or a non-terminal, but not both?"
  ],
  [
    "0081780bb3b2f8ca303dd426ce4ddfd89c13e80d",
    "314d0cb7-412e-4965-9cec-c48232076054",
    "What is the purpose of the average duration reported in Table 1?"
  ],
  [
    "058da91fd7bfe9ecd3163d9d8877e5116cf5cdf6",
    "bb26aa87-4da6-4296-858b-5ff0cb59049f",
    "What is the evidence that the generative model is successful in synthesizing new molecules?"
  ],
  [
    "3f958288ac95aea397ff6c9a6854d14d691778a2",
    "58f1a858-d0f2-440b-9b32-b433f52897ec",
    "Can the proposed algorithm be used to recover real neural networks?"
  ],
  [
    "d256c384aa446ef6ba7d69269df08e3dbbdb2db9",
    "d9d2f6e1-832e-431b-bff3-1a901f30f62e",
    "How difficult is it to construct the phylogenetic tree required as input for the algorithm?"
  ],
  [
    "5f4c9dea82aa176c2e42cd2c59ff3da0fce4a367",
    "d9d2f6e1-832e-431b-bff3-1a901f30f62e",
    "Can the pairwise distance of the latent variables be compared to the distance when fitting a proper phylogenetic tree?"
  ],
  [
    "cf26cc0cd1000ad63bfba19b5159a20efba34b18",
    "d9d2f6e1-832e-431b-bff3-1a901f30f62e",
    "Can the parameters of the BLOSUM matrix be estimated by the model instead of being pre-computed?"
  ],
  [
    "7088d27d671a415164b81660a60173ea7602d968",
    "d9d2f6e1-832e-431b-bff3-1a901f30f62e",
    "Why is a GRU decoder used for sequences that are all the same length?"
  ],
  [
    "815b242c673ecb7aea2ccec4f6c83ab1191a9124",
    "d9d2f6e1-832e-431b-bff3-1a901f30f62e",
    "Why can't a Multi-Layer Perceptron (MLP) be used to decode the aligned sequences instead of a Gated Recurrent Unit (GRU) decoder?"
  ],
  [
    "b8c9effb94fbab2db127d7262041fecdb031c1dd",
    "4ebb6889-e98f-485d-a613-bfaa7c2abe32",
    "Does the same preprocessing step of converting numerical features to categorical ones for language modelling occur for all methods used in the paper?"
  ],
  [
    "e2ee4a1059cbb3c736b7b00cd902bbbd428423e8",
    "4ebb6889-e98f-485d-a613-bfaa7c2abe32",
    "Have the authors considered formulating the Darpa dataset as a dynamic graph for network intrusion detection?"
  ],
  [
    "264d5ee47c97b02b5a597533254b78c97cb41f8b",
    "4ebb6889-e98f-485d-a613-bfaa7c2abe32",
    "Are there any limitations in the Darpa dataset which prevent it from being a graph dataset?"
  ],
  [
    "de0f53c58cedd98fd958715bb1a2f5a3d24e829d",
    "4ebb6889-e98f-485d-a613-bfaa7c2abe32",
    "Why were the source and destination IP addresses and port numbers discarded?"
  ],
  [
    "1d2837da9c9a557b18715b0482bea0532e3ac176",
    "4ebb6889-e98f-485d-a613-bfaa7c2abe32",
    "Why is the performance of the Transformer model proposed in this paper (BERT for Anomalies) lower in the IDD split than in the Near split?"
  ],
  [
    "ee956c6a1b9b9808fc77d7d1f8f82237123f5000",
    "4cc9dc57-037d-4222-9d12-cc5755e4270b",
    "What is meant by \"edit boundaries might be unusual\"?"
  ],
  [
    "f3d892c65c8fc7edb3624cad07d6ef39115402eb",
    "4cc9dc57-037d-4222-9d12-cc5755e4270b",
    "Why was a new set of error categories designed instead of using the tagset presented in Nicholls (2003) or the CoNLL-2014 tagset?"
  ],
  [
    "962be5303d07e7707c9750241730986cfeb7d754",
    "4cc9dc57-037d-4222-9d12-cc5755e4270b",
    "What data was used to perform the significance test?"
  ],
  [
    "49887aceab5099bc8a45f1f01aa437f760c289a5",
    "676bb0b5-6ae7-4e29-af86-c3cd083acd65",
    "Is the 90% IG achieved by the mixture model on the real data example indicative of its performance on other datasets?"
  ],
  [
    "4d8419e9aeb2f3d606bca8774d3618d08b70c41f",
    "676bb0b5-6ae7-4e29-af86-c3cd083acd65",
    "What benefits does improving the upper bound for the Information Gain evaluation metric provide in practice?"
  ],
  [
    "7869279cfc2dc07fcd82704dc07789afa6de5c82",
    "676bb0b5-6ae7-4e29-af86-c3cd083acd65",
    "Does regularisation improve the performance of current pe oracles, or is the issue with the pe oracle itself?"
  ],
  [
    "96a32bff80b5928198a99a4fc2c2e24cd1a982dd",
    "de9f3733-37e8-4b16-b92b-3d104b770cdb",
    "What is the magnitude of the noise in the data?"
  ],
  [
    "92394e14628bdc9941b0581b43b20ab42dbdd3fd",
    "de9f3733-37e8-4b16-b92b-3d104b770cdb",
    "Does the expected label always match the most probable label given the noisy data?"
  ],
  [
    "3b4dcf624027feff21ac63b6e451169e1ca6bf2a",
    "de9f3733-37e8-4b16-b92b-3d104b770cdb",
    "What is the proposed criterion for quantifying generalization performance?"
  ],
  [
    "834016a31e50565175511dcdf3d75a1be44b532c",
    "de9f3733-37e8-4b16-b92b-3d104b770cdb",
    "What is the definition of difficulty used in the paper to analyze the learning path of the network's predicted distribution?"
  ],
  [
    "a48eb6eab4e9448324227205ae04b8d47a5b181e",
    "de9f3733-37e8-4b16-b92b-3d104b770cdb",
    "What other criteria are being considered to define generalization, in addition to accuracy and expected calibration error?"
  ],
  [
    "67b6a78d6cea6ff4cd6a6cdd262aaf4e4bfea275",
    "de9f3733-37e8-4b16-b92b-3d104b770cdb",
    "What is the relationship between the p*() label and the one-hot encoding for the hard sample in Figure 3?"
  ],
  [
    "fffbbdd88b4cdc0b98de790921df08f7be1eed7d",
    "de9f3733-37e8-4b16-b92b-3d104b770cdb",
    "What is the prevalence of this behavior across the dataset?"
  ],
  [
    "924a054e5ec561c4d58306dfd312782d7b4f70ca",
    "de9f3733-37e8-4b16-b92b-3d104b770cdb",
    "What labels are used to supervise students?"
  ],
  [
    "01163085d0c4776005e14d8621ce2bbdd3cc1c13",
    "de9f3733-37e8-4b16-b92b-3d104b770cdb",
    "How does the performance of the filter-kd model compare to models trained using label smoothing and knowledge distillation with the optimum temperature?"
  ],
  [
    "3493acb3c91a1415959829136fe3e250966fc8f0",
    "0938171d-b781-4c35-91d7-ce157fb0c7e4",
    "What differences exist between the prompt design of BioNLP tasks and general NLP tasks?"
  ],
  [
    "0c4afb8ced370f2f67477fe4617ff846513cfb6d",
    "0938171d-b781-4c35-91d7-ce157fb0c7e4",
    "What values are represented in Table 2?"
  ],
  [
    "06dc05622c2bee5d4a078d2ef7542457179ac90b",
    "7c8da9b8-2af1-4cdb-afa3-901be13d9c13",
    "Why does the baseline have significantly better performance on ACE 2004 and ACE 2005 compared to Yu et al. (2020), but similar performance on OntoNotes 5 and CoNLL 2003?"
  ],
  [
    "e7e5b24e35bd512176a8587170677228842e2e24",
    "78795096-d71a-43fa-a8b1-2366d59f697a",
    "Can we assume that coastal seaweeds, which have a very low surface-to-volume ratio, would be competitive in iron uptake against the mostly small and specialized open ocean phytoplankton that have a high surface-to-volume ratio, especially in iron-limited areas?"
  ],
  [
    "27a8c35fcd38d0a141fb5248ad93038196553dfb",
    "78795096-d71a-43fa-a8b1-2366d59f697a",
    "Do the authors assume that iron is sourced from the platform when considering the feasibility of coastal seaweeds, which have a very low surface-to-volume ratio, competing for iron against the typically small and specialized open ocean phytoplankton that have a high surface-to-volume ratio?"
  ],
  [
    "fcf91acb3ff79184eb4af002b876fec65732620c",
    "78795096-d71a-43fa-a8b1-2366d59f697a",
    "How was partial pressure of carbon dioxide (pCO2) calculated for the measurement of dissolved inorganic carbon (MOS_DIC)?"
  ],
  [
    "c322ea8d24002e229a330243b37f745a157a764e",
    "78795096-d71a-43fa-a8b1-2366d59f697a",
    "Where can the reference numbers for the delta to the DIC pool, as mentioned in Table 1, be verified?"
  ],
  [
    "cd020940c9b12a598dae5fc4fde1d63c2d88d88d",
    "78795096-d71a-43fa-a8b1-2366d59f697a",
    "Have you considered the potential for longer permanence of CO2 storage if sediment carbonate dissolution occurs due to high respiratory dissolved inorganic carbon at the bottom?"
  ],
  [
    "9c04f85fb5baad69d0ae21c1c2c07abc0422bd55",
    "610ab376-7725-4a60-a90d-e5241b3e5280",
    "What concrete steps can be taken to ensure a more diverse cross-cultural representation when building an NLP model?"
  ],
  [
    "36e40e97993a08a2c5e50bfc69c991334be39e6e",
    "a8e81350-d0ff-49be-a2d6-d898357606ca",
    "Why are the noise heads discarded at test time?"
  ],
  [
    "c67443bf273772ac2d4297564f839c0a0229e6eb",
    "a8e81350-d0ff-49be-a2d6-d898357606ca",
    "What is the purpose of the greyed out portion of Figure 3?"
  ],
  [
    "c83f53bbc1390bf3f6a15aa58e1c559cf391a507",
    "a8e81350-d0ff-49be-a2d6-d898357606ca",
    "What is the task described in Section 4.1 of the paper?"
  ],
  [
    "31314c6ad7630579c350af928493caac9c563dbb",
    "3b388051-fbe9-45d5-bbe5-15e4f558103b",
    "What are the minimum and maximum threshold (t_min, t_max) values?"
  ],
  [
    "c5b7931f3e58dd10d67e388fcd5680c37e267022",
    "3b388051-fbe9-45d5-bbe5-15e4f558103b",
    "What is the reasoning behind the selection of the threshold values?"
  ],
  [
    "0258b0f39ec3f7316f9d299a25a7cd36274e9631",
    "75d0b400-870e-40ab-8780-78b52892ac5b",
    "Did you rank the concepts according to the recommended method of using the TCAV_Q score when assessing the relative importance of multiple concepts?"
  ],
  [
    "9824d5fa73a188c99c7d977a3dda3d2b24856f9d",
    "75d0b400-870e-40ab-8780-78b52892ac5b",
    "Have the results of the experiments on the third dataset about mortality risk been reviewed by a medical expert?"
  ],
  [
    "b9d3ed7981d9f1e47fea48aaf2b4037fe7b25658",
    "75d0b400-870e-40ab-8780-78b52892ac5b",
    "What is the purpose of finetuning the same network for g?"
  ],
  [
    "0c64726cf3b593196fd8f350d0f2c2d4aba98d1e",
    "e80d3352-5026-48ba-857f-e35819584791",
    "What is the label mapping of Electra's binary classification head (RTE)?"
  ],
  [
    "44279226e8c3ea5497ac4c43754e3e890e0183fb",
    "177f59d1-e8dd-405a-a723-7f520942aeb8",
    "What is the best way to compare the performance of different models/architectures (x, x', x'', etc.)?"
  ],
  [
    "ba25580bbc4ec4f20348cefaf968e1cdea408642",
    "177f59d1-e8dd-405a-a723-7f520942aeb8",
    "What is the difference between the 8 positive and 8 negative examples used in the domain shift experiment in Section 5.3?"
  ],
  [
    "9ee8e92c0faa3a3d17cff68d75d28be3dca8e8c4",
    "b2debaec-7788-42a7-9812-969c77f82186",
    "What is the impact of adjusting $\\delta$ on the results of Table 1?"
  ],
  [
    "bf41e9f2b170cb8e1801812167b945e8f56aa8cb",
    "b2debaec-7788-42a7-9812-969c77f82186",
    "What is the significance of the results in Table 1?"
  ],
  [
    "a81ef48de406906c5a847928da2bc47079136f55",
    "5fc8cd7a-19c0-4a1e-bfde-b042b7df7a43",
    "What is the number of distinct phrases used for phrase addition in the adversarial attack generation?"
  ],
  [
    "5678b6bf40f5958402473fd66a08dc836eaa98a7",
    "5fc8cd7a-19c0-4a1e-bfde-b042b7df7a43",
    "Is the coreference resolution pipeline depicted in Table 1 universally accepted in the field of coreference resolution?"
  ],
  [
    "587b8f363bb9be4e82b38b70f74608f844559b6f",
    "5fc8cd7a-19c0-4a1e-bfde-b042b7df7a43",
    "What is the data augmentation strategy?"
  ],
  [
    "1a42a5af41f66bb6428c643d96fd05eba81ce635",
    "ea860013-da60-4c8e-82e9-02948865e7b9",
    "Do the proposed feature and experiments account for named entities that are not listed on Wikipedia?"
  ],
  [
    "1aa55ba3f2f47947ad4df928f35cfc4c7d7035ec",
    "57427c3c-058d-409f-a65e-c3aab3b67934",
    "What is the focus of this paper: question answering, intent classification, entity classification, or translation?"
  ],
  [
    "8fc13b01107e614b030a2c7dbc65aa19d0363778",
    "57427c3c-058d-409f-a65e-c3aab3b67934",
    "Did the authors run their experiments multiple times with different random initializations to confirm the results?"
  ],
  [
    "96525f380b4694ec387b707fa87e78c972a12b4c",
    "f11487f4-d498-4379-aedf-c3abdd737f16",
    "What is the effectiveness of training on the Dartset dataset?"
  ],
  [
    "b879c4d1344942fd8fab9fbe8fc495f4ae67c0b0",
    "153de1ca-7233-406f-9f95-13604a59377c",
    "Is $\\omega$ defined to be an element of $\\mathbb{R}$ in Definition 1.1?"
  ],
  [
    "10dbff5874380289cdab832a0eecab1cc3c34117",
    "153de1ca-7233-406f-9f95-13604a59377c",
    "Is the proposed algorithm related to Stochastic Variance Reduced Gradient (SVRG) or any other algorithms in that family?"
  ],
  [
    "b97a690598219404cca037101ff05ec348b5e525",
    "c33623df-b41a-48bb-b326-bb65379bbeb1",
    "Is there a plan to open-source the proprietary medical knowledge base and the telemedicine software?"
  ],
  [
    "92c772c75354552e709f16f3e3b15a31e395f1cf",
    "c33623df-b41a-48bb-b326-bb65379bbeb1",
    "What is the rationale for changing the data distribution if the KB was compiled by medical papers?"
  ],
  [
    "70418ac3cb9f40b039a74031b89324e2b891ccf5",
    "491bdbca-400c-4b14-98e2-6ce8a98d49ca",
    "What method is used to initialize the word embeddings?"
  ],
  [
    "89fb9729921ad950b90987550b32f9ede60c8a8c",
    "ea3a4092-7f55-43f3-87a1-469b5604a77f",
    "Did something go wrong during the training of the Deep Gaussian Process (DGP) for the interpolation experiment on the Mauna Loa dataset?"
  ],
  [
    "d49df57b22ec381fed263033d6a02678f16a18c1",
    "ea3a4092-7f55-43f3-87a1-469b5604a77f",
    "How does the width of the model affect its performance, given that the prior functions are the same for all units?"
  ],
  [
    "e0385760ba4f37eeba3376cf4dd4ed0727cedb81",
    "ea3a4092-7f55-43f3-87a1-469b5604a77f",
    "What is the rationale for choosing the prior for the model?"
  ],
  [
    "81b292ac9640d75024251269649ea71272b1710c",
    "ea3a4092-7f55-43f3-87a1-469b5604a77f",
    "What is the tradeoff between runtime and performance when both DGP and DVIp use the same GP priors?"
  ],
  [
    "428b48f2d5cfea8890c3fe80599575b25565a976",
    "ea3a4092-7f55-43f3-87a1-469b5604a77f",
    "How does using only one mixture component for training the model compare to using 100 components during testing?"
  ],
  [
    "65df6e41f1c8c77eec8b264ef0a3dcd104abb9dc",
    "c500f909-8395-4e58-8e11-20c46c4dee5d",
    "What is the reason for the higher average out-degree of normal users compared to fraudsters?"
  ],
  [
    "60c2bf23190bf1120b8652501ff951bae6f3e046",
    "c500f909-8395-4e58-8e11-20c46c4dee5d",
    "What is the rationale for comparing 2-hop homophily (xbx/xtx) with 1-hop homophily (xx) in Figure 3(d)?"
  ],
  [
    "1cd2ab406e0d21ae6597092cde6d7a2fca652f82",
    "c500f909-8395-4e58-8e11-20c46c4dee5d",
    "Do dynamic nodes need to be included in the data for the dynamic graph approach to be effective?"
  ],
  [
    "9073f9407118eaf0b34170000a57846d672b4a5d",
    "bd211b04-9e45-4202-9349-d0a74d388264",
    "What criteria were used to select the four CQA models?"
  ],
  [
    "5227809e5dbd6a7ef588b7a84fc243e6cd0eed8d",
    "bd211b04-9e45-4202-9349-d0a74d388264",
    "What was the compensation rate for the annotators hired for the Mechanical Turk crowdsourcing work?"
  ],
  [
    "c3790b0e0925f080c8d91160ae348f35ac367993",
    "bd211b04-9e45-4202-9349-d0a74d388264",
    "Why was word overlap used instead of an exact match when comparing f1(s\u2217_{j,1}, s_{j,1})?"
  ],
  [
    "721388955b97db097e963c4b221fe9af9e2da4ae",
    "bd211b04-9e45-4202-9349-d0a74d388264",
    "What does it mean when the absolute accuracy numbers are higher than the F1 scores?"
  ],
  [
    "672a51e77d6a34b48ad5a93aa1a37139d71e3c72",
    "b999072d-5dd2-4844-93ec-bedf25cf3b4b",
    "What is the rationale for only analyzing the self-attention functions in the last layer of the BERT model?"
  ],
  [
    "7b375e548c69cd6c0b0d75953da0021adb9e2a7e",
    "cc3427ac-1b38-4c36-8131-7a43fc70ae9e",
    "What is the difference in computation time between REDQ and SAC per update?"
  ],
  [
    "abf4bcae7809ff5b01e8cf7fdb201caa7b8421ac",
    "cc3427ac-1b38-4c36-8131-7a43fc70ae9e",
    "What computation time are you referring to when you mention that REDQ runs more than 2 times slower than SAC?"
  ],
  [
    "4bb993f44c76628b67f41da43c78aa82b50cbc19",
    "cc3427ac-1b38-4c36-8131-7a43fc70ae9e",
    "What is the full name of the algorithm DUVN?"
  ],
  [
    "c3b651600b60b22f2a4c805aeb87745aff3c0c84",
    "cc3427ac-1b38-4c36-8131-7a43fc70ae9e",
    "What is the full name of the proposed method \"redq\" mentioned in the paper?"
  ],
  [
    "8a7925cf9978728b68e7bc89204643a94468964a",
    "cc3427ac-1b38-4c36-8131-7a43fc70ae9e",
    "What is the benefit of using Layer Normalization in combination with Dropout?"
  ],
  [
    "2074c1cd08c7d4b134ac01c5ee57f13765a4cc47",
    "cc3427ac-1b38-4c36-8131-7a43fc70ae9e",
    "What advantages does layer normalization have over other regularization schemes for improving the performance of dropout in deep reinforcement learning?"
  ],
  [
    "f567015ed8777554298ac8d5b511b255c317d3da",
    "cc3427ac-1b38-4c36-8131-7a43fc70ae9e",
    "What is the reason why dropout on its own does not work for the proposed method?"
  ],
  [
    "9dfb86a40b292918a304254d03b72b6fee37e740",
    "93966bb9-cad0-4c30-abf7-4996b7e247d4",
    "What is the target KB size used for MedMentions in the experiments?"
  ],
  [
    "3356f072c76c12c8ebc215b5bd495d5ccbea0126",
    "93966bb9-cad0-4c30-abf7-4996b7e247d4",
    "Do the training and inference batches contain mentions from the same document to leverage coreferent mentions as much as possible?"
  ],
  [
    "b509625d37c9da4c7585e00370dc166be8e9be88",
    "93966bb9-cad0-4c30-abf7-4996b7e247d4",
    "Does the model need to compute \\psi(m_i, e) for all entities in the target knowledge base during inference?"
  ],
  [
    "4a12daa058e224f39629de8997d5de7c8b0c2d3c",
    "93966bb9-cad0-4c30-abf7-4996b7e247d4",
    "Why not use the complete graph for training instead of pruning it?"
  ],
  [
    "8d69a05246c31778897996bc35b60061f15554f3",
    "a3248a19-206a-4db6-9825-f10db792114e",
    "Are the results presented in the paper task- and dataset-specific, or can they be generalized to other tasks and datasets?"
  ],
  [
    "720c06898bbd974bce657e8eefac71ea8641b762",
    "5a3d5455-97dd-438e-af41-0fb3d18369bb",
    "How are position embeddings generated in a way similar to word embeddings?"
  ],
  [
    "52e2f447dc9bcf3dc208f99a89d4b59da4004923",
    "5a3d5455-97dd-438e-af41-0fb3d18369bb",
    "Are position embeddings randomly initialized?"
  ],
  [
    "45a145511dd96e98d18e5ac09f454b95ceee5a38",
    "5a3d5455-97dd-438e-af41-0fb3d18369bb",
    "How are left and right neighbors used to create the representation?"
  ],
  [
    "8b0ad0aeb866b9064a6bd71b1559fe85d81a612b",
    "5a3d5455-97dd-438e-af41-0fb3d18369bb",
    "What is the purpose of the event-mention representation v_e?"
  ],
  [
    "c8e25c77b2ec42c4f94bc044959aa372dd3f9638",
    "0f277030-e36d-4982-b8d3-53d91cfca790",
    "Is the design of the proposed method arbitrary for all layers of a given VIT model, or are some layers fixed?"
  ],
  [
    "3f32444ad6371e1401f9047615faeed1a6572e73",
    "0f277030-e36d-4982-b8d3-53d91cfca790",
    "What is the difference between the results in Table 1 and Table 2(a)?"
  ],
  [
    "d753561800b2ad04d3d8262519328d014142d717",
    "0f277030-e36d-4982-b8d3-53d91cfca790",
    "What is the rationale for choosing to remove tokens at the 4th, 7th, and 10th layers when processing other VIT models?"
  ],
  [
    "5ab449ca87a0bb86111a5e4e594ac959ec7d0e86",
    "716edd6c-cd4d-4e27-bba7-2d2bc048d436",
    "Why were post-editing rates chosen over prediction (h)ter for intrinsic uncertainty evaluation?"
  ],
  [
    "65ca807b7bfc58200ae0e5c46fcec1e31096cbf5",
    "ef21cd8b-9d47-4b9c-acdc-68e646a6e1d6",
    "Is the boost in performance of the parace model seen in the selected datasets expected to be seen in other datasets as well?"
  ],
  [
    "dfa7d8d8808a8928555e5e665068db90d3261334",
    "ef21cd8b-9d47-4b9c-acdc-68e646a6e1d6",
    "Is it valid to assume that finite evaluations of one arm are sufficient to obtain an accurate reward?"
  ],
  [
    "496d254bdc722d815fb98ad3903cdc34df700fce",
    "0004ad35-4736-4bde-9e0b-39a3b3d3b469",
    "Why does $t=\\frac{\\delta_f}{\\epsilon^{1.75}}\\log d$ not match the complexity in Theorem 2?"
  ],
  [
    "b248a530072224a71459f4ce7aa708f9990067c2",
    "0004ad35-4736-4bde-9e0b-39a3b3d3b469",
    "What value of the parameter $\\mu$ is used in the experiments?"
  ],
  [
    "986801b0e009ad637f5bc4d62c8af27a2580f7b9",
    "e7ac36a2-7d25-405a-94b9-b60480c8f9d2",
    "To what extent is the degree of formality and the use of persian/sanskrit forms maintained in Hindi when transferring formality?"
  ],
  [
    "fced44b64830046f3b8766306510296277091b7a",
    "e7ac36a2-7d25-405a-94b9-b60480c8f9d2",
    "What does the degree of formality and the use of persian/sanskrit forms look like for the other languages studied?"
  ],
  [
    "dc3fd256c5702edb18e7a21a01836945f7bc0b17",
    "617c6397-0c9d-4504-bde9-02e3b234b8a8",
    "How do the limitations on aggregation, combination, and readout components, as detailed in the paper, affect the applicability of the results to other GNN models?"
  ],
  [
    "1b3c40fd196db55e9ffea18c2b7d9ffe988c5ad2",
    "f3e5fb5c-efe8-4087-8cba-8239d29e72e6",
    "What is the role of the kernel k_{split} in the algorithm?"
  ],
  [
    "888ba5daeae0d5b3d5120c824c8f61abd5b77ee3",
    "f3e5fb5c-efe8-4087-8cba-8239d29e72e6",
    "What is the explanation for the difference in error between the KT+ and ST (Hitch IMQ K) methods when comparing coreset size?"
  ],
  [
    "2e474adb8f0c5bd3285e43db4bfb774e7cd5b7a5",
    "6013991a-64b4-483b-9720-967e0f5a7346",
    "Would the proposed knowledge prompting method still be effective when using a smaller language model as the knowledge source?"
  ],
  [
    "5af1f02badf7c044e04f7544f4881486216b9f42",
    "6095169e-ba65-48d0-8a6c-4341e22e4a80",
    "Why is a new metric, concept purity, introduced instead of using the same set of metrics provided in Yuan et al. (2020)?"
  ],
  [
    "a74c71ff53a5ff84cacb938350996a66ceb0ae12",
    "751115d1-4284-4ace-b3a3-dc23da96328b",
    "What is the training process for the proposal network in phase 1?"
  ],
  [
    "4191cd3e5bd482f3d097b689c9857bf3d727f98b",
    "751115d1-4284-4ace-b3a3-dc23da96328b",
    "How are the depth and normal maps combined for training the proposal network in phase 1?"
  ],
  [
    "5739894b5714e42337f53319a265bb28e2f6e18d",
    "751115d1-4284-4ace-b3a3-dc23da96328b",
    "Would using RGB images in addition to depth and normal images in the first step lead to worse performance?"
  ],
  [
    "a4439f559ec40c32bb7edf1ee7fa3a854ed2b883",
    "751115d1-4284-4ace-b3a3-dc23da96328b",
    "Does combining RGB, depth, and normals improve the performance of the model for smaller classes?"
  ],
  [
    "87b3ef59cb4832acb7306d8368503784307b1adc",
    "751115d1-4284-4ace-b3a3-dc23da96328b",
    "What are the advantages of proposal-based object detectors compared to DETR and other similar object detectors?"
  ],
  [
    "888f26be8b81ccb011c79b043940eec47d9b414e",
    "751115d1-4284-4ace-b3a3-dc23da96328b",
    "How is the same network used for both depth and normal?"
  ],
  [
    "c12d6fb12967631fd985c4b437ee77cd1d2a8e2e",
    "751115d1-4284-4ace-b3a3-dc23da96328b",
    "Do you use any filtering when combining multiple modalities for your model?"
  ],
  [
    "68cdad6fbee4b667c3a6b10e5761a4ccf660e32b",
    "8a1d8ba0-ff5a-47f6-b909-615d2f2e48fa",
    "Do you have data that shows how the location of a country within its time zone affects the results?"
  ],
  [
    "ecbcef296be692e081bf8e60e3d63dcbf2090dbf",
    "8a1d8ba0-ff5a-47f6-b909-615d2f2e48fa",
    "Was a language expert consulted to verify the accuracy of the translations of English time expressions into other languages?"
  ],
  [
    "e7ac4283196f09643f681e08d6093e9313983cf1",
    "8a1d8ba0-ff5a-47f6-b909-615d2f2e48fa",
    "Has the translation of the template \u201cthe <morning> starts at <9.00>\u201d been adapted to account for languages with grammatical gender, such as Italian?"
  ],
  [
    "f21290e4ef8f0ead710c484502ad8e103217da11",
    "d3921a93-4c52-4585-80e7-540f6a16f9d9",
    "Have the authors tried training other models, such as versions of VGG and DenseNet, on ImageNet?"
  ],
  [
    "cf66689ffef1b230b7bab23901a8bf528a8e97f8",
    "457b0a51-573d-4fed-9038-d45c6511681a",
    "How does the paper incorporate section titles into the BOS representation?"
  ],
  [
    "691280cb66aae7b9fee2d8ac0937e5f970437b43",
    "457b0a51-573d-4fed-9038-d45c6511681a",
    "Do you have any human evaluation results for the text generation task?"
  ],
  [
    "8e2cb1c95dffd133cc91ab3123074a0853c829fb",
    "457b0a51-573d-4fed-9038-d45c6511681a",
    "What motivated the authors to experiment with their model on CNN/DM?"
  ],
  [
    "a7d741be648d514c67c1a0468a78782b19c6d11c",
    "e5b684b5-3ef2-45cc-b46c-011879fbefe5",
    "Does label smoothing always improve performance, or are there cases where it can degrade performance?"
  ],
  [
    "953feae01ae0b8d2066fd035c079f0a5dd581aaf",
    "e5b684b5-3ef2-45cc-b46c-011879fbefe5",
    "Does label smoothing always improve the performance of the hyperparameter-fine tuning procedure?"
  ],
  [
    "e111e75817d67b6fbeec06d5ba117b3419bf2f0f",
    "e5b684b5-3ef2-45cc-b46c-011879fbefe5",
    "What is the initial value being discussed in Section 3.2?"
  ],
  [
    "08c2ff08d58f88bfead47fc3783d34333d02f023",
    "e5b684b5-3ef2-45cc-b46c-011879fbefe5",
    "What is the rationale behind choosing a label-smoothing coefficient of 0.9?"
  ],
  [
    "ca87a914265cffe46bfb63e2e24a3568efbc7888",
    "e5b684b5-3ef2-45cc-b46c-011879fbefe5",
    "Are there cases where the 'end' histogram does not match the e_y when using different head types for hyperparameter tuning?"
  ],
  [
    "ffe260fb92f4c53395118a567f59d32fd365c351",
    "e5b684b5-3ef2-45cc-b46c-011879fbefe5",
    "What factors influence the degree of separability when adapting a model to a task?"
  ],
  [
    "28241e220ebb3458ef6d5eaf6829f8563161a7c1",
    "e5b684b5-3ef2-45cc-b46c-011879fbefe5",
    "What is the effect of mild adaptation on the separability of the features?"
  ],
  [
    "9b256b585691520864c3cf7d1b8cfb8f863d6663",
    "e5b684b5-3ef2-45cc-b46c-011879fbefe5",
    "Do the results of the study suggest any implications beyond the general empirical impression?"
  ],
  [
    "789b3799040a63d59e93a029bab4459c7ff3aa2c",
    "e5b684b5-3ef2-45cc-b46c-011879fbefe5",
    "What is meant by \"information exchanges between the two parts of the network\" in Fig. 1?"
  ],
  [
    "e0bf6addede2ca2d1e76eac67027e72e3ff385f5",
    "b7b26770-a84d-4890-952e-889c84eb7376",
    "Why is Hanja considered an extinct language rather than a script?"
  ],
  [
    "f29ff7d6be64035f374fe6b3fc470453591154e9",
    "dfabc08f-e2b5-471a-a935-f95983b22560",
    "Will the annotated mistakes be released upon the release of MNV2?"
  ],
  [
    "36fdc759d8b028d2f3c0c5cb9e8c26b5744962d0",
    "18fa40db-9226-432f-b47c-d33294a644d8",
    "How does the performance of ILA-DA with different intermediate layers compare to other transferable attack methods such as LinBP or CTM family?"
  ],
  [
    "99d5ca18b41cac7092cd7ca7cf0888b8a29a3018",
    "18fa40db-9226-432f-b47c-d33294a644d8",
    "Can the new automated image transformation strategy be applied to other transfer-based methods such as MI-FGSM and DIM?"
  ],
  [
    "726e56359927a7fc14be9019ea69e50929fa9e67",
    "ea1b4971-72d5-4c83-bb84-f875b760492b",
    "What are the effects of using predicted cues instead of gold cue information for scope resolution?"
  ],
  [
    "9246fb2439ec9512f9298b927660f030736765c0",
    "82cb67ec-ea16-4eb2-94ac-05817084eb13",
    "Is the training process for the proposed model truly end-to-end?"
  ],
  [
    "6ed4842f06973b3b3b83a068d590e3a5421678f8",
    "82cb67ec-ea16-4eb2-94ac-05817084eb13",
    "Would using a transformer-based acoustic model instead of an LSTM-based one improve the model architecture?"
  ],
  [
    "9ff146fb1145a7e6cd038252a41b96f5c6ac0494",
    "ff0b5edf-a364-49f4-a208-4fc3595d418d",
    "Are there any examples of synthetic and ground-truth data that can be used to compare the performance of the model?"
  ],
  [
    "9f2536e9ca279c79b121794af57c2ad02d6b13c8",
    "ff0b5edf-a364-49f4-a208-4fc3595d418d",
    "Does the use of synthetic data improve the performance of a model when a large amount of ground-truth data is available?"
  ],
  [
    "f8b91940d2ce9e4e0df966f18d724e12b5aac0e5",
    "ff0b5edf-a364-49f4-a208-4fc3595d418d",
    "What are the effects of synthetic data on image recognition in a fully-supervised setting?"
  ],
  [
    "94d3d227741b9bf8258649cb3567dc790b9dca07",
    "ef4569a5-0fbb-4a61-bfaf-b29656181f08",
    "Why do the authors use regularization for label embeddings but not for instance embeddings?"
  ],
  [
    "e57f2a5a860c3aa8c1e0f8ca5a3375dd735d463c",
    "57c240e0-e3c6-43aa-9dfe-36d4b73b7438",
    "What is the reason for the performance drop after 100 epochs in Figure 4 (c)?"
  ],
  [
    "cbd5e6e55ec199de5569c76823febc8a19d28e5e",
    "57c240e0-e3c6-43aa-9dfe-36d4b73b7438",
    "Can your NAS method be trained on a large-scale dataset such as ImageNet?"
  ],
  [
    "14f24eacc79985de8d643389b87e35ceb5209775",
    "57c240e0-e3c6-43aa-9dfe-36d4b73b7438",
    "What is the search cost of the proposed method?"
  ],
  [
    "f18d6c2ac81007f7b42cab31234075307c85ee0a",
    "ae1b0ba8-394b-4458-b7f0-e05d2bf7b56c",
    "Do the training languages used for the non-baseline models in the cross-lingual experiments have any overlap with German?"
  ],
  [
    "c0701214e646badb3bbb44e4985982c8e9e65fc7",
    "21abfdcc-14ac-48f4-adbb-f0ff37709328",
    "What measures have been taken to ensure the accuracy of the confidence network?"
  ],
  [
    "ba8b96d10b44463b1ec163db65a29b6145f8729a",
    "924690f3-ff33-4437-b47e-813d287a1043",
    "Could you please explain the dynamics of the networks by providing an analysis of the learning curves obtained during training?"
  ],
  [
    "7a4e6842b9fed6c17b9fc508c5e7f7bdc1614d7c",
    "924690f3-ff33-4437-b47e-813d287a1043",
    "Do you have any specific guidelines or strategies that you use when designing network architectures?"
  ],
  [
    "6f797e6284c2b0ebd83dc98348c33626ac517dbb",
    "0055bca4-a1fa-45b7-bfdf-0e3cea093c14",
    "Are there any other differences between the locations in the benchmark besides the weather?"
  ],
  [
    "b9dc0dacfa9d5676e09c6d6308a65cb2885392cd",
    "cfb5e02c-a48a-4be4-902c-4d03ed09933e",
    "Have the results of the Montreal Forced Aligner been manually checked for accuracy when applied to singing?"
  ],
  [
    "0eb6095e3dbae2dd6e1abc90265e56378f49fa1a",
    "aa74ce07-82a5-4d89-aff1-5a9acabe176a",
    "Are any individual atoms added to the partial graph when using Algorithm 2 with larger vocabulary sizes?"
  ],
  [
    "ff310f12cf0c134c9763ec3389c106e6c16dc65c",
    "aa74ce07-82a5-4d89-aff1-5a9acabe176a",
    "What is the definition of the metrics \"score\" and \"quality\" used in Table 2 of the paper?"
  ],
  [
    "be0cd13d8445fb87a73943d5acf2e5089a02876c",
    "fdb84cfa-92eb-4624-90cd-40399298d402",
    "Does the optimization (eqn. 1-4) happen over the geo-centric map only for the set of map locations observed so far?"
  ],
  [
    "d539111f12ea7af828c1637c34c8e6fcb06f589a",
    "fdb84cfa-92eb-4624-90cd-40399298d402",
    "Do you think that the same framework on variance of ensembles would work equally well in the semantic feature space as in the space of logits?"
  ],
  [
    "3f6d76b052a19c42fdc0f3fa7a03e99d76e053d5",
    "fdb84cfa-92eb-4624-90cd-40399298d402",
    "Do the navigation tasks require the semantic layout of unseen regions in order to be solved, or can they be solved with just what is observed?"
  ],
  [
    "46357f5d8816d410e6100ea03a5fde2f576ae270",
    "e2aeae2e-c3a9-47ec-a388-e6be685c29f1",
    "What is the nature of the extracted patterns?"
  ],
  [
    "67314f99bdc98da9611efbf0de1f4660e36f629c",
    "e2aeae2e-c3a9-47ec-a388-e6be685c29f1",
    "What is the sampling method used to extract patterns with a time window?"
  ],
  [
    "030389c23b9697a71ca59dec3bcab088d7f20ced",
    "e2aeae2e-c3a9-47ec-a388-e6be685c29f1",
    "Is equation 1 a summation from j=0 to k?"
  ],
  [
    "05fe45ed14b202a953eb1a25a0c4552d1d2ff42d",
    "5f208baf-9560-4933-b14c-beb338d5a5e5",
    "Can the proposed method be applied to simpler models such as multilayer perceptrons (MLPs)?"
  ],
  [
    "280960bc073f24e47cd5b63da7388c21eb12d9be",
    "d1fbcce7-ef64-45f4-a455-10242b379879",
    "What justification was used to select the CNN classifier for sentiment analysis experiments?"
  ],
  [
    "0d68ad6ddb3ddfccd1c2d71ae7fc8a724843e891",
    "d3c8e1ac-09a6-4cf1-9f28-95526b675063",
    "What econometric models and economic theories were used to write equations (2), (3), (4), (5), (6), (7), (8), (9), and (10)?"
  ],
  [
    "d59bc31fea9ec1c2594f0ed7813ed2d9348abc75",
    "ebfd2830-b4b0-481f-8ec3-c897335d2748",
    "What is the difference between the task described in the paper and the task of \"taxonomy induction\"?"
  ],
  [
    "fd9af00fe3f20196d71e9e364f55c157d4cd2cd3",
    "ebfd2830-b4b0-481f-8ec3-c897335d2748",
    "What measures were taken to ensure that paths were not shared between the training and test sets?"
  ],
  [
    "d8ac040e919b01e19818a6416896dd66bd58e69d",
    "ebfd2830-b4b0-481f-8ec3-c897335d2748",
    "What factors were responsible for the success of the path-based model?"
  ],
  [
    "06b380902968cd38bbb66c2a75d9372c2f039f2f",
    "fec61102-bca7-4df9-8db5-befabde1f9bc",
    "What evidence is there to suggest that a graph with an average diameter of 10-50 is sufficient to indicate the presence of long-range relationships?"
  ],
  [
    "3611098cfd2590d775531ef564d87617713fe8bf",
    "592c7f9f-379e-4f68-aff8-c0d684411f29",
    "What is the rationale for using ese methods on user-generated text?"
  ],
  [
    "9d285bc752521120d3b45a5b35069f1365c8f603",
    "592c7f9f-379e-4f68-aff8-c0d684411f29",
    "What is the rationale for using set expansion approaches to construct a dictionary from customer reviews?"
  ],
  [
    "ae75b890d30e2879b6a6571bbc634ee4e6157e30",
    "592c7f9f-379e-4f68-aff8-c0d684411f29",
    "What is the practical application of measuring the performance of a method using MAP at gold-k?"
  ],
  [
    "23c1d98a22e68ab8a92b7b1cd2fee83fa79e9a86",
    "592c7f9f-379e-4f68-aff8-c0d684411f29",
    "What is the optimal depth of a ranked list of entities returned by an entity search engine approach for a user to find all relevant entities?"
  ],
  [
    "f2564b011de1ce166a9e4410c3857b5a9c2496ce",
    "592c7f9f-379e-4f68-aff8-c0d684411f29",
    "Can the authors provide an explanation for the significant performance difference between cgexpan and lm-base?"
  ],
  [
    "8952c8598f43e3e36131d56d62db44fded0352d3",
    "592c7f9f-379e-4f68-aff8-c0d684411f29",
    "What factors contribute to the improved performance of the lm-base model compared to the cgexpan model on user-generated datasets?"
  ],
  [
    "7f3ceaefa9722ecb3ce14f4c48d0191a6893f607",
    "43af43c1-f4ca-4e26-934c-2caa913fb036",
    "Where did the lupeol and pristimerin used in this study come from?"
  ],
  [
    "fd627c54f792bc91ab0db4a51e8311a23d8af60d",
    "910e5ec4-4d7d-4c0f-8cf5-ea3e3d4e32a5",
    "What method was used to obtain trait scores for prompts that did not have them in the original dataset?"
  ],
  [
    "9d42f16a53845fd4e37c5676307657425faf1538",
    "12c143a8-5e07-4073-b823-8d946f24723c",
    "Is it feasible to generate a lattice graph for a large dataset?"
  ],
  [
    "c0ddf26bae180b57c24cd4b90e7a0da4a0676425",
    "2cb45f1e-fa36-4f08-9453-af227fd1c0b0",
    "Could you please explain what you mean by a \"real-world pushing task\"?"
  ],
  [
    "c0f788c455af0bee35fb5b9cc36af6a5ec6aaf3f",
    "835d6609-3e60-4c5a-b5a0-66e02e47f920",
    "Why was additional fine-tuning on Clip not included in the experiments?"
  ],
  [
    "e26fc7a2455a2acb2de4d608a7ca7bf1c8fb62a1",
    "57fc3265-d231-40b6-b9f7-b259ddef6e58",
    "What does it mean for a neural module to be \"in charge of solving one specific subproblem\"?"
  ],
  [
    "bf76bbb77fabe1a9105b55efcd591d179958b2c6",
    "57fc3265-d231-40b6-b9f7-b259ddef6e58",
    "What does it mean to optimize the reward with respect to the possible combinations of modules?"
  ],
  [
    "1b206d1d36f66f3d336a33e34858567e8a593ab0",
    "57fc3265-d231-40b6-b9f7-b259ddef6e58",
    "What is the architecture of the system being discussed in the paper?"
  ],
  [
    "5219ee2947eb66850d9df883d32c6549b914d086",
    "57fc3265-d231-40b6-b9f7-b259ddef6e58",
    "How many modules does the system contain?"
  ],
  [
    "a3566edd083568caf0264186c9b8e1658c31e561",
    "57fc3265-d231-40b6-b9f7-b259ddef6e58",
    "What is the rationale behind the design of the different modules?"
  ],
  [
    "b6cb81cf492f5369fa4051c1d2e90b05b0aa9247",
    "57fc3265-d231-40b6-b9f7-b259ddef6e58",
    "How were the modules partitioned?"
  ],
  [
    "be6ee11df60dadea667438571e3ed15560c3cb04",
    "e8a79661-d834-479e-9373-35c911921033",
    "What is the difference between the MOMA dataset and the MOMA-LRG dataset?"
  ],
  [
    "1740b93cc1257022895a050e975d38feebe0f904",
    "e8a79661-d834-479e-9373-35c911921033",
    "Is the activity graph an abstraction of the MOMA dataset that enables evaluation on different levels of granularity?"
  ],
  [
    "e774f0bb72932f381463769b74f98a8f360db732",
    "e8a79661-d834-479e-9373-35c911921033",
    "How does GraphVLM relate to the activity graphs proposed as an abstraction on top of the MOMA dataset?"
  ],
  [
    "0dcfbcf7b77777639a682294aaf99c3fff25cd20",
    "e8a79661-d834-479e-9373-35c911921033",
    "How can the authors ensure that the natural language sentences produced from the \"ground truth\" activity graphs accurately describe the scene?"
  ],
  [
    "a37fa0862f28bd4005c14092d19b86f84c30c983",
    "e8a79661-d834-479e-9373-35c911921033",
    "What criteria were used to select the videos and classes for the study?"
  ],
  [
    "507cdeff564fe9a3d5fe258fe00eef340d135d9b",
    "e8a79661-d834-479e-9373-35c911921033",
    "What ethical considerations were taken into account when selecting the data for the dataset?"
  ],
  [
    "e5d8459c3ebc7cdeb1a56ddced28a7467921a917",
    "e8a79661-d834-479e-9373-35c911921033",
    "What is the source of the dataset?"
  ],
  [
    "62968052606a9490b4add4170997a2c69f19d4ec",
    "e8a79661-d834-479e-9373-35c911921033",
    "Is the dataset under a Creative Commons license?"
  ],
  [
    "5c090b48e2d8b39f413f602a716b92676b7e7ba7",
    "dd7e15d5-6b10-4472-879e-43979253bac7",
    "Do the baselines have any adjustable parameters that could be adjusted to improve performance?"
  ],
  [
    "6159c6e153be58a55c17f3cda104c7ebdd581acc",
    "dd7e15d5-6b10-4472-879e-43979253bac7",
    "What is the rationale for using the cs-error metric instead of sps metrics?"
  ],
  [
    "f5c277093cecafb8c4d4588dfbae6b5279b14a79",
    "********-6e90-4154-8860-152b57fe975e",
    "Do you perform re-annotation for the expanded dataset?"
  ],
  [
    "5ac9e91c8d313af9a0771c5e3e46e95d2a7c2315",
    "********-6e90-4154-8860-152b57fe975e",
    "What is the new evaluation metric proposed in the paper?"
  ],
  [
    "c68719740a590d79c10dfd14e5cb4e1f51938871",
    "********-6e90-4154-8860-152b57fe975e",
    "What is the reason for the low performance of the \"description\" in Table 5?"
  ],
  [
    "8c6b063b9a5318af6557db02c0c7dbc93f8939be",
    "56a696d5-045d-4278-a9e1-f498f75aedb1",
    "What is the relationship between the 16k real captured views and the 9k multi-view sets?"
  ],
  [
    "3318142bc7bd1401191fcc4a9712243c0df0f1df",
    "56a696d5-045d-4278-a9e1-f498f75aedb1",
    "What does it mean when \"high inter-class view similarity causes uncertainty of class labels for uninformative views, which generates multi-view label noise\"?"
  ],
  [
    "61588ca196125738d21c2d191ecac13249af297d",
    "806931c0-fd65-409c-83fe-643bf95bae69",
    "Can the algorithm be tested on larger networks?"
  ],
  [
    "42aec356c91c672d813f540951f0b79d9f57705f",
    "806931c0-fd65-409c-83fe-643bf95bae69",
    "What is DeepNN?"
  ],
  [
    "7cd7d4d944b16a2603aaaf3ebb5628edd813a346",
    "dcef6dba-db39-4589-ac25-98bb7ff5157a",
    "What is the criteria for delimiter placement in the sentence aggregation model?"
  ],
  [
    "b49d17a4084f479d1c1f3e920a863be370968f66",
    "dcef6dba-db39-4589-ac25-98bb7ff5157a",
    "What methods were used to develop the per-triple templates?"
  ],
  [
    "86b7bff4eb8f5701bb87715221c22a2db29eaae1",
    "4a44aee1-e332-43e9-b889-f696a32e3cd8",
    "What method is used to retrieve the data tuple in Algorithm 1, line 4?"
  ],
  [
    "e1369f11b53bb858522bacf4bf2e9d8448dc1ef5",
    "1012488f-387b-4d71-a6ba-e2447625c90e",
    "Why is it important for the metric to be insensitive to the number of layers when evaluating the rank correlations?"
  ],
  [
    "eb715a337474694a5b2fa3212f3936f2979ff998",
    "8571dace-e3e4-436e-a1ff-c5093290a3f3",
    "Can you provide evidence that different contrastive learning methods can be represented using Definition 3.2?"
  ],
  [
    "cd38abc68b46b12d953fddc8838eb77978963fca",
    "8571dace-e3e4-436e-a1ff-c5093290a3f3",
    "What is the definition of simclr and dcl as used in Proposition 3.1?"
  ],
  [
    "19e742075a73b79f8d1593d22060ea47535a485a",
    "2c4957ee-64da-4f35-9459-c6b24d6cfb17",
    "What is the purpose of Figure 3a?"
  ],
  [
    "4b00407f8b0e58aafdaee12f078de8b313872f04",
    "2c4957ee-64da-4f35-9459-c6b24d6cfb17",
    "Does the proposed alternate way to use linear by computing the mean absolute value of the weights associated with it differ from the original linear model proposed by Dalvi et al. (2019)?"
  ],
  [
    "b0767779541047ab4deb8c71f900288615ddd5a7",
    "a0802236-fc59-4563-9681-86ccdf4c9d1c",
    "What factors contribute to VIT's better generalization to the open-set scenario on ImageNet compared to other methods?"
  ],
  [
    "c908b12fc3ea26161680a836fc0ee29b02fd4e96",
    "340079b7-5239-4802-972a-32649f56ab2e",
    "How can the region classification be performed if the novel categories have a large vocabulary of class names that are not present in the image?"
  ],
  [
    "3e6d53b8861714d6727e6f1a924eb2046baac6a7",
    "340079b7-5239-4802-972a-32649f56ab2e",
    "How does the accuracy of the model change when novel categories with large vocabularies are added to the set of candidate categories?"
  ],
  [
    "ec3f80fbff718abc4b5fae665cfdc994570329fb",
    "340079b7-5239-4802-972a-32649f56ab2e",
    "Can a quantitative evaluation of the experiment on ego4d be performed?"
  ],
  [
    "1eafcdeb90458c749f5b2e6dcdaaa06a4ba58abd",
    "340079b7-5239-4802-972a-32649f56ab2e",
    "What does \"but train the detector head with $r(\\cdot)$ online in a single stage\" mean?"
  ],
  [
    "f117dce3beae4a1fc909bbadebcc235634d017c0",
    "18c6f815-dcd6-42bd-91d0-4c2e7885bb2f",
    "What boundary conditions were applied in the seismic forward modeling?"
  ],
  [
    "601d6dade2b1d6724ae69aafc64a71bafd79062e",
    "18c6f815-dcd6-42bd-91d0-4c2e7885bb2f",
    "Will the published code include the algorithms used to generate the data?"
  ],
  [
    "5420a636705116e4e99d17572011f028d54a72b2",
    "9b2416b3-72db-440b-b1d0-068babd61272",
    "What metrics have been used in prior work related to this paper?"
  ],
  [
    "fcd1a1d599ae695d923bfabe4f62e5e457ca2de1",
    "9b2416b3-72db-440b-b1d0-068babd61272",
    "Do the time requirements for training the surrogate outweigh the benefits of using it to speed up the evaluation of the dynamic system?"
  ],
  [
    "fef7c2c7a69370b666710942e3d931819e7bc02d",
    "9b2416b3-72db-440b-b1d0-068babd61272",
    "How does the accuracy of the surrogate affect the accuracy of the downstream task?"
  ],
  [
    "a8466adf7868015b87e7447c11f576b29d121012",
    "84c55c11-027f-450d-a2e4-dbdeba73afeb",
    "What is meant by the phrase \"environment's health\" in the sentence \"most countries have long ago adopted recycling to preserve the environment's health\"?"
  ],
  [
    "8397ceb9d91201c9f2eb30de3c87e1e8243a827d",
    "84c55c11-027f-450d-a2e4-dbdeba73afeb",
    "What is the conceptual model presented in Figure 2?"
  ],
  [
    "174eff40340ae0616a2328f75efd8cf8431b3150",
    "84c55c11-027f-450d-a2e4-dbdeba73afeb",
    "How was the conceptual model in Figure 2 developed?"
  ],
  [
    "dd594c4d0897a3c1259ae3d2d2d23859fbb96f04",
    "f9525fd8-975b-4ca3-8beb-9d1f2c4e6aa7",
    "What is the purpose of the saliva sample?"
  ],
  [
    "127423c37403d9d9d34a21c17bfb33234b5f944a",
    "08f8904d-3ff1-47c9-bb45-460de6a3a645",
    "Did the authors use a validation set to tune the hyperparameters on the QM9 dataset?"
  ],
  [
    "73faf71e5e746272235608cd46a8fda2b309ef70",
    "08f8904d-3ff1-47c9-bb45-460de6a3a645",
    "What do the authors mean by tuning \"approximately\" 5 values on the QM9 dataset?"
  ],
  [
    "ce87b952cfde98f1de69d4c860537a4d3989c67a",
    "726c5bd7-fff4-4027-81e6-d222df310025",
    "Can the authors provide more information about the 30,229 sequences used in the study, such as geographic origin, genetic nomenclature, and variants of concern?"
  ],
  [
    "273390ef5c449b8574f4d7b6e46f332e7152a15d",
    "4d267f76-dc2e-4b23-b34f-6088e202b3c4",
    "What is the intuition and motivation behind the head-to-tail method in section 3.2.3?"
  ],
  [
    "41338acb67bcf333b2817c15b8b74290fb3d9327",
    "4d267f76-dc2e-4b23-b34f-6088e202b3c4",
    "What other downstream tasks, such as natural language inference, question answering, and semantic role labeling, have been tested using an encoder that has been transferred from language 1 to language 2 without any parameter updates?"
  ],
  [
    "d3ce17368ff699857360c15972ad48cb235350b8",
    "4d267f76-dc2e-4b23-b34f-6088e202b3c4",
    "Why does setting the vector dimension to 10 cause the conditional token distribution to become close to the Zipfian distribution?"
  ],
  [
    "46780f3f8ab86a46782f68b7ca66e5e1438afc01",
    "6aa517cb-f4af-4a4d-8121-01adaff65c5f",
    "What criteria were used to determine if an image's radius was \"too big\"?"
  ],
  [
    "bc6f50621da1a65a6e46211a4f48751a6da35304",
    "6aa517cb-f4af-4a4d-8121-01adaff65c5f",
    "How does the paper define the concept of an \"adversarial L2 ball\" when it appears to suggest that every sample should have the same classification as \\tilde{x}, contrary to the expectation that each sample within the ball should have a different classification compared to x?"
  ],
  [
    "d7f32782ecbb103a3971b1e3a918576549b44723",
    "6aa517cb-f4af-4a4d-8121-01adaff65c5f",
    "What is the motivation for building provably-robust adversarial examples?"
  ],
  [
    "94974352b0e42eb1b459e4b85aea1ca6ddb9b713",
    "6aa517cb-f4af-4a4d-8121-01adaff65c5f",
    "Does the robust region proposed in this paper contain physical distortions that may be encountered in real-world cases, and how often?"
  ],
  [
    "77d9dbb3a3af5156b369e66924d4bcf14f794893",
    "6aa517cb-f4af-4a4d-8121-01adaff65c5f",
    "What is the criteria used to evaluate \"provability\" in the paper?"
  ],
  [
    "2a76dc6fa246d4afcfe9aea7050a47aa3949d755",
    "5c520bc0-1964-4fe4-881d-36f155f9256d",
    "Do you concatenate the camera pose with the latent code in the same way as the EG3D method?"
  ],
  [
    "****************************************",
    "5c520bc0-1964-4fe4-881d-36f155f9256d",
    "Did the authors observe any jittering in the processed videos?"
  ],
  [
    "f87855d105235224a2584b0e0716b794ef647705",
    "5c520bc0-1964-4fe4-881d-36f155f9256d",
    "Did the authors take steps to address any jittering in the processed videos that may have been caused by the alignment?"
  ],
  [
    "4006c4e45f822e15f9ccc27df354f3bcd8298509",
    "5c520bc0-1964-4fe4-881d-36f155f9256d",
    "What are the potential effects of using alignment for video preprocessing instead of a fixed cropping window?"
  ],
  [
    "d88d8f911add3ead1f649741f7c22700199f9094",
    "0382ecbf-6eeb-4a6f-96ce-72d946945608",
    "Will the code and dataset associated with this paper be made available to the public?"
  ],
  [
    "1fd3a7fbc973d1042fee519c269028acdbb1ccec",
    "70a5a67a-7c91-49db-83b8-d0fa58883e87",
    "Why is the antecedent included in the sum in #6, rather than using \"verb-nsubj:noun-obj:noun => nsubj:noun-verb-obj:noun\"?"
  ],
  [
    "df9756e054d7db2937ebc51e1ed8477345e57387",
    "70a5a67a-7c91-49db-83b8-d0fa58883e87",
    "What does the logical atom \"nsubj:noun-verb-obj:noun\" mean in the context of the paper?"
  ],
  [
    "ff4b45b2af4e13f58512fc1783fc12dd129feb6f",
    "67c7c6c9-8681-42b3-97cf-0d3d61c1a60d",
    "Does the model incorporate wave erosion?"
  ],
  [
    "70ee52925bfa4ed21c8798964ec74fcdd0f9845f",
    "67c7c6c9-8681-42b3-97cf-0d3d61c1a60d",
    "Is the subaerial erosion rate equivalent to the maximum weathering rate?"
  ],
  [
    "a8d6ed01ff1866040e47a7082ff97ea95a6edd03",
    "67c7c6c9-8681-42b3-97cf-0d3d61c1a60d",
    "Is there a scientific reason for the Greenland Ice Sheet (GRIS) to start melting before the Antarctic Ice Sheet (AIS), beyond the convenience of creating two distinct peaks in a model or analysis?"
  ],
  [
    "fc938634e35cc53e7b6cb50564929eabb0fc7afe",
    "67c7c6c9-8681-42b3-97cf-0d3d61c1a60d",
    "Could you clarify what is meant by \"production versus depth curves\"?"
  ],
  [
    "9be6e9b8ef3f3db6f29bafd74243978f78f2f657",
    "67c7c6c9-8681-42b3-97cf-0d3d61c1a60d",
    "Could you clarify what you mean by \"actual exposed MIS 5e facies are lacking\"?"
  ],
  [
    "e7b7d480aa1076d06dccb8bcb2b7f2b1fd0f9c87",
    "67c7c6c9-8681-42b3-97cf-0d3d61c1a60d",
    "Do you believe that the model presented in your study can be easily applied to other geographical areas?"
  ],
  [
    "eeba4b725b3f8ad526cddce373ff444a591594c9",
    "a1eb5702-7fc0-4a0d-baa3-016c2072b61f",
    "What could explain the difference in performance between the DICTA test set and the new test set, particularly the difference between the cha and wor scores?"
  ],
  [
    "9373d254f956bcbffe53a9ba10531f5102ecdb83",
    "533b5fb3-54d3-485b-b787-0e7c36de5d38",
    "Could the authors provide further justification for their claim that the gradient-based attack is responsible for the shift between test and training data observed in the adversarial attack?"
  ],
  [
    "7e53c05206cc77e9d6e3b28338d1c85336543244",
    "05e29db6-d08d-4393-a0bc-f1420a67be8b",
    "How do the authors determine the accuracy of the CMIP6 climate models in simulating the processes?"
  ],
  [
    "6c9381de277251ad2ce40cd39b94a872cbc4126e",
    "05e29db6-d08d-4393-a0bc-f1420a67be8b",
    "Is the authors' conclusion about the accuracy of the CMIP6 climate models in simulating the processes based on the agreement between the observed data and the models' predictions in terms of the residual variability?"
  ],
  [
    "6610ad96e462f49d6d8f20fee0cdc6dd8a70175a",
    "05e29db6-d08d-4393-a0bc-f1420a67be8b",
    "What is the impact on detection or signal-to-noise ratio (SNR) if RR-based fingerprint construction is not applied?"
  ],
  [
    "7c6f77a64467e8275e381a36386d66650b13e832",
    "a114fbb4-0b8c-4694-9a68-6ba1c4b522d6",
    "Does the term \"aes\" refer to the phenomenon that smiling faces are estimated to be older than neutral faces in direct age estimations, or does it also refer to the phenomenon that smiling face group is retrospectively estimated to be younger?"
  ],
  [
    "69d95e16f2f308754136f8e2245592ca5497ff5f",
    "3977957d-b3d6-4170-9b82-e1fd11d74321",
    "What pairs of sentences are used to train the model to be similar or different between the source and hypothesis in the \"roscoe training\" process?"
  ],
  [
    "911fa2b76fba7e5ae58fb8322c5940c42acbd848",
    "3977957d-b3d6-4170-9b82-e1fd11d74321",
    "Can you provide evidence to support the claim that the proposed taxonomy of generic reasoning errors is comprehensive?"
  ],
  [
    "cbb83b653ecc965d0b930f4f016e4ff93c485696",
    "96c6645c-a165-494f-81b1-bdbb522d7e35",
    "Why does RPM-Random work poorly in pure cooperation compared to the Prisoner's Dilemma case?"
  ],
  [
    "aab380aaa605fffff2d765c9cb058cfc03ee1729",
    "63c9bd1c-c30b-48a9-a7d4-a71bf25f163d",
    "What is the rationale for only keeping scenarios that cause collision with at least two algorithms?"
  ],
  [
    "42b9bcc5c85c3d4087d4f57791f953fa732fc625",
    "63c9bd1c-c30b-48a9-a7d4-a71bf25f163d",
    "What is the rationale for selecting the scenarios used in the paper?"
  ],
  [
    "5b14dc7213f8e7181d9bf848cef4fb79a7b1ad10",
    "63c9bd1c-c30b-48a9-a7d4-a71bf25f163d",
    "What metrics are used to evaluate the performance of scenario generation algorithms?"
  ],
  [
    "de2dc4d1f8b898e5b34a256294729fe7b46f6fda",
    "56b406fd-8e57-42e6-b58e-9a44709826a1",
    "What types of win conditions are used in the board games studied?"
  ],
  [
    "f2e744ebd60bf15d94cd1b9a5cdc7db9f0c4ad93",
    "56b406fd-8e57-42e6-b58e-9a44709826a1",
    "Do you evaluate playing strength of agents by restricting them by MCTS iteration counts or by time limits?"
  ],
  [
    "9dbf9a9e3f0cc960065255b48616ad3b79759600",
    "f62261e1-89ca-461c-b3a9-934961d718de",
    "What is the process for determining the kernel size of the different decouplers in the MDSF module?"
  ],
  [
    "b16ae6d142599eafe257ac83cdf206be914a7a7e",
    "f62261e1-89ca-461c-b3a9-934961d718de",
    "Why are the Multi-scale Deep-scale Feature (MDSF) and Multi-scale Context-scale Feature (MCSF) modules concatenated in Figure 1?"
  ],
  [
    "6d0c861407de2db08718ca55a383b59284a8e223",
    "957f49aa-edfc-45c2-ade5-ca2ae5b98892",
    "What validation data is used to determine the number of selected in-domain sentences?"
  ],
  [
    "d07dca8f8e126c43dacdaf145ec4103ef25400f5",
    "01fe4e66-ceb0-4de3-9237-ef72ca0b99f8",
    "What would be the impact of not having access to pre-trained embeddings on the performance of this method?"
  ],
  [
    "9505c56639fea265e46601d12575e9d9715b9e7a",
    "e8d84e97-125b-4719-86b5-cdbc419caf25",
    "What is the variational approximation of c given by the query and support sets?"
  ],
  [
    "7110b14e5ab532a6273415a059f6808204376ee6",
    "e8d84e97-125b-4719-86b5-cdbc419caf25",
    "Why is the sequential neural process setting not included as a comparison?"
  ],
  [
    "2f75586071f2de4ab14810c7f2bd7f7b4e143fb6",
    "e8d84e97-125b-4719-86b5-cdbc419caf25",
    "Why are popular meta-learning frameworks like MAML [1] and Probablistic MAML [2] not considered in the experiments?"
  ],
  [
    "064bd1dff89282732ddcf6c71a98975792d8b3d4",
    "bf963177-de8d-40e7-9ee3-36dcedae5261",
    "Can you explain how the two-time-scale algorithm can be extended to the mini-batch setting?"
  ],
  [
    "587b947b50e65e3caa8174633245ab39edbdb0f0",
    "26acb693-9f13-4672-9974-d9e3397acffa",
    "Did the authors conduct analyses using detrended data to isolate the effect of the temperature increase?"
  ],
  [
    "1818a27b73310c09fb736d5e044187417bda0dbc",
    "7b8113f5-81e2-4e8a-83c6-98e79e032430",
    "Do the experts who annotated the dataset have expertise in linguistics or in the domain of the dataset?"
  ],
  [
    "83065c1670e7bbe6476efd1959f21480a4f3bf69",
    "7b8113f5-81e2-4e8a-83c6-98e79e032430",
    "What is the distinction between \"skill\", \"knowledge\" and \"attitude\" in this work?"
  ],
  [
    "876d1ffd9379695a117eb81936e7bb2b0ffb1e9d",
    "f997a147-9fbe-4366-baa2-0dd77354b612",
    "What tuning methods were used to optimize the baseline algorithms?"
  ],
  [
    "d096c58eea777208cfd4ba272dac018b8a808d6c",
    "f997a147-9fbe-4366-baa2-0dd77354b612",
    "What is the rationale for selecting the simulation environments used in the paper?"
  ],
  [
    "5c790742a803e76547d117cf4a77434d1737b5b1",
    "9f12b974-fca5-42aa-8f50-37c2f6752077",
    "What factors, besides temperature and precipitation, could influence the distribution of glaciers in a given area?"
  ],
  [
    "cfbd6962220a29ddfda999443b628e02ebd2d79b",
    "9f12b974-fca5-42aa-8f50-37c2f6752077",
    "Does the orientation of the grid, with potentially more north-facing slopes in the Elwha area than in the Quinault area, affect the absorption of solar radiation, thereby influencing glacier distribution?"
  ],
  [
    "836969164a688341782ffa72b87f1348ba1ee4ac",
    "80140938-7e4c-44c2-b9c4-03c5e24188b3",
    "How many 3D models were used in the pre-collection stage of the data collection process?"
  ],
  [
    "aae1c73c7b0de5fb88e34c245782e2ecb4dcb24d",
    "80140938-7e4c-44c2-b9c4-03c5e24188b3",
    "Is the dataset balanced across different classes?"
  ],
  [
    "e232c66d9986ff1ac6f532437bd94f0b71a44ca5",
    "80140938-7e4c-44c2-b9c4-03c5e24188b3",
    "Are ten locations sufficient to represent the variety of surfaces in urban environments?"
  ],
  [
    "f5fe5047a045ce5a97066fd72458d8951c846342",
    "80140938-7e4c-44c2-b9c4-03c5e24188b3",
    "How many 3D replicas were used in the survey described in Section 2.1?"
  ],
  [
    "3546db32608ccae0b45d96e051b10a8967437d6f",
    "80140938-7e4c-44c2-b9c4-03c5e24188b3",
    "How many people were employed to take photos for the survey described in Section 2.1?"
  ],
  [
    "916923a8909ab06632f575b7f36db3ac70642419",
    "7a1dcd91-160c-4737-923e-d899267bbeca",
    "What are the relative sizes of the mixing efficiencies, kappa(m) and kappa(v)?"
  ],
  [
    "27642536b6affc6438fc0e5a5b2ce6b2d5208309",
    "7a1dcd91-160c-4737-923e-d899267bbeca",
    "Does this study apply to nearshore, offshore, or both environments, considering the parameters given appear to be mostly relevant for surfzone dynamics?"
  ],
  [
    "130985a6f0c94e81204c5a5014faa6017dc2a328",
    "7a1dcd91-160c-4737-923e-d899267bbeca",
    "Is the problem under investigation genuinely nonlinear, or does it pertain more accurately to a weakly nonlinear context?"
  ],
  [
    "09c36735b520089d4936e6966157cb10f8f1ed0e",
    "9837c99a-5d93-4e5c-9409-fe208df8f8ea",
    "Do all of the other hyperparameters remain constant when scaling depth (d) and width (w) in the VIT model configurations?"
  ],
  [
    "d250649b5c73368021f92321b3d59f4c1d3c762f",
    "9837c99a-5d93-4e5c-9409-fe208df8f8ea",
    "What hardware was used to compute the msec/example in Figures 1 and 2?"
  ],
  [
    "e7cb5933a3df86f543cb36cb77b3f41cd7ad4021",
    "652c1828-f4a5-44f5-869c-941a7fa3c25b",
    "What advantages does a rule-based approach have over statistical or neural methods when working with small corpora for indigenous languages?"
  ],
  [
    "cf91a671c7d2248b716cf143ff64e032fed4681e",
    "0074ee7d-1436-4e40-97ca-02aa0d31c627",
    "What impact does the layer norm approximation have on the performance of the tasks discussed in the paper?"
  ],
  [
    "08ee038d964c18feafe50974403477b69a786d82",
    "1eae0cf6-f3b3-4f54-93ac-d554d71272ee",
    "What is the dimension of the variables and functions in equations 8-13?"
  ],
  [
    "006b4d78ff2835159d4e1f745a3f9c4f41fe8351",
    "1eae0cf6-f3b3-4f54-93ac-d554d71272ee",
    "Is the Markovian assumption in equation 6 valid given that the current random walk step is determined by a given relation type and starting time which never changes in a random walk?"
  ],
  [
    "add8f70fda4a981fbac7e3f41f938eeecd3ccd4d",
    "1eae0cf6-f3b3-4f54-93ac-d554d71272ee",
    "What is the rationale behind calling the first kind of rule in Section 4 \"Markovian\"?"
  ],
  [
    "95d0f3eec5444caddab3df7e45aa31db81cabef8",
    "1eae0cf6-f3b3-4f54-93ac-d554d71272ee",
    "What does the operator m represent?"
  ],
  [
    "317ee5566b85c3b36699add3f268020579e8b718",
    "1eae0cf6-f3b3-4f54-93ac-d554d71272ee",
    "What are the drawbacks of using statistic learning methods such as StreamLearner and TLogic?"
  ],
  [
    "85afe8245083d99893657bc1eeadbcefa12dbf59",
    "b24a2382-07dd-4e7c-97b4-46f6e68ae81f",
    "What does \"1 psnr\" mean?"
  ],
  [
    "53c8c64ae66712791ba2a355e4cc97a262c61acc",
    "b24a2382-07dd-4e7c-97b4-46f6e68ae81f",
    "What is the definition of the r-d function for probability distributions where the expectation is undefined, such as the Cauchy distribution?"
  ],
  [
    "****************************************",
    "b24a2382-07dd-4e7c-97b4-46f6e68ae81f",
    "Can the proposed algorithms be applied to other classes of probability distributions beyond the Gaussian distribution and the \"banana\" source?"
  ],
  [
    "33ac8263606098fca8bcdc4746cd3f4235387b26",
    "1c48b17b-24b0-42e2-b002-4b48bd610c05",
    "Does the new method run faster than existing distributed training methods such as data and model parallelism?"
  ],
  [
    "8192e96a224224e5fc15e03019d0ac65253d1492",
    "63d51283-319f-49cc-a288-b386abe968fe",
    "What is the purpose of the \u201crange\u201d parameter?"
  ],
  [
    "ebd7cf5f3adbc674bd5ce88a563f3fea990873a9",
    "c50edf25-ab9f-4550-b45d-d8700831f264",
    "What is the rationale for training on the first six months of data and validating the models on the last two months?"
  ],
  [
    "3b1176248b0cfc5fb6e1786bb4007f98aa2ac210",
    "c50edf25-ab9f-4550-b45d-d8700831f264",
    "What is the process used to obtain the first and second disparities?"
  ],
  [
    "8f0ed4f134911b593527e3793459b6d55faf5923",
    "3e7dfccf-7c45-46be-9f4c-65c2638b5c06",
    "Who holds the copyright for the images used in the paper?"
  ],
  [
    "a9dce4895de5aac10ec1c0b1da92de90c47582f9",
    "a0022a03-f5b8-4462-900a-befc204f91c2",
    "How long were the EGN and Meta-EGN models pretrained?"
  ],
  [
    "1be49494b9a1c964df99b3dabe0af0bfdc970713",
    "a0022a03-f5b8-4462-900a-befc204f91c2",
    "Can the methodology be applied to multi-task learning?"
  ],
  [
    "9776571072ba250ab654c2a326bd48a527e61213",
    "a0022a03-f5b8-4462-900a-befc204f91c2",
    "Can the proposed framework be extended to other applications or tasks?"
  ],
  [
    "207945518935728931a4b020daa416e8fc8f1cda",
    "a0022a03-f5b8-4462-900a-befc204f91c2",
    "Are the relaxations in Table 2 previously established or newly derived?"
  ],
  [
    "bc784ef2841eb98841522b97ab75da7a7106b99c",
    "a0022a03-f5b8-4462-900a-befc204f91c2",
    "What is the meaning of \"fine-tuning timing for classical solver\"?"
  ],
  [
    "413aa7a24c99874e0aae31b569348cc6c4e39b14",
    "e7a6d1d5-93dd-4d11-b795-5929ea1b77c1",
    "What recommendations can be made based on the views expressed on coordination, preparation, and decision-makers?"
  ],
  [
    "b4885c9ebb178f8daa578cef0c857a1de41d8d54",
    "39db8923-b2d6-4dab-ab2b-9fe4bf53cd7c",
    "What is the relationship between the location of conferences and diversity of participation in NLP research?"
  ],
  [
    "d5b246ec5a8edcc34e88a24fb9bfd7d313572647",
    "68760b44-1818-4514-884b-fae585d63dbd",
    "What is the process for obtaining $v^{tar}_t$ in equation (1)?"
  ],
  [
    "7603a58573eeaabda0e22ca42e407dd44c83bd3e",
    "c1d8e0b6-9208-493d-8bf1-1ab3a6022faf",
    "What are \"action frames\"?"
  ],
  [
    "6de0c620431f72ce5a6331d7dde1b8df91e24936",
    "c1d8e0b6-9208-493d-8bf1-1ab3a6022faf",
    "What criteria do you use to determine which knowledge dimensions to annotate for each frame?"
  ],
  [
    "92b04f60c27edb89dcdc8dcd575bcb9872f0e307",
    "c1d8e0b6-9208-493d-8bf1-1ab3a6022faf",
    "Are the incorrectly-classified actions/objects ambiguous for humans?"
  ],
  [
    "748b3e8ce8fe5bb6fa899f962211e41d18c30cae",
    "c0f2949b-66c3-4f20-ad66-33a755613d11",
    "What is the meaning of the bounds presented in Table 1?"
  ],
  [
    "0544f3fb3619a15d1f6076086707d91cea93b334",
    "c0f2949b-66c3-4f20-ad66-33a755613d11",
    "Can the authors provide a justification for why only four datasets were used to evaluate the visual search models, rather than a more diverse collection of datasets?"
  ],
  [
    "21f851f7058a46d8f9904d493b82811edf3aa8f3",
    "bfbc68f7-b5c1-437e-abee-adc72503c48d",
    "Are there any connections between this work and existing research on learning data weighting using multi-arm bandits?"
  ],
  [
    "04e9ce2f786d7603574645eafe3bfe6e1a603190",
    "bfbc68f7-b5c1-437e-abee-adc72503c48d",
    "Is the choice of mirror descent necessary for the proposed method?"
  ],
  [
    "a7ad87d54def516b43292de78c758cf6107320f7",
    "bfbc68f7-b5c1-437e-abee-adc72503c48d",
    "Can the proposed method be implemented using projected gradient methods instead of mirror descent?"
  ],
  [
    "ddbf5cd6168ece49006281d9def64503e3610f0f",
    "bfbc68f7-b5c1-437e-abee-adc72503c48d",
    "Is it necessary for \\phi to be a vector given assumption b?"
  ],
  [
    "c44a73f20a295acb499abf61c0f0b96e4080d9ba",
    "bfbc68f7-b5c1-437e-abee-adc72503c48d",
    "Is there a \\forall quantifier, for example \\forall \\phi \\in \\phi, before equation 3.5 in definition 3.2 (transferability) of the paper?"
  ],
  [
    "8f304b893f44b97b98ba8df1ea43ce0fcb657b87",
    "bfbc68f7-b5c1-437e-abee-adc72503c48d",
    "Is there a reason why the expression (\\sum...) is always positive, and if not, how can we take a power to 1/p?"
  ],
  [
    "caa94f24704e76df0ceac395ee650a45f7a174e1",
    "bfbc68f7-b5c1-437e-abee-adc72503c48d",
    "What are the most popular and advanced methods in cross-task learning?"
  ],
  [
    "fe2486dcff37fd819cb07981aa6a1e026c1f52e5",
    "bfbc68f7-b5c1-437e-abee-adc72503c48d",
    "How does this method compare to the most advanced methods in cross-task learning?"
  ],
  [
    "530a4fe0ab0d3f801dab10132e0e5791d29d2051",
    "feaedd6c-fb14-48b3-8de4-c9ee5c90149a",
    "Are the results in Table 1 with decoder downsampling obtained with or without the two-step decoding method?"
  ],
  [
    "da72c5aada0a4b421a7919d5ef2739ddb658ae5e",
    "feaedd6c-fb14-48b3-8de4-c9ee5c90149a",
    "How many samples were used to generate the results in Table 2?"
  ],
  [
    "1874144ac78d10d99982bc7f6446545ac56a4805",
    "680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052",
    "What further analysis has been conducted regarding the observation that models struggle with associations that are not visually salient?"
  ],
  [
    "9baa6e51bac226403b63b8bef97ea58737e9f14c",
    "680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052",
    "What factors make the 10 & 12 task more difficult than the 5 & 6 task, given that humans achieve similar performance on both tasks?"
  ],
  [
    "c5bafca1e41f1bfed6bf6063b321e6bb5102b171",
    "680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052",
    "What is the reason for the poorer performance of the Clip-VIL model compared to the other models?"
  ],
  [
    "3684e20e39c14e7d52c12084515e178aa0789584",
    "680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052",
    "Why should associations that are solvable by AI be kept in the framework, when the purpose of the framework is to collect associations that are difficult for models but solvable by humans?"
  ],
  [
    "26c5a6b5fdd3b1d0b2d97c1550126b503f144eed",
    "680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052",
    "What is the chance performance reported in Table 4?"
  ],
  [
    "b79e294382978a8a9cebe595211529bcb653e0f2",
    "b84a70b0-6319-450c-afe7-10da9e7dbbb2",
    "How are the topics for n-grams chosen?"
  ]
]
output = []

counter = 0
for (d of input) {
  question_id = d[0]
  paper_id = d[1]
  question_text = d[2]

  response = await get_response(paper_id, question_text)
  output.push([question_id, paper_id, question_text, response])

  await new Promise(resolve => setTimeout(resolve, Math.floor(Math.random() * 2000) + 1000));
  console.log(`Processed question ${question_id} for paper ${paper_id} (${counter + 1}/${input.length})`);
  counter++;
}

saveToFile(output, 'output.json');