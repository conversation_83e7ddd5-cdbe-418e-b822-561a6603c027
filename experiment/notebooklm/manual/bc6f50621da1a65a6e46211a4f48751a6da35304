)]}'

3036
[["wrb.fr",null,"[[\"The paper defines the concept of an \\\"adversarial L2 ball\\\" in the context of measuring the strength of an adversarial attack on smoothed classifiers [1]. Specifically, **Definition 2** states that\",null,[\"1f90c148-8032-41ee-b87e-a859555cc0a3\",\"7b00239d-8cfc-4c3a-b5fb-c0cda6ace17d\",2209701689],null,[[null,[[[\"5c85bd04-b486-4b6f-a343-949261db70c9\"],[null,0,147]]]],null,null,[[[\"5c85bd04-b486-4b6f-a343-949261db70c9\"],[null,null,0.7473042876547293,[[null,8490,9429]],[[[8490,8798,[[[8490,8798,[\"We define adversarial attacks on smoothed classifiers, as follows: Definition 1 (Adversarial attack on smoothed classifiers). For a fixed σ, α and an adversarial distance R′ ∈ R\\u003e0, we call x̃ ∈ Rn0 an adversarial attack on the smoothed classifier g at the point x ∈ Rn0 , if ‖x̃− x‖2 \\u003c R′ and g(x) 6\\u003d g(x̃). \"]]]]],[8798,9429,[[[8798,9429,[\"Similarly to generating adversarial attacks on the network f , we need to balance the adversarial distance R′ on g. If too big — the problem becomes trivial; if too small — no attacks exist. We outline the exact procedure we use to heuristically select R′ in Appendix C.4. Using the above definition, we define the strength of an attack x̃ as follows: Definition 2 (Strength of adversarial attack on smoothed classifiers). We measure the strength of an attack x̃ in terms of Radv– the radius around x̃, whose L2 ball is certified to be the same adversarial class as x̃ on the smoothed network g using CERTIFY for a chosen σ and α. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"5c85bd04-b486-4b6f-a343-949261db70c9\"]]]]]],[[null,null,0.7473042876547293,[[null,8490,9429]],[[[8490,8798,[[[8490,8798,[\"We define adversarial attacks on smoothed classifiers, as follows: Definition 1 (Adversarial attack on smoothed classifiers). For a fixed σ, α and an adversarial distance R′ ∈ R\\u003e0, we call x̃ ∈ Rn0 an adversarial attack on the smoothed classifier g at the point x ∈ Rn0 , if ‖x̃− x‖2 \\u003c R′ and g(x) 6\\u003d g(x̃). \"]]]]],[8798,9429,[[[8798,9429,[\"Similarly to generating adversarial attacks on the network f , we need to balance the adversarial distance R′ on g. If too big — the problem becomes trivial; if too small — no attacks exist. We outline the exact procedure we use to heuristically select R′ in Appendix C.4. Using the above definition, we define the strength of an attack x̃ as follows: Definition 2 (Strength of adversarial attack on smoothed classifiers). We measure the strength of an attack x̃ in terms of Radv– the radius around x̃, whose L2 ball is certified to be the same adversarial class as x̃ on the smoothed network g using CERTIFY for a chosen σ and α. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"5c85bd04-b486-4b6f-a343-949261db70c9\"]]],[[[null,0,147],[0]]],[[\"What is a provably robust adversarial example?\",\"What is PARADE's primary purpose?\",\"What is the role of DeepPoly?\"]]]"]]
4319
[["wrb.fr",null,"[[\"The paper defines the concept of an \\\"adversarial L2 ball\\\" in the context of measuring the strength of an adversarial attack on smoothed classifiers [1]. Specifically, **Definition 2** states that the strength of an attack $\\\\tilde{x}$ is measured in terms of **Radv**, which is \\\"the radius around $\\\\tilde{x}$, whose L2 ball is certified to be the **same adversarial class as $\\\\tilde{x}$** on the smoothed network g using CERTIFY for a chosen $\\\\sigma$ and $\\\\alpha$\\\" [1].\\n\\nThe goal here is not for samples within the ball around $\\\\tilde{x}$ to have a different classification compared to the original input $x$. Instead, $\\\\tilde{x}$ is already considered an adversarial attack on the smoothed classifier $g$ if its classification $g(\\\\tilde{x})$\",null,[\"1f90c148-8032-41ee-b87e-a859555cc0a3\",\"7b00239d-8cfc-4c3a-b5fb-c0cda6ace17d\",2209701689],null,[[[[0,448,[[[0,163,[\"The paper defines the concept of an \\\"adversarial L2 ball\\\" in the context of measuring the strength of an adversarial attack on smoothed classifiers. Specifically, \"]],[163,175,[\"Definition 2\",[true]]],[175,250,[\" states that the strength of an attack $\\\\tilde{x}$ is measured in terms of \"]],[250,254,[\"Radv\",[true]]],[254,334,[\", which is \\\"the radius around $\\\\tilde{x}$, whose L2 ball is certified to be the \"]],[334,371,[\"same adversarial class as $\\\\tilde{x}$\",[true]]],[371,448,[\" on the smoothed network g using CERTIFY for a chosen $\\\\sigma$ and $\\\\alpha$\\\".\"]]]]]],[[[\"70f3bf3f-d5bc-4e89-87e5-bcbea9a81152\"],[null,0,147]],[[\"70f3bf3f-d5bc-4e89-87e5-bcbea9a81152\"],[null,372,447]]]],null,null,[[[\"70f3bf3f-d5bc-4e89-87e5-bcbea9a81152\"],[null,null,0.7473042876547293,[[null,8490,9429]],[[[8490,8798,[[[8490,8798,[\"We define adversarial attacks on smoothed classifiers, as follows: Definition 1 (Adversarial attack on smoothed classifiers). For a fixed σ, α and an adversarial distance R′ ∈ R\\u003e0, we call x̃ ∈ Rn0 an adversarial attack on the smoothed classifier g at the point x ∈ Rn0 , if ‖x̃− x‖2 \\u003c R′ and g(x) 6\\u003d g(x̃). \"]]]]],[8798,9429,[[[8798,9429,[\"Similarly to generating adversarial attacks on the network f , we need to balance the adversarial distance R′ on g. If too big — the problem becomes trivial; if too small — no attacks exist. We outline the exact procedure we use to heuristically select R′ in Appendix C.4. Using the above definition, we define the strength of an attack x̃ as follows: Definition 2 (Strength of adversarial attack on smoothed classifiers). We measure the strength of an attack x̃ in terms of Radv– the radius around x̃, whose L2 ball is certified to be the same adversarial class as x̃ on the smoothed network g using CERTIFY for a chosen σ and α. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"70f3bf3f-d5bc-4e89-87e5-bcbea9a81152\"]]]]]],[[null,null,0.7473042876547293,[[null,8490,9429]],[[[8490,8798,[[[8490,8798,[\"We define adversarial attacks on smoothed classifiers, as follows: Definition 1 (Adversarial attack on smoothed classifiers). For a fixed σ, α and an adversarial distance R′ ∈ R\\u003e0, we call x̃ ∈ Rn0 an adversarial attack on the smoothed classifier g at the point x ∈ Rn0 , if ‖x̃− x‖2 \\u003c R′ and g(x) 6\\u003d g(x̃). \"]]]]],[8798,9429,[[[8798,9429,[\"Similarly to generating adversarial attacks on the network f , we need to balance the adversarial distance R′ on g. If too big — the problem becomes trivial; if too small — no attacks exist. We outline the exact procedure we use to heuristically select R′ in Appendix C.4. Using the above definition, we define the strength of an attack x̃ as follows: Definition 2 (Strength of adversarial attack on smoothed classifiers). We measure the strength of an attack x̃ in terms of Radv– the radius around x̃, whose L2 ball is certified to be the same adversarial class as x̃ on the smoothed network g using CERTIFY for a chosen σ and α. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"70f3bf3f-d5bc-4e89-87e5-bcbea9a81152\"]]],[[[null,0,147],[0]],[[null,372,447],[0]]],[[\"What is a provably robust adversarial example?\",\"What is PARADE's primary purpose?\",\"What is the role of DeepPoly?\"]]]"]]
7373
[["wrb.fr",null,"[[\"The paper defines the concept of an \\\"adversarial L2 ball\\\" in the context of measuring the strength of an adversarial attack on smoothed classifiers [1]. Specifically, **Definition 2** states that the strength of an attack $\\\\tilde{x}$ is measured in terms of **Radv**, which is \\\"the radius around $\\\\tilde{x}$, whose L2 ball is certified to be the **same adversarial class as $\\\\tilde{x}$** on the smoothed network g using CERTIFY for a chosen $\\\\sigma$ and $\\\\alpha$\\\" [1].\\n\\nThe goal here is not for samples within the ball around $\\\\tilde{x}$ to have a different classification compared to the original input $x$. Instead, $\\\\tilde{x}$ is already considered an adversarial attack on the smoothed classifier $g$ if its classification $g(\\\\tilde{x})$ is different from the original input's classification $g(x)$, i.e., $g(x) \\\\neq g(\\\\tilde{x})$ [1]. The Radv, or the adversarial L2 ball, then measures **how robustly $\\\\tilde{x}$ maintains its adversarial classification**. Intuitively, if Radv is larger, it means the smoothed classifier is less confident about predicting the correct class for the region around $\\\\tilde{x}$, as more adversarial examples are sampled in this region, thus indicating a stronger attack on $g$ [2].\",null,[\"1f90c148-8032-41ee-b87e-a859555cc0a3\",\"7b00239d-8cfc-4c3a-b5fb-c0cda6ace17d\",2209701689],null,[[[[0,448,[[[0,163,[\"The paper defines the concept of an \\\"adversarial L2 ball\\\" in the context of measuring the strength of an adversarial attack on smoothed classifiers. Specifically, \"]],[163,175,[\"Definition 2\",[true]]],[175,250,[\" states that the strength of an attack $\\\\tilde{x}$ is measured in terms of \"]],[250,254,[\"Radv\",[true]]],[254,334,[\", which is \\\"the radius around $\\\\tilde{x}$, whose L2 ball is certified to be the \"]],[334,371,[\"same adversarial class as $\\\\tilde{x}$\",[true]]],[371,448,[\" on the smoothed network g using CERTIFY for a chosen $\\\\sigma$ and $\\\\alpha$\\\".\"]]]]]],[[[\"f13159d6-fefa-47ff-a295-fdfbbab3fcf0\"],[null,0,147]],[[\"f13159d6-fefa-47ff-a295-fdfbbab3fcf0\"],[null,372,447]],[[\"f13159d6-fefa-47ff-a295-fdfbbab3fcf0\"],[null,448,812]],[[\"feb6119a-22d3-4c4f-9a6b-74a9c40f7a4b\"],[null,931,1183]]]],null,null,[[[\"f13159d6-fefa-47ff-a295-fdfbbab3fcf0\"],[null,null,0.7473042876547293,[[null,8490,9429]],[[[8490,8798,[[[8490,8798,[\"We define adversarial attacks on smoothed classifiers, as follows: Definition 1 (Adversarial attack on smoothed classifiers). For a fixed σ, α and an adversarial distance R′ ∈ R\\u003e0, we call x̃ ∈ Rn0 an adversarial attack on the smoothed classifier g at the point x ∈ Rn0 , if ‖x̃− x‖2 \\u003c R′ and g(x) 6\\u003d g(x̃). \"]]]]],[8798,9429,[[[8798,9429,[\"Similarly to generating adversarial attacks on the network f , we need to balance the adversarial distance R′ on g. If too big — the problem becomes trivial; if too small — no attacks exist. We outline the exact procedure we use to heuristically select R′ in Appendix C.4. Using the above definition, we define the strength of an attack x̃ as follows: Definition 2 (Strength of adversarial attack on smoothed classifiers). We measure the strength of an attack x̃ in terms of Radv– the radius around x̃, whose L2 ball is certified to be the same adversarial class as x̃ on the smoothed network g using CERTIFY for a chosen σ and α. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"f13159d6-fefa-47ff-a295-fdfbbab3fcf0\"]]],[[\"feb6119a-22d3-4c4f-9a6b-74a9c40f7a4b\"],[null,null,0.7102214152640758,[[null,9429,10314]],[[[9429,9815,[[[9429,9815,[\"Intuitively, this definition states that for points x̃ for which Radv is bigger, the smoothed classifier is less confident about predicting the correct class, since more adversarial examples are sampled in this region and therefore, the attack on g is stronger. We use this measure in Section 5 to compare the effectiveness of our adversarial examples to examples obtained by PGD on g. \"]]]]],[9815,9826,[[[9815,9826,[\"3 OVERVIEW \"]]]]],[9826,10314,[[[9826,10314,[\"Existing methods for generating robust adversarial examples focus on achieving empirical robustness (Qin et al., 2019; Athalye et al., 2018). In contrast, we consider provably robust adversarial examples, defined below: Definition 3 (Provably Robust Adversarial Example). We define a provably robust adversarial example to be any large connected neural network input region, defined by a set of perturbations of an input, that can be formally proven to only contain adversarial examples. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"feb6119a-22d3-4c4f-9a6b-74a9c40f7a4b\"]]]]]],[[null,null,0.7473042876547293,[[null,8490,9429]],[[[8490,8798,[[[8490,8798,[\"We define adversarial attacks on smoothed classifiers, as follows: Definition 1 (Adversarial attack on smoothed classifiers). For a fixed σ, α and an adversarial distance R′ ∈ R\\u003e0, we call x̃ ∈ Rn0 an adversarial attack on the smoothed classifier g at the point x ∈ Rn0 , if ‖x̃− x‖2 \\u003c R′ and g(x) 6\\u003d g(x̃). \"]]]]],[8798,9429,[[[8798,9429,[\"Similarly to generating adversarial attacks on the network f , we need to balance the adversarial distance R′ on g. If too big — the problem becomes trivial; if too small — no attacks exist. We outline the exact procedure we use to heuristically select R′ in Appendix C.4. Using the above definition, we define the strength of an attack x̃ as follows: Definition 2 (Strength of adversarial attack on smoothed classifiers). We measure the strength of an attack x̃ in terms of Radv– the radius around x̃, whose L2 ball is certified to be the same adversarial class as x̃ on the smoothed network g using CERTIFY for a chosen σ and α. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"f13159d6-fefa-47ff-a295-fdfbbab3fcf0\"]],[null,null,0.7102214152640758,[[null,9429,10314]],[[[9429,9815,[[[9429,9815,[\"Intuitively, this definition states that for points x̃ for which Radv is bigger, the smoothed classifier is less confident about predicting the correct class, since more adversarial examples are sampled in this region and therefore, the attack on g is stronger. We use this measure in Section 5 to compare the effectiveness of our adversarial examples to examples obtained by PGD on g. \"]]]]],[9815,9826,[[[9815,9826,[\"3 OVERVIEW \"]]]]],[9826,10314,[[[9826,10314,[\"Existing methods for generating robust adversarial examples focus on achieving empirical robustness (Qin et al., 2019; Athalye et al., 2018). In contrast, we consider provably robust adversarial examples, defined below: Definition 3 (Provably Robust Adversarial Example). We define a provably robust adversarial example to be any large connected neural network input region, defined by a set of perturbations of an input, that can be formally proven to only contain adversarial examples. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"feb6119a-22d3-4c4f-9a6b-74a9c40f7a4b\"]]],[[[null,0,147],[0]],[[null,372,447],[0]],[[null,448,812],[0]],[[null,931,1183],[1]]],[[\"What is a provably robust adversarial example?\",\"What is PARADE's primary purpose?\",\"What is the role of DeepPoly?\"]]]"]]
8204
[["wrb.fr",null,"[[\"The paper defines the concept of an \\\"adversarial L2 ball\\\" in the context of measuring the strength of an adversarial attack on smoothed classifiers [1]. Specifically, **Definition 2** states that the strength of an attack $\\\\tilde{x}$ is measured in terms of **Radv**, which is \\\"the radius around $\\\\tilde{x}$, whose L2 ball is certified to be the **same adversarial class as $\\\\tilde{x}$** on the smoothed network g using CERTIFY for a chosen $\\\\sigma$ and $\\\\alpha$\\\" [1].\\n\\nThe goal here is not for samples within the ball around $\\\\tilde{x}$ to have a different classification compared to the original input $x$. Instead, $\\\\tilde{x}$ is already considered an adversarial attack on the smoothed classifier $g$ if its classification $g(\\\\tilde{x})$ is different from the original input's classification $g(x)$, i.e., $g(x) \\\\neq g(\\\\tilde{x})$ [1]. The Radv, or the adversarial L2 ball, then measures **how robustly $\\\\tilde{x}$ maintains its adversarial classification**. Intuitively, if Radv is larger, it means the smoothed classifier is less confident about predicting the correct class for the region around $\\\\tilde{x}$, as more adversarial examples are sampled in this region, thus indicating a stronger attack on $g$ [2].\",null,[\"1f90c148-8032-41ee-b87e-a859555cc0a3\",\"7b00239d-8cfc-4c3a-b5fb-c0cda6ace17d\",2209701689],null,[[[[0,448,[[[0,163,[\"The paper defines the concept of an \\\"adversarial L2 ball\\\" in the context of measuring the strength of an adversarial attack on smoothed classifiers. Specifically, \"]],[163,175,[\"Definition 2\",[true]]],[175,250,[\" states that the strength of an attack $\\\\tilde{x}$ is measured in terms of \"]],[250,254,[\"Radv\",[true]]],[254,334,[\", which is \\\"the radius around $\\\\tilde{x}$, whose L2 ball is certified to be the \"]],[334,371,[\"same adversarial class as $\\\\tilde{x}$\",[true]]],[371,448,[\" on the smoothed network g using CERTIFY for a chosen $\\\\sigma$ and $\\\\alpha$\\\".\"]]]]],[448,1184,[[[448,866,[\"The goal here is not for samples within the ball around $\\\\tilde{x}$ to have a different classification compared to the original input $x$. Instead, $\\\\tilde{x}$ is already considered an adversarial attack on the smoothed classifier $g$ if its classification $g(\\\\tilde{x})$ is different from the original input's classification $g(x)$, i.e., $g(x) \\\\neq g(\\\\tilde{x})$. The Radv, or the adversarial L2 ball, then measures \"]],[866,931,[\"how robustly $\\\\tilde{x}$ maintains its adversarial classification\",[true]]],[931,1184,[\". Intuitively, if Radv is larger, it means the smoothed classifier is less confident about predicting the correct class for the region around $\\\\tilde{x}$, as more adversarial examples are sampled in this region, thus indicating a stronger attack on $g$.\"]]]]]],[[[\"f13159d6-fefa-47ff-a295-fdfbbab3fcf0\"],[null,0,147]],[[\"f13159d6-fefa-47ff-a295-fdfbbab3fcf0\"],[null,372,447]],[[\"f13159d6-fefa-47ff-a295-fdfbbab3fcf0\"],[null,448,812]],[[\"feb6119a-22d3-4c4f-9a6b-74a9c40f7a4b\"],[null,931,1183]]]],null,null,[[[\"f13159d6-fefa-47ff-a295-fdfbbab3fcf0\"],[null,null,0.7473042876547293,[[null,8490,9429]],[[[8490,8798,[[[8490,8798,[\"We define adversarial attacks on smoothed classifiers, as follows: Definition 1 (Adversarial attack on smoothed classifiers). For a fixed σ, α and an adversarial distance R′ ∈ R\\u003e0, we call x̃ ∈ Rn0 an adversarial attack on the smoothed classifier g at the point x ∈ Rn0 , if ‖x̃− x‖2 \\u003c R′ and g(x) 6\\u003d g(x̃). \"]]]]],[8798,9429,[[[8798,9429,[\"Similarly to generating adversarial attacks on the network f , we need to balance the adversarial distance R′ on g. If too big — the problem becomes trivial; if too small — no attacks exist. We outline the exact procedure we use to heuristically select R′ in Appendix C.4. Using the above definition, we define the strength of an attack x̃ as follows: Definition 2 (Strength of adversarial attack on smoothed classifiers). We measure the strength of an attack x̃ in terms of Radv– the radius around x̃, whose L2 ball is certified to be the same adversarial class as x̃ on the smoothed network g using CERTIFY for a chosen σ and α. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"f13159d6-fefa-47ff-a295-fdfbbab3fcf0\"]]],[[\"feb6119a-22d3-4c4f-9a6b-74a9c40f7a4b\"],[null,null,0.7102214152640758,[[null,9429,10314]],[[[9429,9815,[[[9429,9815,[\"Intuitively, this definition states that for points x̃ for which Radv is bigger, the smoothed classifier is less confident about predicting the correct class, since more adversarial examples are sampled in this region and therefore, the attack on g is stronger. We use this measure in Section 5 to compare the effectiveness of our adversarial examples to examples obtained by PGD on g. \"]]]]],[9815,9826,[[[9815,9826,[\"3 OVERVIEW \"]]]]],[9826,10314,[[[9826,10314,[\"Existing methods for generating robust adversarial examples focus on achieving empirical robustness (Qin et al., 2019; Athalye et al., 2018). In contrast, we consider provably robust adversarial examples, defined below: Definition 3 (Provably Robust Adversarial Example). We define a provably robust adversarial example to be any large connected neural network input region, defined by a set of perturbations of an input, that can be formally proven to only contain adversarial examples. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"feb6119a-22d3-4c4f-9a6b-74a9c40f7a4b\"]]]]]],[[null,null,0.7473042876547293,[[null,8490,9429]],[[[8490,8798,[[[8490,8798,[\"We define adversarial attacks on smoothed classifiers, as follows: Definition 1 (Adversarial attack on smoothed classifiers). For a fixed σ, α and an adversarial distance R′ ∈ R\\u003e0, we call x̃ ∈ Rn0 an adversarial attack on the smoothed classifier g at the point x ∈ Rn0 , if ‖x̃− x‖2 \\u003c R′ and g(x) 6\\u003d g(x̃). \"]]]]],[8798,9429,[[[8798,9429,[\"Similarly to generating adversarial attacks on the network f , we need to balance the adversarial distance R′ on g. If too big — the problem becomes trivial; if too small — no attacks exist. We outline the exact procedure we use to heuristically select R′ in Appendix C.4. Using the above definition, we define the strength of an attack x̃ as follows: Definition 2 (Strength of adversarial attack on smoothed classifiers). We measure the strength of an attack x̃ in terms of Radv– the radius around x̃, whose L2 ball is certified to be the same adversarial class as x̃ on the smoothed network g using CERTIFY for a chosen σ and α. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"f13159d6-fefa-47ff-a295-fdfbbab3fcf0\"]],[null,null,0.7102214152640758,[[null,9429,10314]],[[[9429,9815,[[[9429,9815,[\"Intuitively, this definition states that for points x̃ for which Radv is bigger, the smoothed classifier is less confident about predicting the correct class, since more adversarial examples are sampled in this region and therefore, the attack on g is stronger. We use this measure in Section 5 to compare the effectiveness of our adversarial examples to examples obtained by PGD on g. \"]]]]],[9815,9826,[[[9815,9826,[\"3 OVERVIEW \"]]]]],[9826,10314,[[[9826,10314,[\"Existing methods for generating robust adversarial examples focus on achieving empirical robustness (Qin et al., 2019; Athalye et al., 2018). In contrast, we consider provably robust adversarial examples, defined below: Definition 3 (Provably Robust Adversarial Example). We define a provably robust adversarial example to be any large connected neural network input region, defined by a set of perturbations of an input, that can be formally proven to only contain adversarial examples. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"feb6119a-22d3-4c4f-9a6b-74a9c40f7a4b\"]]],[[[null,0,147],[0]],[[null,372,447],[0]],[[null,448,812],[0]],[[null,931,1183],[1]]],[[\"What is a provably robust adversarial example?\",\"What is PARADE's primary purpose?\",\"What is the role of DeepPoly?\"]]]"]]
60
[["di",8596],["af.httprm",8595,"-8523396708763495078",54]]
27
[["e",7,null,null,23345]]
