)]}'

13091
[["wrb.fr",null,"[[\"The authors ensure that the natural language sentences produced from the \\\"ground truth\\\" activity graphs accurately describe the scene by implementing specific conventions in their Language-Refined Graph annotations [1-3].\\n\\nThese conventions include:\\n*   **Structuring predicate classes in a `[src] [predicate] [trg]` format**, which allows for straightforward conversion into natural language sentences [2]. For example, a predicate edge like `[src] talking to [trg]` from a\",null,[\"173ee47f-5d07-4380-be84-9f7631468ff4\",\"fc0523d1-dd8a-41c5-a380-499bc78978cc\",1535589725],null,[[[[0,215,[[[0,215,[\"The authors ensure that the natural language sentences produced from the \\\"ground truth\\\" activity graphs accurately describe the scene by implementing specific conventions in their Language-Refined Graph annotations.\"]]]]],[215,241,[[[215,241,[\"These conventions include:\"]]]]]],[[[\"88c302fb-7e10-4faa-b28e-08554737a826\"],[null,0,214]],[[\"77931b5f-0c8c-4e83-98c6-ab343d84f75f\"],[null,0,214]],[[\"0fca5faa-c0b0-4844-9bc0-5ba965d4b9e9\"],[null,0,214]],[[\"77931b5f-0c8c-4e83-98c6-ab343d84f75f\"],[null,306,383]]]],null,null,[[[\"88c302fb-7e10-4faa-b28e-08554737a826\"],[null,null,0.6717907866477157,[[null,5727,6290]],[[[5727,6290,[[[5727,6290,[\"Next, we introduce the MOMA-LRG dataset, a novel activity recognition dataset that leverages both the descriptive capacity of activity graphs and the expressiveness of natural language. MOMA-LRG involves videos with Multiple Objects and Multiple Actors (MOMA) and is designed to enable models to understand a broad set of human activities. To enable few-shot activity recognition with language, MOMA-LRG provides Language-Refined Graph annotations in a format that enables easy conversion from the structured graph representation into natural language sentences. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"88c302fb-7e10-4faa-b28e-08554737a826\"]]],[[\"77931b5f-0c8c-4e83-98c6-ab343d84f75f\"],[null,null,0.6888544055827637,[[null,21489,22769]],[[[21489,21637,[[[21489,21637,[\"action interaction instances, which can be further broken down into actors, objects, relationships, and attributes. Specifically, MOMA-LRG contains \"]]]]],[21637,21637,[]],[21637,21704,[[[21637,21704,[\" 104,564 actor instances (636,194 bounding boxes) from 26 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[21704,21779,[[[21704,21779,[\" 47,494 object instances (349,034 bounding boxes) from 225 unique classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,2]]],[21779,21821,[[[21779,21821,[\" 1,037,319 relationships from 52 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,3]]],[21821,21861,[[[21821,21861,[\" 704,230 relationships from 13 classes. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,4]]],[21861,22769,[[[21861,22769,[\"Language-Refined Graphs. One of MOMA-LRG’s distinguishing features is that it enables few-shot capabilities. To do this, we provide graphical annotations that are easily compatible with natural language through two conventions. First, predicate classes are of the form [src] [predicate] [trg], where src is the source entity and trg the target entity. This enables easy conversion to natural language given graphical annotations. For example, given an outgoing predicate edge with class [src] talking to [trg] from the entity cashier onto the entity customer, we can produce the sentence the cashier is talking to a customer. Second, all of our annotations are in the present continuous tense, e.g. the player is throwing a frisbee, which resembles a live narration in a fashion similar to existing video-language datasets (e.g. YouCook2 [90], HowTo100M [9], etc.) created from instructional YouTube videos. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"77931b5f-0c8c-4e83-98c6-ab343d84f75f\"]]],[[\"0fca5faa-c0b0-4844-9bc0-5ba965d4b9e9\"],[null,null,0.6933049670544629,[[null,31671,32566]],[[[31671,31697,[[[31671,31697,[\"4.3 GraphVLM: Text Stream \"]]]]],[31697,31890,[[[31697,31890,[\"In order to effectively leverage the natural language capabilities of VLMs, we convert all levels of the MOMA-LRG activity graph hierarchy to natural language via our graph-to-language module. \"]]]]],[31890,32566,[[[31890,32566,[\"Graph-to-language module. At the activity level, each class name is a noun, thus it can be represented by its class name or via prompting (e.g. by prepending \\\"A video of [CLS_NAME]\\\"). At the sub-activity level, class names are descriptions of the sub-activities in the present continuous tense (narration-style). At the atomic action level, we tag all predicates with [src], and [trg] templates to allow for easy conversion into a full grammatically correct sentence in its present continuous form. For example, the predicate touching is represented as [src] touching [trg]. So, given the entities [src]\\u003dperson and [trg]\\u003dtable, the sentence is A person is touching the table. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"0fca5faa-c0b0-4844-9bc0-5ba965d4b9e9\"]]]]]],[[null,null,0.6717907866477157,[[null,5727,6290]],[[[5727,6290,[[[5727,6290,[\"Next, we introduce the MOMA-LRG dataset, a novel activity recognition dataset that leverages both the descriptive capacity of activity graphs and the expressiveness of natural language. MOMA-LRG involves videos with Multiple Objects and Multiple Actors (MOMA) and is designed to enable models to understand a broad set of human activities. To enable few-shot activity recognition with language, MOMA-LRG provides Language-Refined Graph annotations in a format that enables easy conversion from the structured graph representation into natural language sentences. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"88c302fb-7e10-4faa-b28e-08554737a826\"]],[null,null,0.6888544055827637,[[null,21489,22769]],[[[21489,21637,[[[21489,21637,[\"action interaction instances, which can be further broken down into actors, objects, relationships, and attributes. Specifically, MOMA-LRG contains \"]]]]],[21637,21637,[]],[21637,21704,[[[21637,21704,[\" 104,564 actor instances (636,194 bounding boxes) from 26 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[21704,21779,[[[21704,21779,[\" 47,494 object instances (349,034 bounding boxes) from 225 unique classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,2]]],[21779,21821,[[[21779,21821,[\" 1,037,319 relationships from 52 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,3]]],[21821,21861,[[[21821,21861,[\" 704,230 relationships from 13 classes. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,4]]],[21861,22769,[[[21861,22769,[\"Language-Refined Graphs. One of MOMA-LRG’s distinguishing features is that it enables few-shot capabilities. To do this, we provide graphical annotations that are easily compatible with natural language through two conventions. First, predicate classes are of the form [src] [predicate] [trg], where src is the source entity and trg the target entity. This enables easy conversion to natural language given graphical annotations. For example, given an outgoing predicate edge with class [src] talking to [trg] from the entity cashier onto the entity customer, we can produce the sentence the cashier is talking to a customer. Second, all of our annotations are in the present continuous tense, e.g. the player is throwing a frisbee, which resembles a live narration in a fashion similar to existing video-language datasets (e.g. YouCook2 [90], HowTo100M [9], etc.) created from instructional YouTube videos. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"77931b5f-0c8c-4e83-98c6-ab343d84f75f\"]],[null,null,0.6933049670544629,[[null,31671,32566]],[[[31671,31697,[[[31671,31697,[\"4.3 GraphVLM: Text Stream \"]]]]],[31697,31890,[[[31697,31890,[\"In order to effectively leverage the natural language capabilities of VLMs, we convert all levels of the MOMA-LRG activity graph hierarchy to natural language via our graph-to-language module. \"]]]]],[31890,32566,[[[31890,32566,[\"Graph-to-language module. At the activity level, each class name is a noun, thus it can be represented by its class name or via prompting (e.g. by prepending \\\"A video of [CLS_NAME]\\\"). At the sub-activity level, class names are descriptions of the sub-activities in the present continuous tense (narration-style). At the atomic action level, we tag all predicates with [src], and [trg] templates to allow for easy conversion into a full grammatically correct sentence in its present continuous form. For example, the predicate touching is represented as [src] touching [trg]. So, given the entities [src]\\u003dperson and [trg]\\u003dtable, the sentence is A person is touching the table. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"0fca5faa-c0b0-4844-9bc0-5ba965d4b9e9\"]]],[[[null,0,214],[0,1,2]],[[null,306,383],[1]]],[[\"How is an activity graph defined?\",\"What are the three hierarchy levels?\",\"How does MOMA-LRG compare with MOMA?\"]]]"]]
15764
[["wrb.fr",null,"[[\"The authors ensure that the natural language sentences produced from the \\\"ground truth\\\" activity graphs accurately describe the scene by implementing specific conventions in their Language-Refined Graph annotations [1-3].\\n\\nThese conventions include:\\n*   **Structuring predicate classes in a `[src] [predicate] [trg]` format**, which allows for straightforward conversion into natural language sentences [2]. For example, a predicate edge like `[src] talking to [trg]` from a cashier to a customer can be easily converted to \\\"the cashier is talking to a customer\\\" [2].\\n*   **Annotating all activity, sub-activity, and atomic action levels in the present continuous tense**, resembling a live narration style, similar to existing video-language datasets [2, 3].\\n*   **Tagging atomic action predicates with `[src]` and `[trg]` templates** to enable their conversion into full, grammatically correct sentences in the present continuous form [3]. For instance, a predicate \\\"touching\\\" with `[src]\\u003dperson` and `[trg]\\u003dtable`\",null,[\"173ee47f-5d07-4380-be84-9f7631468ff4\",\"fc0523d1-dd8a-41c5-a380-499bc78978cc\",1535589725],null,[[[[0,215,[[[0,215,[\"The authors ensure that the natural language sentences produced from the \\\"ground truth\\\" activity graphs accurately describe the scene by implementing specific conventions in their Language-Refined Graph annotations.\"]]]]],[215,241,[[[215,241,[\"These conventions include:\"]]]]],[241,538,[[[241,276,[\"Structuring predicate classes in a \",[true]]],[276,299,[\"[src] [predicate] [trg]\",[true,null,null,null,null,null,null,true]]],[299,306,[\" format\",[true]]],[306,420,[\", which allows for straightforward conversion into natural language sentences. For example, a predicate edge like \"]],[420,442,[\"[src] talking to [trg]\",[null,null,null,null,null,null,null,true]]],[442,538,[\" from a cashier to a customer can be easily converted to \\\"the cashier is talking to a customer\\\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[538,714,[[[538,633,[\"Annotating all activity, sub-activity, and atomic action levels in the present continuous tense\",[true]]],[633,714,[\", resembling a live narration style, similar to existing video-language datasets.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]]],[[[\"cd2372d8-42f0-4693-aaad-719eee732f7e\"],[null,0,214]],[[\"dbeb364c-e83e-4ccf-be76-604e63e41399\"],[null,0,214]],[[\"ed176e92-c9b1-494f-a720-1a867eb3b584\"],[null,0,214]],[[\"dbeb364c-e83e-4ccf-be76-604e63e41399\"],[null,306,383]],[[\"dbeb364c-e83e-4ccf-be76-604e63e41399\"],[null,443,537]],[[\"dbeb364c-e83e-4ccf-be76-604e63e41399\"],[null,633,713]],[[\"ed176e92-c9b1-494f-a720-1a867eb3b584\"],[null,633,713]],[[\"ed176e92-c9b1-494f-a720-1a867eb3b584\"],[null,778,878]]]],null,null,[[[\"cd2372d8-42f0-4693-aaad-719eee732f7e\"],[null,null,0.6717907866477157,[[null,5727,6290]],[[[5727,6290,[[[5727,6290,[\"Next, we introduce the MOMA-LRG dataset, a novel activity recognition dataset that leverages both the descriptive capacity of activity graphs and the expressiveness of natural language. MOMA-LRG involves videos with Multiple Objects and Multiple Actors (MOMA) and is designed to enable models to understand a broad set of human activities. To enable few-shot activity recognition with language, MOMA-LRG provides Language-Refined Graph annotations in a format that enables easy conversion from the structured graph representation into natural language sentences. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"cd2372d8-42f0-4693-aaad-719eee732f7e\"]]],[[\"dbeb364c-e83e-4ccf-be76-604e63e41399\"],[null,null,0.6888544055827637,[[null,21489,22769]],[[[21489,21637,[[[21489,21637,[\"action interaction instances, which can be further broken down into actors, objects, relationships, and attributes. Specifically, MOMA-LRG contains \"]]]]],[21637,21637,[]],[21637,21704,[[[21637,21704,[\" 104,564 actor instances (636,194 bounding boxes) from 26 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[21704,21779,[[[21704,21779,[\" 47,494 object instances (349,034 bounding boxes) from 225 unique classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,2]]],[21779,21821,[[[21779,21821,[\" 1,037,319 relationships from 52 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,3]]],[21821,21861,[[[21821,21861,[\" 704,230 relationships from 13 classes. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,4]]],[21861,22769,[[[21861,22769,[\"Language-Refined Graphs. One of MOMA-LRG’s distinguishing features is that it enables few-shot capabilities. To do this, we provide graphical annotations that are easily compatible with natural language through two conventions. First, predicate classes are of the form [src] [predicate] [trg], where src is the source entity and trg the target entity. This enables easy conversion to natural language given graphical annotations. For example, given an outgoing predicate edge with class [src] talking to [trg] from the entity cashier onto the entity customer, we can produce the sentence the cashier is talking to a customer. Second, all of our annotations are in the present continuous tense, e.g. the player is throwing a frisbee, which resembles a live narration in a fashion similar to existing video-language datasets (e.g. YouCook2 [90], HowTo100M [9], etc.) created from instructional YouTube videos. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"dbeb364c-e83e-4ccf-be76-604e63e41399\"]]],[[\"ed176e92-c9b1-494f-a720-1a867eb3b584\"],[null,null,0.6933049670544629,[[null,31671,32566]],[[[31671,31697,[[[31671,31697,[\"4.3 GraphVLM: Text Stream \"]]]]],[31697,31890,[[[31697,31890,[\"In order to effectively leverage the natural language capabilities of VLMs, we convert all levels of the MOMA-LRG activity graph hierarchy to natural language via our graph-to-language module. \"]]]]],[31890,32566,[[[31890,32566,[\"Graph-to-language module. At the activity level, each class name is a noun, thus it can be represented by its class name or via prompting (e.g. by prepending \\\"A video of [CLS_NAME]\\\"). At the sub-activity level, class names are descriptions of the sub-activities in the present continuous tense (narration-style). At the atomic action level, we tag all predicates with [src], and [trg] templates to allow for easy conversion into a full grammatically correct sentence in its present continuous form. For example, the predicate touching is represented as [src] touching [trg]. So, given the entities [src]\\u003dperson and [trg]\\u003dtable, the sentence is A person is touching the table. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"ed176e92-c9b1-494f-a720-1a867eb3b584\"]]]]]],[[null,null,0.6717907866477157,[[null,5727,6290]],[[[5727,6290,[[[5727,6290,[\"Next, we introduce the MOMA-LRG dataset, a novel activity recognition dataset that leverages both the descriptive capacity of activity graphs and the expressiveness of natural language. MOMA-LRG involves videos with Multiple Objects and Multiple Actors (MOMA) and is designed to enable models to understand a broad set of human activities. To enable few-shot activity recognition with language, MOMA-LRG provides Language-Refined Graph annotations in a format that enables easy conversion from the structured graph representation into natural language sentences. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"cd2372d8-42f0-4693-aaad-719eee732f7e\"]],[null,null,0.6888544055827637,[[null,21489,22769]],[[[21489,21637,[[[21489,21637,[\"action interaction instances, which can be further broken down into actors, objects, relationships, and attributes. Specifically, MOMA-LRG contains \"]]]]],[21637,21637,[]],[21637,21704,[[[21637,21704,[\" 104,564 actor instances (636,194 bounding boxes) from 26 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[21704,21779,[[[21704,21779,[\" 47,494 object instances (349,034 bounding boxes) from 225 unique classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,2]]],[21779,21821,[[[21779,21821,[\" 1,037,319 relationships from 52 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,3]]],[21821,21861,[[[21821,21861,[\" 704,230 relationships from 13 classes. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,4]]],[21861,22769,[[[21861,22769,[\"Language-Refined Graphs. One of MOMA-LRG’s distinguishing features is that it enables few-shot capabilities. To do this, we provide graphical annotations that are easily compatible with natural language through two conventions. First, predicate classes are of the form [src] [predicate] [trg], where src is the source entity and trg the target entity. This enables easy conversion to natural language given graphical annotations. For example, given an outgoing predicate edge with class [src] talking to [trg] from the entity cashier onto the entity customer, we can produce the sentence the cashier is talking to a customer. Second, all of our annotations are in the present continuous tense, e.g. the player is throwing a frisbee, which resembles a live narration in a fashion similar to existing video-language datasets (e.g. YouCook2 [90], HowTo100M [9], etc.) created from instructional YouTube videos. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"dbeb364c-e83e-4ccf-be76-604e63e41399\"]],[null,null,0.6933049670544629,[[null,31671,32566]],[[[31671,31697,[[[31671,31697,[\"4.3 GraphVLM: Text Stream \"]]]]],[31697,31890,[[[31697,31890,[\"In order to effectively leverage the natural language capabilities of VLMs, we convert all levels of the MOMA-LRG activity graph hierarchy to natural language via our graph-to-language module. \"]]]]],[31890,32566,[[[31890,32566,[\"Graph-to-language module. At the activity level, each class name is a noun, thus it can be represented by its class name or via prompting (e.g. by prepending \\\"A video of [CLS_NAME]\\\"). At the sub-activity level, class names are descriptions of the sub-activities in the present continuous tense (narration-style). At the atomic action level, we tag all predicates with [src], and [trg] templates to allow for easy conversion into a full grammatically correct sentence in its present continuous form. For example, the predicate touching is represented as [src] touching [trg]. So, given the entities [src]\\u003dperson and [trg]\\u003dtable, the sentence is A person is touching the table. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"ed176e92-c9b1-494f-a720-1a867eb3b584\"]]],[[[null,0,214],[0,1,2]],[[null,306,383],[1]],[[null,443,537],[1]],[[null,633,713],[1,2]],[[null,778,878],[2]]],[[\"How is an activity graph defined?\",\"What are the three hierarchy levels?\",\"How does MOMA-LRG compare with MOMA?\"]]]"]]
15916
[["wrb.fr",null,"[[\"The authors ensure that the natural language sentences produced from the \\\"ground truth\\\" activity graphs accurately describe the scene by implementing specific conventions in their Language-Refined Graph annotations [1-3].\\n\\nThese conventions include:\\n*   **Structuring predicate classes in a `[src] [predicate] [trg]` format**, which allows for straightforward conversion into natural language sentences [2]. For example, a predicate edge like `[src] talking to [trg]` from a cashier to a customer can be easily converted to \\\"the cashier is talking to a customer\\\" [2].\\n*   **Annotating all activity, sub-activity, and atomic action levels in the present continuous tense**, resembling a live narration style, similar to existing video-language datasets [2, 3].\\n*   **Tagging atomic action predicates with `[src]` and `[trg]` templates** to enable their conversion into full, grammatically correct sentences in the present continuous form [3]. For instance, a predicate \\\"touching\\\" with `[src]\\u003dperson` and `[trg]\\u003dtable` converts to the sentence \\\"A person is touching the table\\\" [3].\",null,[\"173ee47f-5d07-4380-be84-9f7631468ff4\",\"fc0523d1-dd8a-41c5-a380-499bc78978cc\",1535589725],null,[[[[0,215,[[[0,215,[\"The authors ensure that the natural language sentences produced from the \\\"ground truth\\\" activity graphs accurately describe the scene by implementing specific conventions in their Language-Refined Graph annotations.\"]]]]],[215,241,[[[215,241,[\"These conventions include:\"]]]]],[241,538,[[[241,276,[\"Structuring predicate classes in a \",[true]]],[276,299,[\"[src] [predicate] [trg]\",[true,null,null,null,null,null,null,true]]],[299,306,[\" format\",[true]]],[306,420,[\", which allows for straightforward conversion into natural language sentences. For example, a predicate edge like \"]],[420,442,[\"[src] talking to [trg]\",[null,null,null,null,null,null,null,true]]],[442,538,[\" from a cashier to a customer can be easily converted to \\\"the cashier is talking to a customer\\\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[538,714,[[[538,633,[\"Annotating all activity, sub-activity, and atomic action levels in the present continuous tense\",[true]]],[633,714,[\", resembling a live narration style, similar to existing video-language datasets.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]]],[[[\"b026144b-41e8-41fd-97f7-0dd595120ee1\"],[null,0,214]],[[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"],[null,0,214]],[[\"11890c69-a264-47e1-af77-87179ffaf1bd\"],[null,0,214]],[[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"],[null,306,383]],[[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"],[null,443,537]],[[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"],[null,633,713]],[[\"11890c69-a264-47e1-af77-87179ffaf1bd\"],[null,633,713]],[[\"11890c69-a264-47e1-af77-87179ffaf1bd\"],[null,778,878]],[[\"11890c69-a264-47e1-af77-87179ffaf1bd\"],[null,951,1008]]]],null,null,[[[\"b026144b-41e8-41fd-97f7-0dd595120ee1\"],[null,null,0.6717907866477157,[[null,5727,6290]],[[[5727,6290,[[[5727,6290,[\"Next, we introduce the MOMA-LRG dataset, a novel activity recognition dataset that leverages both the descriptive capacity of activity graphs and the expressiveness of natural language. MOMA-LRG involves videos with Multiple Objects and Multiple Actors (MOMA) and is designed to enable models to understand a broad set of human activities. To enable few-shot activity recognition with language, MOMA-LRG provides Language-Refined Graph annotations in a format that enables easy conversion from the structured graph representation into natural language sentences. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"b026144b-41e8-41fd-97f7-0dd595120ee1\"]]],[[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"],[null,null,0.6888544055827637,[[null,21489,22769]],[[[21489,21637,[[[21489,21637,[\"action interaction instances, which can be further broken down into actors, objects, relationships, and attributes. Specifically, MOMA-LRG contains \"]]]]],[21637,21637,[]],[21637,21704,[[[21637,21704,[\" 104,564 actor instances (636,194 bounding boxes) from 26 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[21704,21779,[[[21704,21779,[\" 47,494 object instances (349,034 bounding boxes) from 225 unique classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,2]]],[21779,21821,[[[21779,21821,[\" 1,037,319 relationships from 52 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,3]]],[21821,21861,[[[21821,21861,[\" 704,230 relationships from 13 classes. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,4]]],[21861,22769,[[[21861,22769,[\"Language-Refined Graphs. One of MOMA-LRG’s distinguishing features is that it enables few-shot capabilities. To do this, we provide graphical annotations that are easily compatible with natural language through two conventions. First, predicate classes are of the form [src] [predicate] [trg], where src is the source entity and trg the target entity. This enables easy conversion to natural language given graphical annotations. For example, given an outgoing predicate edge with class [src] talking to [trg] from the entity cashier onto the entity customer, we can produce the sentence the cashier is talking to a customer. Second, all of our annotations are in the present continuous tense, e.g. the player is throwing a frisbee, which resembles a live narration in a fashion similar to existing video-language datasets (e.g. YouCook2 [90], HowTo100M [9], etc.) created from instructional YouTube videos. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"]]],[[\"11890c69-a264-47e1-af77-87179ffaf1bd\"],[null,null,0.6933049670544629,[[null,31671,32566]],[[[31671,31697,[[[31671,31697,[\"4.3 GraphVLM: Text Stream \"]]]]],[31697,31890,[[[31697,31890,[\"In order to effectively leverage the natural language capabilities of VLMs, we convert all levels of the MOMA-LRG activity graph hierarchy to natural language via our graph-to-language module. \"]]]]],[31890,32566,[[[31890,32566,[\"Graph-to-language module. At the activity level, each class name is a noun, thus it can be represented by its class name or via prompting (e.g. by prepending \\\"A video of [CLS_NAME]\\\"). At the sub-activity level, class names are descriptions of the sub-activities in the present continuous tense (narration-style). At the atomic action level, we tag all predicates with [src], and [trg] templates to allow for easy conversion into a full grammatically correct sentence in its present continuous form. For example, the predicate touching is represented as [src] touching [trg]. So, given the entities [src]\\u003dperson and [trg]\\u003dtable, the sentence is A person is touching the table. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"11890c69-a264-47e1-af77-87179ffaf1bd\"]]]]]],[[null,null,0.6717907866477157,[[null,5727,6290]],[[[5727,6290,[[[5727,6290,[\"Next, we introduce the MOMA-LRG dataset, a novel activity recognition dataset that leverages both the descriptive capacity of activity graphs and the expressiveness of natural language. MOMA-LRG involves videos with Multiple Objects and Multiple Actors (MOMA) and is designed to enable models to understand a broad set of human activities. To enable few-shot activity recognition with language, MOMA-LRG provides Language-Refined Graph annotations in a format that enables easy conversion from the structured graph representation into natural language sentences. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"b026144b-41e8-41fd-97f7-0dd595120ee1\"]],[null,null,0.6888544055827637,[[null,21489,22769]],[[[21489,21637,[[[21489,21637,[\"action interaction instances, which can be further broken down into actors, objects, relationships, and attributes. Specifically, MOMA-LRG contains \"]]]]],[21637,21637,[]],[21637,21704,[[[21637,21704,[\" 104,564 actor instances (636,194 bounding boxes) from 26 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[21704,21779,[[[21704,21779,[\" 47,494 object instances (349,034 bounding boxes) from 225 unique classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,2]]],[21779,21821,[[[21779,21821,[\" 1,037,319 relationships from 52 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,3]]],[21821,21861,[[[21821,21861,[\" 704,230 relationships from 13 classes. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,4]]],[21861,22769,[[[21861,22769,[\"Language-Refined Graphs. One of MOMA-LRG’s distinguishing features is that it enables few-shot capabilities. To do this, we provide graphical annotations that are easily compatible with natural language through two conventions. First, predicate classes are of the form [src] [predicate] [trg], where src is the source entity and trg the target entity. This enables easy conversion to natural language given graphical annotations. For example, given an outgoing predicate edge with class [src] talking to [trg] from the entity cashier onto the entity customer, we can produce the sentence the cashier is talking to a customer. Second, all of our annotations are in the present continuous tense, e.g. the player is throwing a frisbee, which resembles a live narration in a fashion similar to existing video-language datasets (e.g. YouCook2 [90], HowTo100M [9], etc.) created from instructional YouTube videos. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"]],[null,null,0.6933049670544629,[[null,31671,32566]],[[[31671,31697,[[[31671,31697,[\"4.3 GraphVLM: Text Stream \"]]]]],[31697,31890,[[[31697,31890,[\"In order to effectively leverage the natural language capabilities of VLMs, we convert all levels of the MOMA-LRG activity graph hierarchy to natural language via our graph-to-language module. \"]]]]],[31890,32566,[[[31890,32566,[\"Graph-to-language module. At the activity level, each class name is a noun, thus it can be represented by its class name or via prompting (e.g. by prepending \\\"A video of [CLS_NAME]\\\"). At the sub-activity level, class names are descriptions of the sub-activities in the present continuous tense (narration-style). At the atomic action level, we tag all predicates with [src], and [trg] templates to allow for easy conversion into a full grammatically correct sentence in its present continuous form. For example, the predicate touching is represented as [src] touching [trg]. So, given the entities [src]\\u003dperson and [trg]\\u003dtable, the sentence is A person is touching the table. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"11890c69-a264-47e1-af77-87179ffaf1bd\"]]],[[[null,0,214],[0,1,2]],[[null,306,383],[1]],[[null,443,537],[1]],[[null,633,713],[1,2]],[[null,778,878],[2]],[[null,951,1008],[2]]],[[\"How is an activity graph defined?\",\"What are the three hierarchy levels?\",\"How does MOMA-LRG compare with MOMA?\"]]]"]]
17135
[["wrb.fr",null,"[[\"The authors ensure that the natural language sentences produced from the \\\"ground truth\\\" activity graphs accurately describe the scene by implementing specific conventions in their Language-Refined Graph annotations [1-3].\\n\\nThese conventions include:\\n*   **Structuring predicate classes in a `[src] [predicate] [trg]` format**, which allows for straightforward conversion into natural language sentences [2]. For example, a predicate edge like `[src] talking to [trg]` from a cashier to a customer can be easily converted to \\\"the cashier is talking to a customer\\\" [2].\\n*   **Annotating all activity, sub-activity, and atomic action levels in the present continuous tense**, resembling a live narration style, similar to existing video-language datasets [2, 3].\\n*   **Tagging atomic action predicates with `[src]` and `[trg]` templates** to enable their conversion into full, grammatically correct sentences in the present continuous form [3]. For instance, a predicate \\\"touching\\\" with `[src]\\u003dperson` and `[trg]\\u003dtable` converts to the sentence \\\"A person is touching the table\\\" [3].\",null,[\"173ee47f-5d07-4380-be84-9f7631468ff4\",\"fc0523d1-dd8a-41c5-a380-499bc78978cc\",1535589725],null,[[[[0,215,[[[0,215,[\"The authors ensure that the natural language sentences produced from the \\\"ground truth\\\" activity graphs accurately describe the scene by implementing specific conventions in their Language-Refined Graph annotations.\"]]]]],[215,241,[[[215,241,[\"These conventions include:\"]]]]],[241,538,[[[241,276,[\"Structuring predicate classes in a \",[true]]],[276,299,[\"[src] [predicate] [trg]\",[true,null,null,null,null,null,null,true]]],[299,306,[\" format\",[true]]],[306,420,[\", which allows for straightforward conversion into natural language sentences. For example, a predicate edge like \"]],[420,442,[\"[src] talking to [trg]\",[null,null,null,null,null,null,null,true]]],[442,538,[\" from a cashier to a customer can be easily converted to \\\"the cashier is talking to a customer\\\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[538,714,[[[538,633,[\"Annotating all activity, sub-activity, and atomic action levels in the present continuous tense\",[true]]],[633,714,[\", resembling a live narration style, similar to existing video-language datasets.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[714,1009,[[[714,752,[\"Tagging atomic action predicates with \",[true]]],[752,757,[\"[src]\",[true,null,null,null,null,null,null,true]]],[757,762,[\" and \",[true]]],[762,767,[\"[trg]\",[true,null,null,null,null,null,null,true]]],[767,777,[\" templates\",[true]]],[777,922,[\" to enable their conversion into full, grammatically correct sentences in the present continuous form. For instance, a predicate \\\"touching\\\" with \"]],[922,934,[\"[src]\\u003dperson\",[null,null,null,null,null,null,null,true]]],[934,939,[\" and \"]],[939,950,[\"[trg]\\u003dtable\",[null,null,null,null,null,null,null,true]]],[950,1009,[\" converts to the sentence \\\"A person is touching the table\\\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]]],[[[\"b026144b-41e8-41fd-97f7-0dd595120ee1\"],[null,0,214]],[[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"],[null,0,214]],[[\"11890c69-a264-47e1-af77-87179ffaf1bd\"],[null,0,214]],[[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"],[null,306,383]],[[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"],[null,443,537]],[[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"],[null,633,713]],[[\"11890c69-a264-47e1-af77-87179ffaf1bd\"],[null,633,713]],[[\"11890c69-a264-47e1-af77-87179ffaf1bd\"],[null,778,878]],[[\"11890c69-a264-47e1-af77-87179ffaf1bd\"],[null,951,1008]]]],null,null,[[[\"b026144b-41e8-41fd-97f7-0dd595120ee1\"],[null,null,0.6717907866477157,[[null,5727,6290]],[[[5727,6290,[[[5727,6290,[\"Next, we introduce the MOMA-LRG dataset, a novel activity recognition dataset that leverages both the descriptive capacity of activity graphs and the expressiveness of natural language. MOMA-LRG involves videos with Multiple Objects and Multiple Actors (MOMA) and is designed to enable models to understand a broad set of human activities. To enable few-shot activity recognition with language, MOMA-LRG provides Language-Refined Graph annotations in a format that enables easy conversion from the structured graph representation into natural language sentences. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"b026144b-41e8-41fd-97f7-0dd595120ee1\"]]],[[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"],[null,null,0.6888544055827637,[[null,21489,22769]],[[[21489,21637,[[[21489,21637,[\"action interaction instances, which can be further broken down into actors, objects, relationships, and attributes. Specifically, MOMA-LRG contains \"]]]]],[21637,21637,[]],[21637,21704,[[[21637,21704,[\" 104,564 actor instances (636,194 bounding boxes) from 26 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[21704,21779,[[[21704,21779,[\" 47,494 object instances (349,034 bounding boxes) from 225 unique classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,2]]],[21779,21821,[[[21779,21821,[\" 1,037,319 relationships from 52 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,3]]],[21821,21861,[[[21821,21861,[\" 704,230 relationships from 13 classes. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,4]]],[21861,22769,[[[21861,22769,[\"Language-Refined Graphs. One of MOMA-LRG’s distinguishing features is that it enables few-shot capabilities. To do this, we provide graphical annotations that are easily compatible with natural language through two conventions. First, predicate classes are of the form [src] [predicate] [trg], where src is the source entity and trg the target entity. This enables easy conversion to natural language given graphical annotations. For example, given an outgoing predicate edge with class [src] talking to [trg] from the entity cashier onto the entity customer, we can produce the sentence the cashier is talking to a customer. Second, all of our annotations are in the present continuous tense, e.g. the player is throwing a frisbee, which resembles a live narration in a fashion similar to existing video-language datasets (e.g. YouCook2 [90], HowTo100M [9], etc.) created from instructional YouTube videos. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"]]],[[\"11890c69-a264-47e1-af77-87179ffaf1bd\"],[null,null,0.6933049670544629,[[null,31671,32566]],[[[31671,31697,[[[31671,31697,[\"4.3 GraphVLM: Text Stream \"]]]]],[31697,31890,[[[31697,31890,[\"In order to effectively leverage the natural language capabilities of VLMs, we convert all levels of the MOMA-LRG activity graph hierarchy to natural language via our graph-to-language module. \"]]]]],[31890,32566,[[[31890,32566,[\"Graph-to-language module. At the activity level, each class name is a noun, thus it can be represented by its class name or via prompting (e.g. by prepending \\\"A video of [CLS_NAME]\\\"). At the sub-activity level, class names are descriptions of the sub-activities in the present continuous tense (narration-style). At the atomic action level, we tag all predicates with [src], and [trg] templates to allow for easy conversion into a full grammatically correct sentence in its present continuous form. For example, the predicate touching is represented as [src] touching [trg]. So, given the entities [src]\\u003dperson and [trg]\\u003dtable, the sentence is A person is touching the table. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"11890c69-a264-47e1-af77-87179ffaf1bd\"]]]]]],[[null,null,0.6717907866477157,[[null,5727,6290]],[[[5727,6290,[[[5727,6290,[\"Next, we introduce the MOMA-LRG dataset, a novel activity recognition dataset that leverages both the descriptive capacity of activity graphs and the expressiveness of natural language. MOMA-LRG involves videos with Multiple Objects and Multiple Actors (MOMA) and is designed to enable models to understand a broad set of human activities. To enable few-shot activity recognition with language, MOMA-LRG provides Language-Refined Graph annotations in a format that enables easy conversion from the structured graph representation into natural language sentences. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"b026144b-41e8-41fd-97f7-0dd595120ee1\"]],[null,null,0.6888544055827637,[[null,21489,22769]],[[[21489,21637,[[[21489,21637,[\"action interaction instances, which can be further broken down into actors, objects, relationships, and attributes. Specifically, MOMA-LRG contains \"]]]]],[21637,21637,[]],[21637,21704,[[[21637,21704,[\" 104,564 actor instances (636,194 bounding boxes) from 26 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[21704,21779,[[[21704,21779,[\" 47,494 object instances (349,034 bounding boxes) from 225 unique classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,2]]],[21779,21821,[[[21779,21821,[\" 1,037,319 relationships from 52 classes; \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,3]]],[21821,21861,[[[21821,21861,[\" 704,230 relationships from 13 classes. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,4]]],[21861,22769,[[[21861,22769,[\"Language-Refined Graphs. One of MOMA-LRG’s distinguishing features is that it enables few-shot capabilities. To do this, we provide graphical annotations that are easily compatible with natural language through two conventions. First, predicate classes are of the form [src] [predicate] [trg], where src is the source entity and trg the target entity. This enables easy conversion to natural language given graphical annotations. For example, given an outgoing predicate edge with class [src] talking to [trg] from the entity cashier onto the entity customer, we can produce the sentence the cashier is talking to a customer. Second, all of our annotations are in the present continuous tense, e.g. the player is throwing a frisbee, which resembles a live narration in a fashion similar to existing video-language datasets (e.g. YouCook2 [90], HowTo100M [9], etc.) created from instructional YouTube videos. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"bcb77b08-9dd6-468c-8ade-c15918f96817\"]],[null,null,0.6933049670544629,[[null,31671,32566]],[[[31671,31697,[[[31671,31697,[\"4.3 GraphVLM: Text Stream \"]]]]],[31697,31890,[[[31697,31890,[\"In order to effectively leverage the natural language capabilities of VLMs, we convert all levels of the MOMA-LRG activity graph hierarchy to natural language via our graph-to-language module. \"]]]]],[31890,32566,[[[31890,32566,[\"Graph-to-language module. At the activity level, each class name is a noun, thus it can be represented by its class name or via prompting (e.g. by prepending \\\"A video of [CLS_NAME]\\\"). At the sub-activity level, class names are descriptions of the sub-activities in the present continuous tense (narration-style). At the atomic action level, we tag all predicates with [src], and [trg] templates to allow for easy conversion into a full grammatically correct sentence in its present continuous form. For example, the predicate touching is represented as [src] touching [trg]. So, given the entities [src]\\u003dperson and [trg]\\u003dtable, the sentence is A person is touching the table. \"]]]]]]],[[[\"e8a79661-d834-479e-9373-35c911921033\"],\"6d2b9015-1c56-4e23-b198-76016b99e57b\"]],[\"11890c69-a264-47e1-af77-87179ffaf1bd\"]]],[[[null,0,214],[0,1,2]],[[null,306,383],[1]],[[null,443,537],[1]],[[null,633,713],[1,2]],[[null,778,878],[2]],[[null,951,1008],[2]]],[[\"How is an activity graph defined?\",\"What are the three hierarchy levels?\",\"How does MOMA-LRG compare with MOMA?\"]]]"]]
60
[["di",6309],["af.httprm",6308,"-8724955427064637023",55]]
27
[["e",7,null,null,62117]]
