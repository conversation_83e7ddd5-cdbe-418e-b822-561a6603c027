)]}'

3342
[["wrb.fr",null,"[[\"The \\\"real-world pushing task\\\" involves using a **UR5 robotic arm and an RGB camera** to push a correct object to a **pink target area** [1]. The images for this task, taken from both human and robot domains, have unique perspectives [1].\\n\\nFor evaluation, 8 novel objects were used, and the task was deemed successful if the robot pushed the target object **within the center of the pink region** [1]. Each object was tested 20\",null,[\"aaf18d1b-7a9e-4a19-aac9-2ea7c9842a6e\",\"4d951fe8-0867-45c0-874e-3275d7f03aa4\",3090253456],null,[[[[0,221,[[[0,47,[\"The \\\"real-world pushing task\\\" involves using a \"]],[47,80,[\"UR5 robotic arm and an RGB camera\",[true]]],[80,111,[\" to push a correct object to a \"]],[111,127,[\"pink target area\",[true]]],[127,221,[\". The images for this task, taken from both human and robot domains, have unique perspectives.\"]]]]]],[[[\"b7fb76ce-006f-49ec-9d06-7682b8eb8512\"],[null,127,127]],[[\"b7fb76ce-006f-49ec-9d06-7682b8eb8512\"],[null,127,220]],[[\"b7fb76ce-006f-49ec-9d06-7682b8eb8512\"],[null,373,373]]]],null,null,[[[\"b7fb76ce-006f-49ec-9d06-7682b8eb8512\"],[null,null,0.713884600169177,[[null,31011,31771]],[[[31011,31034,[[[31011,31034,[\"5.2 REAL-WORLD PUSHING \"]]]]],[31034,31771,[[[31034,31771,[\"We designed the real-world pushing environment using a UR5 robotic arm and RGB camera. The goal is to push the correct object to the pink target area. The sample videos are shown in Figure 2. Images taken from the human and robot domains have unique perspectives. For meta-training, we collected a dataset with 11 objects, consisting of 550 human demonstrations, 550 robot random videos and 110 human-robot paired videos. For DAML, random videos are replaced by 550 UR5 demonstrations corresponding to humans (include images and actions). During the evaluation, we provided 8 novel objects. The task is considered successful if the robot pushes the target object within the center of pink region. We tested each of the objects 20 times. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"b7fb76ce-006f-49ec-9d06-7682b8eb8512\"]]]]]],[[null,null,0.713884600169177,[[null,31011,31771]],[[[31011,31034,[[[31011,31034,[\"5.2 REAL-WORLD PUSHING \"]]]]],[31034,31771,[[[31034,31771,[\"We designed the real-world pushing environment using a UR5 robotic arm and RGB camera. The goal is to push the correct object to the pink target area. The sample videos are shown in Figure 2. Images taken from the human and robot domains have unique perspectives. For meta-training, we collected a dataset with 11 objects, consisting of 550 human demonstrations, 550 robot random videos and 110 human-robot paired videos. For DAML, random videos are replaced by 550 UR5 demonstrations corresponding to humans (include images and actions). During the evaluation, we provided 8 novel objects. The task is considered successful if the robot pushes the target object within the center of pink region. We tested each of the objects 20 times. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"b7fb76ce-006f-49ec-9d06-7682b8eb8512\"]]],[[[null,127,127],[0]],[[null,127,220],[0]],[[null,373,373],[0]]],[[\"What is A-CycleGAN?\",\"What is the purpose of SSIM?\",\"What is MILV?\"]]]"]]
8419
[["wrb.fr",null,"[[\"The \\\"real-world pushing task\\\" involves using a **UR5 robotic arm and an RGB camera** to push a correct object to a **pink target area** [1]. The images for this task, taken from both human and robot domains, have unique perspectives [1].\\n\\nFor evaluation, 8 novel objects were used, and the task was deemed successful if the robot pushed the target object **within the center of the pink region** [1]. Each object was tested 20 times [1]. The overall success rate in the real-world environment was lower than in simulation, mainly due to observation and action noise in the real world [2]. An example of this task's setup and execution can be seen in Figure 2 [1, 3].\",null,[\"aaf18d1b-7a9e-4a19-aac9-2ea7c9842a6e\",\"4d951fe8-0867-45c0-874e-3275d7f03aa4\",3090253456],null,[[[[0,221,[[[0,47,[\"The \\\"real-world pushing task\\\" involves using a \"]],[47,80,[\"UR5 robotic arm and an RGB camera\",[true]]],[80,111,[\" to push a correct object to a \"]],[111,127,[\"pink target area\",[true]]],[127,221,[\". The images for this task, taken from both human and robot domains, have unique perspectives.\"]]]]]],[[[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"],[null,127,127]],[[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"],[null,127,220]],[[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"],[null,373,373]],[[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"],[null,373,406]],[[\"ad22ec51-d342-4e52-8865-a380caefd52e\"],[null,406,553]],[[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"],[null,553,624]],[[\"6bc9ad41-d4f5-4c47-bdf4-e2c22022ec7c\"],[null,553,624]]]],null,null,[[[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"],[null,null,0.713884600169177,[[null,31011,31771]],[[[31011,31034,[[[31011,31034,[\"5.2 REAL-WORLD PUSHING \"]]]]],[31034,31771,[[[31034,31771,[\"We designed the real-world pushing environment using a UR5 robotic arm and RGB camera. The goal is to push the correct object to the pink target area. The sample videos are shown in Figure 2. Images taken from the human and robot domains have unique perspectives. For meta-training, we collected a dataset with 11 objects, consisting of 550 human demonstrations, 550 robot random videos and 110 human-robot paired videos. For DAML, random videos are replaced by 550 UR5 demonstrations corresponding to humans (include images and actions). During the evaluation, we provided 8 novel objects. The task is considered successful if the robot pushes the target object within the center of pink region. We tested each of the objects 20 times. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"]]],[[\"ad22ec51-d342-4e52-8865-a380caefd52e\"],[null,null,0.6419930283907684,[[null,31771,32367]],[[[31771,32367,[[[31771,32367,[\"The testing results in the third column of Table 1 report the success rate of our real-world exper-iments. The whole success rate (lower than 60%) is lower than in simulation environment, which is mainly influenced by the observation and action noise in real-world. Our method (56.3%) still achieves the comparable results to DAML (58.8%). The performance of DeGI(32.1%) and FeedImg (38.1%) is down greatly and again verifies the two modules of our method are indispensable. Figure 6 shows the real-world testing visualization results. Additional experiments testing results refer to Appendix B. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"ad22ec51-d342-4e52-8865-a380caefd52e\"]]],[[\"6bc9ad41-d4f5-4c47-bdf4-e2c22022ec7c\"],[null,null,0.6570110801690852,[[null,1571,2750]],[[[1571,2750,[[[1571,2750,[\"1 INTRODUCTION The demonstration provides skill guidance for specifying robotic tasks. Through it, robots can ac-quire many complex skills, including table tennis (Mülling et al., 2013), pouring water (Sermanet et al., 2018), and picking objects (Levine et al., 2018). Most prior work assumes that robots can receive demonstrations via kinesthetic teaching (Elliott et al., 2017; Ragaglia et al., 2018), teleoper-ation (Savarimuthu et al., 2017; Zhang et al., 2018), or crowdsourcing platform (Mandlekar et al., 2018; Wang et al., 2021), which are distinct from the way how humans imitate others. Due to the mirror neurons (Tranel et al., 2003; Molenberghs et al., 2009), humans are often able to watch others act, infer the intention, map it to their own embodiment, expand skill set and enhance the representa-tions of the world. Motivated by this, we aim to endow robots with the ability to learn manipulation skills via video demonstrations from humans without access to the actions of the demonstrator. Then the key challenge we would face is how to bridge the human-robot domain gap caused by the morphological difference and infer the performed skills from the raw video. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"6bc9ad41-d4f5-4c47-bdf4-e2c22022ec7c\"]]]]]],[[null,null,0.713884600169177,[[null,31011,31771]],[[[31011,31034,[[[31011,31034,[\"5.2 REAL-WORLD PUSHING \"]]]]],[31034,31771,[[[31034,31771,[\"We designed the real-world pushing environment using a UR5 robotic arm and RGB camera. The goal is to push the correct object to the pink target area. The sample videos are shown in Figure 2. Images taken from the human and robot domains have unique perspectives. For meta-training, we collected a dataset with 11 objects, consisting of 550 human demonstrations, 550 robot random videos and 110 human-robot paired videos. For DAML, random videos are replaced by 550 UR5 demonstrations corresponding to humans (include images and actions). During the evaluation, we provided 8 novel objects. The task is considered successful if the robot pushes the target object within the center of pink region. We tested each of the objects 20 times. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"]],[null,null,0.6419930283907684,[[null,31771,32367]],[[[31771,32367,[[[31771,32367,[\"The testing results in the third column of Table 1 report the success rate of our real-world exper-iments. The whole success rate (lower than 60%) is lower than in simulation environment, which is mainly influenced by the observation and action noise in real-world. Our method (56.3%) still achieves the comparable results to DAML (58.8%). The performance of DeGI(32.1%) and FeedImg (38.1%) is down greatly and again verifies the two modules of our method are indispensable. Figure 6 shows the real-world testing visualization results. Additional experiments testing results refer to Appendix B. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"ad22ec51-d342-4e52-8865-a380caefd52e\"]],[null,null,0.6570110801690852,[[null,1571,2750]],[[[1571,2750,[[[1571,2750,[\"1 INTRODUCTION The demonstration provides skill guidance for specifying robotic tasks. Through it, robots can ac-quire many complex skills, including table tennis (Mülling et al., 2013), pouring water (Sermanet et al., 2018), and picking objects (Levine et al., 2018). Most prior work assumes that robots can receive demonstrations via kinesthetic teaching (Elliott et al., 2017; Ragaglia et al., 2018), teleoper-ation (Savarimuthu et al., 2017; Zhang et al., 2018), or crowdsourcing platform (Mandlekar et al., 2018; Wang et al., 2021), which are distinct from the way how humans imitate others. Due to the mirror neurons (Tranel et al., 2003; Molenberghs et al., 2009), humans are often able to watch others act, infer the intention, map it to their own embodiment, expand skill set and enhance the representa-tions of the world. Motivated by this, we aim to endow robots with the ability to learn manipulation skills via video demonstrations from humans without access to the actions of the demonstrator. Then the key challenge we would face is how to bridge the human-robot domain gap caused by the morphological difference and infer the performed skills from the raw video. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"6bc9ad41-d4f5-4c47-bdf4-e2c22022ec7c\"]]],[[[null,127,127],[0]],[[null,127,220],[0]],[[null,373,373],[0]],[[null,373,406],[0]],[[null,406,553],[1]],[[null,553,624],[0,2]]],[[\"What is A-CycleGAN?\",\"What is the purpose of SSIM?\",\"What is MILV?\"]]]"]]
8895
[["wrb.fr",null,"[[\"The \\\"real-world pushing task\\\" involves using a **UR5 robotic arm and an RGB camera** to push a correct object to a **pink target area** [1]. The images for this task, taken from both human and robot domains, have unique perspectives [1].\\n\\nFor evaluation, 8 novel objects were used, and the task was deemed successful if the robot pushed the target object **within the center of the pink region** [1]. Each object was tested 20 times [1]. The overall success rate in the real-world environment was lower than in simulation, mainly due to observation and action noise in the real world [2]. An example of this task's setup and execution can be seen in Figure 2 [1, 3].\",null,[\"aaf18d1b-7a9e-4a19-aac9-2ea7c9842a6e\",\"4d951fe8-0867-45c0-874e-3275d7f03aa4\",3090253456],null,[[[[0,221,[[[0,47,[\"The \\\"real-world pushing task\\\" involves using a \"]],[47,80,[\"UR5 robotic arm and an RGB camera\",[true]]],[80,111,[\" to push a correct object to a \"]],[111,127,[\"pink target area\",[true]]],[127,221,[\". The images for this task, taken from both human and robot domains, have unique perspectives.\"]]]]],[221,625,[[[221,337,[\"For evaluation, 8 novel objects were used, and the task was deemed successful if the robot pushed the target object \"]],[337,373,[\"within the center of the pink region\",[true]]],[373,625,[\". Each object was tested 20 times. The overall success rate in the real-world environment was lower than in simulation, mainly due to observation and action noise in the real world. An example of this task's setup and execution can be seen in Figure 2.\"]]]]]],[[[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"],[null,127,127]],[[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"],[null,127,220]],[[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"],[null,373,373]],[[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"],[null,373,406]],[[\"ad22ec51-d342-4e52-8865-a380caefd52e\"],[null,406,553]],[[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"],[null,553,624]],[[\"6bc9ad41-d4f5-4c47-bdf4-e2c22022ec7c\"],[null,553,624]]]],null,null,[[[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"],[null,null,0.713884600169177,[[null,31011,31771]],[[[31011,31034,[[[31011,31034,[\"5.2 REAL-WORLD PUSHING \"]]]]],[31034,31771,[[[31034,31771,[\"We designed the real-world pushing environment using a UR5 robotic arm and RGB camera. The goal is to push the correct object to the pink target area. The sample videos are shown in Figure 2. Images taken from the human and robot domains have unique perspectives. For meta-training, we collected a dataset with 11 objects, consisting of 550 human demonstrations, 550 robot random videos and 110 human-robot paired videos. For DAML, random videos are replaced by 550 UR5 demonstrations corresponding to humans (include images and actions). During the evaluation, we provided 8 novel objects. The task is considered successful if the robot pushes the target object within the center of pink region. We tested each of the objects 20 times. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"]]],[[\"ad22ec51-d342-4e52-8865-a380caefd52e\"],[null,null,0.6419930283907684,[[null,31771,32367]],[[[31771,32367,[[[31771,32367,[\"The testing results in the third column of Table 1 report the success rate of our real-world exper-iments. The whole success rate (lower than 60%) is lower than in simulation environment, which is mainly influenced by the observation and action noise in real-world. Our method (56.3%) still achieves the comparable results to DAML (58.8%). The performance of DeGI(32.1%) and FeedImg (38.1%) is down greatly and again verifies the two modules of our method are indispensable. Figure 6 shows the real-world testing visualization results. Additional experiments testing results refer to Appendix B. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"ad22ec51-d342-4e52-8865-a380caefd52e\"]]],[[\"6bc9ad41-d4f5-4c47-bdf4-e2c22022ec7c\"],[null,null,0.6570110801690852,[[null,1571,2750]],[[[1571,2750,[[[1571,2750,[\"1 INTRODUCTION The demonstration provides skill guidance for specifying robotic tasks. Through it, robots can ac-quire many complex skills, including table tennis (Mülling et al., 2013), pouring water (Sermanet et al., 2018), and picking objects (Levine et al., 2018). Most prior work assumes that robots can receive demonstrations via kinesthetic teaching (Elliott et al., 2017; Ragaglia et al., 2018), teleoper-ation (Savarimuthu et al., 2017; Zhang et al., 2018), or crowdsourcing platform (Mandlekar et al., 2018; Wang et al., 2021), which are distinct from the way how humans imitate others. Due to the mirror neurons (Tranel et al., 2003; Molenberghs et al., 2009), humans are often able to watch others act, infer the intention, map it to their own embodiment, expand skill set and enhance the representa-tions of the world. Motivated by this, we aim to endow robots with the ability to learn manipulation skills via video demonstrations from humans without access to the actions of the demonstrator. Then the key challenge we would face is how to bridge the human-robot domain gap caused by the morphological difference and infer the performed skills from the raw video. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"6bc9ad41-d4f5-4c47-bdf4-e2c22022ec7c\"]]]]]],[[null,null,0.713884600169177,[[null,31011,31771]],[[[31011,31034,[[[31011,31034,[\"5.2 REAL-WORLD PUSHING \"]]]]],[31034,31771,[[[31034,31771,[\"We designed the real-world pushing environment using a UR5 robotic arm and RGB camera. The goal is to push the correct object to the pink target area. The sample videos are shown in Figure 2. Images taken from the human and robot domains have unique perspectives. For meta-training, we collected a dataset with 11 objects, consisting of 550 human demonstrations, 550 robot random videos and 110 human-robot paired videos. For DAML, random videos are replaced by 550 UR5 demonstrations corresponding to humans (include images and actions). During the evaluation, we provided 8 novel objects. The task is considered successful if the robot pushes the target object within the center of pink region. We tested each of the objects 20 times. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"7d5c18e6-1963-4628-a8d5-f6770f73cd40\"]],[null,null,0.6419930283907684,[[null,31771,32367]],[[[31771,32367,[[[31771,32367,[\"The testing results in the third column of Table 1 report the success rate of our real-world exper-iments. The whole success rate (lower than 60%) is lower than in simulation environment, which is mainly influenced by the observation and action noise in real-world. Our method (56.3%) still achieves the comparable results to DAML (58.8%). The performance of DeGI(32.1%) and FeedImg (38.1%) is down greatly and again verifies the two modules of our method are indispensable. Figure 6 shows the real-world testing visualization results. Additional experiments testing results refer to Appendix B. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"ad22ec51-d342-4e52-8865-a380caefd52e\"]],[null,null,0.6570110801690852,[[null,1571,2750]],[[[1571,2750,[[[1571,2750,[\"1 INTRODUCTION The demonstration provides skill guidance for specifying robotic tasks. Through it, robots can ac-quire many complex skills, including table tennis (Mülling et al., 2013), pouring water (Sermanet et al., 2018), and picking objects (Levine et al., 2018). Most prior work assumes that robots can receive demonstrations via kinesthetic teaching (Elliott et al., 2017; Ragaglia et al., 2018), teleoper-ation (Savarimuthu et al., 2017; Zhang et al., 2018), or crowdsourcing platform (Mandlekar et al., 2018; Wang et al., 2021), which are distinct from the way how humans imitate others. Due to the mirror neurons (Tranel et al., 2003; Molenberghs et al., 2009), humans are often able to watch others act, infer the intention, map it to their own embodiment, expand skill set and enhance the representa-tions of the world. Motivated by this, we aim to endow robots with the ability to learn manipulation skills via video demonstrations from humans without access to the actions of the demonstrator. Then the key challenge we would face is how to bridge the human-robot domain gap caused by the morphological difference and infer the performed skills from the raw video. \"]]]]]]],[[[\"2cb45f1e-fa36-4f08-9453-af227fd1c0b0\"],\"f2ad2310-0298-4bec-a88a-5793c312ef9c\"]],[\"6bc9ad41-d4f5-4c47-bdf4-e2c22022ec7c\"]]],[[[null,127,127],[0]],[[null,127,220],[0]],[[null,373,373],[0]],[[null,373,406],[0]],[[null,406,553],[1]],[[null,553,624],[0,2]]],[[\"What is A-CycleGAN?\",\"What is the purpose of SSIM?\",\"What is MILV?\"]]]"]]
59
[["di",2621],["af.httprm",2620,"1371015555068899173",54]]
27
[["e",6,null,null,20768]]
