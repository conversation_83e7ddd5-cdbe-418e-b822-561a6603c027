)]}'

6607
[["wrb.fr",null,"[[\"The expressions within the summations that are raised to a power, such as those found in the performance guarantees for TAWT, are always positive based on their definition and context within the sources [1, 2].\\n\\nSpecifically, let\",null,[\"f44a53b9-4080-4345-b5a3-73505bc249c5\",\"877bb796-89b9-49b0-8005-5e41f381bd6b\",1683326744],null,[[[[0,203,[[[0,203,[\"The expressions within the summations that are raised to a power, such as those found in the performance guarantees for TAWT, are always positive based on their definition and context within the sources.\"]]]]]],[[[\"d11516ca-a2bc-4bed-bfc9-cd3ad0352bfa\"],[null,0,202]],[[\"cc982c05-c2e5-4b47-9a6e-6032d8e997e6\"],[null,0,202]]]],null,null,[[[\"d11516ca-a2bc-4bed-bfc9-cd3ad0352bfa\"],[null,null,0.6619592623438405,[[null,23429,24027]],[[[23429,23465,[[[23429,23465,[\"√ (νΦ log δ−1)/nT + (νF + log T )/n-\"]]]]],[23465,23671,[[[23465,23671,[\"neighborhood of Φ̄ω4, there exists a specific φ̄ω ∈ Φ̄ω such that φ̄ω is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]],[23671,23706,[[[23671,23706,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[23706,23723,[[[23706,23723,[\"[( νF + log(1/δ) \"]]]]],[23723,23726,[[[23723,23726,[\"n0 \"]]]]],[23726,23732,[[[23726,23732,[\") 1 2 \"]]]]],[23732,23739,[[[23732,23739,[\"+ β1/ρ \"]]]]],[23739,23755,[[[23739,23755,[\"( νΦ + log(1/δ) \"]]]]],[23755,23771,[[[23755,23771,[\"nT + νF + log T \"]]]]],[23771,23773,[[[23771,23773,[\"n \"]]]]],[23773,23782,[[[23773,23782,[\") 1 2ρ ] \"]]]]],[23782,23798,[[[23782,23798,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[23798,23807,[[[23798,23807,[\"ω̂tDt,D0 \"]]]]],[23807,23817,[[[23807,23817,[\") . (3.8) \"]]]]],[23817,24027,[[[23817,24027,[\"The upper bound in (3.8) is a superposition of three terms. Let us disregard the log(1/δ) terms for now and focus on the dependence on the sample sizes and problem dimensions. The first term, which scales with \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"d11516ca-a2bc-4bed-bfc9-cd3ad0352bfa\"]]],[[\"cc982c05-c2e5-4b47-9a6e-6032d8e997e6\"],[null,null,0.6973815519041682,[[null,44343,45056]],[[[44343,44378,[[[44343,44378,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[44378,44395,[[[44378,44395,[\"[( νF + log(1/δ) \"]]]]],[44395,44398,[[[44395,44398,[\"n0 \"]]]]],[44398,44404,[[[44398,44404,[\") 1 2 \"]]]]],[44404,44406,[[[44404,44406,[\"+ \"]]]]],[44406,44428,[[[44406,44428,[\"( νΦ + TνF + log(1/δ) \"]]]]],[44428,44431,[[[44428,44431,[\"Nω \"]]]]],[44431,44440,[[[44431,44440,[\") 1 2ρ ] \"]]]]],[44440,44456,[[[44440,44456,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[44456,44464,[[[44456,44464,[\"ωtDt,D0 \"]]]]],[44464,44474,[[[44464,44474,[\") . (A.1) \"]]]]],[44474,44499,[[[44474,44499,[\"Proof. See Appendix A.3. \"]]]]],[44499,44743,[[[44499,44743,[\"Note that the task distance naturally appears in the upper bound above. This theorem can be regarded as a predecessor of Theorem 3.1, and we refer readers to Section 3.2 for a detailed exposition on the meaning of each term in the upper bound. \"]]]]],[44743,44801,[[[44743,44801,[\"A.2 A SUFFICIENT CONDITION FOR EXACTLY MATCHING φ̄ω \\u003d φ?0 \"]]]]],[44801,45056,[[[44801,45056,[\"Proposition A.1 (Weighting and task distance minimization). Suppose that for every φ ∈ Φ, there exists two source tasks 1 ≤ t1, t2 ≤ T such that L?t1(φ) ≤ L?0(φ) ≤ L?t2(φ). Then there exists a choice of weights ω, possibly depending on φ, such that dist( \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"cc982c05-c2e5-4b47-9a6e-6032d8e997e6\"]]]]]],[[null,null,0.6619592623438405,[[null,23429,24027]],[[[23429,23465,[[[23429,23465,[\"√ (νΦ log δ−1)/nT + (νF + log T )/n-\"]]]]],[23465,23671,[[[23465,23671,[\"neighborhood of Φ̄ω4, there exists a specific φ̄ω ∈ Φ̄ω such that φ̄ω is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]],[23671,23706,[[[23671,23706,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[23706,23723,[[[23706,23723,[\"[( νF + log(1/δ) \"]]]]],[23723,23726,[[[23723,23726,[\"n0 \"]]]]],[23726,23732,[[[23726,23732,[\") 1 2 \"]]]]],[23732,23739,[[[23732,23739,[\"+ β1/ρ \"]]]]],[23739,23755,[[[23739,23755,[\"( νΦ + log(1/δ) \"]]]]],[23755,23771,[[[23755,23771,[\"nT + νF + log T \"]]]]],[23771,23773,[[[23771,23773,[\"n \"]]]]],[23773,23782,[[[23773,23782,[\") 1 2ρ ] \"]]]]],[23782,23798,[[[23782,23798,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[23798,23807,[[[23798,23807,[\"ω̂tDt,D0 \"]]]]],[23807,23817,[[[23807,23817,[\") . (3.8) \"]]]]],[23817,24027,[[[23817,24027,[\"The upper bound in (3.8) is a superposition of three terms. Let us disregard the log(1/δ) terms for now and focus on the dependence on the sample sizes and problem dimensions. The first term, which scales with \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"d11516ca-a2bc-4bed-bfc9-cd3ad0352bfa\"]],[null,null,0.6973815519041682,[[null,44343,45056]],[[[44343,44378,[[[44343,44378,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[44378,44395,[[[44378,44395,[\"[( νF + log(1/δ) \"]]]]],[44395,44398,[[[44395,44398,[\"n0 \"]]]]],[44398,44404,[[[44398,44404,[\") 1 2 \"]]]]],[44404,44406,[[[44404,44406,[\"+ \"]]]]],[44406,44428,[[[44406,44428,[\"( νΦ + TνF + log(1/δ) \"]]]]],[44428,44431,[[[44428,44431,[\"Nω \"]]]]],[44431,44440,[[[44431,44440,[\") 1 2ρ ] \"]]]]],[44440,44456,[[[44440,44456,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[44456,44464,[[[44456,44464,[\"ωtDt,D0 \"]]]]],[44464,44474,[[[44464,44474,[\") . (A.1) \"]]]]],[44474,44499,[[[44474,44499,[\"Proof. See Appendix A.3. \"]]]]],[44499,44743,[[[44499,44743,[\"Note that the task distance naturally appears in the upper bound above. This theorem can be regarded as a predecessor of Theorem 3.1, and we refer readers to Section 3.2 for a detailed exposition on the meaning of each term in the upper bound. \"]]]]],[44743,44801,[[[44743,44801,[\"A.2 A SUFFICIENT CONDITION FOR EXACTLY MATCHING φ̄ω \\u003d φ?0 \"]]]]],[44801,45056,[[[44801,45056,[\"Proposition A.1 (Weighting and task distance minimization). Suppose that for every φ ∈ Φ, there exists two source tasks 1 ≤ t1, t2 ≤ T such that L?t1(φ) ≤ L?0(φ) ≤ L?t2(φ). Then there exists a choice of weights ω, possibly depending on φ, such that dist( \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"cc982c05-c2e5-4b47-9a6e-6032d8e997e6\"]]],[[[null,0,202],[0,1]]]]"]]
17954
[["wrb.fr",null,"[[\"The expressions within the summations that are raised to a power, such as those found in the performance guarantees for TAWT, are always positive based on their definition and context within the sources [1, 2].\\n\\nSpecifically, let's examine the components that form these terms:\\n*   **`νΦ` and `νF`**: These are constants greater than zero, controlling the complexity of function classes via uniform entropy [3].\\n*   **`log(1/δ)`**: `δ` is a probability defined within the interval (0, 1) [4, 5]. Therefore, `1/δ` will always be greater than 1, ensuring that `log(1/δ)` is a positive value.\\n*   **`T`**: Represents the number of source tasks, which is a positive integer [6].\\n\",null,[\"f44a53b9-4080-4345-b5a3-73505bc249c5\",\"877bb796-89b9-49b0-8005-5e41f381bd6b\",1683326744],null,[[[[0,203,[[[0,203,[\"The expressions within the summations that are raised to a power, such as those found in the performance guarantees for TAWT, are always positive based on their definition and context within the sources.\"]]]]],[203,268,[[[203,268,[\"Specifically, let's examine the components that form these terms:\"]]]]],[268,385,[[[268,270,[\"νΦ\",[true,null,null,null,null,null,null,true]]],[270,275,[\" and \",[true]]],[275,277,[\"νF\",[true,null,null,null,null,null,null,true]]],[277,385,[\": These are constants greater than zero, controlling the complexity of function classes via uniform entropy.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[385,539,[[[385,393,[\"log(1/δ)\",[true,null,null,null,null,null,null,true]]],[393,395,[\": \"]],[395,396,[\"δ\",[null,null,null,null,null,null,null,true]]],[396,461,[\" is a probability defined within the interval (0, 1). Therefore, \"]],[461,464,[\"1/δ\",[null,null,null,null,null,null,null,true]]],[464,510,[\" will always be greater than 1, ensuring that \"]],[510,518,[\"log(1/δ)\",[null,null,null,null,null,null,null,true]]],[518,539,[\" is a positive value.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]]],[[[\"7c60f713-ac56-43d8-9353-90366a6aab5f\"],[null,0,202]],[[\"880876a5-380d-4506-bbb0-304a6988e7a9\"],[null,0,202]],[[\"a8d54e83-2ec8-4a8e-b50c-ed27d4736426\"],[null,277,384]],[[\"0dd18d39-08f2-4838-8812-191dd520d0a6\"],[null,397,448]],[[\"91deab84-49d8-4e03-9ef0-8f7a0b998eee\"],[null,397,448]],[[\"8fcfdf58-4f9d-406b-9575-31d30c7b8190\"],[null,540,608]]]],null,null,[[[\"7c60f713-ac56-43d8-9353-90366a6aab5f\"],[null,null,0.6619592623438405,[[null,23429,24027]],[[[23429,23465,[[[23429,23465,[\"√ (νΦ log δ−1)/nT + (νF + log T )/n-\"]]]]],[23465,23671,[[[23465,23671,[\"neighborhood of Φ̄ω4, there exists a specific φ̄ω ∈ Φ̄ω such that φ̄ω is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]],[23671,23706,[[[23671,23706,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[23706,23723,[[[23706,23723,[\"[( νF + log(1/δ) \"]]]]],[23723,23726,[[[23723,23726,[\"n0 \"]]]]],[23726,23732,[[[23726,23732,[\") 1 2 \"]]]]],[23732,23739,[[[23732,23739,[\"+ β1/ρ \"]]]]],[23739,23755,[[[23739,23755,[\"( νΦ + log(1/δ) \"]]]]],[23755,23771,[[[23755,23771,[\"nT + νF + log T \"]]]]],[23771,23773,[[[23771,23773,[\"n \"]]]]],[23773,23782,[[[23773,23782,[\") 1 2ρ ] \"]]]]],[23782,23798,[[[23782,23798,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[23798,23807,[[[23798,23807,[\"ω̂tDt,D0 \"]]]]],[23807,23817,[[[23807,23817,[\") . (3.8) \"]]]]],[23817,24027,[[[23817,24027,[\"The upper bound in (3.8) is a superposition of three terms. Let us disregard the log(1/δ) terms for now and focus on the dependence on the sample sizes and problem dimensions. The first term, which scales with \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"7c60f713-ac56-43d8-9353-90366a6aab5f\"]]],[[\"880876a5-380d-4506-bbb0-304a6988e7a9\"],[null,null,0.6973815519041682,[[null,44343,45056]],[[[44343,44378,[[[44343,44378,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[44378,44395,[[[44378,44395,[\"[( νF + log(1/δ) \"]]]]],[44395,44398,[[[44395,44398,[\"n0 \"]]]]],[44398,44404,[[[44398,44404,[\") 1 2 \"]]]]],[44404,44406,[[[44404,44406,[\"+ \"]]]]],[44406,44428,[[[44406,44428,[\"( νΦ + TνF + log(1/δ) \"]]]]],[44428,44431,[[[44428,44431,[\"Nω \"]]]]],[44431,44440,[[[44431,44440,[\") 1 2ρ ] \"]]]]],[44440,44456,[[[44440,44456,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[44456,44464,[[[44456,44464,[\"ωtDt,D0 \"]]]]],[44464,44474,[[[44464,44474,[\") . (A.1) \"]]]]],[44474,44499,[[[44474,44499,[\"Proof. See Appendix A.3. \"]]]]],[44499,44743,[[[44499,44743,[\"Note that the task distance naturally appears in the upper bound above. This theorem can be regarded as a predecessor of Theorem 3.1, and we refer readers to Section 3.2 for a detailed exposition on the meaning of each term in the upper bound. \"]]]]],[44743,44801,[[[44743,44801,[\"A.2 A SUFFICIENT CONDITION FOR EXACTLY MATCHING φ̄ω \\u003d φ?0 \"]]]]],[44801,45056,[[[44801,45056,[\"Proposition A.1 (Weighting and task distance minimization). Suppose that for every φ ∈ Φ, there exists two source tasks 1 ≤ t1, t2 ≤ T such that L?t1(φ) ≤ L?0(φ) ≤ L?t2(φ). Then there exists a choice of weights ω, possibly depending on φ, such that dist( \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"880876a5-380d-4506-bbb0-304a6988e7a9\"]]],[[\"a8d54e83-2ec8-4a8e-b50c-ed27d4736426\"],[null,null,0.6788500196198473,[[null,19331,20304]],[[[19331,19376,[[[19331,19376,[\"N (Φ;L2(QX ); ε) ≤ (CΦ/ε) νΦ , ∀ε \\u003e 0, (3.3) \"]]]]],[19376,19615,[[[19376,19615,[\"whereN (Φ;L2(QX ); ε) is the L2(QX ) covering number of Φ (i.e., the minimum number of L2(QX ) balls3 with radius ε required to cover Φ). In parallel, there exist CF \\u003e 0, νF \\u003e 0, such that for any probability measure QZ on Z ⊆ Rr, we have \"]]]]],[19615,19660,[[[19615,19660,[\"N (F ;L2(QZ); ε) ≤ (CF/ε) νG , ∀ε \\u003e 0, (3.4) \"]]]]],[19660,19720,[[[19660,19720,[\"where N (F ;L2(QZ); ε) is the L2(QZ) covering number of F . \"]]]]],[19720,20304,[[[19720,20304,[\"Uniform entropy generalizes the notion of Vapnik-Chervonenkis dimension (Vapnik, 2013) and allows us to give a unified treatment of regression and classification problems. For this reason, function classes satisfying the above assumption are also referred to as “VC-type classes” in the literature (Koltchinskii, 2006). In particular, if each coordinate of Φ has VC-subgraph dimension c(Φ), then (3.3) is satisfied with νΦ \\u003d Θ(r · c(Φ)) (recall that r is the dimension of the latent space Z). Similarly, if F has VC-subgraph dimension c(F), then (3.4) is satisfied with νF \\u003d Θ(c(F)). \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"a8d54e83-2ec8-4a8e-b50c-ed27d4736426\"]]],[[\"0dd18d39-08f2-4838-8812-191dd520d0a6\"],[null,null,0.6349943414497436,[[null,22901,23429]],[[[22901,22990,[[[22901,22990,[\"The following theorem gives performance guarantees for the sample split version of TAWT. \"]]]]],[22990,23429,[[[22990,23429,[\"Theorem 3.1 (Performance of TAWT with sample splitting). Let (φ̂, f̂0) be obtained via solving (3.6)–(3.7). Let Assumptions A and B hold. In addition, assume that the learned weights satisfy ω̂ ∈ Wβ :\\u003d {ω ∈ ∆T−1 : β−1 ≤ ωt/ωt′ ≤ β,∀t 6\\u003d t′}, where β ≥ 1 is an absolute constant. Fix δ ∈ (0, 1). There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any weights ω ∈ Wβ and any representation φ in a Cβ · \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"0dd18d39-08f2-4838-8812-191dd520d0a6\"]]],[[\"91deab84-49d8-4e03-9ef0-8f7a0b998eee\"],[null,null,0.693613329557906,[[null,43770,44343]],[[[43770,43973,[[[43770,43973,[\"Theorem A.1 (Performance of the two-step procedure with fixed weights). Let (φ̂, f̂0) be obtained via solving (2.2)–(2.3) with fixed ω. Let Assumptions A and B hold. Fix any δ ∈ (0, 1) and define Nω \\u003d ( \"]]]]],[43973,43982,[[[43973,43982,[\"∑T t\\u003d1 ω \"]]]]],[43982,43991,[[[43982,43991,[\"2 t /nt) \"]]]]],[43991,44113,[[[43991,44113,[\"−1. There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any representation φ in a C \"]]]]],[44113,44155,[[[44113,44155,[\"√ (νΦ + TνF + log δ−1)/Nω-neighborhood of \"]]]]],[44155,44343,[[[44155,44343,[\"Φ̄ω6, there exists a specific φ̄ω ∈ Φ̄ω such that φ is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"91deab84-49d8-4e03-9ef0-8f7a0b998eee\"]]],[[\"8fcfdf58-4f9d-406b-9575-31d30c7b8190\"],[null,null,0.6513285309780665,[[null,6569,7274]],[[[6569,6608,[[[6569,6608,[\"2 TAWT: TARGET-AWARE WEIGHTED TRAINING \"]]]]],[6608,6626,[[[6608,6626,[\"2.1 PRELIMINARIES \"]]]]],[6626,7274,[[[6626,7274,[\"Suppose we have T source tasks, represented by a collection of probability distributions {Dt}Tt\\u003d1 on the sample space X × Y , where X ⊆ Rd is the feature space and Y ⊆ R is the label space. For classification problems, we take Y to be a finite subset of R. We have a single target task, whose probability distribution is denoted as D0. For the t-th task, where t \\u003d 0, 1, . . . , T , we observe nt i.i.d. samples St \\u003d {(xti, yti)}nti\\u003d1 from Dt. Typically, the number of samples from the target task, n0, is much smaller than the samples from the source tasks, and the goal is to use samples from source tasks to aid the learning of the target task. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"8fcfdf58-4f9d-406b-9575-31d30c7b8190\"]]]]]],[[null,null,0.6619592623438405,[[null,23429,24027]],[[[23429,23465,[[[23429,23465,[\"√ (νΦ log δ−1)/nT + (νF + log T )/n-\"]]]]],[23465,23671,[[[23465,23671,[\"neighborhood of Φ̄ω4, there exists a specific φ̄ω ∈ Φ̄ω such that φ̄ω is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]],[23671,23706,[[[23671,23706,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[23706,23723,[[[23706,23723,[\"[( νF + log(1/δ) \"]]]]],[23723,23726,[[[23723,23726,[\"n0 \"]]]]],[23726,23732,[[[23726,23732,[\") 1 2 \"]]]]],[23732,23739,[[[23732,23739,[\"+ β1/ρ \"]]]]],[23739,23755,[[[23739,23755,[\"( νΦ + log(1/δ) \"]]]]],[23755,23771,[[[23755,23771,[\"nT + νF + log T \"]]]]],[23771,23773,[[[23771,23773,[\"n \"]]]]],[23773,23782,[[[23773,23782,[\") 1 2ρ ] \"]]]]],[23782,23798,[[[23782,23798,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[23798,23807,[[[23798,23807,[\"ω̂tDt,D0 \"]]]]],[23807,23817,[[[23807,23817,[\") . (3.8) \"]]]]],[23817,24027,[[[23817,24027,[\"The upper bound in (3.8) is a superposition of three terms. Let us disregard the log(1/δ) terms for now and focus on the dependence on the sample sizes and problem dimensions. The first term, which scales with \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"7c60f713-ac56-43d8-9353-90366a6aab5f\"]],[null,null,0.6973815519041682,[[null,44343,45056]],[[[44343,44378,[[[44343,44378,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[44378,44395,[[[44378,44395,[\"[( νF + log(1/δ) \"]]]]],[44395,44398,[[[44395,44398,[\"n0 \"]]]]],[44398,44404,[[[44398,44404,[\") 1 2 \"]]]]],[44404,44406,[[[44404,44406,[\"+ \"]]]]],[44406,44428,[[[44406,44428,[\"( νΦ + TνF + log(1/δ) \"]]]]],[44428,44431,[[[44428,44431,[\"Nω \"]]]]],[44431,44440,[[[44431,44440,[\") 1 2ρ ] \"]]]]],[44440,44456,[[[44440,44456,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[44456,44464,[[[44456,44464,[\"ωtDt,D0 \"]]]]],[44464,44474,[[[44464,44474,[\") . (A.1) \"]]]]],[44474,44499,[[[44474,44499,[\"Proof. See Appendix A.3. \"]]]]],[44499,44743,[[[44499,44743,[\"Note that the task distance naturally appears in the upper bound above. This theorem can be regarded as a predecessor of Theorem 3.1, and we refer readers to Section 3.2 for a detailed exposition on the meaning of each term in the upper bound. \"]]]]],[44743,44801,[[[44743,44801,[\"A.2 A SUFFICIENT CONDITION FOR EXACTLY MATCHING φ̄ω \\u003d φ?0 \"]]]]],[44801,45056,[[[44801,45056,[\"Proposition A.1 (Weighting and task distance minimization). Suppose that for every φ ∈ Φ, there exists two source tasks 1 ≤ t1, t2 ≤ T such that L?t1(φ) ≤ L?0(φ) ≤ L?t2(φ). Then there exists a choice of weights ω, possibly depending on φ, such that dist( \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"880876a5-380d-4506-bbb0-304a6988e7a9\"]],[null,null,0.6788500196198473,[[null,19331,20304]],[[[19331,19376,[[[19331,19376,[\"N (Φ;L2(QX ); ε) ≤ (CΦ/ε) νΦ , ∀ε \\u003e 0, (3.3) \"]]]]],[19376,19615,[[[19376,19615,[\"whereN (Φ;L2(QX ); ε) is the L2(QX ) covering number of Φ (i.e., the minimum number of L2(QX ) balls3 with radius ε required to cover Φ). In parallel, there exist CF \\u003e 0, νF \\u003e 0, such that for any probability measure QZ on Z ⊆ Rr, we have \"]]]]],[19615,19660,[[[19615,19660,[\"N (F ;L2(QZ); ε) ≤ (CF/ε) νG , ∀ε \\u003e 0, (3.4) \"]]]]],[19660,19720,[[[19660,19720,[\"where N (F ;L2(QZ); ε) is the L2(QZ) covering number of F . \"]]]]],[19720,20304,[[[19720,20304,[\"Uniform entropy generalizes the notion of Vapnik-Chervonenkis dimension (Vapnik, 2013) and allows us to give a unified treatment of regression and classification problems. For this reason, function classes satisfying the above assumption are also referred to as “VC-type classes” in the literature (Koltchinskii, 2006). In particular, if each coordinate of Φ has VC-subgraph dimension c(Φ), then (3.3) is satisfied with νΦ \\u003d Θ(r · c(Φ)) (recall that r is the dimension of the latent space Z). Similarly, if F has VC-subgraph dimension c(F), then (3.4) is satisfied with νF \\u003d Θ(c(F)). \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"a8d54e83-2ec8-4a8e-b50c-ed27d4736426\"]],[null,null,0.6349943414497436,[[null,22901,23429]],[[[22901,22990,[[[22901,22990,[\"The following theorem gives performance guarantees for the sample split version of TAWT. \"]]]]],[22990,23429,[[[22990,23429,[\"Theorem 3.1 (Performance of TAWT with sample splitting). Let (φ̂, f̂0) be obtained via solving (3.6)–(3.7). Let Assumptions A and B hold. In addition, assume that the learned weights satisfy ω̂ ∈ Wβ :\\u003d {ω ∈ ∆T−1 : β−1 ≤ ωt/ωt′ ≤ β,∀t 6\\u003d t′}, where β ≥ 1 is an absolute constant. Fix δ ∈ (0, 1). There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any weights ω ∈ Wβ and any representation φ in a Cβ · \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"0dd18d39-08f2-4838-8812-191dd520d0a6\"]],[null,null,0.693613329557906,[[null,43770,44343]],[[[43770,43973,[[[43770,43973,[\"Theorem A.1 (Performance of the two-step procedure with fixed weights). Let (φ̂, f̂0) be obtained via solving (2.2)–(2.3) with fixed ω. Let Assumptions A and B hold. Fix any δ ∈ (0, 1) and define Nω \\u003d ( \"]]]]],[43973,43982,[[[43973,43982,[\"∑T t\\u003d1 ω \"]]]]],[43982,43991,[[[43982,43991,[\"2 t /nt) \"]]]]],[43991,44113,[[[43991,44113,[\"−1. There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any representation φ in a C \"]]]]],[44113,44155,[[[44113,44155,[\"√ (νΦ + TνF + log δ−1)/Nω-neighborhood of \"]]]]],[44155,44343,[[[44155,44343,[\"Φ̄ω6, there exists a specific φ̄ω ∈ Φ̄ω such that φ is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"91deab84-49d8-4e03-9ef0-8f7a0b998eee\"]],[null,null,0.6513285309780665,[[null,6569,7274]],[[[6569,6608,[[[6569,6608,[\"2 TAWT: TARGET-AWARE WEIGHTED TRAINING \"]]]]],[6608,6626,[[[6608,6626,[\"2.1 PRELIMINARIES \"]]]]],[6626,7274,[[[6626,7274,[\"Suppose we have T source tasks, represented by a collection of probability distributions {Dt}Tt\\u003d1 on the sample space X × Y , where X ⊆ Rd is the feature space and Y ⊆ R is the label space. For classification problems, we take Y to be a finite subset of R. We have a single target task, whose probability distribution is denoted as D0. For the t-th task, where t \\u003d 0, 1, . . . , T , we observe nt i.i.d. samples St \\u003d {(xti, yti)}nti\\u003d1 from Dt. Typically, the number of samples from the target task, n0, is much smaller than the samples from the source tasks, and the goal is to use samples from source tasks to aid the learning of the target task. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"8fcfdf58-4f9d-406b-9575-31d30c7b8190\"]]],[[[null,0,202],[0,1]],[[null,277,384],[2]],[[null,397,448],[3,4]],[[null,540,608],[5]]]]"]]
21072
[["wrb.fr",null,"[[\"The expressions within the summations that are raised to a power, such as those found in the performance guarantees for TAWT, are always positive based on their definition and context within the sources [1, 2].\\n\\nSpecifically, let's examine the components that form these terms:\\n*   **`νΦ` and `νF`**: These are constants greater than zero, controlling the complexity of function classes via uniform entropy [3].\\n*   **`log(1/δ)`**: `δ` is a probability defined within the interval (0, 1) [4, 5]. Therefore, `1/δ` will always be greater than 1, ensuring that `log(1/δ)` is a positive value.\\n*   **`T`**: Represents the number of source tasks, which is a positive integer [6].\\n*   **`n0`, `n`, `nT`, `Nω`**: These refer to sample sizes or quantities derived from sample sizes, which are inherently positive [6]. For example, `Nω` is defined as `(∑T t\\u003d1 ωt^2 / nt)^-1`, where `ωt ≥ 0` and `nt \\u003e 0` [5]. This construction guarantees `Nω` is positive.\\n\\nGiven that all individual components are positive, their sums and ratios, as structured in the theorems, will also be **positive** [1, 2].\\n\\nThe\",null,[\"f44a53b9-4080-4345-b5a3-73505bc249c5\",\"877bb796-89b9-49b0-8005-5e41f381bd6b\",1683326744],null,[[[[0,203,[[[0,203,[\"The expressions within the summations that are raised to a power, such as those found in the performance guarantees for TAWT, are always positive based on their definition and context within the sources.\"]]]]],[203,268,[[[203,268,[\"Specifically, let's examine the components that form these terms:\"]]]]],[268,385,[[[268,270,[\"νΦ\",[true,null,null,null,null,null,null,true]]],[270,275,[\" and \",[true]]],[275,277,[\"νF\",[true,null,null,null,null,null,null,true]]],[277,385,[\": These are constants greater than zero, controlling the complexity of function classes via uniform entropy.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[385,539,[[[385,393,[\"log(1/δ)\",[true,null,null,null,null,null,null,true]]],[393,395,[\": \"]],[395,396,[\"δ\",[null,null,null,null,null,null,null,true]]],[396,461,[\" is a probability defined within the interval (0, 1). Therefore, \"]],[461,464,[\"1/δ\",[null,null,null,null,null,null,null,true]]],[464,510,[\" will always be greater than 1, ensuring that \"]],[510,518,[\"log(1/δ)\",[null,null,null,null,null,null,null,true]]],[518,539,[\" is a positive value.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[539,609,[[[539,540,[\"T\",[true,null,null,null,null,null,null,true]]],[540,609,[\": Represents the number of source tasks, which is a positive integer.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]],[609,846,[[[609,611,[\"n0\",[true,null,null,null,null,null,null,true]]],[611,613,[\", \",[true]]],[613,614,[\"n\",[true,null,null,null,null,null,null,true]]],[614,616,[\", \",[true]]],[616,618,[\"nT\",[true,null,null,null,null,null,null,true]]],[618,620,[\", \",[true]]],[620,622,[\"Nω\",[true,null,null,null,null,null,null,true]]],[622,737,[\": These refer to sample sizes or quantities derived from sample sizes, which are inherently positive. For example, \"]],[737,739,[\"Nω\",[null,null,null,null,null,null,null,true]]],[739,754,[\" is defined as \"]],[754,775,[\"(∑T t\\u003d1 ωt^2 / nt)^-1\",[null,null,null,null,null,null,null,true]]],[775,783,[\", where \"]],[783,789,[\"ωt ≥ 0\",[null,null,null,null,null,null,null,true]]],[789,794,[\" and \"]],[794,800,[\"nt \\u003e 0\",[null,null,null,null,null,null,null,true]]],[800,831,[\". This construction guarantees \"]],[831,833,[\"Nω\",[null,null,null,null,null,null,null,true]]],[833,846,[\" is positive.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,3]]],[846,973,[[[846,964,[\"Given that all individual components are positive, their sums and ratios, as structured in the theorems, will also be \"]],[964,972,[\"positive\",[true]]],[972,973,[\".\"]]]]]],[[[\"2aa5df38-1e2a-4807-b299-7152031bc4bc\"],[null,0,202]],[[\"8087c22b-aaeb-43c8-8224-708f3dbc3aed\"],[null,0,202]],[[\"66b99153-0387-4672-a6c4-90d222e01c95\"],[null,277,384]],[[\"42afe98b-f57c-4ac5-934a-9dab51fcd934\"],[null,397,448]],[[\"6e184b9e-104b-4482-933e-5a8cde90e0c9\"],[null,397,448]],[[\"8f24a291-682b-4388-b3c9-7064c109eb1a\"],[null,540,608]],[[\"8f24a291-682b-4388-b3c9-7064c109eb1a\"],[null,622,722]],[[\"6e184b9e-104b-4482-933e-5a8cde90e0c9\"],[null,800,800]],[[\"2aa5df38-1e2a-4807-b299-7152031bc4bc\"],[null,972,972]],[[\"8087c22b-aaeb-43c8-8224-708f3dbc3aed\"],[null,972,972]]]],null,null,[[[\"2aa5df38-1e2a-4807-b299-7152031bc4bc\"],[null,null,0.6619592623438405,[[null,23429,24027]],[[[23429,23465,[[[23429,23465,[\"√ (νΦ log δ−1)/nT + (νF + log T )/n-\"]]]]],[23465,23671,[[[23465,23671,[\"neighborhood of Φ̄ω4, there exists a specific φ̄ω ∈ Φ̄ω such that φ̄ω is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]],[23671,23706,[[[23671,23706,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[23706,23723,[[[23706,23723,[\"[( νF + log(1/δ) \"]]]]],[23723,23726,[[[23723,23726,[\"n0 \"]]]]],[23726,23732,[[[23726,23732,[\") 1 2 \"]]]]],[23732,23739,[[[23732,23739,[\"+ β1/ρ \"]]]]],[23739,23755,[[[23739,23755,[\"( νΦ + log(1/δ) \"]]]]],[23755,23771,[[[23755,23771,[\"nT + νF + log T \"]]]]],[23771,23773,[[[23771,23773,[\"n \"]]]]],[23773,23782,[[[23773,23782,[\") 1 2ρ ] \"]]]]],[23782,23798,[[[23782,23798,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[23798,23807,[[[23798,23807,[\"ω̂tDt,D0 \"]]]]],[23807,23817,[[[23807,23817,[\") . (3.8) \"]]]]],[23817,24027,[[[23817,24027,[\"The upper bound in (3.8) is a superposition of three terms. Let us disregard the log(1/δ) terms for now and focus on the dependence on the sample sizes and problem dimensions. The first term, which scales with \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"2aa5df38-1e2a-4807-b299-7152031bc4bc\"]]],[[\"8087c22b-aaeb-43c8-8224-708f3dbc3aed\"],[null,null,0.6973815519041682,[[null,44343,45056]],[[[44343,44378,[[[44343,44378,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[44378,44395,[[[44378,44395,[\"[( νF + log(1/δ) \"]]]]],[44395,44398,[[[44395,44398,[\"n0 \"]]]]],[44398,44404,[[[44398,44404,[\") 1 2 \"]]]]],[44404,44406,[[[44404,44406,[\"+ \"]]]]],[44406,44428,[[[44406,44428,[\"( νΦ + TνF + log(1/δ) \"]]]]],[44428,44431,[[[44428,44431,[\"Nω \"]]]]],[44431,44440,[[[44431,44440,[\") 1 2ρ ] \"]]]]],[44440,44456,[[[44440,44456,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[44456,44464,[[[44456,44464,[\"ωtDt,D0 \"]]]]],[44464,44474,[[[44464,44474,[\") . (A.1) \"]]]]],[44474,44499,[[[44474,44499,[\"Proof. See Appendix A.3. \"]]]]],[44499,44743,[[[44499,44743,[\"Note that the task distance naturally appears in the upper bound above. This theorem can be regarded as a predecessor of Theorem 3.1, and we refer readers to Section 3.2 for a detailed exposition on the meaning of each term in the upper bound. \"]]]]],[44743,44801,[[[44743,44801,[\"A.2 A SUFFICIENT CONDITION FOR EXACTLY MATCHING φ̄ω \\u003d φ?0 \"]]]]],[44801,45056,[[[44801,45056,[\"Proposition A.1 (Weighting and task distance minimization). Suppose that for every φ ∈ Φ, there exists two source tasks 1 ≤ t1, t2 ≤ T such that L?t1(φ) ≤ L?0(φ) ≤ L?t2(φ). Then there exists a choice of weights ω, possibly depending on φ, such that dist( \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"8087c22b-aaeb-43c8-8224-708f3dbc3aed\"]]],[[\"66b99153-0387-4672-a6c4-90d222e01c95\"],[null,null,0.6788500196198473,[[null,19331,20304]],[[[19331,19376,[[[19331,19376,[\"N (Φ;L2(QX ); ε) ≤ (CΦ/ε) νΦ , ∀ε \\u003e 0, (3.3) \"]]]]],[19376,19615,[[[19376,19615,[\"whereN (Φ;L2(QX ); ε) is the L2(QX ) covering number of Φ (i.e., the minimum number of L2(QX ) balls3 with radius ε required to cover Φ). In parallel, there exist CF \\u003e 0, νF \\u003e 0, such that for any probability measure QZ on Z ⊆ Rr, we have \"]]]]],[19615,19660,[[[19615,19660,[\"N (F ;L2(QZ); ε) ≤ (CF/ε) νG , ∀ε \\u003e 0, (3.4) \"]]]]],[19660,19720,[[[19660,19720,[\"where N (F ;L2(QZ); ε) is the L2(QZ) covering number of F . \"]]]]],[19720,20304,[[[19720,20304,[\"Uniform entropy generalizes the notion of Vapnik-Chervonenkis dimension (Vapnik, 2013) and allows us to give a unified treatment of regression and classification problems. For this reason, function classes satisfying the above assumption are also referred to as “VC-type classes” in the literature (Koltchinskii, 2006). In particular, if each coordinate of Φ has VC-subgraph dimension c(Φ), then (3.3) is satisfied with νΦ \\u003d Θ(r · c(Φ)) (recall that r is the dimension of the latent space Z). Similarly, if F has VC-subgraph dimension c(F), then (3.4) is satisfied with νF \\u003d Θ(c(F)). \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"66b99153-0387-4672-a6c4-90d222e01c95\"]]],[[\"42afe98b-f57c-4ac5-934a-9dab51fcd934\"],[null,null,0.6349943414497436,[[null,22901,23429]],[[[22901,22990,[[[22901,22990,[\"The following theorem gives performance guarantees for the sample split version of TAWT. \"]]]]],[22990,23429,[[[22990,23429,[\"Theorem 3.1 (Performance of TAWT with sample splitting). Let (φ̂, f̂0) be obtained via solving (3.6)–(3.7). Let Assumptions A and B hold. In addition, assume that the learned weights satisfy ω̂ ∈ Wβ :\\u003d {ω ∈ ∆T−1 : β−1 ≤ ωt/ωt′ ≤ β,∀t 6\\u003d t′}, where β ≥ 1 is an absolute constant. Fix δ ∈ (0, 1). There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any weights ω ∈ Wβ and any representation φ in a Cβ · \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"42afe98b-f57c-4ac5-934a-9dab51fcd934\"]]],[[\"6e184b9e-104b-4482-933e-5a8cde90e0c9\"],[null,null,0.693613329557906,[[null,43770,44343]],[[[43770,43973,[[[43770,43973,[\"Theorem A.1 (Performance of the two-step procedure with fixed weights). Let (φ̂, f̂0) be obtained via solving (2.2)–(2.3) with fixed ω. Let Assumptions A and B hold. Fix any δ ∈ (0, 1) and define Nω \\u003d ( \"]]]]],[43973,43982,[[[43973,43982,[\"∑T t\\u003d1 ω \"]]]]],[43982,43991,[[[43982,43991,[\"2 t /nt) \"]]]]],[43991,44113,[[[43991,44113,[\"−1. There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any representation φ in a C \"]]]]],[44113,44155,[[[44113,44155,[\"√ (νΦ + TνF + log δ−1)/Nω-neighborhood of \"]]]]],[44155,44343,[[[44155,44343,[\"Φ̄ω6, there exists a specific φ̄ω ∈ Φ̄ω such that φ is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"6e184b9e-104b-4482-933e-5a8cde90e0c9\"]]],[[\"8f24a291-682b-4388-b3c9-7064c109eb1a\"],[null,null,0.6513285309780665,[[null,6569,7274]],[[[6569,6608,[[[6569,6608,[\"2 TAWT: TARGET-AWARE WEIGHTED TRAINING \"]]]]],[6608,6626,[[[6608,6626,[\"2.1 PRELIMINARIES \"]]]]],[6626,7274,[[[6626,7274,[\"Suppose we have T source tasks, represented by a collection of probability distributions {Dt}Tt\\u003d1 on the sample space X × Y , where X ⊆ Rd is the feature space and Y ⊆ R is the label space. For classification problems, we take Y to be a finite subset of R. We have a single target task, whose probability distribution is denoted as D0. For the t-th task, where t \\u003d 0, 1, . . . , T , we observe nt i.i.d. samples St \\u003d {(xti, yti)}nti\\u003d1 from Dt. Typically, the number of samples from the target task, n0, is much smaller than the samples from the source tasks, and the goal is to use samples from source tasks to aid the learning of the target task. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"8f24a291-682b-4388-b3c9-7064c109eb1a\"]]]]]],[[null,null,0.6619592623438405,[[null,23429,24027]],[[[23429,23465,[[[23429,23465,[\"√ (νΦ log δ−1)/nT + (νF + log T )/n-\"]]]]],[23465,23671,[[[23465,23671,[\"neighborhood of Φ̄ω4, there exists a specific φ̄ω ∈ Φ̄ω such that φ̄ω is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]],[23671,23706,[[[23671,23706,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[23706,23723,[[[23706,23723,[\"[( νF + log(1/δ) \"]]]]],[23723,23726,[[[23723,23726,[\"n0 \"]]]]],[23726,23732,[[[23726,23732,[\") 1 2 \"]]]]],[23732,23739,[[[23732,23739,[\"+ β1/ρ \"]]]]],[23739,23755,[[[23739,23755,[\"( νΦ + log(1/δ) \"]]]]],[23755,23771,[[[23755,23771,[\"nT + νF + log T \"]]]]],[23771,23773,[[[23771,23773,[\"n \"]]]]],[23773,23782,[[[23773,23782,[\") 1 2ρ ] \"]]]]],[23782,23798,[[[23782,23798,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[23798,23807,[[[23798,23807,[\"ω̂tDt,D0 \"]]]]],[23807,23817,[[[23807,23817,[\") . (3.8) \"]]]]],[23817,24027,[[[23817,24027,[\"The upper bound in (3.8) is a superposition of three terms. Let us disregard the log(1/δ) terms for now and focus on the dependence on the sample sizes and problem dimensions. The first term, which scales with \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"2aa5df38-1e2a-4807-b299-7152031bc4bc\"]],[null,null,0.6973815519041682,[[null,44343,45056]],[[[44343,44378,[[[44343,44378,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[44378,44395,[[[44378,44395,[\"[( νF + log(1/δ) \"]]]]],[44395,44398,[[[44395,44398,[\"n0 \"]]]]],[44398,44404,[[[44398,44404,[\") 1 2 \"]]]]],[44404,44406,[[[44404,44406,[\"+ \"]]]]],[44406,44428,[[[44406,44428,[\"( νΦ + TνF + log(1/δ) \"]]]]],[44428,44431,[[[44428,44431,[\"Nω \"]]]]],[44431,44440,[[[44431,44440,[\") 1 2ρ ] \"]]]]],[44440,44456,[[[44440,44456,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[44456,44464,[[[44456,44464,[\"ωtDt,D0 \"]]]]],[44464,44474,[[[44464,44474,[\") . (A.1) \"]]]]],[44474,44499,[[[44474,44499,[\"Proof. See Appendix A.3. \"]]]]],[44499,44743,[[[44499,44743,[\"Note that the task distance naturally appears in the upper bound above. This theorem can be regarded as a predecessor of Theorem 3.1, and we refer readers to Section 3.2 for a detailed exposition on the meaning of each term in the upper bound. \"]]]]],[44743,44801,[[[44743,44801,[\"A.2 A SUFFICIENT CONDITION FOR EXACTLY MATCHING φ̄ω \\u003d φ?0 \"]]]]],[44801,45056,[[[44801,45056,[\"Proposition A.1 (Weighting and task distance minimization). Suppose that for every φ ∈ Φ, there exists two source tasks 1 ≤ t1, t2 ≤ T such that L?t1(φ) ≤ L?0(φ) ≤ L?t2(φ). Then there exists a choice of weights ω, possibly depending on φ, such that dist( \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"8087c22b-aaeb-43c8-8224-708f3dbc3aed\"]],[null,null,0.6788500196198473,[[null,19331,20304]],[[[19331,19376,[[[19331,19376,[\"N (Φ;L2(QX ); ε) ≤ (CΦ/ε) νΦ , ∀ε \\u003e 0, (3.3) \"]]]]],[19376,19615,[[[19376,19615,[\"whereN (Φ;L2(QX ); ε) is the L2(QX ) covering number of Φ (i.e., the minimum number of L2(QX ) balls3 with radius ε required to cover Φ). In parallel, there exist CF \\u003e 0, νF \\u003e 0, such that for any probability measure QZ on Z ⊆ Rr, we have \"]]]]],[19615,19660,[[[19615,19660,[\"N (F ;L2(QZ); ε) ≤ (CF/ε) νG , ∀ε \\u003e 0, (3.4) \"]]]]],[19660,19720,[[[19660,19720,[\"where N (F ;L2(QZ); ε) is the L2(QZ) covering number of F . \"]]]]],[19720,20304,[[[19720,20304,[\"Uniform entropy generalizes the notion of Vapnik-Chervonenkis dimension (Vapnik, 2013) and allows us to give a unified treatment of regression and classification problems. For this reason, function classes satisfying the above assumption are also referred to as “VC-type classes” in the literature (Koltchinskii, 2006). In particular, if each coordinate of Φ has VC-subgraph dimension c(Φ), then (3.3) is satisfied with νΦ \\u003d Θ(r · c(Φ)) (recall that r is the dimension of the latent space Z). Similarly, if F has VC-subgraph dimension c(F), then (3.4) is satisfied with νF \\u003d Θ(c(F)). \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"66b99153-0387-4672-a6c4-90d222e01c95\"]],[null,null,0.6349943414497436,[[null,22901,23429]],[[[22901,22990,[[[22901,22990,[\"The following theorem gives performance guarantees for the sample split version of TAWT. \"]]]]],[22990,23429,[[[22990,23429,[\"Theorem 3.1 (Performance of TAWT with sample splitting). Let (φ̂, f̂0) be obtained via solving (3.6)–(3.7). Let Assumptions A and B hold. In addition, assume that the learned weights satisfy ω̂ ∈ Wβ :\\u003d {ω ∈ ∆T−1 : β−1 ≤ ωt/ωt′ ≤ β,∀t 6\\u003d t′}, where β ≥ 1 is an absolute constant. Fix δ ∈ (0, 1). There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any weights ω ∈ Wβ and any representation φ in a Cβ · \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"42afe98b-f57c-4ac5-934a-9dab51fcd934\"]],[null,null,0.693613329557906,[[null,43770,44343]],[[[43770,43973,[[[43770,43973,[\"Theorem A.1 (Performance of the two-step procedure with fixed weights). Let (φ̂, f̂0) be obtained via solving (2.2)–(2.3) with fixed ω. Let Assumptions A and B hold. Fix any δ ∈ (0, 1) and define Nω \\u003d ( \"]]]]],[43973,43982,[[[43973,43982,[\"∑T t\\u003d1 ω \"]]]]],[43982,43991,[[[43982,43991,[\"2 t /nt) \"]]]]],[43991,44113,[[[43991,44113,[\"−1. There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any representation φ in a C \"]]]]],[44113,44155,[[[44113,44155,[\"√ (νΦ + TνF + log δ−1)/Nω-neighborhood of \"]]]]],[44155,44343,[[[44155,44343,[\"Φ̄ω6, there exists a specific φ̄ω ∈ Φ̄ω such that φ is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"6e184b9e-104b-4482-933e-5a8cde90e0c9\"]],[null,null,0.6513285309780665,[[null,6569,7274]],[[[6569,6608,[[[6569,6608,[\"2 TAWT: TARGET-AWARE WEIGHTED TRAINING \"]]]]],[6608,6626,[[[6608,6626,[\"2.1 PRELIMINARIES \"]]]]],[6626,7274,[[[6626,7274,[\"Suppose we have T source tasks, represented by a collection of probability distributions {Dt}Tt\\u003d1 on the sample space X × Y , where X ⊆ Rd is the feature space and Y ⊆ R is the label space. For classification problems, we take Y to be a finite subset of R. We have a single target task, whose probability distribution is denoted as D0. For the t-th task, where t \\u003d 0, 1, . . . , T , we observe nt i.i.d. samples St \\u003d {(xti, yti)}nti\\u003d1 from Dt. Typically, the number of samples from the target task, n0, is much smaller than the samples from the source tasks, and the goal is to use samples from source tasks to aid the learning of the target task. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"8f24a291-682b-4388-b3c9-7064c109eb1a\"]]],[[[null,0,202],[0,1]],[[null,277,384],[2]],[[null,397,448],[3,4]],[[null,540,608],[5]],[[null,622,722],[5]],[[null,800,800],[4]],[[null,972,972],[0,1]]]]"]]
24820
[["wrb.fr",null,"[[\"The expressions within the summations that are raised to a power, such as those found in the performance guarantees for TAWT, are always positive based on their definition and context within the sources [1, 2].\\n\\nSpecifically, let's examine the components that form these terms:\\n*   **`νΦ` and `νF`**: These are constants greater than zero, controlling the complexity of function classes via uniform entropy [3].\\n*   **`log(1/δ)`**: `δ` is a probability defined within the interval (0, 1) [4, 5]. Therefore, `1/δ` will always be greater than 1, ensuring that `log(1/δ)` is a positive value.\\n*   **`T`**: Represents the number of source tasks, which is a positive integer [6].\\n*   **`n0`, `n`, `nT`, `Nω`**: These refer to sample sizes or quantities derived from sample sizes, which are inherently positive [6]. For example, `Nω` is defined as `(∑T t\\u003d1 ωt^2 / nt)^-1`, where `ωt ≥ 0` and `nt \\u003e 0` [5]. This construction guarantees `Nω` is positive.\\n\\nGiven that all individual components are positive, their sums and ratios, as structured in the theorems, will also be **positive** [1, 2].\\n\\nThe exponent `1/ρ` or `1/2ρ` is well-defined because `ρ \\u003e 0` [7]. Since the base of the power is always positive, taking a fractional power (like a root) of these expressions is a standard mathematical operation and does not present any issues related to non-positive values. The sources do not discuss scenarios where these expressions might be non-positive because, by their fundamental definitions, they cannot be.\",null,[\"f44a53b9-4080-4345-b5a3-73505bc249c5\",\"877bb796-89b9-49b0-8005-5e41f381bd6b\",1683326744],null,[[[[0,203,[[[0,203,[\"The expressions within the summations that are raised to a power, such as those found in the performance guarantees for TAWT, are always positive based on their definition and context within the sources.\"]]]]],[203,268,[[[203,268,[\"Specifically, let's examine the components that form these terms:\"]]]]],[268,385,[[[268,270,[\"νΦ\",[true,null,null,null,null,null,null,true]]],[270,275,[\" and \",[true]]],[275,277,[\"νF\",[true,null,null,null,null,null,null,true]]],[277,385,[\": These are constants greater than zero, controlling the complexity of function classes via uniform entropy.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[385,539,[[[385,393,[\"log(1/δ)\",[true,null,null,null,null,null,null,true]]],[393,395,[\": \"]],[395,396,[\"δ\",[null,null,null,null,null,null,null,true]]],[396,461,[\" is a probability defined within the interval (0, 1). Therefore, \"]],[461,464,[\"1/δ\",[null,null,null,null,null,null,null,true]]],[464,510,[\" will always be greater than 1, ensuring that \"]],[510,518,[\"log(1/δ)\",[null,null,null,null,null,null,null,true]]],[518,539,[\" is a positive value.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[539,609,[[[539,540,[\"T\",[true,null,null,null,null,null,null,true]]],[540,609,[\": Represents the number of source tasks, which is a positive integer.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]],[609,846,[[[609,611,[\"n0\",[true,null,null,null,null,null,null,true]]],[611,613,[\", \",[true]]],[613,614,[\"n\",[true,null,null,null,null,null,null,true]]],[614,616,[\", \",[true]]],[616,618,[\"nT\",[true,null,null,null,null,null,null,true]]],[618,620,[\", \",[true]]],[620,622,[\"Nω\",[true,null,null,null,null,null,null,true]]],[622,737,[\": These refer to sample sizes or quantities derived from sample sizes, which are inherently positive. For example, \"]],[737,739,[\"Nω\",[null,null,null,null,null,null,null,true]]],[739,754,[\" is defined as \"]],[754,775,[\"(∑T t\\u003d1 ωt^2 / nt)^-1\",[null,null,null,null,null,null,null,true]]],[775,783,[\", where \"]],[783,789,[\"ωt ≥ 0\",[null,null,null,null,null,null,null,true]]],[789,794,[\" and \"]],[794,800,[\"nt \\u003e 0\",[null,null,null,null,null,null,null,true]]],[800,831,[\". This construction guarantees \"]],[831,833,[\"Nω\",[null,null,null,null,null,null,null,true]]],[833,846,[\" is positive.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,3]]],[846,973,[[[846,964,[\"Given that all individual components are positive, their sums and ratios, as structured in the theorems, will also be \"]],[964,972,[\"positive\",[true]]],[972,973,[\".\"]]]]]],[[[\"9e5d9f45-9e51-4914-b3a0-ecf5c6aeaae4\"],[null,0,202]],[[\"d3e64a16-9cf6-4d4d-8383-c975581fef97\"],[null,0,202]],[[\"0563e882-38c5-4f22-ae56-2bd29729cd5b\"],[null,277,384]],[[\"caec9ceb-7ef7-4fb9-a89b-d57e74328a04\"],[null,397,448]],[[\"905c5f22-87b6-4432-9a13-479b86ab3627\"],[null,397,448]],[[\"a3c12785-6cbd-421d-953e-5b6f95239eb7\"],[null,540,608]],[[\"a3c12785-6cbd-421d-953e-5b6f95239eb7\"],[null,622,722]],[[\"905c5f22-87b6-4432-9a13-479b86ab3627\"],[null,800,800]],[[\"9e5d9f45-9e51-4914-b3a0-ecf5c6aeaae4\"],[null,972,972]],[[\"d3e64a16-9cf6-4d4d-8383-c975581fef97\"],[null,972,972]],[[\"eac6138f-fa20-48b6-ab63-0c1b78002855\"],[null,1027,1027]]]],null,null,[[[\"9e5d9f45-9e51-4914-b3a0-ecf5c6aeaae4\"],[null,null,0.6619592623438405,[[null,23429,24027]],[[[23429,23465,[[[23429,23465,[\"√ (νΦ log δ−1)/nT + (νF + log T )/n-\"]]]]],[23465,23671,[[[23465,23671,[\"neighborhood of Φ̄ω4, there exists a specific φ̄ω ∈ Φ̄ω such that φ̄ω is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]],[23671,23706,[[[23671,23706,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[23706,23723,[[[23706,23723,[\"[( νF + log(1/δ) \"]]]]],[23723,23726,[[[23723,23726,[\"n0 \"]]]]],[23726,23732,[[[23726,23732,[\") 1 2 \"]]]]],[23732,23739,[[[23732,23739,[\"+ β1/ρ \"]]]]],[23739,23755,[[[23739,23755,[\"( νΦ + log(1/δ) \"]]]]],[23755,23771,[[[23755,23771,[\"nT + νF + log T \"]]]]],[23771,23773,[[[23771,23773,[\"n \"]]]]],[23773,23782,[[[23773,23782,[\") 1 2ρ ] \"]]]]],[23782,23798,[[[23782,23798,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[23798,23807,[[[23798,23807,[\"ω̂tDt,D0 \"]]]]],[23807,23817,[[[23807,23817,[\") . (3.8) \"]]]]],[23817,24027,[[[23817,24027,[\"The upper bound in (3.8) is a superposition of three terms. Let us disregard the log(1/δ) terms for now and focus on the dependence on the sample sizes and problem dimensions. The first term, which scales with \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"9e5d9f45-9e51-4914-b3a0-ecf5c6aeaae4\"]]],[[\"d3e64a16-9cf6-4d4d-8383-c975581fef97\"],[null,null,0.6973815519041682,[[null,44343,45056]],[[[44343,44378,[[[44343,44378,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[44378,44395,[[[44378,44395,[\"[( νF + log(1/δ) \"]]]]],[44395,44398,[[[44395,44398,[\"n0 \"]]]]],[44398,44404,[[[44398,44404,[\") 1 2 \"]]]]],[44404,44406,[[[44404,44406,[\"+ \"]]]]],[44406,44428,[[[44406,44428,[\"( νΦ + TνF + log(1/δ) \"]]]]],[44428,44431,[[[44428,44431,[\"Nω \"]]]]],[44431,44440,[[[44431,44440,[\") 1 2ρ ] \"]]]]],[44440,44456,[[[44440,44456,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[44456,44464,[[[44456,44464,[\"ωtDt,D0 \"]]]]],[44464,44474,[[[44464,44474,[\") . (A.1) \"]]]]],[44474,44499,[[[44474,44499,[\"Proof. See Appendix A.3. \"]]]]],[44499,44743,[[[44499,44743,[\"Note that the task distance naturally appears in the upper bound above. This theorem can be regarded as a predecessor of Theorem 3.1, and we refer readers to Section 3.2 for a detailed exposition on the meaning of each term in the upper bound. \"]]]]],[44743,44801,[[[44743,44801,[\"A.2 A SUFFICIENT CONDITION FOR EXACTLY MATCHING φ̄ω \\u003d φ?0 \"]]]]],[44801,45056,[[[44801,45056,[\"Proposition A.1 (Weighting and task distance minimization). Suppose that for every φ ∈ Φ, there exists two source tasks 1 ≤ t1, t2 ≤ T such that L?t1(φ) ≤ L?0(φ) ≤ L?t2(φ). Then there exists a choice of weights ω, possibly depending on φ, such that dist( \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"d3e64a16-9cf6-4d4d-8383-c975581fef97\"]]],[[\"0563e882-38c5-4f22-ae56-2bd29729cd5b\"],[null,null,0.6788500196198473,[[null,19331,20304]],[[[19331,19376,[[[19331,19376,[\"N (Φ;L2(QX ); ε) ≤ (CΦ/ε) νΦ , ∀ε \\u003e 0, (3.3) \"]]]]],[19376,19615,[[[19376,19615,[\"whereN (Φ;L2(QX ); ε) is the L2(QX ) covering number of Φ (i.e., the minimum number of L2(QX ) balls3 with radius ε required to cover Φ). In parallel, there exist CF \\u003e 0, νF \\u003e 0, such that for any probability measure QZ on Z ⊆ Rr, we have \"]]]]],[19615,19660,[[[19615,19660,[\"N (F ;L2(QZ); ε) ≤ (CF/ε) νG , ∀ε \\u003e 0, (3.4) \"]]]]],[19660,19720,[[[19660,19720,[\"where N (F ;L2(QZ); ε) is the L2(QZ) covering number of F . \"]]]]],[19720,20304,[[[19720,20304,[\"Uniform entropy generalizes the notion of Vapnik-Chervonenkis dimension (Vapnik, 2013) and allows us to give a unified treatment of regression and classification problems. For this reason, function classes satisfying the above assumption are also referred to as “VC-type classes” in the literature (Koltchinskii, 2006). In particular, if each coordinate of Φ has VC-subgraph dimension c(Φ), then (3.3) is satisfied with νΦ \\u003d Θ(r · c(Φ)) (recall that r is the dimension of the latent space Z). Similarly, if F has VC-subgraph dimension c(F), then (3.4) is satisfied with νF \\u003d Θ(c(F)). \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"0563e882-38c5-4f22-ae56-2bd29729cd5b\"]]],[[\"caec9ceb-7ef7-4fb9-a89b-d57e74328a04\"],[null,null,0.6349943414497436,[[null,22901,23429]],[[[22901,22990,[[[22901,22990,[\"The following theorem gives performance guarantees for the sample split version of TAWT. \"]]]]],[22990,23429,[[[22990,23429,[\"Theorem 3.1 (Performance of TAWT with sample splitting). Let (φ̂, f̂0) be obtained via solving (3.6)–(3.7). Let Assumptions A and B hold. In addition, assume that the learned weights satisfy ω̂ ∈ Wβ :\\u003d {ω ∈ ∆T−1 : β−1 ≤ ωt/ωt′ ≤ β,∀t 6\\u003d t′}, where β ≥ 1 is an absolute constant. Fix δ ∈ (0, 1). There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any weights ω ∈ Wβ and any representation φ in a Cβ · \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"caec9ceb-7ef7-4fb9-a89b-d57e74328a04\"]]],[[\"905c5f22-87b6-4432-9a13-479b86ab3627\"],[null,null,0.693613329557906,[[null,43770,44343]],[[[43770,43973,[[[43770,43973,[\"Theorem A.1 (Performance of the two-step procedure with fixed weights). Let (φ̂, f̂0) be obtained via solving (2.2)–(2.3) with fixed ω. Let Assumptions A and B hold. Fix any δ ∈ (0, 1) and define Nω \\u003d ( \"]]]]],[43973,43982,[[[43973,43982,[\"∑T t\\u003d1 ω \"]]]]],[43982,43991,[[[43982,43991,[\"2 t /nt) \"]]]]],[43991,44113,[[[43991,44113,[\"−1. There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any representation φ in a C \"]]]]],[44113,44155,[[[44113,44155,[\"√ (νΦ + TνF + log δ−1)/Nω-neighborhood of \"]]]]],[44155,44343,[[[44155,44343,[\"Φ̄ω6, there exists a specific φ̄ω ∈ Φ̄ω such that φ is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"905c5f22-87b6-4432-9a13-479b86ab3627\"]]],[[\"a3c12785-6cbd-421d-953e-5b6f95239eb7\"],[null,null,0.6513285309780665,[[null,6569,7274]],[[[6569,6608,[[[6569,6608,[\"2 TAWT: TARGET-AWARE WEIGHTED TRAINING \"]]]]],[6608,6626,[[[6608,6626,[\"2.1 PRELIMINARIES \"]]]]],[6626,7274,[[[6626,7274,[\"Suppose we have T source tasks, represented by a collection of probability distributions {Dt}Tt\\u003d1 on the sample space X × Y , where X ⊆ Rd is the feature space and Y ⊆ R is the label space. For classification problems, we take Y to be a finite subset of R. We have a single target task, whose probability distribution is denoted as D0. For the t-th task, where t \\u003d 0, 1, . . . , T , we observe nt i.i.d. samples St \\u003d {(xti, yti)}nti\\u003d1 from Dt. Typically, the number of samples from the target task, n0, is much smaller than the samples from the source tasks, and the goal is to use samples from source tasks to aid the learning of the target task. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"a3c12785-6cbd-421d-953e-5b6f95239eb7\"]]],[[\"eac6138f-fa20-48b6-ab63-0c1b78002855\"],[null,null,0.693413539935627,[[null,20304,21441]],[[[20304,20635,[[[20304,20635,[\"The following definition characterizes how “transferable” a representation φ is from the ω-weighted source tasks to the target task. Definition 3.2 (Transferability). A representation φ ∈ Φ is (ρ, Cρ)-transferable from ω-weighted source tasks to the target task, if there exists ρ \\u003e 0, Cρ \\u003e 0 such that for any φ̄ω ∈ Φ̄ω , we have \"]]]]],[20635,20666,[[[20635,20666,[\"L?0(φ)− L?0(φ̄ω) ≤ Cρ ( T∑ t\\u003d1 \"]]]]],[20666,20689,[[[20666,20689,[\"ωt[L?t (φ)− L?t (φ̄ω)] \"]]]]],[20689,20694,[[[20689,20694,[\")1/ρ \"]]]]],[20694,20702,[[[20694,20702,[\". (3.5) \"]]]]],[20702,21441,[[[20702,21441,[\"Intuitively, the above definition says that relative to φ̄ω, the risk of φ on the target task can be controlled by a polynomial of the average risk of φ on the source tasks. This can be regarded as an adaptation of the notions of transfer exponent and relative signal exponent (Hanneke \\u0026 Kpotufe, 2019; Cai \\u0026 Wei, 2021) originated from the transfer learning literature to the representation learning setting. This can also be seen as a generalization of the task diversity assumption (Tripuraneni et al., 2020; Du et al., 2021) to the case when the optimal representations {φ?t }Tt\\u003d1 do not coincide. In addition, Tripuraneni et al. (2020) and Du et al. (2021) proved that ρ \\u003d 1 in certain simple models and under some simplified setting. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"eac6138f-fa20-48b6-ab63-0c1b78002855\"]]]]]],[[null,null,0.6619592623438405,[[null,23429,24027]],[[[23429,23465,[[[23429,23465,[\"√ (νΦ log δ−1)/nT + (νF + log T )/n-\"]]]]],[23465,23671,[[[23465,23671,[\"neighborhood of Φ̄ω4, there exists a specific φ̄ω ∈ Φ̄ω such that φ̄ω is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]],[23671,23706,[[[23671,23706,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[23706,23723,[[[23706,23723,[\"[( νF + log(1/δ) \"]]]]],[23723,23726,[[[23723,23726,[\"n0 \"]]]]],[23726,23732,[[[23726,23732,[\") 1 2 \"]]]]],[23732,23739,[[[23732,23739,[\"+ β1/ρ \"]]]]],[23739,23755,[[[23739,23755,[\"( νΦ + log(1/δ) \"]]]]],[23755,23771,[[[23755,23771,[\"nT + νF + log T \"]]]]],[23771,23773,[[[23771,23773,[\"n \"]]]]],[23773,23782,[[[23773,23782,[\") 1 2ρ ] \"]]]]],[23782,23798,[[[23782,23798,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[23798,23807,[[[23798,23807,[\"ω̂tDt,D0 \"]]]]],[23807,23817,[[[23807,23817,[\") . (3.8) \"]]]]],[23817,24027,[[[23817,24027,[\"The upper bound in (3.8) is a superposition of three terms. Let us disregard the log(1/δ) terms for now and focus on the dependence on the sample sizes and problem dimensions. The first term, which scales with \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"9e5d9f45-9e51-4914-b3a0-ecf5c6aeaae4\"]],[null,null,0.6973815519041682,[[null,44343,45056]],[[[44343,44378,[[[44343,44378,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[44378,44395,[[[44378,44395,[\"[( νF + log(1/δ) \"]]]]],[44395,44398,[[[44395,44398,[\"n0 \"]]]]],[44398,44404,[[[44398,44404,[\") 1 2 \"]]]]],[44404,44406,[[[44404,44406,[\"+ \"]]]]],[44406,44428,[[[44406,44428,[\"( νΦ + TνF + log(1/δ) \"]]]]],[44428,44431,[[[44428,44431,[\"Nω \"]]]]],[44431,44440,[[[44431,44440,[\") 1 2ρ ] \"]]]]],[44440,44456,[[[44440,44456,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[44456,44464,[[[44456,44464,[\"ωtDt,D0 \"]]]]],[44464,44474,[[[44464,44474,[\") . (A.1) \"]]]]],[44474,44499,[[[44474,44499,[\"Proof. See Appendix A.3. \"]]]]],[44499,44743,[[[44499,44743,[\"Note that the task distance naturally appears in the upper bound above. This theorem can be regarded as a predecessor of Theorem 3.1, and we refer readers to Section 3.2 for a detailed exposition on the meaning of each term in the upper bound. \"]]]]],[44743,44801,[[[44743,44801,[\"A.2 A SUFFICIENT CONDITION FOR EXACTLY MATCHING φ̄ω \\u003d φ?0 \"]]]]],[44801,45056,[[[44801,45056,[\"Proposition A.1 (Weighting and task distance minimization). Suppose that for every φ ∈ Φ, there exists two source tasks 1 ≤ t1, t2 ≤ T such that L?t1(φ) ≤ L?0(φ) ≤ L?t2(φ). Then there exists a choice of weights ω, possibly depending on φ, such that dist( \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"d3e64a16-9cf6-4d4d-8383-c975581fef97\"]],[null,null,0.6788500196198473,[[null,19331,20304]],[[[19331,19376,[[[19331,19376,[\"N (Φ;L2(QX ); ε) ≤ (CΦ/ε) νΦ , ∀ε \\u003e 0, (3.3) \"]]]]],[19376,19615,[[[19376,19615,[\"whereN (Φ;L2(QX ); ε) is the L2(QX ) covering number of Φ (i.e., the minimum number of L2(QX ) balls3 with radius ε required to cover Φ). In parallel, there exist CF \\u003e 0, νF \\u003e 0, such that for any probability measure QZ on Z ⊆ Rr, we have \"]]]]],[19615,19660,[[[19615,19660,[\"N (F ;L2(QZ); ε) ≤ (CF/ε) νG , ∀ε \\u003e 0, (3.4) \"]]]]],[19660,19720,[[[19660,19720,[\"where N (F ;L2(QZ); ε) is the L2(QZ) covering number of F . \"]]]]],[19720,20304,[[[19720,20304,[\"Uniform entropy generalizes the notion of Vapnik-Chervonenkis dimension (Vapnik, 2013) and allows us to give a unified treatment of regression and classification problems. For this reason, function classes satisfying the above assumption are also referred to as “VC-type classes” in the literature (Koltchinskii, 2006). In particular, if each coordinate of Φ has VC-subgraph dimension c(Φ), then (3.3) is satisfied with νΦ \\u003d Θ(r · c(Φ)) (recall that r is the dimension of the latent space Z). Similarly, if F has VC-subgraph dimension c(F), then (3.4) is satisfied with νF \\u003d Θ(c(F)). \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"0563e882-38c5-4f22-ae56-2bd29729cd5b\"]],[null,null,0.6349943414497436,[[null,22901,23429]],[[[22901,22990,[[[22901,22990,[\"The following theorem gives performance guarantees for the sample split version of TAWT. \"]]]]],[22990,23429,[[[22990,23429,[\"Theorem 3.1 (Performance of TAWT with sample splitting). Let (φ̂, f̂0) be obtained via solving (3.6)–(3.7). Let Assumptions A and B hold. In addition, assume that the learned weights satisfy ω̂ ∈ Wβ :\\u003d {ω ∈ ∆T−1 : β−1 ≤ ωt/ωt′ ≤ β,∀t 6\\u003d t′}, where β ≥ 1 is an absolute constant. Fix δ ∈ (0, 1). There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any weights ω ∈ Wβ and any representation φ in a Cβ · \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"caec9ceb-7ef7-4fb9-a89b-d57e74328a04\"]],[null,null,0.693613329557906,[[null,43770,44343]],[[[43770,43973,[[[43770,43973,[\"Theorem A.1 (Performance of the two-step procedure with fixed weights). Let (φ̂, f̂0) be obtained via solving (2.2)–(2.3) with fixed ω. Let Assumptions A and B hold. Fix any δ ∈ (0, 1) and define Nω \\u003d ( \"]]]]],[43973,43982,[[[43973,43982,[\"∑T t\\u003d1 ω \"]]]]],[43982,43991,[[[43982,43991,[\"2 t /nt) \"]]]]],[43991,44113,[[[43991,44113,[\"−1. There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any representation φ in a C \"]]]]],[44113,44155,[[[44113,44155,[\"√ (νΦ + TνF + log δ−1)/Nω-neighborhood of \"]]]]],[44155,44343,[[[44155,44343,[\"Φ̄ω6, there exists a specific φ̄ω ∈ Φ̄ω such that φ is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"905c5f22-87b6-4432-9a13-479b86ab3627\"]],[null,null,0.6513285309780665,[[null,6569,7274]],[[[6569,6608,[[[6569,6608,[\"2 TAWT: TARGET-AWARE WEIGHTED TRAINING \"]]]]],[6608,6626,[[[6608,6626,[\"2.1 PRELIMINARIES \"]]]]],[6626,7274,[[[6626,7274,[\"Suppose we have T source tasks, represented by a collection of probability distributions {Dt}Tt\\u003d1 on the sample space X × Y , where X ⊆ Rd is the feature space and Y ⊆ R is the label space. For classification problems, we take Y to be a finite subset of R. We have a single target task, whose probability distribution is denoted as D0. For the t-th task, where t \\u003d 0, 1, . . . , T , we observe nt i.i.d. samples St \\u003d {(xti, yti)}nti\\u003d1 from Dt. Typically, the number of samples from the target task, n0, is much smaller than the samples from the source tasks, and the goal is to use samples from source tasks to aid the learning of the target task. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"a3c12785-6cbd-421d-953e-5b6f95239eb7\"]],[null,null,0.693413539935627,[[null,20304,21441]],[[[20304,20635,[[[20304,20635,[\"The following definition characterizes how “transferable” a representation φ is from the ω-weighted source tasks to the target task. Definition 3.2 (Transferability). A representation φ ∈ Φ is (ρ, Cρ)-transferable from ω-weighted source tasks to the target task, if there exists ρ \\u003e 0, Cρ \\u003e 0 such that for any φ̄ω ∈ Φ̄ω , we have \"]]]]],[20635,20666,[[[20635,20666,[\"L?0(φ)− L?0(φ̄ω) ≤ Cρ ( T∑ t\\u003d1 \"]]]]],[20666,20689,[[[20666,20689,[\"ωt[L?t (φ)− L?t (φ̄ω)] \"]]]]],[20689,20694,[[[20689,20694,[\")1/ρ \"]]]]],[20694,20702,[[[20694,20702,[\". (3.5) \"]]]]],[20702,21441,[[[20702,21441,[\"Intuitively, the above definition says that relative to φ̄ω, the risk of φ on the target task can be controlled by a polynomial of the average risk of φ on the source tasks. This can be regarded as an adaptation of the notions of transfer exponent and relative signal exponent (Hanneke \\u0026 Kpotufe, 2019; Cai \\u0026 Wei, 2021) originated from the transfer learning literature to the representation learning setting. This can also be seen as a generalization of the task diversity assumption (Tripuraneni et al., 2020; Du et al., 2021) to the case when the optimal representations {φ?t }Tt\\u003d1 do not coincide. In addition, Tripuraneni et al. (2020) and Du et al. (2021) proved that ρ \\u003d 1 in certain simple models and under some simplified setting. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"eac6138f-fa20-48b6-ab63-0c1b78002855\"]]],[[[null,0,202],[0,1]],[[null,277,384],[2]],[[null,397,448],[3,4]],[[null,540,608],[5]],[[null,622,722],[5]],[[null,800,800],[4]],[[null,972,972],[0,1]],[[null,1027,1027],[6]]]]"]]
25498
[["wrb.fr",null,"[[\"The expressions within the summations that are raised to a power, such as those found in the performance guarantees for TAWT, are always positive based on their definition and context within the sources [1, 2].\\n\\nSpecifically, let's examine the components that form these terms:\\n*   **`νΦ` and `νF`**: These are constants greater than zero, controlling the complexity of function classes via uniform entropy [3].\\n*   **`log(1/δ)`**: `δ` is a probability defined within the interval (0, 1) [4, 5]. Therefore, `1/δ` will always be greater than 1, ensuring that `log(1/δ)` is a positive value.\\n*   **`T`**: Represents the number of source tasks, which is a positive integer [6].\\n*   **`n0`, `n`, `nT`, `Nω`**: These refer to sample sizes or quantities derived from sample sizes, which are inherently positive [6]. For example, `Nω` is defined as `(∑T t\\u003d1 ωt^2 / nt)^-1`, where `ωt ≥ 0` and `nt \\u003e 0` [5]. This construction guarantees `Nω` is positive.\\n\\nGiven that all individual components are positive, their sums and ratios, as structured in the theorems, will also be **positive** [1, 2].\\n\\nThe exponent `1/ρ` or `1/2ρ` is well-defined because `ρ \\u003e 0` [7]. Since the base of the power is always positive, taking a fractional power (like a root) of these expressions is a standard mathematical operation and does not present any issues related to non-positive values. The sources do not discuss scenarios where these expressions might be non-positive because, by their fundamental definitions, they cannot be.\",null,[\"f44a53b9-4080-4345-b5a3-73505bc249c5\",\"877bb796-89b9-49b0-8005-5e41f381bd6b\",1683326744],null,[[[[0,203,[[[0,203,[\"The expressions within the summations that are raised to a power, such as those found in the performance guarantees for TAWT, are always positive based on their definition and context within the sources.\"]]]]],[203,268,[[[203,268,[\"Specifically, let's examine the components that form these terms:\"]]]]],[268,385,[[[268,270,[\"νΦ\",[true,null,null,null,null,null,null,true]]],[270,275,[\" and \",[true]]],[275,277,[\"νF\",[true,null,null,null,null,null,null,true]]],[277,385,[\": These are constants greater than zero, controlling the complexity of function classes via uniform entropy.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[385,539,[[[385,393,[\"log(1/δ)\",[true,null,null,null,null,null,null,true]]],[393,395,[\": \"]],[395,396,[\"δ\",[null,null,null,null,null,null,null,true]]],[396,461,[\" is a probability defined within the interval (0, 1). Therefore, \"]],[461,464,[\"1/δ\",[null,null,null,null,null,null,null,true]]],[464,510,[\" will always be greater than 1, ensuring that \"]],[510,518,[\"log(1/δ)\",[null,null,null,null,null,null,null,true]]],[518,539,[\" is a positive value.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[539,609,[[[539,540,[\"T\",[true,null,null,null,null,null,null,true]]],[540,609,[\": Represents the number of source tasks, which is a positive integer.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]],[609,846,[[[609,611,[\"n0\",[true,null,null,null,null,null,null,true]]],[611,613,[\", \",[true]]],[613,614,[\"n\",[true,null,null,null,null,null,null,true]]],[614,616,[\", \",[true]]],[616,618,[\"nT\",[true,null,null,null,null,null,null,true]]],[618,620,[\", \",[true]]],[620,622,[\"Nω\",[true,null,null,null,null,null,null,true]]],[622,737,[\": These refer to sample sizes or quantities derived from sample sizes, which are inherently positive. For example, \"]],[737,739,[\"Nω\",[null,null,null,null,null,null,null,true]]],[739,754,[\" is defined as \"]],[754,775,[\"(∑T t\\u003d1 ωt^2 / nt)^-1\",[null,null,null,null,null,null,null,true]]],[775,783,[\", where \"]],[783,789,[\"ωt ≥ 0\",[null,null,null,null,null,null,null,true]]],[789,794,[\" and \"]],[794,800,[\"nt \\u003e 0\",[null,null,null,null,null,null,null,true]]],[800,831,[\". This construction guarantees \"]],[831,833,[\"Nω\",[null,null,null,null,null,null,null,true]]],[833,846,[\" is positive.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,3]]],[846,973,[[[846,964,[\"Given that all individual components are positive, their sums and ratios, as structured in the theorems, will also be \"]],[964,972,[\"positive\",[true]]],[972,973,[\".\"]]]]],[973,1380,[[[973,986,[\"The exponent \"]],[986,989,[\"1/ρ\",[null,null,null,null,null,null,null,true]]],[989,993,[\" or \"]],[993,997,[\"1/2ρ\",[null,null,null,null,null,null,null,true]]],[997,1022,[\" is well-defined because \"]],[1022,1027,[\"ρ \\u003e 0\",[null,null,null,null,null,null,null,true]]],[1027,1380,[\". Since the base of the power is always positive, taking a fractional power (like a root) of these expressions is a standard mathematical operation and does not present any issues related to non-positive values. The sources do not discuss scenarios where these expressions might be non-positive because, by their fundamental definitions, they cannot be.\"]]]]]],[[[\"9e5d9f45-9e51-4914-b3a0-ecf5c6aeaae4\"],[null,0,202]],[[\"d3e64a16-9cf6-4d4d-8383-c975581fef97\"],[null,0,202]],[[\"0563e882-38c5-4f22-ae56-2bd29729cd5b\"],[null,277,384]],[[\"caec9ceb-7ef7-4fb9-a89b-d57e74328a04\"],[null,397,448]],[[\"905c5f22-87b6-4432-9a13-479b86ab3627\"],[null,397,448]],[[\"a3c12785-6cbd-421d-953e-5b6f95239eb7\"],[null,540,608]],[[\"a3c12785-6cbd-421d-953e-5b6f95239eb7\"],[null,622,722]],[[\"905c5f22-87b6-4432-9a13-479b86ab3627\"],[null,800,800]],[[\"9e5d9f45-9e51-4914-b3a0-ecf5c6aeaae4\"],[null,972,972]],[[\"d3e64a16-9cf6-4d4d-8383-c975581fef97\"],[null,972,972]],[[\"eac6138f-fa20-48b6-ab63-0c1b78002855\"],[null,1027,1027]]]],null,null,[[[\"9e5d9f45-9e51-4914-b3a0-ecf5c6aeaae4\"],[null,null,0.6619592623438405,[[null,23429,24027]],[[[23429,23465,[[[23429,23465,[\"√ (νΦ log δ−1)/nT + (νF + log T )/n-\"]]]]],[23465,23671,[[[23465,23671,[\"neighborhood of Φ̄ω4, there exists a specific φ̄ω ∈ Φ̄ω such that φ̄ω is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]],[23671,23706,[[[23671,23706,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[23706,23723,[[[23706,23723,[\"[( νF + log(1/δ) \"]]]]],[23723,23726,[[[23723,23726,[\"n0 \"]]]]],[23726,23732,[[[23726,23732,[\") 1 2 \"]]]]],[23732,23739,[[[23732,23739,[\"+ β1/ρ \"]]]]],[23739,23755,[[[23739,23755,[\"( νΦ + log(1/δ) \"]]]]],[23755,23771,[[[23755,23771,[\"nT + νF + log T \"]]]]],[23771,23773,[[[23771,23773,[\"n \"]]]]],[23773,23782,[[[23773,23782,[\") 1 2ρ ] \"]]]]],[23782,23798,[[[23782,23798,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[23798,23807,[[[23798,23807,[\"ω̂tDt,D0 \"]]]]],[23807,23817,[[[23807,23817,[\") . (3.8) \"]]]]],[23817,24027,[[[23817,24027,[\"The upper bound in (3.8) is a superposition of three terms. Let us disregard the log(1/δ) terms for now and focus on the dependence on the sample sizes and problem dimensions. The first term, which scales with \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"9e5d9f45-9e51-4914-b3a0-ecf5c6aeaae4\"]]],[[\"d3e64a16-9cf6-4d4d-8383-c975581fef97\"],[null,null,0.6973815519041682,[[null,44343,45056]],[[[44343,44378,[[[44343,44378,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[44378,44395,[[[44378,44395,[\"[( νF + log(1/δ) \"]]]]],[44395,44398,[[[44395,44398,[\"n0 \"]]]]],[44398,44404,[[[44398,44404,[\") 1 2 \"]]]]],[44404,44406,[[[44404,44406,[\"+ \"]]]]],[44406,44428,[[[44406,44428,[\"( νΦ + TνF + log(1/δ) \"]]]]],[44428,44431,[[[44428,44431,[\"Nω \"]]]]],[44431,44440,[[[44431,44440,[\") 1 2ρ ] \"]]]]],[44440,44456,[[[44440,44456,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[44456,44464,[[[44456,44464,[\"ωtDt,D0 \"]]]]],[44464,44474,[[[44464,44474,[\") . (A.1) \"]]]]],[44474,44499,[[[44474,44499,[\"Proof. See Appendix A.3. \"]]]]],[44499,44743,[[[44499,44743,[\"Note that the task distance naturally appears in the upper bound above. This theorem can be regarded as a predecessor of Theorem 3.1, and we refer readers to Section 3.2 for a detailed exposition on the meaning of each term in the upper bound. \"]]]]],[44743,44801,[[[44743,44801,[\"A.2 A SUFFICIENT CONDITION FOR EXACTLY MATCHING φ̄ω \\u003d φ?0 \"]]]]],[44801,45056,[[[44801,45056,[\"Proposition A.1 (Weighting and task distance minimization). Suppose that for every φ ∈ Φ, there exists two source tasks 1 ≤ t1, t2 ≤ T such that L?t1(φ) ≤ L?0(φ) ≤ L?t2(φ). Then there exists a choice of weights ω, possibly depending on φ, such that dist( \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"d3e64a16-9cf6-4d4d-8383-c975581fef97\"]]],[[\"0563e882-38c5-4f22-ae56-2bd29729cd5b\"],[null,null,0.6788500196198473,[[null,19331,20304]],[[[19331,19376,[[[19331,19376,[\"N (Φ;L2(QX ); ε) ≤ (CΦ/ε) νΦ , ∀ε \\u003e 0, (3.3) \"]]]]],[19376,19615,[[[19376,19615,[\"whereN (Φ;L2(QX ); ε) is the L2(QX ) covering number of Φ (i.e., the minimum number of L2(QX ) balls3 with radius ε required to cover Φ). In parallel, there exist CF \\u003e 0, νF \\u003e 0, such that for any probability measure QZ on Z ⊆ Rr, we have \"]]]]],[19615,19660,[[[19615,19660,[\"N (F ;L2(QZ); ε) ≤ (CF/ε) νG , ∀ε \\u003e 0, (3.4) \"]]]]],[19660,19720,[[[19660,19720,[\"where N (F ;L2(QZ); ε) is the L2(QZ) covering number of F . \"]]]]],[19720,20304,[[[19720,20304,[\"Uniform entropy generalizes the notion of Vapnik-Chervonenkis dimension (Vapnik, 2013) and allows us to give a unified treatment of regression and classification problems. For this reason, function classes satisfying the above assumption are also referred to as “VC-type classes” in the literature (Koltchinskii, 2006). In particular, if each coordinate of Φ has VC-subgraph dimension c(Φ), then (3.3) is satisfied with νΦ \\u003d Θ(r · c(Φ)) (recall that r is the dimension of the latent space Z). Similarly, if F has VC-subgraph dimension c(F), then (3.4) is satisfied with νF \\u003d Θ(c(F)). \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"0563e882-38c5-4f22-ae56-2bd29729cd5b\"]]],[[\"caec9ceb-7ef7-4fb9-a89b-d57e74328a04\"],[null,null,0.6349943414497436,[[null,22901,23429]],[[[22901,22990,[[[22901,22990,[\"The following theorem gives performance guarantees for the sample split version of TAWT. \"]]]]],[22990,23429,[[[22990,23429,[\"Theorem 3.1 (Performance of TAWT with sample splitting). Let (φ̂, f̂0) be obtained via solving (3.6)–(3.7). Let Assumptions A and B hold. In addition, assume that the learned weights satisfy ω̂ ∈ Wβ :\\u003d {ω ∈ ∆T−1 : β−1 ≤ ωt/ωt′ ≤ β,∀t 6\\u003d t′}, where β ≥ 1 is an absolute constant. Fix δ ∈ (0, 1). There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any weights ω ∈ Wβ and any representation φ in a Cβ · \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"caec9ceb-7ef7-4fb9-a89b-d57e74328a04\"]]],[[\"905c5f22-87b6-4432-9a13-479b86ab3627\"],[null,null,0.693613329557906,[[null,43770,44343]],[[[43770,43973,[[[43770,43973,[\"Theorem A.1 (Performance of the two-step procedure with fixed weights). Let (φ̂, f̂0) be obtained via solving (2.2)–(2.3) with fixed ω. Let Assumptions A and B hold. Fix any δ ∈ (0, 1) and define Nω \\u003d ( \"]]]]],[43973,43982,[[[43973,43982,[\"∑T t\\u003d1 ω \"]]]]],[43982,43991,[[[43982,43991,[\"2 t /nt) \"]]]]],[43991,44113,[[[43991,44113,[\"−1. There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any representation φ in a C \"]]]]],[44113,44155,[[[44113,44155,[\"√ (νΦ + TνF + log δ−1)/Nω-neighborhood of \"]]]]],[44155,44343,[[[44155,44343,[\"Φ̄ω6, there exists a specific φ̄ω ∈ Φ̄ω such that φ is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"905c5f22-87b6-4432-9a13-479b86ab3627\"]]],[[\"a3c12785-6cbd-421d-953e-5b6f95239eb7\"],[null,null,0.6513285309780665,[[null,6569,7274]],[[[6569,6608,[[[6569,6608,[\"2 TAWT: TARGET-AWARE WEIGHTED TRAINING \"]]]]],[6608,6626,[[[6608,6626,[\"2.1 PRELIMINARIES \"]]]]],[6626,7274,[[[6626,7274,[\"Suppose we have T source tasks, represented by a collection of probability distributions {Dt}Tt\\u003d1 on the sample space X × Y , where X ⊆ Rd is the feature space and Y ⊆ R is the label space. For classification problems, we take Y to be a finite subset of R. We have a single target task, whose probability distribution is denoted as D0. For the t-th task, where t \\u003d 0, 1, . . . , T , we observe nt i.i.d. samples St \\u003d {(xti, yti)}nti\\u003d1 from Dt. Typically, the number of samples from the target task, n0, is much smaller than the samples from the source tasks, and the goal is to use samples from source tasks to aid the learning of the target task. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"a3c12785-6cbd-421d-953e-5b6f95239eb7\"]]],[[\"eac6138f-fa20-48b6-ab63-0c1b78002855\"],[null,null,0.693413539935627,[[null,20304,21441]],[[[20304,20635,[[[20304,20635,[\"The following definition characterizes how “transferable” a representation φ is from the ω-weighted source tasks to the target task. Definition 3.2 (Transferability). A representation φ ∈ Φ is (ρ, Cρ)-transferable from ω-weighted source tasks to the target task, if there exists ρ \\u003e 0, Cρ \\u003e 0 such that for any φ̄ω ∈ Φ̄ω , we have \"]]]]],[20635,20666,[[[20635,20666,[\"L?0(φ)− L?0(φ̄ω) ≤ Cρ ( T∑ t\\u003d1 \"]]]]],[20666,20689,[[[20666,20689,[\"ωt[L?t (φ)− L?t (φ̄ω)] \"]]]]],[20689,20694,[[[20689,20694,[\")1/ρ \"]]]]],[20694,20702,[[[20694,20702,[\". (3.5) \"]]]]],[20702,21441,[[[20702,21441,[\"Intuitively, the above definition says that relative to φ̄ω, the risk of φ on the target task can be controlled by a polynomial of the average risk of φ on the source tasks. This can be regarded as an adaptation of the notions of transfer exponent and relative signal exponent (Hanneke \\u0026 Kpotufe, 2019; Cai \\u0026 Wei, 2021) originated from the transfer learning literature to the representation learning setting. This can also be seen as a generalization of the task diversity assumption (Tripuraneni et al., 2020; Du et al., 2021) to the case when the optimal representations {φ?t }Tt\\u003d1 do not coincide. In addition, Tripuraneni et al. (2020) and Du et al. (2021) proved that ρ \\u003d 1 in certain simple models and under some simplified setting. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"eac6138f-fa20-48b6-ab63-0c1b78002855\"]]]]]],[[null,null,0.6619592623438405,[[null,23429,24027]],[[[23429,23465,[[[23429,23465,[\"√ (νΦ log δ−1)/nT + (νF + log T )/n-\"]]]]],[23465,23671,[[[23465,23671,[\"neighborhood of Φ̄ω4, there exists a specific φ̄ω ∈ Φ̄ω such that φ̄ω is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]],[23671,23706,[[[23671,23706,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[23706,23723,[[[23706,23723,[\"[( νF + log(1/δ) \"]]]]],[23723,23726,[[[23723,23726,[\"n0 \"]]]]],[23726,23732,[[[23726,23732,[\") 1 2 \"]]]]],[23732,23739,[[[23732,23739,[\"+ β1/ρ \"]]]]],[23739,23755,[[[23739,23755,[\"( νΦ + log(1/δ) \"]]]]],[23755,23771,[[[23755,23771,[\"nT + νF + log T \"]]]]],[23771,23773,[[[23771,23773,[\"n \"]]]]],[23773,23782,[[[23773,23782,[\") 1 2ρ ] \"]]]]],[23782,23798,[[[23782,23798,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[23798,23807,[[[23798,23807,[\"ω̂tDt,D0 \"]]]]],[23807,23817,[[[23807,23817,[\") . (3.8) \"]]]]],[23817,24027,[[[23817,24027,[\"The upper bound in (3.8) is a superposition of three terms. Let us disregard the log(1/δ) terms for now and focus on the dependence on the sample sizes and problem dimensions. The first term, which scales with \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"9e5d9f45-9e51-4914-b3a0-ecf5c6aeaae4\"]],[null,null,0.6973815519041682,[[null,44343,45056]],[[[44343,44378,[[[44343,44378,[\"L0(φ̂, f̂0)− L0(φ?0, f ? 0 ) ≤ C ′ \"]]]]],[44378,44395,[[[44378,44395,[\"[( νF + log(1/δ) \"]]]]],[44395,44398,[[[44395,44398,[\"n0 \"]]]]],[44398,44404,[[[44398,44404,[\") 1 2 \"]]]]],[44404,44406,[[[44404,44406,[\"+ \"]]]]],[44406,44428,[[[44406,44428,[\"( νΦ + TνF + log(1/δ) \"]]]]],[44428,44431,[[[44428,44431,[\"Nω \"]]]]],[44431,44440,[[[44431,44440,[\") 1 2ρ ] \"]]]]],[44440,44456,[[[44440,44456,[\"+ dist ( T∑ t\\u003d1 \"]]]]],[44456,44464,[[[44456,44464,[\"ωtDt,D0 \"]]]]],[44464,44474,[[[44464,44474,[\") . (A.1) \"]]]]],[44474,44499,[[[44474,44499,[\"Proof. See Appendix A.3. \"]]]]],[44499,44743,[[[44499,44743,[\"Note that the task distance naturally appears in the upper bound above. This theorem can be regarded as a predecessor of Theorem 3.1, and we refer readers to Section 3.2 for a detailed exposition on the meaning of each term in the upper bound. \"]]]]],[44743,44801,[[[44743,44801,[\"A.2 A SUFFICIENT CONDITION FOR EXACTLY MATCHING φ̄ω \\u003d φ?0 \"]]]]],[44801,45056,[[[44801,45056,[\"Proposition A.1 (Weighting and task distance minimization). Suppose that for every φ ∈ Φ, there exists two source tasks 1 ≤ t1, t2 ≤ T such that L?t1(φ) ≤ L?0(φ) ≤ L?t2(φ). Then there exists a choice of weights ω, possibly depending on φ, such that dist( \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"d3e64a16-9cf6-4d4d-8383-c975581fef97\"]],[null,null,0.6788500196198473,[[null,19331,20304]],[[[19331,19376,[[[19331,19376,[\"N (Φ;L2(QX ); ε) ≤ (CΦ/ε) νΦ , ∀ε \\u003e 0, (3.3) \"]]]]],[19376,19615,[[[19376,19615,[\"whereN (Φ;L2(QX ); ε) is the L2(QX ) covering number of Φ (i.e., the minimum number of L2(QX ) balls3 with radius ε required to cover Φ). In parallel, there exist CF \\u003e 0, νF \\u003e 0, such that for any probability measure QZ on Z ⊆ Rr, we have \"]]]]],[19615,19660,[[[19615,19660,[\"N (F ;L2(QZ); ε) ≤ (CF/ε) νG , ∀ε \\u003e 0, (3.4) \"]]]]],[19660,19720,[[[19660,19720,[\"where N (F ;L2(QZ); ε) is the L2(QZ) covering number of F . \"]]]]],[19720,20304,[[[19720,20304,[\"Uniform entropy generalizes the notion of Vapnik-Chervonenkis dimension (Vapnik, 2013) and allows us to give a unified treatment of regression and classification problems. For this reason, function classes satisfying the above assumption are also referred to as “VC-type classes” in the literature (Koltchinskii, 2006). In particular, if each coordinate of Φ has VC-subgraph dimension c(Φ), then (3.3) is satisfied with νΦ \\u003d Θ(r · c(Φ)) (recall that r is the dimension of the latent space Z). Similarly, if F has VC-subgraph dimension c(F), then (3.4) is satisfied with νF \\u003d Θ(c(F)). \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"0563e882-38c5-4f22-ae56-2bd29729cd5b\"]],[null,null,0.6349943414497436,[[null,22901,23429]],[[[22901,22990,[[[22901,22990,[\"The following theorem gives performance guarantees for the sample split version of TAWT. \"]]]]],[22990,23429,[[[22990,23429,[\"Theorem 3.1 (Performance of TAWT with sample splitting). Let (φ̂, f̂0) be obtained via solving (3.6)–(3.7). Let Assumptions A and B hold. In addition, assume that the learned weights satisfy ω̂ ∈ Wβ :\\u003d {ω ∈ ∆T−1 : β−1 ≤ ωt/ωt′ ≤ β,∀t 6\\u003d t′}, where β ≥ 1 is an absolute constant. Fix δ ∈ (0, 1). There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any weights ω ∈ Wβ and any representation φ in a Cβ · \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"caec9ceb-7ef7-4fb9-a89b-d57e74328a04\"]],[null,null,0.693613329557906,[[null,43770,44343]],[[[43770,43973,[[[43770,43973,[\"Theorem A.1 (Performance of the two-step procedure with fixed weights). Let (φ̂, f̂0) be obtained via solving (2.2)–(2.3) with fixed ω. Let Assumptions A and B hold. Fix any δ ∈ (0, 1) and define Nω \\u003d ( \"]]]]],[43973,43982,[[[43973,43982,[\"∑T t\\u003d1 ω \"]]]]],[43982,43991,[[[43982,43991,[\"2 t /nt) \"]]]]],[43991,44113,[[[43991,44113,[\"−1. There exists a constant C \\u003d C(L`, LF , CΦ, CF ) \\u003e 0 such that the following holds: if for any representation φ in a C \"]]]]],[44113,44155,[[[44113,44155,[\"√ (νΦ + TνF + log δ−1)/Nω-neighborhood of \"]]]]],[44155,44343,[[[44155,44343,[\"Φ̄ω6, there exists a specific φ̄ω ∈ Φ̄ω such that φ is (ρ, Cρ)-transferable, then there exists another C ′ \\u003d C ′(L`, LF , CΦ, CF , Cρ, ρ) such that with probability at least 1− δ, we have \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"905c5f22-87b6-4432-9a13-479b86ab3627\"]],[null,null,0.6513285309780665,[[null,6569,7274]],[[[6569,6608,[[[6569,6608,[\"2 TAWT: TARGET-AWARE WEIGHTED TRAINING \"]]]]],[6608,6626,[[[6608,6626,[\"2.1 PRELIMINARIES \"]]]]],[6626,7274,[[[6626,7274,[\"Suppose we have T source tasks, represented by a collection of probability distributions {Dt}Tt\\u003d1 on the sample space X × Y , where X ⊆ Rd is the feature space and Y ⊆ R is the label space. For classification problems, we take Y to be a finite subset of R. We have a single target task, whose probability distribution is denoted as D0. For the t-th task, where t \\u003d 0, 1, . . . , T , we observe nt i.i.d. samples St \\u003d {(xti, yti)}nti\\u003d1 from Dt. Typically, the number of samples from the target task, n0, is much smaller than the samples from the source tasks, and the goal is to use samples from source tasks to aid the learning of the target task. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"a3c12785-6cbd-421d-953e-5b6f95239eb7\"]],[null,null,0.693413539935627,[[null,20304,21441]],[[[20304,20635,[[[20304,20635,[\"The following definition characterizes how “transferable” a representation φ is from the ω-weighted source tasks to the target task. Definition 3.2 (Transferability). A representation φ ∈ Φ is (ρ, Cρ)-transferable from ω-weighted source tasks to the target task, if there exists ρ \\u003e 0, Cρ \\u003e 0 such that for any φ̄ω ∈ Φ̄ω , we have \"]]]]],[20635,20666,[[[20635,20666,[\"L?0(φ)− L?0(φ̄ω) ≤ Cρ ( T∑ t\\u003d1 \"]]]]],[20666,20689,[[[20666,20689,[\"ωt[L?t (φ)− L?t (φ̄ω)] \"]]]]],[20689,20694,[[[20689,20694,[\")1/ρ \"]]]]],[20694,20702,[[[20694,20702,[\". (3.5) \"]]]]],[20702,21441,[[[20702,21441,[\"Intuitively, the above definition says that relative to φ̄ω, the risk of φ on the target task can be controlled by a polynomial of the average risk of φ on the source tasks. This can be regarded as an adaptation of the notions of transfer exponent and relative signal exponent (Hanneke \\u0026 Kpotufe, 2019; Cai \\u0026 Wei, 2021) originated from the transfer learning literature to the representation learning setting. This can also be seen as a generalization of the task diversity assumption (Tripuraneni et al., 2020; Du et al., 2021) to the case when the optimal representations {φ?t }Tt\\u003d1 do not coincide. In addition, Tripuraneni et al. (2020) and Du et al. (2021) proved that ρ \\u003d 1 in certain simple models and under some simplified setting. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"eac6138f-fa20-48b6-ab63-0c1b78002855\"]]],[[[null,0,202],[0,1]],[[null,277,384],[2]],[[null,397,448],[3,4]],[[null,540,608],[5]],[[null,622,722],[5]],[[null,800,800],[4]],[[null,972,972],[0,1]],[[null,1027,1027],[6]]]]"]]
59
[["di",7878],["af.httprm",7877,"5240892457182432708",49]]
27
[["e",8,null,null,98558]]
