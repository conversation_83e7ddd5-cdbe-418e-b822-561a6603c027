)]}'

412
[["wrb.fr",null,"[[\"The \\\"fine-tuning timing for classical solver\\\" refers to the **time it takes for a classical combinatorial optimization solver, such as Gurobi9.5,\",null,[\"c3e9d781-4708-4067-a3d9-0ad76e488460\",\"1cf26148-30e2-4af5-b2b9-db2783c01f15\",2668043993],null,[[]]],null,null,[[\"What is Meta-EGN?\",\"Why is instance-wise optimality important?\",\"What are limitations of Meta-EGN?\"]]]"]]
9684
[["wrb.fr",null,"[[\"The \\\"fine-tuning timing for classical solver\\\" refers to the **time it takes for a classical combinatorial optimization solver, such as Gurobi9.5, to find a solution for a given problem instance** [1, 2]. Unlike neural network-based methods that might fine-tune a pre-trained model, classical solvers are designed to directly compute a solution for each encountered instance [1, 3]. The paper notes that this time is generally **\\\"Long\\\"** for classical solvers [1]. For example, the commercial solver Gurobi9.5 is given a time budget for solving integer programming problems, and its actual time usage includes pre-processing plus the solving time [2].\",null,[\"c3e9d781-4708-4067-a3d9-0ad76e488460\",\"1cf26148-30e2-4af5-b2b9-db2783c01f15\",2668043993],null,[[null,[[[\"33956884-4a76-4009-aa0e-57865e0c9a79\"],[null,191,191]],[[\"321441ed-4786-4dcf-a891-7d7e086c599e\"],[null,191,191]],[[\"33956884-4a76-4009-aa0e-57865e0c9a79\"],[null,191,362]],[[\"5f3f3847-78e8-4efe-b30f-3a65261f8605\"],[null,191,362]],[[\"33956884-4a76-4009-aa0e-57865e0c9a79\"],[null,415,436]],[[\"321441ed-4786-4dcf-a891-7d7e086c599e\"],[null,436,619]]]],null,null,[[[\"33956884-4a76-4009-aa0e-57865e0c9a79\"],[null,null,0.7038456977810955,[[null,21017,21519]],[[[21017,21017,[]],[21017,21018,[[[21017,21018,[\" \"]]]]],[21018,21209,[[[21018,21209,[\"Table 1: Comparison between different unsupervised frameworks. G denotes the test instance and Gi, 1 ≤ i ≤ m are training instances. The standard EGN pipeline does not adopt any fine-tuning. \"]]]]],[21209,21239,[[[21209,21239,[\"EGN (Karalias \\u0026 Loukas, 2020) \"]]]]],[21239,21270,[[[21239,21270,[\"P-I GNN (Schuetz et al., 2022) \"]]]]],[21270,21286,[[[21270,21286,[\"Meta-EGN (Ours) \"]]]]],[21286,21330,[[[21286,21330,[\"Classical Solver Gurobi Optimization (2022) \"]]]]],[21330,21357,[[[21330,21357,[\"Obj. to optimize the NN ∑m \"]]]]],[21357,21379,[[[21357,21379,[\"i\\u003d1 l(θ;Gi) l(θ;G) ∑m \"]]]]],[21379,21450,[[[21379,21450,[\"i\\u003d1 l(θ −∇θl(θ;Gi);Gi) f(X;G) s.t. X ∈ Ω Training or not Yes No Yes No \"]]]]],[21450,21519,[[[21450,21519,[\"Fine-tune timing No Long Short/No Long Generalization Good - Better -\"]]]]]]],[[[\"a0022a03-f5b8-4462-900a-befc204f91c2\"],\"e1d2cd22-2705-4948-b914-905ec75c6b02\"]],[\"33956884-4a76-4009-aa0e-57865e0c9a79\"]]],[[\"321441ed-4786-4dcf-a891-7d7e086c599e\"],[null,null,0.6780315086206454,[[null,28886,30221]],[[[28886,30221,[[[28886,30221,[\"Baselines: Our baselines include unsupervised learning methods, heuristics, and traditional CO solvers. For the MC and MVC problems, we take our direct baseline EGN (Karalias \\u0026 Loukas, 2020), and also take RUN-CSP (Toenshoff et al., 2021) as another baseline. We do not consider other learning-based methods because they generally perform worse than EGN (Karalias \\u0026 Loukas, 2020). As to the heuristics, we use greedy algorithms as heuristic baselines. For traditional CO solvers, we compare against the best commercial CO problem solver Gurobi9.5 (Gurobi Optimization, 2022) via converting the problems into integer programming form. We track the time t that the models use from the start of inferring to the end of rounding to output feasible solutions. We set this time t as the time budget of Gurobi9.5 for purely solving the integer programming, and list the actual time usage of Gurobi9.5 which includes pre-processing plus t. As to the MIS problem, we take PI-GNN (Schuetz et al., 2022) and EGN Karalias \\u0026 Loukas (2020) as the learning-based baselines. We take the random greedy algorithm (RGA) and degree-based greedy algorithm (DGA) as introduced in Angelini \\u0026 Ricci-Tersenghi (2019) as the heuristic baselines. When we consider fine-tuning EGN and Meta-EGN over a test instance, we use 1-step gradient descent as fine-tuning. \"]]]]]]],[[[\"a0022a03-f5b8-4462-900a-befc204f91c2\"],\"e1d2cd22-2705-4948-b914-905ec75c6b02\"]],[\"321441ed-4786-4dcf-a891-7d7e086c599e\"]]],[[\"5f3f3847-78e8-4efe-b30f-3a65261f8605\"],[null,null,0.713314733784657,[[null,4493,5605]],[[[4493,4942,[[[4493,4942,[\"Figure 1: Approximation Rates of different methods in the MIS problem. Meta-EGN and EGN (Karalias \\u0026 Loukas, 2020) are trained on RRGs with 1000 nodes and with node degree randomly sampled from 3, 7, 10, 20. Meta-EGN and EGN are evaluated over larger RRGs with 103 ∼ 105 nodes. More details about the setting are in Secs. 5.1 and 5.4. Meta-EGN outperforms DGA (Angelini \\u0026 Ricci-Tersenghi, 2019) by about 0.3%− 0.5% in approximation rates on average. \"]]]]],[4942,5605,[[[4942,5605,[\"induces a concern when we apply NNs in practice because practical problems often expect to have a good solution to every encountered instance. For example, allocating surveillance cameras is crucial for each-time exhibition in every art gallery. Solvers when applied to this problem (O’rourke, 1987; Yabuta \\u0026 Kitazawa, 2008) should output a good solution every time. Traditional CO solvers are designed toward this goal. However, it is time-consuming and unable to learn heuristics from his-torical instances. So, can we leverage the benefit of learning from history with the goal of achieving an instance-wise good solution instead of an averaged good solution? \"]]]]]]],[[[\"a0022a03-f5b8-4462-900a-befc204f91c2\"],\"e1d2cd22-2705-4948-b914-905ec75c6b02\"]],[\"5f3f3847-78e8-4efe-b30f-3a65261f8605\"]]]]]],[[null,null,0.7038456977810955,[[null,21017,21519]],[[[21017,21017,[]],[21017,21018,[[[21017,21018,[\" \"]]]]],[21018,21209,[[[21018,21209,[\"Table 1: Comparison between different unsupervised frameworks. G denotes the test instance and Gi, 1 ≤ i ≤ m are training instances. The standard EGN pipeline does not adopt any fine-tuning. \"]]]]],[21209,21239,[[[21209,21239,[\"EGN (Karalias \\u0026 Loukas, 2020) \"]]]]],[21239,21270,[[[21239,21270,[\"P-I GNN (Schuetz et al., 2022) \"]]]]],[21270,21286,[[[21270,21286,[\"Meta-EGN (Ours) \"]]]]],[21286,21330,[[[21286,21330,[\"Classical Solver Gurobi Optimization (2022) \"]]]]],[21330,21357,[[[21330,21357,[\"Obj. to optimize the NN ∑m \"]]]]],[21357,21379,[[[21357,21379,[\"i\\u003d1 l(θ;Gi) l(θ;G) ∑m \"]]]]],[21379,21450,[[[21379,21450,[\"i\\u003d1 l(θ −∇θl(θ;Gi);Gi) f(X;G) s.t. X ∈ Ω Training or not Yes No Yes No \"]]]]],[21450,21519,[[[21450,21519,[\"Fine-tune timing No Long Short/No Long Generalization Good - Better -\"]]]]]]],[[[\"a0022a03-f5b8-4462-900a-befc204f91c2\"],\"e1d2cd22-2705-4948-b914-905ec75c6b02\"]],[\"33956884-4a76-4009-aa0e-57865e0c9a79\"]],[null,null,0.6780315086206454,[[null,28886,30221]],[[[28886,30221,[[[28886,30221,[\"Baselines: Our baselines include unsupervised learning methods, heuristics, and traditional CO solvers. For the MC and MVC problems, we take our direct baseline EGN (Karalias \\u0026 Loukas, 2020), and also take RUN-CSP (Toenshoff et al., 2021) as another baseline. We do not consider other learning-based methods because they generally perform worse than EGN (Karalias \\u0026 Loukas, 2020). As to the heuristics, we use greedy algorithms as heuristic baselines. For traditional CO solvers, we compare against the best commercial CO problem solver Gurobi9.5 (Gurobi Optimization, 2022) via converting the problems into integer programming form. We track the time t that the models use from the start of inferring to the end of rounding to output feasible solutions. We set this time t as the time budget of Gurobi9.5 for purely solving the integer programming, and list the actual time usage of Gurobi9.5 which includes pre-processing plus t. As to the MIS problem, we take PI-GNN (Schuetz et al., 2022) and EGN Karalias \\u0026 Loukas (2020) as the learning-based baselines. We take the random greedy algorithm (RGA) and degree-based greedy algorithm (DGA) as introduced in Angelini \\u0026 Ricci-Tersenghi (2019) as the heuristic baselines. When we consider fine-tuning EGN and Meta-EGN over a test instance, we use 1-step gradient descent as fine-tuning. \"]]]]]]],[[[\"a0022a03-f5b8-4462-900a-befc204f91c2\"],\"e1d2cd22-2705-4948-b914-905ec75c6b02\"]],[\"321441ed-4786-4dcf-a891-7d7e086c599e\"]],[null,null,0.713314733784657,[[null,4493,5605]],[[[4493,4942,[[[4493,4942,[\"Figure 1: Approximation Rates of different methods in the MIS problem. Meta-EGN and EGN (Karalias \\u0026 Loukas, 2020) are trained on RRGs with 1000 nodes and with node degree randomly sampled from 3, 7, 10, 20. Meta-EGN and EGN are evaluated over larger RRGs with 103 ∼ 105 nodes. More details about the setting are in Secs. 5.1 and 5.4. Meta-EGN outperforms DGA (Angelini \\u0026 Ricci-Tersenghi, 2019) by about 0.3%− 0.5% in approximation rates on average. \"]]]]],[4942,5605,[[[4942,5605,[\"induces a concern when we apply NNs in practice because practical problems often expect to have a good solution to every encountered instance. For example, allocating surveillance cameras is crucial for each-time exhibition in every art gallery. Solvers when applied to this problem (O’rourke, 1987; Yabuta \\u0026 Kitazawa, 2008) should output a good solution every time. Traditional CO solvers are designed toward this goal. However, it is time-consuming and unable to learn heuristics from his-torical instances. So, can we leverage the benefit of learning from history with the goal of achieving an instance-wise good solution instead of an averaged good solution? \"]]]]]]],[[[\"a0022a03-f5b8-4462-900a-befc204f91c2\"],\"e1d2cd22-2705-4948-b914-905ec75c6b02\"]],[\"5f3f3847-78e8-4efe-b30f-3a65261f8605\"]]],[[[null,191,191],[0,1]],[[null,191,362],[0,2]],[[null,415,436],[0]],[[null,436,619],[1]]],[[\"What is Meta-EGN?\",\"Why is instance-wise optimality important?\",\"What are limitations of Meta-EGN?\"]]]"]]
10420
[["wrb.fr",null,"[[\"The \\\"fine-tuning timing for classical solver\\\" refers to the **time it takes for a classical combinatorial optimization solver, such as Gurobi9.5, to find a solution for a given problem instance** [1, 2]. Unlike neural network-based methods that might fine-tune a pre-trained model, classical solvers are designed to directly compute a solution for each encountered instance [1, 3]. The paper notes that this time is generally **\\\"Long\\\"** for classical solvers [1]. For example, the commercial solver Gurobi9.5 is given a time budget for solving integer programming problems, and its actual time usage includes pre-processing plus the solving time [2].\",null,[\"c3e9d781-4708-4067-a3d9-0ad76e488460\",\"1cf26148-30e2-4af5-b2b9-db2783c01f15\",2668043993],null,[[[[0,620,[[[0,60,[\"The \\\"fine-tuning timing for classical solver\\\" refers to the \"]],[60,191,[\"time it takes for a classical combinatorial optimization solver, such as Gurobi9.5, to find a solution for a given problem instance\",[true]]],[191,408,[\". Unlike neural network-based methods that might fine-tune a pre-trained model, classical solvers are designed to directly compute a solution for each encountered instance. The paper notes that this time is generally \"]],[408,414,[\"\\\"Long\\\"\",[true]]],[414,620,[\" for classical solvers. For example, the commercial solver Gurobi9.5 is given a time budget for solving integer programming problems, and its actual time usage includes pre-processing plus the solving time.\"]]]]]],[[[\"33956884-4a76-4009-aa0e-57865e0c9a79\"],[null,191,191]],[[\"321441ed-4786-4dcf-a891-7d7e086c599e\"],[null,191,191]],[[\"33956884-4a76-4009-aa0e-57865e0c9a79\"],[null,191,362]],[[\"5f3f3847-78e8-4efe-b30f-3a65261f8605\"],[null,191,362]],[[\"33956884-4a76-4009-aa0e-57865e0c9a79\"],[null,415,436]],[[\"321441ed-4786-4dcf-a891-7d7e086c599e\"],[null,436,619]]]],null,null,[[[\"33956884-4a76-4009-aa0e-57865e0c9a79\"],[null,null,0.7038456977810955,[[null,21017,21519]],[[[21017,21017,[]],[21017,21018,[[[21017,21018,[\" \"]]]]],[21018,21209,[[[21018,21209,[\"Table 1: Comparison between different unsupervised frameworks. G denotes the test instance and Gi, 1 ≤ i ≤ m are training instances. The standard EGN pipeline does not adopt any fine-tuning. \"]]]]],[21209,21239,[[[21209,21239,[\"EGN (Karalias \\u0026 Loukas, 2020) \"]]]]],[21239,21270,[[[21239,21270,[\"P-I GNN (Schuetz et al., 2022) \"]]]]],[21270,21286,[[[21270,21286,[\"Meta-EGN (Ours) \"]]]]],[21286,21330,[[[21286,21330,[\"Classical Solver Gurobi Optimization (2022) \"]]]]],[21330,21357,[[[21330,21357,[\"Obj. to optimize the NN ∑m \"]]]]],[21357,21379,[[[21357,21379,[\"i\\u003d1 l(θ;Gi) l(θ;G) ∑m \"]]]]],[21379,21450,[[[21379,21450,[\"i\\u003d1 l(θ −∇θl(θ;Gi);Gi) f(X;G) s.t. X ∈ Ω Training or not Yes No Yes No \"]]]]],[21450,21519,[[[21450,21519,[\"Fine-tune timing No Long Short/No Long Generalization Good - Better -\"]]]]]]],[[[\"a0022a03-f5b8-4462-900a-befc204f91c2\"],\"e1d2cd22-2705-4948-b914-905ec75c6b02\"]],[\"33956884-4a76-4009-aa0e-57865e0c9a79\"]]],[[\"321441ed-4786-4dcf-a891-7d7e086c599e\"],[null,null,0.6780315086206454,[[null,28886,30221]],[[[28886,30221,[[[28886,30221,[\"Baselines: Our baselines include unsupervised learning methods, heuristics, and traditional CO solvers. For the MC and MVC problems, we take our direct baseline EGN (Karalias \\u0026 Loukas, 2020), and also take RUN-CSP (Toenshoff et al., 2021) as another baseline. We do not consider other learning-based methods because they generally perform worse than EGN (Karalias \\u0026 Loukas, 2020). As to the heuristics, we use greedy algorithms as heuristic baselines. For traditional CO solvers, we compare against the best commercial CO problem solver Gurobi9.5 (Gurobi Optimization, 2022) via converting the problems into integer programming form. We track the time t that the models use from the start of inferring to the end of rounding to output feasible solutions. We set this time t as the time budget of Gurobi9.5 for purely solving the integer programming, and list the actual time usage of Gurobi9.5 which includes pre-processing plus t. As to the MIS problem, we take PI-GNN (Schuetz et al., 2022) and EGN Karalias \\u0026 Loukas (2020) as the learning-based baselines. We take the random greedy algorithm (RGA) and degree-based greedy algorithm (DGA) as introduced in Angelini \\u0026 Ricci-Tersenghi (2019) as the heuristic baselines. When we consider fine-tuning EGN and Meta-EGN over a test instance, we use 1-step gradient descent as fine-tuning. \"]]]]]]],[[[\"a0022a03-f5b8-4462-900a-befc204f91c2\"],\"e1d2cd22-2705-4948-b914-905ec75c6b02\"]],[\"321441ed-4786-4dcf-a891-7d7e086c599e\"]]],[[\"5f3f3847-78e8-4efe-b30f-3a65261f8605\"],[null,null,0.713314733784657,[[null,4493,5605]],[[[4493,4942,[[[4493,4942,[\"Figure 1: Approximation Rates of different methods in the MIS problem. Meta-EGN and EGN (Karalias \\u0026 Loukas, 2020) are trained on RRGs with 1000 nodes and with node degree randomly sampled from 3, 7, 10, 20. Meta-EGN and EGN are evaluated over larger RRGs with 103 ∼ 105 nodes. More details about the setting are in Secs. 5.1 and 5.4. Meta-EGN outperforms DGA (Angelini \\u0026 Ricci-Tersenghi, 2019) by about 0.3%− 0.5% in approximation rates on average. \"]]]]],[4942,5605,[[[4942,5605,[\"induces a concern when we apply NNs in practice because practical problems often expect to have a good solution to every encountered instance. For example, allocating surveillance cameras is crucial for each-time exhibition in every art gallery. Solvers when applied to this problem (O’rourke, 1987; Yabuta \\u0026 Kitazawa, 2008) should output a good solution every time. Traditional CO solvers are designed toward this goal. However, it is time-consuming and unable to learn heuristics from his-torical instances. So, can we leverage the benefit of learning from history with the goal of achieving an instance-wise good solution instead of an averaged good solution? \"]]]]]]],[[[\"a0022a03-f5b8-4462-900a-befc204f91c2\"],\"e1d2cd22-2705-4948-b914-905ec75c6b02\"]],[\"5f3f3847-78e8-4efe-b30f-3a65261f8605\"]]]]]],[[null,null,0.7038456977810955,[[null,21017,21519]],[[[21017,21017,[]],[21017,21018,[[[21017,21018,[\" \"]]]]],[21018,21209,[[[21018,21209,[\"Table 1: Comparison between different unsupervised frameworks. G denotes the test instance and Gi, 1 ≤ i ≤ m are training instances. The standard EGN pipeline does not adopt any fine-tuning. \"]]]]],[21209,21239,[[[21209,21239,[\"EGN (Karalias \\u0026 Loukas, 2020) \"]]]]],[21239,21270,[[[21239,21270,[\"P-I GNN (Schuetz et al., 2022) \"]]]]],[21270,21286,[[[21270,21286,[\"Meta-EGN (Ours) \"]]]]],[21286,21330,[[[21286,21330,[\"Classical Solver Gurobi Optimization (2022) \"]]]]],[21330,21357,[[[21330,21357,[\"Obj. to optimize the NN ∑m \"]]]]],[21357,21379,[[[21357,21379,[\"i\\u003d1 l(θ;Gi) l(θ;G) ∑m \"]]]]],[21379,21450,[[[21379,21450,[\"i\\u003d1 l(θ −∇θl(θ;Gi);Gi) f(X;G) s.t. X ∈ Ω Training or not Yes No Yes No \"]]]]],[21450,21519,[[[21450,21519,[\"Fine-tune timing No Long Short/No Long Generalization Good - Better -\"]]]]]]],[[[\"a0022a03-f5b8-4462-900a-befc204f91c2\"],\"e1d2cd22-2705-4948-b914-905ec75c6b02\"]],[\"33956884-4a76-4009-aa0e-57865e0c9a79\"]],[null,null,0.6780315086206454,[[null,28886,30221]],[[[28886,30221,[[[28886,30221,[\"Baselines: Our baselines include unsupervised learning methods, heuristics, and traditional CO solvers. For the MC and MVC problems, we take our direct baseline EGN (Karalias \\u0026 Loukas, 2020), and also take RUN-CSP (Toenshoff et al., 2021) as another baseline. We do not consider other learning-based methods because they generally perform worse than EGN (Karalias \\u0026 Loukas, 2020). As to the heuristics, we use greedy algorithms as heuristic baselines. For traditional CO solvers, we compare against the best commercial CO problem solver Gurobi9.5 (Gurobi Optimization, 2022) via converting the problems into integer programming form. We track the time t that the models use from the start of inferring to the end of rounding to output feasible solutions. We set this time t as the time budget of Gurobi9.5 for purely solving the integer programming, and list the actual time usage of Gurobi9.5 which includes pre-processing plus t. As to the MIS problem, we take PI-GNN (Schuetz et al., 2022) and EGN Karalias \\u0026 Loukas (2020) as the learning-based baselines. We take the random greedy algorithm (RGA) and degree-based greedy algorithm (DGA) as introduced in Angelini \\u0026 Ricci-Tersenghi (2019) as the heuristic baselines. When we consider fine-tuning EGN and Meta-EGN over a test instance, we use 1-step gradient descent as fine-tuning. \"]]]]]]],[[[\"a0022a03-f5b8-4462-900a-befc204f91c2\"],\"e1d2cd22-2705-4948-b914-905ec75c6b02\"]],[\"321441ed-4786-4dcf-a891-7d7e086c599e\"]],[null,null,0.713314733784657,[[null,4493,5605]],[[[4493,4942,[[[4493,4942,[\"Figure 1: Approximation Rates of different methods in the MIS problem. Meta-EGN and EGN (Karalias \\u0026 Loukas, 2020) are trained on RRGs with 1000 nodes and with node degree randomly sampled from 3, 7, 10, 20. Meta-EGN and EGN are evaluated over larger RRGs with 103 ∼ 105 nodes. More details about the setting are in Secs. 5.1 and 5.4. Meta-EGN outperforms DGA (Angelini \\u0026 Ricci-Tersenghi, 2019) by about 0.3%− 0.5% in approximation rates on average. \"]]]]],[4942,5605,[[[4942,5605,[\"induces a concern when we apply NNs in practice because practical problems often expect to have a good solution to every encountered instance. For example, allocating surveillance cameras is crucial for each-time exhibition in every art gallery. Solvers when applied to this problem (O’rourke, 1987; Yabuta \\u0026 Kitazawa, 2008) should output a good solution every time. Traditional CO solvers are designed toward this goal. However, it is time-consuming and unable to learn heuristics from his-torical instances. So, can we leverage the benefit of learning from history with the goal of achieving an instance-wise good solution instead of an averaged good solution? \"]]]]]]],[[[\"a0022a03-f5b8-4462-900a-befc204f91c2\"],\"e1d2cd22-2705-4948-b914-905ec75c6b02\"]],[\"5f3f3847-78e8-4efe-b30f-3a65261f8605\"]]],[[[null,191,191],[0,1]],[[null,191,362],[0,2]],[[null,415,436],[0]],[[null,436,619],[1]]],[[\"What is Meta-EGN?\",\"Why is instance-wise optimality important?\",\"What are limitations of Meta-EGN?\"]]]"]]
60
[["di",4091],["af.httprm",4090,"-8554513765380327249",71]]
27
[["e",6,null,null,20729]]
