)]}'

4008
[["wrb.fr",null,"[[\"In the \\\"ROSCOE Training\\\" process, to obtain reasoning step embeddings, a supervised sentence similarity model called SimCSE is finetuned [1]. For **contrastive learning**, this model is trained on triplets, specifically using:\\n\\n*   **Positive samples**: The **context and reference reasoning steps (\",null,[\"603aa48a-2639-44bc-9af5-57ce1ed38caf\",\"9679674b-4353-409c-93f1-879b5dc982b4\",296476350],null,[[[[0,218,[[[0,142,[\"In the \\\"ROSCOE Training\\\" process, to obtain reasoning step embeddings, a supervised sentence similarity model called SimCSE is finetuned. For \"]],[142,162,[\"contrastive learning\",[true]]],[162,218,[\", this model is trained on triplets, specifically using:\"]]]]]],[[[\"247be47e-00c8-47cd-abca-4a83f2896e6e\"],[null,0,136]]]],null,null,[[[\"247be47e-00c8-47cd-abca-4a83f2896e6e\"],[null,null,0.7575767963761386,[[null,23715,24996]],[[[23715,24996,[[[23715,24996,[\"ROSCOE Training. To obtain reasoning step embeddings, we finetune SimCSE (Gao et al., 2021), a supervised sentence similarity model extending the RoBERTa word embedding model (Liu et al., 2019) on multi-step reasoning datasets we listed in §5 (see details in Table 11)4. SimCSE is a contrastive learning model that is trained on triplets of reference reasoning steps, positive and hard-negative hypothesis reasoning steps to minimize the cross-entropy objective with in-batch negatives. For contrastive learning, we use the context and reference reasoning steps as a positive sample (s, r), and context and perturbed reference steps (s,h) as hard-negative pairs. For finetuning, we embed source context and hypothesis chain as a whole, without splitting it into steps. With the finetuned model we embed each individual step, as well as a reasoning chain as a whole. We use the pretrained checkpoint of supervised SimCSE model sup-simcse-roberta-base to initialize our model, and further train it for five epochs on our synthetic train data (details in App. G). We also compare ROSCOE scores calculated against sup-simcse-roberta-base SimCSE model, and all-mpnet-base-v2 sentence embedding model (Reimers \\u0026 Gurevych, 2019) to understand metrics sensitivity to the embedding method. \"]]]]]]],[[[\"3977957d-b3d6-4170-9b82-e1fd11d74321\"],\"29ed24b7-b35a-498b-8635-1b29abc9747e\"]],[\"247be47e-00c8-47cd-abca-4a83f2896e6e\"]]]]]],[[null,null,0.7575767963761386,[[null,23715,24996]],[[[23715,24996,[[[23715,24996,[\"ROSCOE Training. To obtain reasoning step embeddings, we finetune SimCSE (Gao et al., 2021), a supervised sentence similarity model extending the RoBERTa word embedding model (Liu et al., 2019) on multi-step reasoning datasets we listed in §5 (see details in Table 11)4. SimCSE is a contrastive learning model that is trained on triplets of reference reasoning steps, positive and hard-negative hypothesis reasoning steps to minimize the cross-entropy objective with in-batch negatives. For contrastive learning, we use the context and reference reasoning steps as a positive sample (s, r), and context and perturbed reference steps (s,h) as hard-negative pairs. For finetuning, we embed source context and hypothesis chain as a whole, without splitting it into steps. With the finetuned model we embed each individual step, as well as a reasoning chain as a whole. We use the pretrained checkpoint of supervised SimCSE model sup-simcse-roberta-base to initialize our model, and further train it for five epochs on our synthetic train data (details in App. G). We also compare ROSCOE scores calculated against sup-simcse-roberta-base SimCSE model, and all-mpnet-base-v2 sentence embedding model (Reimers \\u0026 Gurevych, 2019) to understand metrics sensitivity to the embedding method. \"]]]]]]],[[[\"3977957d-b3d6-4170-9b82-e1fd11d74321\"],\"29ed24b7-b35a-498b-8635-1b29abc9747e\"]],[\"247be47e-00c8-47cd-abca-4a83f2896e6e\"]]],[[[null,0,136],[0]]],[[\"How is ROSCOE evaluated?\",\"What are ROSCOE's four perspectives?\",\"What is SimCSE's role?\"]]]"]]
6041
[["wrb.fr",null,"[[\"In the \\\"ROSCOE Training\\\" process, to obtain reasoning step embeddings, a supervised sentence similarity model called SimCSE is finetuned [1]. For **contrastive learning**, this model is trained on triplets, specifically using:\\n\\n*   **Positive samples**: The **context and reference reasoning steps (s, r)** [1].\\n*   **Hard-negative pairs**: The **context and perturbed reference steps (s, h)** [1].\\n\\nThis training aims to minimize the cross-entropy objective with in-batch negatives [1]. When finetuning, the model embeds the source context and hypothesis chain as a whole, without splitting them into individual steps [1].\",null,[\"603aa48a-2639-44bc-9af5-57ce1ed38caf\",\"9679674b-4353-409c-93f1-879b5dc982b4\",296476350],null,[[[[0,218,[[[0,142,[\"In the \\\"ROSCOE Training\\\" process, to obtain reasoning step embeddings, a supervised sentence similarity model called SimCSE is finetuned. For \"]],[142,162,[\"contrastive learning\",[true]]],[162,218,[\", this model is trained on triplets, specifically using:\"]]]]],[218,285,[[[218,234,[\"Positive samples\",[true]]],[234,240,[\": The \"]],[240,284,[\"context and reference reasoning steps (s, r)\",[true]]],[284,285,[\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[285,355,[[[285,304,[\"Hard-negative pairs\",[true]]],[304,310,[\": The \"]],[310,354,[\"context and perturbed reference steps (s, h)\",[true]]],[354,355,[\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]]],[[[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"],[null,0,136]],[[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"],[null,284,284]],[[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"],[null,354,354]],[[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"],[null,355,437]],[[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"],[null,437,569]]]],null,null,[[[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"],[null,null,0.7575767963761386,[[null,23715,24996]],[[[23715,24996,[[[23715,24996,[\"ROSCOE Training. To obtain reasoning step embeddings, we finetune SimCSE (Gao et al., 2021), a supervised sentence similarity model extending the RoBERTa word embedding model (Liu et al., 2019) on multi-step reasoning datasets we listed in §5 (see details in Table 11)4. SimCSE is a contrastive learning model that is trained on triplets of reference reasoning steps, positive and hard-negative hypothesis reasoning steps to minimize the cross-entropy objective with in-batch negatives. For contrastive learning, we use the context and reference reasoning steps as a positive sample (s, r), and context and perturbed reference steps (s,h) as hard-negative pairs. For finetuning, we embed source context and hypothesis chain as a whole, without splitting it into steps. With the finetuned model we embed each individual step, as well as a reasoning chain as a whole. We use the pretrained checkpoint of supervised SimCSE model sup-simcse-roberta-base to initialize our model, and further train it for five epochs on our synthetic train data (details in App. G). We also compare ROSCOE scores calculated against sup-simcse-roberta-base SimCSE model, and all-mpnet-base-v2 sentence embedding model (Reimers \\u0026 Gurevych, 2019) to understand metrics sensitivity to the embedding method. \"]]]]]]],[[[\"3977957d-b3d6-4170-9b82-e1fd11d74321\"],\"29ed24b7-b35a-498b-8635-1b29abc9747e\"]],[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"]]]]]],[[null,null,0.7575767963761386,[[null,23715,24996]],[[[23715,24996,[[[23715,24996,[\"ROSCOE Training. To obtain reasoning step embeddings, we finetune SimCSE (Gao et al., 2021), a supervised sentence similarity model extending the RoBERTa word embedding model (Liu et al., 2019) on multi-step reasoning datasets we listed in §5 (see details in Table 11)4. SimCSE is a contrastive learning model that is trained on triplets of reference reasoning steps, positive and hard-negative hypothesis reasoning steps to minimize the cross-entropy objective with in-batch negatives. For contrastive learning, we use the context and reference reasoning steps as a positive sample (s, r), and context and perturbed reference steps (s,h) as hard-negative pairs. For finetuning, we embed source context and hypothesis chain as a whole, without splitting it into steps. With the finetuned model we embed each individual step, as well as a reasoning chain as a whole. We use the pretrained checkpoint of supervised SimCSE model sup-simcse-roberta-base to initialize our model, and further train it for five epochs on our synthetic train data (details in App. G). We also compare ROSCOE scores calculated against sup-simcse-roberta-base SimCSE model, and all-mpnet-base-v2 sentence embedding model (Reimers \\u0026 Gurevych, 2019) to understand metrics sensitivity to the embedding method. \"]]]]]]],[[[\"3977957d-b3d6-4170-9b82-e1fd11d74321\"],\"29ed24b7-b35a-498b-8635-1b29abc9747e\"]],[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"]]],[[[null,0,136],[0]],[[null,284,284],[0]],[[null,354,354],[0]],[[null,355,437],[0]],[[null,437,569],[0]]],[[\"How is ROSCOE evaluated?\",\"What are ROSCOE's four perspectives?\",\"What is SimCSE's role?\"]]]"]]
6287
[["wrb.fr",null,"[[\"In the \\\"ROSCOE Training\\\" process, to obtain reasoning step embeddings, a supervised sentence similarity model called SimCSE is finetuned [1]. For **contrastive learning**, this model is trained on triplets, specifically using:\\n\\n*   **Positive samples**: The **context and reference reasoning steps (s, r)** [1].\\n*   **Hard-negative pairs**: The **context and perturbed reference steps (s, h)** [1].\\n\\nThis training aims to minimize the cross-entropy objective with in-batch negatives [1]. When finetuning, the model embeds the source context and hypothesis chain as a whole, without splitting them into individual steps [1].\",null,[\"603aa48a-2639-44bc-9af5-57ce1ed38caf\",\"9679674b-4353-409c-93f1-879b5dc982b4\",296476350],null,[[[[0,218,[[[0,142,[\"In the \\\"ROSCOE Training\\\" process, to obtain reasoning step embeddings, a supervised sentence similarity model called SimCSE is finetuned. For \"]],[142,162,[\"contrastive learning\",[true]]],[162,218,[\", this model is trained on triplets, specifically using:\"]]]]],[218,285,[[[218,234,[\"Positive samples\",[true]]],[234,240,[\": The \"]],[240,284,[\"context and reference reasoning steps (s, r)\",[true]]],[284,285,[\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[285,355,[[[285,304,[\"Hard-negative pairs\",[true]]],[304,310,[\": The \"]],[310,354,[\"context and perturbed reference steps (s, h)\",[true]]],[354,355,[\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[355,570,[[[355,570,[\"This training aims to minimize the cross-entropy objective with in-batch negatives. When finetuning, the model embeds the source context and hypothesis chain as a whole, without splitting them into individual steps.\"]]]]]],[[[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"],[null,0,136]],[[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"],[null,284,284]],[[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"],[null,354,354]],[[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"],[null,355,437]],[[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"],[null,437,569]]]],null,null,[[[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"],[null,null,0.7575767963761386,[[null,23715,24996]],[[[23715,24996,[[[23715,24996,[\"ROSCOE Training. To obtain reasoning step embeddings, we finetune SimCSE (Gao et al., 2021), a supervised sentence similarity model extending the RoBERTa word embedding model (Liu et al., 2019) on multi-step reasoning datasets we listed in §5 (see details in Table 11)4. SimCSE is a contrastive learning model that is trained on triplets of reference reasoning steps, positive and hard-negative hypothesis reasoning steps to minimize the cross-entropy objective with in-batch negatives. For contrastive learning, we use the context and reference reasoning steps as a positive sample (s, r), and context and perturbed reference steps (s,h) as hard-negative pairs. For finetuning, we embed source context and hypothesis chain as a whole, without splitting it into steps. With the finetuned model we embed each individual step, as well as a reasoning chain as a whole. We use the pretrained checkpoint of supervised SimCSE model sup-simcse-roberta-base to initialize our model, and further train it for five epochs on our synthetic train data (details in App. G). We also compare ROSCOE scores calculated against sup-simcse-roberta-base SimCSE model, and all-mpnet-base-v2 sentence embedding model (Reimers \\u0026 Gurevych, 2019) to understand metrics sensitivity to the embedding method. \"]]]]]]],[[[\"3977957d-b3d6-4170-9b82-e1fd11d74321\"],\"29ed24b7-b35a-498b-8635-1b29abc9747e\"]],[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"]]]]]],[[null,null,0.7575767963761386,[[null,23715,24996]],[[[23715,24996,[[[23715,24996,[\"ROSCOE Training. To obtain reasoning step embeddings, we finetune SimCSE (Gao et al., 2021), a supervised sentence similarity model extending the RoBERTa word embedding model (Liu et al., 2019) on multi-step reasoning datasets we listed in §5 (see details in Table 11)4. SimCSE is a contrastive learning model that is trained on triplets of reference reasoning steps, positive and hard-negative hypothesis reasoning steps to minimize the cross-entropy objective with in-batch negatives. For contrastive learning, we use the context and reference reasoning steps as a positive sample (s, r), and context and perturbed reference steps (s,h) as hard-negative pairs. For finetuning, we embed source context and hypothesis chain as a whole, without splitting it into steps. With the finetuned model we embed each individual step, as well as a reasoning chain as a whole. We use the pretrained checkpoint of supervised SimCSE model sup-simcse-roberta-base to initialize our model, and further train it for five epochs on our synthetic train data (details in App. G). We also compare ROSCOE scores calculated against sup-simcse-roberta-base SimCSE model, and all-mpnet-base-v2 sentence embedding model (Reimers \\u0026 Gurevych, 2019) to understand metrics sensitivity to the embedding method. \"]]]]]]],[[[\"3977957d-b3d6-4170-9b82-e1fd11d74321\"],\"29ed24b7-b35a-498b-8635-1b29abc9747e\"]],[\"803eecbb-7a2e-4365-bcd7-70f19e0aeb6f\"]]],[[[null,0,136],[0]],[[null,284,284],[0]],[[null,354,354],[0]],[[null,355,437],[0]],[[null,437,569],[0]]],[[\"How is ROSCOE evaluated?\",\"What are ROSCOE's four perspectives?\",\"What is SimCSE's role?\"]]]"]]
60
[["di",3810],["af.httprm",3810,"-7289876114960532717",60]]
27
[["e",6,null,null,16459]]
