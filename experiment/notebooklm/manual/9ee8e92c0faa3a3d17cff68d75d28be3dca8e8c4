)]}'

3362
[["wrb.fr",null,"[[\"The provided sources do not directly detail the impact of adjusting $\\\\delta$ on the specific results presented in Table 1 for the Monster-Hunt game [1].\\n\\nHowever, the sources do explain the general role of $\\\\delta$ and its associated hyperparameter $\\\\alpha$:\\n*   $\\\\delta$ is defined as the **novelty threshold** in the iterative constrained optimization problem, where a policy $\\\\pi_k$ must be sufficiently distinct from previously discovered policies $\\\\pi_1, \\\\dots, \\\\pi_{k-1}$ by a distance greater than or equal to $\\\\delta$ [\",null,[\"da135b09-5e1e-4967-953c-f09c5905f5b1\",\"75e2ca76-929e-4d1a-9d23-943f2151aece\",155088721],null,[[[[0,148,[[[0,148,[\"The provided sources do not directly detail the impact of adjusting $\\\\delta$ on the specific results presented in Table 1 for the Monster-Hunt game.\"]]]]],[148,252,[[[148,252,[\"However, the sources do explain the general role of $\\\\delta$ and its associated hyperparameter $\\\\alpha$:\"]]]]]],[[[\"a17af8cf-3170-4293-9141-0b9715ffa98f\"],[null,0,147]]]],null,null,[[[\"a17af8cf-3170-4293-9141-0b9715ffa98f\"],[null,null,0.6243664204182673,[[null,30725,31258]],[[[30725,31028,[[[30725,31028,[\"We adopt both behavior-driven and reward-driven intrinsic rewards in RSPO to tackle Monster-Hunt. Fig. 4 illustrates all the discovered strategies by RSPO over 20 iterations, which covers a wide range of human-interpretable strategies, including the non-cooperative apple-eating strategy as well as the \"]]]]],[31028,31028,[]],[31028,31029,[[[31028,31029,[\" \"]]]]],[31029,31121,[[[31029,31121,[\"Table 1: Types of strategies discovered by each methods in Monster-Hunt over 20 iterations. \"]]]]],[31121,31145,[[[31121,31145,[\"Apple Corner Edge Chase \"]]]]],[31145,31154,[[[31145,31154,[\"Ablation \"]]]]],[31154,31202,[[[31154,31202,[\"RSPO X X X X - No switch X X - No rint X - rint \"]]]]],[31202,31215,[[[31202,31215,[\"B only X X X \"]]]]],[31215,31258,[[[31215,31258,[\"Baseline RPG X X X MAVEN X X PG/DIPG/RND X \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"a17af8cf-3170-4293-9141-0b9715ffa98f\"]]]]]],[[null,null,0.6243664204182673,[[null,30725,31258]],[[[30725,31028,[[[30725,31028,[\"We adopt both behavior-driven and reward-driven intrinsic rewards in RSPO to tackle Monster-Hunt. Fig. 4 illustrates all the discovered strategies by RSPO over 20 iterations, which covers a wide range of human-interpretable strategies, including the non-cooperative apple-eating strategy as well as the \"]]]]],[31028,31028,[]],[31028,31029,[[[31028,31029,[\" \"]]]]],[31029,31121,[[[31029,31121,[\"Table 1: Types of strategies discovered by each methods in Monster-Hunt over 20 iterations. \"]]]]],[31121,31145,[[[31121,31145,[\"Apple Corner Edge Chase \"]]]]],[31145,31154,[[[31145,31154,[\"Ablation \"]]]]],[31154,31202,[[[31154,31202,[\"RSPO X X X X - No switch X X - No rint X - rint \"]]]]],[31202,31215,[[[31202,31215,[\"B only X X X \"]]]]],[31215,31258,[[[31215,31258,[\"Baseline RPG X X X MAVEN X X PG/DIPG/RND X \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"a17af8cf-3170-4293-9141-0b9715ffa98f\"]]],[[[null,0,147],[0]]],[[\"What is RSPO's core contribution?\",\"What is the Monster-Hunt game?\",\"What are two SMAC maps?\"]]]"]]
18672
[["wrb.fr",null,"[[\"The provided sources do not directly detail the impact of adjusting $\\\\delta$ on the specific results presented in Table 1 for the Monster-Hunt game [1].\\n\\nHowever, the sources do explain the general role of $\\\\delta$ and its associated hyperparameter $\\\\alpha$:\\n*   $\\\\delta$ is defined as the **novelty threshold** in the iterative constrained optimization problem, where a policy $\\\\pi_k$ must be sufficiently distinct from previously discovered policies $\\\\pi_1, \\\\dots, \\\\pi_{k-1}$ by a distance greater than or equal to $\\\\delta$ [2, 3].\\n*   The paper utilizes an **automatic threshold selection** where $\\\\delta_j \\u003d \\\\alpha \\\\cdot D(\\\\pi_{rnd}, \\\\pi_j)$, with $\\\\alpha$ being a task-specific hyperparameter [4]. This means $\\\\alpha$ directly controls the effective value of $\\\\delta$.\\n*   The sensitivity analysis indicates that $\\\\alpha$ is the **most important hyperparameter** in RSPO because it determines **which trajectories in a batch are accepted** for training [5].\\n*   A **small $\\\\alpha$** (and thus $\\\\delta$) might cause RSPO to accept\",null,[\"da135b09-5e1e-4967-953c-f09c5905f5b1\",\"75e2ca76-929e-4d1a-9d23-943f2151aece\",155088721],null,[[[[0,148,[[[0,148,[\"The provided sources do not directly detail the impact of adjusting $\\\\delta$ on the specific results presented in Table 1 for the Monster-Hunt game.\"]]]]],[148,252,[[[148,252,[\"However, the sources do explain the general role of $\\\\delta$ and its associated hyperparameter $\\\\alpha$:\"]]]]],[252,511,[[[252,279,[\"$\\\\delta$ is defined as the \"]],[279,296,[\"novelty threshold\",[true]]],[296,511,[\" in the iterative constrained optimization problem, where a policy $\\\\pi_k$ must be sufficiently distinct from previously discovered policies $\\\\pi_1, \\\\dots, \\\\pi_{k-1}$ by a distance greater than or equal to $\\\\delta$.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[511,738,[[[511,533,[\"The paper utilizes an \"]],[533,562,[\"automatic threshold selection\",[true]]],[562,738,[\" where $\\\\delta_j \\u003d \\\\alpha \\\\cdot D(\\\\pi_{rnd}, \\\\pi_j)$, with $\\\\alpha$ being a task-specific hyperparameter. This means $\\\\alpha$ directly controls the effective value of $\\\\delta$.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[738,910,[[[738,794,[\"The sensitivity analysis indicates that $\\\\alpha$ is the \"]],[794,823,[\"most important hyperparameter\",[true]]],[823,854,[\" in RSPO because it determines \"]],[854,896,[\"which trajectories in a batch are accepted\",[true]]],[896,910,[\" for training.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]]],[[[\"6e77f6ce-22c6-44ad-a693-ce88a6e881f8\"],[null,0,147]],[[\"cd67da02-1b32-46c1-bd0b-d0a9a526a61e\"],[null,297,510]],[[\"2b8ade8c-7e58-4520-b8ab-7cabf02287db\"],[null,297,510]],[[\"30c0d443-bc5a-4c23-a5ad-adfcf95a1ca7\"],[null,563,666]],[[\"cb1bb10a-80da-4e6f-b5fb-43c7c1090bcd\"],[null,897,909]]]],null,null,[[[\"6e77f6ce-22c6-44ad-a693-ce88a6e881f8\"],[null,null,0.6243664204182673,[[null,30725,31258]],[[[30725,31028,[[[30725,31028,[\"We adopt both behavior-driven and reward-driven intrinsic rewards in RSPO to tackle Monster-Hunt. Fig. 4 illustrates all the discovered strategies by RSPO over 20 iterations, which covers a wide range of human-interpretable strategies, including the non-cooperative apple-eating strategy as well as the \"]]]]],[31028,31028,[]],[31028,31029,[[[31028,31029,[\" \"]]]]],[31029,31121,[[[31029,31121,[\"Table 1: Types of strategies discovered by each methods in Monster-Hunt over 20 iterations. \"]]]]],[31121,31145,[[[31121,31145,[\"Apple Corner Edge Chase \"]]]]],[31145,31154,[[[31145,31154,[\"Ablation \"]]]]],[31154,31202,[[[31154,31202,[\"RSPO X X X X - No switch X X - No rint X - rint \"]]]]],[31202,31215,[[[31202,31215,[\"B only X X X \"]]]]],[31215,31258,[[[31215,31258,[\"Baseline RPG X X X MAVEN X X PG/DIPG/RND X \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"6e77f6ce-22c6-44ad-a693-ce88a6e881f8\"]]],[[\"cd67da02-1b32-46c1-bd0b-d0a9a526a61e\"],[null,null,0.6412522816745847,[[null,12609,13127]],[[[12609,12865,[[[12609,12865,[\"Rather than learning a single solution for J(θ), we aim to discover a diverse set of M policies, i.e, {πθk |1 ≤ k ≤ M}, such that all of these polices are locally optimized under J(θ) and mutually distinct w.r.t. some distance measure D(πθi , πθj ), i.e., \"]]]]],[12865,12872,[[[12865,12872,[\"max θk \"]]]]],[12872,12938,[[[12872,12938,[\"J(θk) ∀1 ≤ k ≤M, subject to D(πθi , πθj ) ≥ δ, ∀1 ≤ i \\u003c j ≤M. (1) \"]]]]],[12938,13127,[[[12938,13127,[\"Here D(·, ·) measures how different two policies are and δ is the novelty threshold. For conciseness, in the following content, we omit θ and use πk to denote the policy with parameter θk. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"cd67da02-1b32-46c1-bd0b-d0a9a526a61e\"]]],[[\"2b8ade8c-7e58-4520-b8ab-7cabf02287db\"],[null,null,0.6301980528025974,[[null,13785,14650]],[[[13785,13848,[[[13785,13848,[\"θk \\u003d arg max θ J(θ), subject to D(πθ, πj) ≥ δ, ∀1 ≤ j \\u003c k. (2) \"]]]]],[13848,13848,[]],[13848,13849,[[[13848,13849,[\" \"]]]]],[13849,14650,[[[13849,14650,[\"Eq. (2) reduces the population-based objective to a standard constrained optimization problem for a single policy, which is much easier to solve. Such an iterative procedure does not require a large population size M as is typically necessary in population-based methods. And, in practice, only a few iterations could result in a sufficiently diverse collection of policies. We remark that, in theory, directly solving the constraint problem in Eq. (2) may lead to a solution that is not a local optimum w.r.t. the unconstrained objective J(θ). It is because a solution in Eq. (2) can be located on the boundary of the constraint space (i.e., D(πθ, πj) \\u003d δ), which is undesirable according to our original goal. However, this issue can be often alleviated by properly setting the novelty threshold δ. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"2b8ade8c-7e58-4520-b8ab-7cabf02287db\"]]],[[\"30c0d443-bc5a-4c23-a5ad-adfcf95a1ca7\"],[null,null,0.6628766568710132,[[null,22458,24108]],[[[22458,22646,[[[22458,22646,[\"In addition to the aforementioned RSPO algorithm, we also introduce two implementation enhance-ments for better empirical performances, especially in some performance-sensitive scenarios. \"]]]]],[22646,22646,[]],[22646,22647,[[[22646,22647,[\" \"]]]]],[22647,24108,[[[22647,24108,[\"Automatic threshold selection. We provide an empirical way of adjusting δ. In some environments, δ is sensitive to each reference policy. Instead of tuning δ for each reference policy, we choose its corresponding threshold by δj \\u003d α · D(πrnd, πj), where πrnd is a fully random policy and α is a task-specific hyperparameter. We remark that α is a constant parameter across training iterations and is much easier to choose than manually tuning δ, which requires subtle variation throughout multiple training iterations. We use automatic threshold selection by default. Detailed values of α and the methodology of tuning α can be found in App. D.1 and App. B.3 respectively. Smoothed-switching for intrinsic rewards. Intrinsic rewards have multiple switching indicators, i.e., φ1, . . . , φk−1. Moreover, for different trajectories, different subsets of indicators will be turned on and off, which may result in a varying scale of intrinsic rewards and hurt training stability. Therefore, in some constraint-sensitive cases, we propose a smoothed switching mechanism which could further improve practical performance. Specifically, we maintain a running average φ̃j over all the sampled trajectories for each indicator φj(τ), and use these smoothed indicators to compute intrinsic rewards defined in Eq. (9). Smoothed-switching empirically improves training stability when a large number of reference policies exist, such as in stag-hunt games (see Section 4.2). \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"30c0d443-bc5a-4c23-a5ad-adfcf95a1ca7\"]]],[[\"cb1bb10a-80da-4e6f-b5fb-43c7c1090bcd\"],[null,null,0.7099275325715608,[[null,58846,60324]],[[[58846,58871,[[[58846,58871,[\"B.3 SENSITIVITY ANALYSIS \"]]]]],[58871,59058,[[[58871,59058,[\"We have performed a sensitivity analysis over α, λintB and λintR since they are the critical to the performance RSPO. The default values used in our experiments can be found in Table 12. \"]]]]],[59058,60324,[[[59058,60324,[\"α is the most important hyperparameter in RSPO because it determines what trajectories in a batch to be accepted. We focus on the data efficiency, i.e., the proportion of accepted trajectories in a batch. In the sensitivity analysis, we run the second iteration of RSPO in Humanoid with α \\u003d 0.5, 1.0, 1.5, 2 respectively and compute the population diversity score of the 2 resulting policies. The result is shown in Fig. 12 and the left part of Table 9. The result accords with our heuristic to adjust α: with a small α (α \\u003d 0.5), RSPO may accept all the trajectories at the beginning and lead to a similar policy after convergence, which is no better than the PG baseline; with a large α (α \\u003d 1.5 and α \\u003d 2), RSPO may reject too many trajectories at the early stage of training and spend quite a lot of time on exploration, which sacrifices training time for the gain in the diversity score. In practice, we suggest starting with α \\u003d 1 and adjusting it such that the acceptance rate can drop at the start of training and then quickly and smoothly converge to 1, as shown in Fig. 12 (α \\u003d 1) and Fig. 5. α should be decreased if too much data is rejected at the beginning of training and increased if data efficiency always stays high in the early stage of training. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"cb1bb10a-80da-4e6f-b5fb-43c7c1090bcd\"]]]]]],[[null,null,0.6243664204182673,[[null,30725,31258]],[[[30725,31028,[[[30725,31028,[\"We adopt both behavior-driven and reward-driven intrinsic rewards in RSPO to tackle Monster-Hunt. Fig. 4 illustrates all the discovered strategies by RSPO over 20 iterations, which covers a wide range of human-interpretable strategies, including the non-cooperative apple-eating strategy as well as the \"]]]]],[31028,31028,[]],[31028,31029,[[[31028,31029,[\" \"]]]]],[31029,31121,[[[31029,31121,[\"Table 1: Types of strategies discovered by each methods in Monster-Hunt over 20 iterations. \"]]]]],[31121,31145,[[[31121,31145,[\"Apple Corner Edge Chase \"]]]]],[31145,31154,[[[31145,31154,[\"Ablation \"]]]]],[31154,31202,[[[31154,31202,[\"RSPO X X X X - No switch X X - No rint X - rint \"]]]]],[31202,31215,[[[31202,31215,[\"B only X X X \"]]]]],[31215,31258,[[[31215,31258,[\"Baseline RPG X X X MAVEN X X PG/DIPG/RND X \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"6e77f6ce-22c6-44ad-a693-ce88a6e881f8\"]],[null,null,0.6412522816745847,[[null,12609,13127]],[[[12609,12865,[[[12609,12865,[\"Rather than learning a single solution for J(θ), we aim to discover a diverse set of M policies, i.e, {πθk |1 ≤ k ≤ M}, such that all of these polices are locally optimized under J(θ) and mutually distinct w.r.t. some distance measure D(πθi , πθj ), i.e., \"]]]]],[12865,12872,[[[12865,12872,[\"max θk \"]]]]],[12872,12938,[[[12872,12938,[\"J(θk) ∀1 ≤ k ≤M, subject to D(πθi , πθj ) ≥ δ, ∀1 ≤ i \\u003c j ≤M. (1) \"]]]]],[12938,13127,[[[12938,13127,[\"Here D(·, ·) measures how different two policies are and δ is the novelty threshold. For conciseness, in the following content, we omit θ and use πk to denote the policy with parameter θk. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"cd67da02-1b32-46c1-bd0b-d0a9a526a61e\"]],[null,null,0.6301980528025974,[[null,13785,14650]],[[[13785,13848,[[[13785,13848,[\"θk \\u003d arg max θ J(θ), subject to D(πθ, πj) ≥ δ, ∀1 ≤ j \\u003c k. (2) \"]]]]],[13848,13848,[]],[13848,13849,[[[13848,13849,[\" \"]]]]],[13849,14650,[[[13849,14650,[\"Eq. (2) reduces the population-based objective to a standard constrained optimization problem for a single policy, which is much easier to solve. Such an iterative procedure does not require a large population size M as is typically necessary in population-based methods. And, in practice, only a few iterations could result in a sufficiently diverse collection of policies. We remark that, in theory, directly solving the constraint problem in Eq. (2) may lead to a solution that is not a local optimum w.r.t. the unconstrained objective J(θ). It is because a solution in Eq. (2) can be located on the boundary of the constraint space (i.e., D(πθ, πj) \\u003d δ), which is undesirable according to our original goal. However, this issue can be often alleviated by properly setting the novelty threshold δ. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"2b8ade8c-7e58-4520-b8ab-7cabf02287db\"]],[null,null,0.6628766568710132,[[null,22458,24108]],[[[22458,22646,[[[22458,22646,[\"In addition to the aforementioned RSPO algorithm, we also introduce two implementation enhance-ments for better empirical performances, especially in some performance-sensitive scenarios. \"]]]]],[22646,22646,[]],[22646,22647,[[[22646,22647,[\" \"]]]]],[22647,24108,[[[22647,24108,[\"Automatic threshold selection. We provide an empirical way of adjusting δ. In some environments, δ is sensitive to each reference policy. Instead of tuning δ for each reference policy, we choose its corresponding threshold by δj \\u003d α · D(πrnd, πj), where πrnd is a fully random policy and α is a task-specific hyperparameter. We remark that α is a constant parameter across training iterations and is much easier to choose than manually tuning δ, which requires subtle variation throughout multiple training iterations. We use automatic threshold selection by default. Detailed values of α and the methodology of tuning α can be found in App. D.1 and App. B.3 respectively. Smoothed-switching for intrinsic rewards. Intrinsic rewards have multiple switching indicators, i.e., φ1, . . . , φk−1. Moreover, for different trajectories, different subsets of indicators will be turned on and off, which may result in a varying scale of intrinsic rewards and hurt training stability. Therefore, in some constraint-sensitive cases, we propose a smoothed switching mechanism which could further improve practical performance. Specifically, we maintain a running average φ̃j over all the sampled trajectories for each indicator φj(τ), and use these smoothed indicators to compute intrinsic rewards defined in Eq. (9). Smoothed-switching empirically improves training stability when a large number of reference policies exist, such as in stag-hunt games (see Section 4.2). \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"30c0d443-bc5a-4c23-a5ad-adfcf95a1ca7\"]],[null,null,0.7099275325715608,[[null,58846,60324]],[[[58846,58871,[[[58846,58871,[\"B.3 SENSITIVITY ANALYSIS \"]]]]],[58871,59058,[[[58871,59058,[\"We have performed a sensitivity analysis over α, λintB and λintR since they are the critical to the performance RSPO. The default values used in our experiments can be found in Table 12. \"]]]]],[59058,60324,[[[59058,60324,[\"α is the most important hyperparameter in RSPO because it determines what trajectories in a batch to be accepted. We focus on the data efficiency, i.e., the proportion of accepted trajectories in a batch. In the sensitivity analysis, we run the second iteration of RSPO in Humanoid with α \\u003d 0.5, 1.0, 1.5, 2 respectively and compute the population diversity score of the 2 resulting policies. The result is shown in Fig. 12 and the left part of Table 9. The result accords with our heuristic to adjust α: with a small α (α \\u003d 0.5), RSPO may accept all the trajectories at the beginning and lead to a similar policy after convergence, which is no better than the PG baseline; with a large α (α \\u003d 1.5 and α \\u003d 2), RSPO may reject too many trajectories at the early stage of training and spend quite a lot of time on exploration, which sacrifices training time for the gain in the diversity score. In practice, we suggest starting with α \\u003d 1 and adjusting it such that the acceptance rate can drop at the start of training and then quickly and smoothly converge to 1, as shown in Fig. 12 (α \\u003d 1) and Fig. 5. α should be decreased if too much data is rejected at the beginning of training and increased if data efficiency always stays high in the early stage of training. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"cb1bb10a-80da-4e6f-b5fb-43c7c1090bcd\"]]],[[[null,0,147],[0]],[[null,297,510],[1,2]],[[null,563,666],[3]],[[null,897,909],[4]]],[[\"What is RSPO's core contribution?\",\"What is the Monster-Hunt game?\",\"What are two SMAC maps?\"]]]"]]
24309
[["wrb.fr",null,"[[\"The provided sources do not directly detail the impact of adjusting $\\\\delta$ on the specific results presented in Table 1 for the Monster-Hunt game [1].\\n\\nHowever, the sources do explain the general role of $\\\\delta$ and its associated hyperparameter $\\\\alpha$:\\n*   $\\\\delta$ is defined as the **novelty threshold** in the iterative constrained optimization problem, where a policy $\\\\pi_k$ must be sufficiently distinct from previously discovered policies $\\\\pi_1, \\\\dots, \\\\pi_{k-1}$ by a distance greater than or equal to $\\\\delta$ [2, 3].\\n*   The paper utilizes an **automatic threshold selection** where $\\\\delta_j \\u003d \\\\alpha \\\\cdot D(\\\\pi_{rnd}, \\\\pi_j)$, with $\\\\alpha$ being a task-specific hyperparameter [4]. This means $\\\\alpha$ directly controls the effective value of $\\\\delta$.\\n*   The sensitivity analysis indicates that $\\\\alpha$ is the **most important hyperparameter** in RSPO because it determines **which trajectories in a batch are accepted** for training [5].\\n*   A **small $\\\\alpha$** (and thus $\\\\delta$) might cause RSPO to accept too many trajectories initially, potentially leading to a final policy that is similar to previously discovered ones, offering no better performance than a vanilla policy gradient baseline [5].\\n*   A **large $\\\\alpha$** (and thus $\\\\delta$) might cause RSPO to reject too many trajectories in the early stages, leading to a severe wasting of samples and potentially breaking learning due to a lack of feasible trajectories, which sacrifices training time for the gain in diversity [5, 6].\\n*   While the general impact of $\\\\alpha$ on data efficiency and population diversity is discussed and evaluated for other environments like Humanoid \",null,[\"da135b09-5e1e-4967-953c-f09c5905f5b1\",\"75e2ca76-929e-4d1a-9d23-943f2151aece\",155088721],null,[[[[0,148,[[[0,148,[\"The provided sources do not directly detail the impact of adjusting $\\\\delta$ on the specific results presented in Table 1 for the Monster-Hunt game.\"]]]]],[148,252,[[[148,252,[\"However, the sources do explain the general role of $\\\\delta$ and its associated hyperparameter $\\\\alpha$:\"]]]]],[252,511,[[[252,279,[\"$\\\\delta$ is defined as the \"]],[279,296,[\"novelty threshold\",[true]]],[296,511,[\" in the iterative constrained optimization problem, where a policy $\\\\pi_k$ must be sufficiently distinct from previously discovered policies $\\\\pi_1, \\\\dots, \\\\pi_{k-1}$ by a distance greater than or equal to $\\\\delta$.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[511,738,[[[511,533,[\"The paper utilizes an \"]],[533,562,[\"automatic threshold selection\",[true]]],[562,738,[\" where $\\\\delta_j \\u003d \\\\alpha \\\\cdot D(\\\\pi_{rnd}, \\\\pi_j)$, with $\\\\alpha$ being a task-specific hyperparameter. This means $\\\\alpha$ directly controls the effective value of $\\\\delta$.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[738,910,[[[738,794,[\"The sensitivity analysis indicates that $\\\\alpha$ is the \"]],[794,823,[\"most important hyperparameter\",[true]]],[823,854,[\" in RSPO because it determines \"]],[854,896,[\"which trajectories in a batch are accepted\",[true]]],[896,910,[\" for training.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]],[910,1163,[[[910,912,[\"A \"]],[912,926,[\"small $\\\\alpha$\",[true]]],[926,1163,[\" (and thus $\\\\delta$) might cause RSPO to accept too many trajectories initially, potentially leading to a final policy that is similar to previously discovered ones, offering no better performance than a vanilla policy gradient baseline.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,3]]],[1163,1440,[[[1163,1165,[\"A \"]],[1165,1179,[\"large $\\\\alpha$\",[true]]],[1179,1440,[\" (and thus $\\\\delta$) might cause RSPO to reject too many trajectories in the early stages, leading to a severe wasting of samples and potentially breaking learning due to a lack of feasible trajectories, which sacrifices training time for the gain in diversity.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,5,4]]]],[[[\"2ed94bca-896a-47de-b72c-d219495f8f43\"],[null,0,147]],[[\"788d6746-b877-4227-ba14-15f5f75ddb46\"],[null,297,510]],[[\"ff24c5e5-dd6e-4911-ad5c-fb3a0e5ae5ea\"],[null,297,510]],[[\"3e732732-69c9-4d04-8a6e-2fafa24a7860\"],[null,563,666]],[[\"4aaaf721-323e-4705-8db4-60edf49bc26d\"],[null,897,909]],[[\"4aaaf721-323e-4705-8db4-60edf49bc26d\"],[null,927,1162]],[[\"4aaaf721-323e-4705-8db4-60edf49bc26d\"],[null,1180,1439]],[[\"2e58d7ca-e801-4d4e-a311-d02469bb0102\"],[null,1180,1439]]]],null,null,[[[\"2ed94bca-896a-47de-b72c-d219495f8f43\"],[null,null,0.6243664204182673,[[null,30725,31258]],[[[30725,31028,[[[30725,31028,[\"We adopt both behavior-driven and reward-driven intrinsic rewards in RSPO to tackle Monster-Hunt. Fig. 4 illustrates all the discovered strategies by RSPO over 20 iterations, which covers a wide range of human-interpretable strategies, including the non-cooperative apple-eating strategy as well as the \"]]]]],[31028,31028,[]],[31028,31029,[[[31028,31029,[\" \"]]]]],[31029,31121,[[[31029,31121,[\"Table 1: Types of strategies discovered by each methods in Monster-Hunt over 20 iterations. \"]]]]],[31121,31145,[[[31121,31145,[\"Apple Corner Edge Chase \"]]]]],[31145,31154,[[[31145,31154,[\"Ablation \"]]]]],[31154,31202,[[[31154,31202,[\"RSPO X X X X - No switch X X - No rint X - rint \"]]]]],[31202,31215,[[[31202,31215,[\"B only X X X \"]]]]],[31215,31258,[[[31215,31258,[\"Baseline RPG X X X MAVEN X X PG/DIPG/RND X \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"2ed94bca-896a-47de-b72c-d219495f8f43\"]]],[[\"788d6746-b877-4227-ba14-15f5f75ddb46\"],[null,null,0.6412522816745847,[[null,12609,13127]],[[[12609,12865,[[[12609,12865,[\"Rather than learning a single solution for J(θ), we aim to discover a diverse set of M policies, i.e, {πθk |1 ≤ k ≤ M}, such that all of these polices are locally optimized under J(θ) and mutually distinct w.r.t. some distance measure D(πθi , πθj ), i.e., \"]]]]],[12865,12872,[[[12865,12872,[\"max θk \"]]]]],[12872,12938,[[[12872,12938,[\"J(θk) ∀1 ≤ k ≤M, subject to D(πθi , πθj ) ≥ δ, ∀1 ≤ i \\u003c j ≤M. (1) \"]]]]],[12938,13127,[[[12938,13127,[\"Here D(·, ·) measures how different two policies are and δ is the novelty threshold. For conciseness, in the following content, we omit θ and use πk to denote the policy with parameter θk. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"788d6746-b877-4227-ba14-15f5f75ddb46\"]]],[[\"ff24c5e5-dd6e-4911-ad5c-fb3a0e5ae5ea\"],[null,null,0.6301980528025974,[[null,13785,14650]],[[[13785,13848,[[[13785,13848,[\"θk \\u003d arg max θ J(θ), subject to D(πθ, πj) ≥ δ, ∀1 ≤ j \\u003c k. (2) \"]]]]],[13848,13848,[]],[13848,13849,[[[13848,13849,[\" \"]]]]],[13849,14650,[[[13849,14650,[\"Eq. (2) reduces the population-based objective to a standard constrained optimization problem for a single policy, which is much easier to solve. Such an iterative procedure does not require a large population size M as is typically necessary in population-based methods. And, in practice, only a few iterations could result in a sufficiently diverse collection of policies. We remark that, in theory, directly solving the constraint problem in Eq. (2) may lead to a solution that is not a local optimum w.r.t. the unconstrained objective J(θ). It is because a solution in Eq. (2) can be located on the boundary of the constraint space (i.e., D(πθ, πj) \\u003d δ), which is undesirable according to our original goal. However, this issue can be often alleviated by properly setting the novelty threshold δ. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"ff24c5e5-dd6e-4911-ad5c-fb3a0e5ae5ea\"]]],[[\"3e732732-69c9-4d04-8a6e-2fafa24a7860\"],[null,null,0.6628766568710132,[[null,22458,24108]],[[[22458,22646,[[[22458,22646,[\"In addition to the aforementioned RSPO algorithm, we also introduce two implementation enhance-ments for better empirical performances, especially in some performance-sensitive scenarios. \"]]]]],[22646,22646,[]],[22646,22647,[[[22646,22647,[\" \"]]]]],[22647,24108,[[[22647,24108,[\"Automatic threshold selection. We provide an empirical way of adjusting δ. In some environments, δ is sensitive to each reference policy. Instead of tuning δ for each reference policy, we choose its corresponding threshold by δj \\u003d α · D(πrnd, πj), where πrnd is a fully random policy and α is a task-specific hyperparameter. We remark that α is a constant parameter across training iterations and is much easier to choose than manually tuning δ, which requires subtle variation throughout multiple training iterations. We use automatic threshold selection by default. Detailed values of α and the methodology of tuning α can be found in App. D.1 and App. B.3 respectively. Smoothed-switching for intrinsic rewards. Intrinsic rewards have multiple switching indicators, i.e., φ1, . . . , φk−1. Moreover, for different trajectories, different subsets of indicators will be turned on and off, which may result in a varying scale of intrinsic rewards and hurt training stability. Therefore, in some constraint-sensitive cases, we propose a smoothed switching mechanism which could further improve practical performance. Specifically, we maintain a running average φ̃j over all the sampled trajectories for each indicator φj(τ), and use these smoothed indicators to compute intrinsic rewards defined in Eq. (9). Smoothed-switching empirically improves training stability when a large number of reference policies exist, such as in stag-hunt games (see Section 4.2). \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"3e732732-69c9-4d04-8a6e-2fafa24a7860\"]]],[[\"4aaaf721-323e-4705-8db4-60edf49bc26d\"],[null,null,0.7099275325715608,[[null,58846,60324]],[[[58846,58871,[[[58846,58871,[\"B.3 SENSITIVITY ANALYSIS \"]]]]],[58871,59058,[[[58871,59058,[\"We have performed a sensitivity analysis over α, λintB and λintR since they are the critical to the performance RSPO. The default values used in our experiments can be found in Table 12. \"]]]]],[59058,60324,[[[59058,60324,[\"α is the most important hyperparameter in RSPO because it determines what trajectories in a batch to be accepted. We focus on the data efficiency, i.e., the proportion of accepted trajectories in a batch. In the sensitivity analysis, we run the second iteration of RSPO in Humanoid with α \\u003d 0.5, 1.0, 1.5, 2 respectively and compute the population diversity score of the 2 resulting policies. The result is shown in Fig. 12 and the left part of Table 9. The result accords with our heuristic to adjust α: with a small α (α \\u003d 0.5), RSPO may accept all the trajectories at the beginning and lead to a similar policy after convergence, which is no better than the PG baseline; with a large α (α \\u003d 1.5 and α \\u003d 2), RSPO may reject too many trajectories at the early stage of training and spend quite a lot of time on exploration, which sacrifices training time for the gain in the diversity score. In practice, we suggest starting with α \\u003d 1 and adjusting it such that the acceptance rate can drop at the start of training and then quickly and smoothly converge to 1, as shown in Fig. 12 (α \\u003d 1) and Fig. 5. α should be decreased if too much data is rejected at the beginning of training and increased if data efficiency always stays high in the early stage of training. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"4aaaf721-323e-4705-8db4-60edf49bc26d\"]]],[[\"2e58d7ca-e801-4d4e-a311-d02469bb0102\"],[null,null,0.638765286182543,[[null,17046,17934]],[[[17046,17065,[[[17046,17065,[\"Jfilter(θ) \\u003d Eτ∼πθ \"]]]]],[17065,17072,[[[17065,17072,[\"[ φ(τ) \"]]]]],[17072,17076,[[[17072,17076,[\"∑ t \"]]]]],[17076,17081,[[[17076,17081,[\"γtrt \"]]]]],[17081,17099,[[[17081,17099,[\"] , where φ(τ) :\\u003d \"]]]]],[17099,17108,[[[17099,17108,[\"k−1∏ j\\u003d1 \"]]]]],[17108,17131,[[[17108,17131,[\"I[NLL(τ ;πj) ≥ δ]. (5) \"]]]]],[17131,17478,[[[17131,17478,[\"We call the objective in Eq. (5) a filtering objective. We show in App. G that solving Eq. (5) is equivalent to solving Eq. (2) with an even stronger diversity constraint. In addition, we also remark that trajectory filtering shares a conceptually similar motivation with the clipping term in Proximal Policy Optimization (Schulman et al., 2017). \"]]]]],[17478,17526,[[[17478,17526,[\"3.4 INTRINSIC REWARDS FOR DIVERSITY EXPLORATION \"]]]]],[17526,17934,[[[17526,17934,[\"The main issue in Eq. (5) is that trajectory filtering may reject a significant number of trajectories, especially in the early stage of policy learning since the policy is typically initialized to the a random policy. Hence, it is often the case that most of the data in a batch are abandoned, which leads to a severe wasting of samples and may even break learning due to the lack of feasible trajectories. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"2e58d7ca-e801-4d4e-a311-d02469bb0102\"]]]]]],[[null,null,0.6243664204182673,[[null,30725,31258]],[[[30725,31028,[[[30725,31028,[\"We adopt both behavior-driven and reward-driven intrinsic rewards in RSPO to tackle Monster-Hunt. Fig. 4 illustrates all the discovered strategies by RSPO over 20 iterations, which covers a wide range of human-interpretable strategies, including the non-cooperative apple-eating strategy as well as the \"]]]]],[31028,31028,[]],[31028,31029,[[[31028,31029,[\" \"]]]]],[31029,31121,[[[31029,31121,[\"Table 1: Types of strategies discovered by each methods in Monster-Hunt over 20 iterations. \"]]]]],[31121,31145,[[[31121,31145,[\"Apple Corner Edge Chase \"]]]]],[31145,31154,[[[31145,31154,[\"Ablation \"]]]]],[31154,31202,[[[31154,31202,[\"RSPO X X X X - No switch X X - No rint X - rint \"]]]]],[31202,31215,[[[31202,31215,[\"B only X X X \"]]]]],[31215,31258,[[[31215,31258,[\"Baseline RPG X X X MAVEN X X PG/DIPG/RND X \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"2ed94bca-896a-47de-b72c-d219495f8f43\"]],[null,null,0.6412522816745847,[[null,12609,13127]],[[[12609,12865,[[[12609,12865,[\"Rather than learning a single solution for J(θ), we aim to discover a diverse set of M policies, i.e, {πθk |1 ≤ k ≤ M}, such that all of these polices are locally optimized under J(θ) and mutually distinct w.r.t. some distance measure D(πθi , πθj ), i.e., \"]]]]],[12865,12872,[[[12865,12872,[\"max θk \"]]]]],[12872,12938,[[[12872,12938,[\"J(θk) ∀1 ≤ k ≤M, subject to D(πθi , πθj ) ≥ δ, ∀1 ≤ i \\u003c j ≤M. (1) \"]]]]],[12938,13127,[[[12938,13127,[\"Here D(·, ·) measures how different two policies are and δ is the novelty threshold. For conciseness, in the following content, we omit θ and use πk to denote the policy with parameter θk. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"788d6746-b877-4227-ba14-15f5f75ddb46\"]],[null,null,0.6301980528025974,[[null,13785,14650]],[[[13785,13848,[[[13785,13848,[\"θk \\u003d arg max θ J(θ), subject to D(πθ, πj) ≥ δ, ∀1 ≤ j \\u003c k. (2) \"]]]]],[13848,13848,[]],[13848,13849,[[[13848,13849,[\" \"]]]]],[13849,14650,[[[13849,14650,[\"Eq. (2) reduces the population-based objective to a standard constrained optimization problem for a single policy, which is much easier to solve. Such an iterative procedure does not require a large population size M as is typically necessary in population-based methods. And, in practice, only a few iterations could result in a sufficiently diverse collection of policies. We remark that, in theory, directly solving the constraint problem in Eq. (2) may lead to a solution that is not a local optimum w.r.t. the unconstrained objective J(θ). It is because a solution in Eq. (2) can be located on the boundary of the constraint space (i.e., D(πθ, πj) \\u003d δ), which is undesirable according to our original goal. However, this issue can be often alleviated by properly setting the novelty threshold δ. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"ff24c5e5-dd6e-4911-ad5c-fb3a0e5ae5ea\"]],[null,null,0.6628766568710132,[[null,22458,24108]],[[[22458,22646,[[[22458,22646,[\"In addition to the aforementioned RSPO algorithm, we also introduce two implementation enhance-ments for better empirical performances, especially in some performance-sensitive scenarios. \"]]]]],[22646,22646,[]],[22646,22647,[[[22646,22647,[\" \"]]]]],[22647,24108,[[[22647,24108,[\"Automatic threshold selection. We provide an empirical way of adjusting δ. In some environments, δ is sensitive to each reference policy. Instead of tuning δ for each reference policy, we choose its corresponding threshold by δj \\u003d α · D(πrnd, πj), where πrnd is a fully random policy and α is a task-specific hyperparameter. We remark that α is a constant parameter across training iterations and is much easier to choose than manually tuning δ, which requires subtle variation throughout multiple training iterations. We use automatic threshold selection by default. Detailed values of α and the methodology of tuning α can be found in App. D.1 and App. B.3 respectively. Smoothed-switching for intrinsic rewards. Intrinsic rewards have multiple switching indicators, i.e., φ1, . . . , φk−1. Moreover, for different trajectories, different subsets of indicators will be turned on and off, which may result in a varying scale of intrinsic rewards and hurt training stability. Therefore, in some constraint-sensitive cases, we propose a smoothed switching mechanism which could further improve practical performance. Specifically, we maintain a running average φ̃j over all the sampled trajectories for each indicator φj(τ), and use these smoothed indicators to compute intrinsic rewards defined in Eq. (9). Smoothed-switching empirically improves training stability when a large number of reference policies exist, such as in stag-hunt games (see Section 4.2). \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"3e732732-69c9-4d04-8a6e-2fafa24a7860\"]],[null,null,0.7099275325715608,[[null,58846,60324]],[[[58846,58871,[[[58846,58871,[\"B.3 SENSITIVITY ANALYSIS \"]]]]],[58871,59058,[[[58871,59058,[\"We have performed a sensitivity analysis over α, λintB and λintR since they are the critical to the performance RSPO. The default values used in our experiments can be found in Table 12. \"]]]]],[59058,60324,[[[59058,60324,[\"α is the most important hyperparameter in RSPO because it determines what trajectories in a batch to be accepted. We focus on the data efficiency, i.e., the proportion of accepted trajectories in a batch. In the sensitivity analysis, we run the second iteration of RSPO in Humanoid with α \\u003d 0.5, 1.0, 1.5, 2 respectively and compute the population diversity score of the 2 resulting policies. The result is shown in Fig. 12 and the left part of Table 9. The result accords with our heuristic to adjust α: with a small α (α \\u003d 0.5), RSPO may accept all the trajectories at the beginning and lead to a similar policy after convergence, which is no better than the PG baseline; with a large α (α \\u003d 1.5 and α \\u003d 2), RSPO may reject too many trajectories at the early stage of training and spend quite a lot of time on exploration, which sacrifices training time for the gain in the diversity score. In practice, we suggest starting with α \\u003d 1 and adjusting it such that the acceptance rate can drop at the start of training and then quickly and smoothly converge to 1, as shown in Fig. 12 (α \\u003d 1) and Fig. 5. α should be decreased if too much data is rejected at the beginning of training and increased if data efficiency always stays high in the early stage of training. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"4aaaf721-323e-4705-8db4-60edf49bc26d\"]],[null,null,0.638765286182543,[[null,17046,17934]],[[[17046,17065,[[[17046,17065,[\"Jfilter(θ) \\u003d Eτ∼πθ \"]]]]],[17065,17072,[[[17065,17072,[\"[ φ(τ) \"]]]]],[17072,17076,[[[17072,17076,[\"∑ t \"]]]]],[17076,17081,[[[17076,17081,[\"γtrt \"]]]]],[17081,17099,[[[17081,17099,[\"] , where φ(τ) :\\u003d \"]]]]],[17099,17108,[[[17099,17108,[\"k−1∏ j\\u003d1 \"]]]]],[17108,17131,[[[17108,17131,[\"I[NLL(τ ;πj) ≥ δ]. (5) \"]]]]],[17131,17478,[[[17131,17478,[\"We call the objective in Eq. (5) a filtering objective. We show in App. G that solving Eq. (5) is equivalent to solving Eq. (2) with an even stronger diversity constraint. In addition, we also remark that trajectory filtering shares a conceptually similar motivation with the clipping term in Proximal Policy Optimization (Schulman et al., 2017). \"]]]]],[17478,17526,[[[17478,17526,[\"3.4 INTRINSIC REWARDS FOR DIVERSITY EXPLORATION \"]]]]],[17526,17934,[[[17526,17934,[\"The main issue in Eq. (5) is that trajectory filtering may reject a significant number of trajectories, especially in the early stage of policy learning since the policy is typically initialized to the a random policy. Hence, it is often the case that most of the data in a batch are abandoned, which leads to a severe wasting of samples and may even break learning due to the lack of feasible trajectories. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"2e58d7ca-e801-4d4e-a311-d02469bb0102\"]]],[[[null,0,147],[0]],[[null,297,510],[1,2]],[[null,563,666],[3]],[[null,897,909],[4]],[[null,927,1162],[4]],[[null,1180,1439],[4,5]]],[[\"What is RSPO's core contribution?\",\"What is the Monster-Hunt game?\",\"What are two SMAC maps?\"]]]"]]
35782
[["wrb.fr",null,"[[\"The provided sources do not directly detail the impact of adjusting $\\\\delta$ on the specific results presented in Table 1 for the Monster-Hunt game [1].\\n\\nHowever, the sources do explain the general role of $\\\\delta$ and its associated hyperparameter $\\\\alpha$:\\n*   $\\\\delta$ is defined as the **novelty threshold** in the iterative constrained optimization problem, where a policy $\\\\pi_k$ must be sufficiently distinct from previously discovered policies $\\\\pi_1, \\\\dots, \\\\pi_{k-1}$ by a distance greater than or equal to $\\\\delta$ [2, 3].\\n*   The paper utilizes an **automatic threshold selection** where $\\\\delta_j \\u003d \\\\alpha \\\\cdot D(\\\\pi_{rnd}, \\\\pi_j)$, with $\\\\alpha$ being a task-specific hyperparameter [4]. This means $\\\\alpha$ directly controls the effective value of $\\\\delta$.\\n*   The sensitivity analysis indicates that $\\\\alpha$ is the **most important hyperparameter** in RSPO because it determines **which trajectories in a batch are accepted** for training [5].\\n*   A **small $\\\\alpha$** (and thus $\\\\delta$) might cause RSPO to accept too many trajectories initially, potentially leading to a final policy that is similar to previously discovered ones, offering no better performance than a vanilla policy gradient baseline [5].\\n*   A **large $\\\\alpha$** (and thus $\\\\delta$) might cause RSPO to reject too many trajectories in the early stages, leading to a severe wasting of samples and potentially breaking learning due to a lack of feasible trajectories, which sacrifices training time for the gain in diversity [5, 6].\\n*   While the general impact of $\\\\alpha$ on data efficiency and population diversity is discussed and evaluated for other environments like Humanoid [5, 7, 8], the paper does not specify how tuning $\\\\delta$ (or $\\\\alpha$) would alter the specific categories of strategies (Apple, Corner, Edge, Chase) discovered and listed in Table 1 for the Monster-Hunt game [1]. The discussion pertaining to Table 1 primarily focuses on the necessity of reward switching and intrinsic rewards [9].\",null,[\"da135b09-5e1e-4967-953c-f09c5905f5b1\",\"75e2ca76-929e-4d1a-9d23-943f2151aece\",155088721],null,[[[[0,148,[[[0,148,[\"The provided sources do not directly detail the impact of adjusting $\\\\delta$ on the specific results presented in Table 1 for the Monster-Hunt game.\"]]]]],[148,252,[[[148,252,[\"However, the sources do explain the general role of $\\\\delta$ and its associated hyperparameter $\\\\alpha$:\"]]]]],[252,511,[[[252,279,[\"$\\\\delta$ is defined as the \"]],[279,296,[\"novelty threshold\",[true]]],[296,511,[\" in the iterative constrained optimization problem, where a policy $\\\\pi_k$ must be sufficiently distinct from previously discovered policies $\\\\pi_1, \\\\dots, \\\\pi_{k-1}$ by a distance greater than or equal to $\\\\delta$.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[511,738,[[[511,533,[\"The paper utilizes an \"]],[533,562,[\"automatic threshold selection\",[true]]],[562,738,[\" where $\\\\delta_j \\u003d \\\\alpha \\\\cdot D(\\\\pi_{rnd}, \\\\pi_j)$, with $\\\\alpha$ being a task-specific hyperparameter. This means $\\\\alpha$ directly controls the effective value of $\\\\delta$.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[738,910,[[[738,794,[\"The sensitivity analysis indicates that $\\\\alpha$ is the \"]],[794,823,[\"most important hyperparameter\",[true]]],[823,854,[\" in RSPO because it determines \"]],[854,896,[\"which trajectories in a batch are accepted\",[true]]],[896,910,[\" for training.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]],[910,1163,[[[910,912,[\"A \"]],[912,926,[\"small $\\\\alpha$\",[true]]],[926,1163,[\" (and thus $\\\\delta$) might cause RSPO to accept too many trajectories initially, potentially leading to a final policy that is similar to previously discovered ones, offering no better performance than a vanilla policy gradient baseline.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,3]]],[1163,1440,[[[1163,1165,[\"A \"]],[1165,1179,[\"large $\\\\alpha$\",[true]]],[1179,1440,[\" (and thus $\\\\delta$) might cause RSPO to reject too many trajectories in the early stages, leading to a severe wasting of samples and potentially breaking learning due to a lack of feasible trajectories, which sacrifices training time for the gain in diversity.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,5,4]]]],[[[\"62dd6a98-60b1-41e3-ba6e-0c1508cbbf92\"],[null,0,147]],[[\"12b949ab-782f-4e87-a05b-7999850161a4\"],[null,297,510]],[[\"9f842d8b-1ccf-42d7-b3f2-b2988d49559c\"],[null,297,510]],[[\"333e0760-3fac-4056-8ac5-0890fb3bea8e\"],[null,563,666]],[[\"84486d82-b228-438c-8314-895893581bfc\"],[null,897,909]],[[\"84486d82-b228-438c-8314-895893581bfc\"],[null,927,1162]],[[\"84486d82-b228-438c-8314-895893581bfc\"],[null,1180,1439]],[[\"c7184c02-054c-45be-85a3-f870fe4aca8b\"],[null,1180,1439]],[[\"84486d82-b228-438c-8314-895893581bfc\"],[null,1440,1584]],[[\"d471fc1c-3585-42e2-86ea-48004dbd02c7\"],[null,1440,1584]],[[\"f486c02d-1bb5-40ca-a11c-f579f362034b\"],[null,1440,1584]],[[\"62dd6a98-60b1-41e3-ba6e-0c1508cbbf92\"],[null,1584,1784]],[[\"f5ad3c6a-6ec8-40af-9922-934c336e258f\"],[null,1784,1899]]]],null,null,[[[\"62dd6a98-60b1-41e3-ba6e-0c1508cbbf92\"],[null,null,0.6243664204182673,[[null,30725,31258]],[[[30725,31028,[[[30725,31028,[\"We adopt both behavior-driven and reward-driven intrinsic rewards in RSPO to tackle Monster-Hunt. Fig. 4 illustrates all the discovered strategies by RSPO over 20 iterations, which covers a wide range of human-interpretable strategies, including the non-cooperative apple-eating strategy as well as the \"]]]]],[31028,31028,[]],[31028,31029,[[[31028,31029,[\" \"]]]]],[31029,31121,[[[31029,31121,[\"Table 1: Types of strategies discovered by each methods in Monster-Hunt over 20 iterations. \"]]]]],[31121,31145,[[[31121,31145,[\"Apple Corner Edge Chase \"]]]]],[31145,31154,[[[31145,31154,[\"Ablation \"]]]]],[31154,31202,[[[31154,31202,[\"RSPO X X X X - No switch X X - No rint X - rint \"]]]]],[31202,31215,[[[31202,31215,[\"B only X X X \"]]]]],[31215,31258,[[[31215,31258,[\"Baseline RPG X X X MAVEN X X PG/DIPG/RND X \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"62dd6a98-60b1-41e3-ba6e-0c1508cbbf92\"]]],[[\"12b949ab-782f-4e87-a05b-7999850161a4\"],[null,null,0.6412522816745847,[[null,12609,13127]],[[[12609,12865,[[[12609,12865,[\"Rather than learning a single solution for J(θ), we aim to discover a diverse set of M policies, i.e, {πθk |1 ≤ k ≤ M}, such that all of these polices are locally optimized under J(θ) and mutually distinct w.r.t. some distance measure D(πθi , πθj ), i.e., \"]]]]],[12865,12872,[[[12865,12872,[\"max θk \"]]]]],[12872,12938,[[[12872,12938,[\"J(θk) ∀1 ≤ k ≤M, subject to D(πθi , πθj ) ≥ δ, ∀1 ≤ i \\u003c j ≤M. (1) \"]]]]],[12938,13127,[[[12938,13127,[\"Here D(·, ·) measures how different two policies are and δ is the novelty threshold. For conciseness, in the following content, we omit θ and use πk to denote the policy with parameter θk. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"12b949ab-782f-4e87-a05b-7999850161a4\"]]],[[\"9f842d8b-1ccf-42d7-b3f2-b2988d49559c\"],[null,null,0.6301980528025974,[[null,13785,14650]],[[[13785,13848,[[[13785,13848,[\"θk \\u003d arg max θ J(θ), subject to D(πθ, πj) ≥ δ, ∀1 ≤ j \\u003c k. (2) \"]]]]],[13848,13848,[]],[13848,13849,[[[13848,13849,[\" \"]]]]],[13849,14650,[[[13849,14650,[\"Eq. (2) reduces the population-based objective to a standard constrained optimization problem for a single policy, which is much easier to solve. Such an iterative procedure does not require a large population size M as is typically necessary in population-based methods. And, in practice, only a few iterations could result in a sufficiently diverse collection of policies. We remark that, in theory, directly solving the constraint problem in Eq. (2) may lead to a solution that is not a local optimum w.r.t. the unconstrained objective J(θ). It is because a solution in Eq. (2) can be located on the boundary of the constraint space (i.e., D(πθ, πj) \\u003d δ), which is undesirable according to our original goal. However, this issue can be often alleviated by properly setting the novelty threshold δ. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"9f842d8b-1ccf-42d7-b3f2-b2988d49559c\"]]],[[\"333e0760-3fac-4056-8ac5-0890fb3bea8e\"],[null,null,0.6628766568710132,[[null,22458,24108]],[[[22458,22646,[[[22458,22646,[\"In addition to the aforementioned RSPO algorithm, we also introduce two implementation enhance-ments for better empirical performances, especially in some performance-sensitive scenarios. \"]]]]],[22646,22646,[]],[22646,22647,[[[22646,22647,[\" \"]]]]],[22647,24108,[[[22647,24108,[\"Automatic threshold selection. We provide an empirical way of adjusting δ. In some environments, δ is sensitive to each reference policy. Instead of tuning δ for each reference policy, we choose its corresponding threshold by δj \\u003d α · D(πrnd, πj), where πrnd is a fully random policy and α is a task-specific hyperparameter. We remark that α is a constant parameter across training iterations and is much easier to choose than manually tuning δ, which requires subtle variation throughout multiple training iterations. We use automatic threshold selection by default. Detailed values of α and the methodology of tuning α can be found in App. D.1 and App. B.3 respectively. Smoothed-switching for intrinsic rewards. Intrinsic rewards have multiple switching indicators, i.e., φ1, . . . , φk−1. Moreover, for different trajectories, different subsets of indicators will be turned on and off, which may result in a varying scale of intrinsic rewards and hurt training stability. Therefore, in some constraint-sensitive cases, we propose a smoothed switching mechanism which could further improve practical performance. Specifically, we maintain a running average φ̃j over all the sampled trajectories for each indicator φj(τ), and use these smoothed indicators to compute intrinsic rewards defined in Eq. (9). Smoothed-switching empirically improves training stability when a large number of reference policies exist, such as in stag-hunt games (see Section 4.2). \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"333e0760-3fac-4056-8ac5-0890fb3bea8e\"]]],[[\"84486d82-b228-438c-8314-895893581bfc\"],[null,null,0.7099275325715608,[[null,58846,60324]],[[[58846,58871,[[[58846,58871,[\"B.3 SENSITIVITY ANALYSIS \"]]]]],[58871,59058,[[[58871,59058,[\"We have performed a sensitivity analysis over α, λintB and λintR since they are the critical to the performance RSPO. The default values used in our experiments can be found in Table 12. \"]]]]],[59058,60324,[[[59058,60324,[\"α is the most important hyperparameter in RSPO because it determines what trajectories in a batch to be accepted. We focus on the data efficiency, i.e., the proportion of accepted trajectories in a batch. In the sensitivity analysis, we run the second iteration of RSPO in Humanoid with α \\u003d 0.5, 1.0, 1.5, 2 respectively and compute the population diversity score of the 2 resulting policies. The result is shown in Fig. 12 and the left part of Table 9. The result accords with our heuristic to adjust α: with a small α (α \\u003d 0.5), RSPO may accept all the trajectories at the beginning and lead to a similar policy after convergence, which is no better than the PG baseline; with a large α (α \\u003d 1.5 and α \\u003d 2), RSPO may reject too many trajectories at the early stage of training and spend quite a lot of time on exploration, which sacrifices training time for the gain in the diversity score. In practice, we suggest starting with α \\u003d 1 and adjusting it such that the acceptance rate can drop at the start of training and then quickly and smoothly converge to 1, as shown in Fig. 12 (α \\u003d 1) and Fig. 5. α should be decreased if too much data is rejected at the beginning of training and increased if data efficiency always stays high in the early stage of training. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"84486d82-b228-438c-8314-895893581bfc\"]]],[[\"c7184c02-054c-45be-85a3-f870fe4aca8b\"],[null,null,0.638765286182543,[[null,17046,17934]],[[[17046,17065,[[[17046,17065,[\"Jfilter(θ) \\u003d Eτ∼πθ \"]]]]],[17065,17072,[[[17065,17072,[\"[ φ(τ) \"]]]]],[17072,17076,[[[17072,17076,[\"∑ t \"]]]]],[17076,17081,[[[17076,17081,[\"γtrt \"]]]]],[17081,17099,[[[17081,17099,[\"] , where φ(τ) :\\u003d \"]]]]],[17099,17108,[[[17099,17108,[\"k−1∏ j\\u003d1 \"]]]]],[17108,17131,[[[17108,17131,[\"I[NLL(τ ;πj) ≥ δ]. (5) \"]]]]],[17131,17478,[[[17131,17478,[\"We call the objective in Eq. (5) a filtering objective. We show in App. G that solving Eq. (5) is equivalent to solving Eq. (2) with an even stronger diversity constraint. In addition, we also remark that trajectory filtering shares a conceptually similar motivation with the clipping term in Proximal Policy Optimization (Schulman et al., 2017). \"]]]]],[17478,17526,[[[17478,17526,[\"3.4 INTRINSIC REWARDS FOR DIVERSITY EXPLORATION \"]]]]],[17526,17934,[[[17526,17934,[\"The main issue in Eq. (5) is that trajectory filtering may reject a significant number of trajectories, especially in the early stage of policy learning since the policy is typically initialized to the a random policy. Hence, it is often the case that most of the data in a batch are abandoned, which leads to a severe wasting of samples and may even break learning due to the lack of feasible trajectories. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"c7184c02-054c-45be-85a3-f870fe4aca8b\"]]],[[\"d471fc1c-3585-42e2-86ea-48004dbd02c7\"],[null,null,0.6599198382183982,[[null,57350,57853]],[[[57350,57416,[[[57350,57416,[\"Table 8: Population Diversity on the hard map 2c_vs_64zg in SMAC. \"]]]]],[57416,57469,[[[57416,57469,[\"Algorithm # Distinct Strategies Population Diversity \"]]]]],[57469,57495,[[[57469,57495,[\"PG 1 0.981 PBT-CE 2 1.000 \"]]]]],[57495,57507,[[[57495,57507,[\"DvD 3 1.000 \"]]]]],[57507,57520,[[[57507,57520,[\"RSPO 4 1.000 \"]]]]],[57520,57520,[]],[57520,57521,[[[57520,57521,[\" \"]]]]],[57521,57552,[[[57521,57552,[\"0 500 1000 1500 2000 2500 3000 \"]]]]],[57552,57574,[[[57552,57574,[\"Training Update Steps \"]]],[null,4]]],[57574,57578,[[[57574,57578,[\"0.0 \"]]]]],[57578,57582,[[[57578,57582,[\"0.2 \"]]]]],[57582,57586,[[[57582,57586,[\"0.4 \"]]]]],[57586,57590,[[[57586,57590,[\"0.6 \"]]]]],[57590,57594,[[[57590,57594,[\"0.8 \"]]]]],[57594,57598,[[[57594,57598,[\"1.0 \"]]]]],[57598,57603,[[[57598,57603,[\"D at \"]]],[null,4]]],[57603,57607,[[[57603,57607,[\"a E \"]]],[null,4]]],[57607,57614,[[[57607,57614,[\"ffi ci \"]]],[null,4]]],[57614,57620,[[[57614,57620,[\"en cy \"]]],[null,4]]],[57620,57656,[[[57620,57656,[\"alpha\\u003d0.5 alpha\\u003d1 alpha\\u003d1.5 alpha\\u003d2 \"]]],[null,6]]],[57656,57713,[[[57656,57713,[\"Figure 12: Data efficiency with different α in Humanoid. \"]]]]],[57713,57752,[[[57713,57752,[\"0 250 500 750 1000 1250 1500 1750 2000 \"]]]]],[57752,57774,[[[57752,57774,[\"Training Update Steps \"]]],[null,4]]],[57774,57778,[[[57774,57778,[\"0.0 \"]]]]],[57778,57782,[[[57778,57782,[\"0.2 \"]]]]],[57782,57786,[[[57782,57786,[\"0.4 \"]]]]],[57786,57790,[[[57786,57790,[\"0.6 \"]]]]],[57790,57794,[[[57790,57794,[\"0.8 \"]]]]],[57794,57798,[[[57794,57798,[\"1.0 \"]]]]],[57798,57803,[[[57798,57803,[\"W in \"]]],[null,4]]],[57803,57809,[[[57803,57809,[\"ni ng \"]]],[null,4]]],[57809,57816,[[[57809,57816,[\" ra te \"]]],[null,4]]],[57816,57853,[[[57816,57853,[\"lambdaR\\u003d0.2 lambdaR\\u003d0.05 lambdaR\\u003d0.0 \"]]],[null,6]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"d471fc1c-3585-42e2-86ea-48004dbd02c7\"]]],[[\"f486c02d-1bb5-40ca-a11c-f579f362034b\"],[null,null,0.6563970223842246,[[null,57853,58846]],[[[57853,57929,[[[57853,57929,[\"Figure 13: Learning curve with different λintR on the 2m_vs_1z map in SMAC. \"]]]]],[57929,57979,[[[57929,57979,[\"α Population Diversity λintB Population Diversity \"]]]]],[57979,58060,[[[57979,58060,[\"0.5 0.604 0.5 0.640 1.0 0.676 1.0 0.621 1.5 0.687 5.0 0.697 2.0 0.748 10.0 0.697 \"]]]]],[58060,58324,[[[58060,58324,[\"Table 9: Population Diversity scores of the first 2 policies with different hyperparameters in Humanoid. We have scaled the denominator of the RBF kernel in the Population Diversity matrix by a factor 10, such that the difference can be demonstrated more clearly. \"]]]]],[58324,58846,[[[58324,58846,[\"To the best of our knowledge, a commonly accepted policy diversity metric for complex MARL games remains an open question in the existing literature. In our practice, rendering and visualizing the evaluation trajectories remains the best approach to distinguish different learned strategies. We emphasize that qualitatively, we are so far the first paper that ever reports such a visually diverse collection of winning strategies on a hard map in SMAC. Please check our website for policy visualizations (see Appendix A). \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"f486c02d-1bb5-40ca-a11c-f579f362034b\"]]],[[\"f5ad3c6a-6ec8-40af-9922-934c336e258f\"],[null,null,0.6810832532897884,[[null,32492,34064]],[[[32492,32659,[[[32492,32659,[\"We perform ablation studies on RSPO by turning off reward switching (No Switch) or intrinsic reward (No rint) or only using the behavior-driven intrinsic reward (rint \"]]]]],[32659,34064,[[[32659,34064,[\"B only), and evaluate the performances of many baseline methods, including vanilla PG with restarts (PG), DIPG, RND, a popular multi-agent exploration method MAVEN (Mahajan et al., 2019) and reward-randomized policy gradient (RPG) (Tang et al., 2021). We summarize the categories of discovered strategies by all these baselines in Table 1. Apple denotes the non-cooperative apple-eating NE; Chase denotes the optimal NE where both agents actively chase the monster; Corner and Edge denote the sub-optimal cooperative NE where both agents passively wait for the monster at a corner or an edge respectively. Regarding the baselines, PG, DIPG, and RND never discover any strategy beyond the non-cooperative Apple NE. For RPG, even using the domain knowledge to change the reward structure of the game, it never discovers the Edge NE. Regarding the RSPO variants, both reward switching and intrinsic rewards are necessary. Fig. 5 shows that when the intrinsic reward is turned off, the proportion of accepted trajectories per batch stays low throughout training. This implies that the learning policy failed to escape the infeasible subspace. Besides, as shown in Table 1, using behavior-driven exploration alone fails to discover the optimal NE, which suggests the necessity of reward-driven exploration to maximize the divergence of both states and actions in problems with massive equivalent local optima. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"f5ad3c6a-6ec8-40af-9922-934c336e258f\"]]]]]],[[null,null,0.6243664204182673,[[null,30725,31258]],[[[30725,31028,[[[30725,31028,[\"We adopt both behavior-driven and reward-driven intrinsic rewards in RSPO to tackle Monster-Hunt. Fig. 4 illustrates all the discovered strategies by RSPO over 20 iterations, which covers a wide range of human-interpretable strategies, including the non-cooperative apple-eating strategy as well as the \"]]]]],[31028,31028,[]],[31028,31029,[[[31028,31029,[\" \"]]]]],[31029,31121,[[[31029,31121,[\"Table 1: Types of strategies discovered by each methods in Monster-Hunt over 20 iterations. \"]]]]],[31121,31145,[[[31121,31145,[\"Apple Corner Edge Chase \"]]]]],[31145,31154,[[[31145,31154,[\"Ablation \"]]]]],[31154,31202,[[[31154,31202,[\"RSPO X X X X - No switch X X - No rint X - rint \"]]]]],[31202,31215,[[[31202,31215,[\"B only X X X \"]]]]],[31215,31258,[[[31215,31258,[\"Baseline RPG X X X MAVEN X X PG/DIPG/RND X \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"62dd6a98-60b1-41e3-ba6e-0c1508cbbf92\"]],[null,null,0.6412522816745847,[[null,12609,13127]],[[[12609,12865,[[[12609,12865,[\"Rather than learning a single solution for J(θ), we aim to discover a diverse set of M policies, i.e, {πθk |1 ≤ k ≤ M}, such that all of these polices are locally optimized under J(θ) and mutually distinct w.r.t. some distance measure D(πθi , πθj ), i.e., \"]]]]],[12865,12872,[[[12865,12872,[\"max θk \"]]]]],[12872,12938,[[[12872,12938,[\"J(θk) ∀1 ≤ k ≤M, subject to D(πθi , πθj ) ≥ δ, ∀1 ≤ i \\u003c j ≤M. (1) \"]]]]],[12938,13127,[[[12938,13127,[\"Here D(·, ·) measures how different two policies are and δ is the novelty threshold. For conciseness, in the following content, we omit θ and use πk to denote the policy with parameter θk. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"12b949ab-782f-4e87-a05b-7999850161a4\"]],[null,null,0.6301980528025974,[[null,13785,14650]],[[[13785,13848,[[[13785,13848,[\"θk \\u003d arg max θ J(θ), subject to D(πθ, πj) ≥ δ, ∀1 ≤ j \\u003c k. (2) \"]]]]],[13848,13848,[]],[13848,13849,[[[13848,13849,[\" \"]]]]],[13849,14650,[[[13849,14650,[\"Eq. (2) reduces the population-based objective to a standard constrained optimization problem for a single policy, which is much easier to solve. Such an iterative procedure does not require a large population size M as is typically necessary in population-based methods. And, in practice, only a few iterations could result in a sufficiently diverse collection of policies. We remark that, in theory, directly solving the constraint problem in Eq. (2) may lead to a solution that is not a local optimum w.r.t. the unconstrained objective J(θ). It is because a solution in Eq. (2) can be located on the boundary of the constraint space (i.e., D(πθ, πj) \\u003d δ), which is undesirable according to our original goal. However, this issue can be often alleviated by properly setting the novelty threshold δ. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"9f842d8b-1ccf-42d7-b3f2-b2988d49559c\"]],[null,null,0.6628766568710132,[[null,22458,24108]],[[[22458,22646,[[[22458,22646,[\"In addition to the aforementioned RSPO algorithm, we also introduce two implementation enhance-ments for better empirical performances, especially in some performance-sensitive scenarios. \"]]]]],[22646,22646,[]],[22646,22647,[[[22646,22647,[\" \"]]]]],[22647,24108,[[[22647,24108,[\"Automatic threshold selection. We provide an empirical way of adjusting δ. In some environments, δ is sensitive to each reference policy. Instead of tuning δ for each reference policy, we choose its corresponding threshold by δj \\u003d α · D(πrnd, πj), where πrnd is a fully random policy and α is a task-specific hyperparameter. We remark that α is a constant parameter across training iterations and is much easier to choose than manually tuning δ, which requires subtle variation throughout multiple training iterations. We use automatic threshold selection by default. Detailed values of α and the methodology of tuning α can be found in App. D.1 and App. B.3 respectively. Smoothed-switching for intrinsic rewards. Intrinsic rewards have multiple switching indicators, i.e., φ1, . . . , φk−1. Moreover, for different trajectories, different subsets of indicators will be turned on and off, which may result in a varying scale of intrinsic rewards and hurt training stability. Therefore, in some constraint-sensitive cases, we propose a smoothed switching mechanism which could further improve practical performance. Specifically, we maintain a running average φ̃j over all the sampled trajectories for each indicator φj(τ), and use these smoothed indicators to compute intrinsic rewards defined in Eq. (9). Smoothed-switching empirically improves training stability when a large number of reference policies exist, such as in stag-hunt games (see Section 4.2). \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"333e0760-3fac-4056-8ac5-0890fb3bea8e\"]],[null,null,0.7099275325715608,[[null,58846,60324]],[[[58846,58871,[[[58846,58871,[\"B.3 SENSITIVITY ANALYSIS \"]]]]],[58871,59058,[[[58871,59058,[\"We have performed a sensitivity analysis over α, λintB and λintR since they are the critical to the performance RSPO. The default values used in our experiments can be found in Table 12. \"]]]]],[59058,60324,[[[59058,60324,[\"α is the most important hyperparameter in RSPO because it determines what trajectories in a batch to be accepted. We focus on the data efficiency, i.e., the proportion of accepted trajectories in a batch. In the sensitivity analysis, we run the second iteration of RSPO in Humanoid with α \\u003d 0.5, 1.0, 1.5, 2 respectively and compute the population diversity score of the 2 resulting policies. The result is shown in Fig. 12 and the left part of Table 9. The result accords with our heuristic to adjust α: with a small α (α \\u003d 0.5), RSPO may accept all the trajectories at the beginning and lead to a similar policy after convergence, which is no better than the PG baseline; with a large α (α \\u003d 1.5 and α \\u003d 2), RSPO may reject too many trajectories at the early stage of training and spend quite a lot of time on exploration, which sacrifices training time for the gain in the diversity score. In practice, we suggest starting with α \\u003d 1 and adjusting it such that the acceptance rate can drop at the start of training and then quickly and smoothly converge to 1, as shown in Fig. 12 (α \\u003d 1) and Fig. 5. α should be decreased if too much data is rejected at the beginning of training and increased if data efficiency always stays high in the early stage of training. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"84486d82-b228-438c-8314-895893581bfc\"]],[null,null,0.638765286182543,[[null,17046,17934]],[[[17046,17065,[[[17046,17065,[\"Jfilter(θ) \\u003d Eτ∼πθ \"]]]]],[17065,17072,[[[17065,17072,[\"[ φ(τ) \"]]]]],[17072,17076,[[[17072,17076,[\"∑ t \"]]]]],[17076,17081,[[[17076,17081,[\"γtrt \"]]]]],[17081,17099,[[[17081,17099,[\"] , where φ(τ) :\\u003d \"]]]]],[17099,17108,[[[17099,17108,[\"k−1∏ j\\u003d1 \"]]]]],[17108,17131,[[[17108,17131,[\"I[NLL(τ ;πj) ≥ δ]. (5) \"]]]]],[17131,17478,[[[17131,17478,[\"We call the objective in Eq. (5) a filtering objective. We show in App. G that solving Eq. (5) is equivalent to solving Eq. (2) with an even stronger diversity constraint. In addition, we also remark that trajectory filtering shares a conceptually similar motivation with the clipping term in Proximal Policy Optimization (Schulman et al., 2017). \"]]]]],[17478,17526,[[[17478,17526,[\"3.4 INTRINSIC REWARDS FOR DIVERSITY EXPLORATION \"]]]]],[17526,17934,[[[17526,17934,[\"The main issue in Eq. (5) is that trajectory filtering may reject a significant number of trajectories, especially in the early stage of policy learning since the policy is typically initialized to the a random policy. Hence, it is often the case that most of the data in a batch are abandoned, which leads to a severe wasting of samples and may even break learning due to the lack of feasible trajectories. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"c7184c02-054c-45be-85a3-f870fe4aca8b\"]],[null,null,0.6599198382183982,[[null,57350,57853]],[[[57350,57416,[[[57350,57416,[\"Table 8: Population Diversity on the hard map 2c_vs_64zg in SMAC. \"]]]]],[57416,57469,[[[57416,57469,[\"Algorithm # Distinct Strategies Population Diversity \"]]]]],[57469,57495,[[[57469,57495,[\"PG 1 0.981 PBT-CE 2 1.000 \"]]]]],[57495,57507,[[[57495,57507,[\"DvD 3 1.000 \"]]]]],[57507,57520,[[[57507,57520,[\"RSPO 4 1.000 \"]]]]],[57520,57520,[]],[57520,57521,[[[57520,57521,[\" \"]]]]],[57521,57552,[[[57521,57552,[\"0 500 1000 1500 2000 2500 3000 \"]]]]],[57552,57574,[[[57552,57574,[\"Training Update Steps \"]]],[null,4]]],[57574,57578,[[[57574,57578,[\"0.0 \"]]]]],[57578,57582,[[[57578,57582,[\"0.2 \"]]]]],[57582,57586,[[[57582,57586,[\"0.4 \"]]]]],[57586,57590,[[[57586,57590,[\"0.6 \"]]]]],[57590,57594,[[[57590,57594,[\"0.8 \"]]]]],[57594,57598,[[[57594,57598,[\"1.0 \"]]]]],[57598,57603,[[[57598,57603,[\"D at \"]]],[null,4]]],[57603,57607,[[[57603,57607,[\"a E \"]]],[null,4]]],[57607,57614,[[[57607,57614,[\"ffi ci \"]]],[null,4]]],[57614,57620,[[[57614,57620,[\"en cy \"]]],[null,4]]],[57620,57656,[[[57620,57656,[\"alpha\\u003d0.5 alpha\\u003d1 alpha\\u003d1.5 alpha\\u003d2 \"]]],[null,6]]],[57656,57713,[[[57656,57713,[\"Figure 12: Data efficiency with different α in Humanoid. \"]]]]],[57713,57752,[[[57713,57752,[\"0 250 500 750 1000 1250 1500 1750 2000 \"]]]]],[57752,57774,[[[57752,57774,[\"Training Update Steps \"]]],[null,4]]],[57774,57778,[[[57774,57778,[\"0.0 \"]]]]],[57778,57782,[[[57778,57782,[\"0.2 \"]]]]],[57782,57786,[[[57782,57786,[\"0.4 \"]]]]],[57786,57790,[[[57786,57790,[\"0.6 \"]]]]],[57790,57794,[[[57790,57794,[\"0.8 \"]]]]],[57794,57798,[[[57794,57798,[\"1.0 \"]]]]],[57798,57803,[[[57798,57803,[\"W in \"]]],[null,4]]],[57803,57809,[[[57803,57809,[\"ni ng \"]]],[null,4]]],[57809,57816,[[[57809,57816,[\" ra te \"]]],[null,4]]],[57816,57853,[[[57816,57853,[\"lambdaR\\u003d0.2 lambdaR\\u003d0.05 lambdaR\\u003d0.0 \"]]],[null,6]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"d471fc1c-3585-42e2-86ea-48004dbd02c7\"]],[null,null,0.6563970223842246,[[null,57853,58846]],[[[57853,57929,[[[57853,57929,[\"Figure 13: Learning curve with different λintR on the 2m_vs_1z map in SMAC. \"]]]]],[57929,57979,[[[57929,57979,[\"α Population Diversity λintB Population Diversity \"]]]]],[57979,58060,[[[57979,58060,[\"0.5 0.604 0.5 0.640 1.0 0.676 1.0 0.621 1.5 0.687 5.0 0.697 2.0 0.748 10.0 0.697 \"]]]]],[58060,58324,[[[58060,58324,[\"Table 9: Population Diversity scores of the first 2 policies with different hyperparameters in Humanoid. We have scaled the denominator of the RBF kernel in the Population Diversity matrix by a factor 10, such that the difference can be demonstrated more clearly. \"]]]]],[58324,58846,[[[58324,58846,[\"To the best of our knowledge, a commonly accepted policy diversity metric for complex MARL games remains an open question in the existing literature. In our practice, rendering and visualizing the evaluation trajectories remains the best approach to distinguish different learned strategies. We emphasize that qualitatively, we are so far the first paper that ever reports such a visually diverse collection of winning strategies on a hard map in SMAC. Please check our website for policy visualizations (see Appendix A). \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"f486c02d-1bb5-40ca-a11c-f579f362034b\"]],[null,null,0.6810832532897884,[[null,32492,34064]],[[[32492,32659,[[[32492,32659,[\"We perform ablation studies on RSPO by turning off reward switching (No Switch) or intrinsic reward (No rint) or only using the behavior-driven intrinsic reward (rint \"]]]]],[32659,34064,[[[32659,34064,[\"B only), and evaluate the performances of many baseline methods, including vanilla PG with restarts (PG), DIPG, RND, a popular multi-agent exploration method MAVEN (Mahajan et al., 2019) and reward-randomized policy gradient (RPG) (Tang et al., 2021). We summarize the categories of discovered strategies by all these baselines in Table 1. Apple denotes the non-cooperative apple-eating NE; Chase denotes the optimal NE where both agents actively chase the monster; Corner and Edge denote the sub-optimal cooperative NE where both agents passively wait for the monster at a corner or an edge respectively. Regarding the baselines, PG, DIPG, and RND never discover any strategy beyond the non-cooperative Apple NE. For RPG, even using the domain knowledge to change the reward structure of the game, it never discovers the Edge NE. Regarding the RSPO variants, both reward switching and intrinsic rewards are necessary. Fig. 5 shows that when the intrinsic reward is turned off, the proportion of accepted trajectories per batch stays low throughout training. This implies that the learning policy failed to escape the infeasible subspace. Besides, as shown in Table 1, using behavior-driven exploration alone fails to discover the optimal NE, which suggests the necessity of reward-driven exploration to maximize the divergence of both states and actions in problems with massive equivalent local optima. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"f5ad3c6a-6ec8-40af-9922-934c336e258f\"]]],[[[null,0,147],[0]],[[null,297,510],[1,2]],[[null,563,666],[3]],[[null,897,909],[4]],[[null,927,1162],[4]],[[null,1180,1439],[4,5]],[[null,1440,1584],[4,6,7]],[[null,1584,1784],[0]],[[null,1784,1899],[8]]],[[\"What is RSPO's core contribution?\",\"What is the Monster-Hunt game?\",\"What are two SMAC maps?\"]]]"]]
36811
[["wrb.fr",null,"[[\"The provided sources do not directly detail the impact of adjusting $\\\\delta$ on the specific results presented in Table 1 for the Monster-Hunt game [1].\\n\\nHowever, the sources do explain the general role of $\\\\delta$ and its associated hyperparameter $\\\\alpha$:\\n*   $\\\\delta$ is defined as the **novelty threshold** in the iterative constrained optimization problem, where a policy $\\\\pi_k$ must be sufficiently distinct from previously discovered policies $\\\\pi_1, \\\\dots, \\\\pi_{k-1}$ by a distance greater than or equal to $\\\\delta$ [2, 3].\\n*   The paper utilizes an **automatic threshold selection** where $\\\\delta_j \\u003d \\\\alpha \\\\cdot D(\\\\pi_{rnd}, \\\\pi_j)$, with $\\\\alpha$ being a task-specific hyperparameter [4]. This means $\\\\alpha$ directly controls the effective value of $\\\\delta$.\\n*   The sensitivity analysis indicates that $\\\\alpha$ is the **most important hyperparameter** in RSPO because it determines **which trajectories in a batch are accepted** for training [5].\\n*   A **small $\\\\alpha$** (and thus $\\\\delta$) might cause RSPO to accept too many trajectories initially, potentially leading to a final policy that is similar to previously discovered ones, offering no better performance than a vanilla policy gradient baseline [5].\\n*   A **large $\\\\alpha$** (and thus $\\\\delta$) might cause RSPO to reject too many trajectories in the early stages, leading to a severe wasting of samples and potentially breaking learning due to a lack of feasible trajectories, which sacrifices training time for the gain in diversity [5, 6].\\n*   While the general impact of $\\\\alpha$ on data efficiency and population diversity is discussed and evaluated for other environments like Humanoid [5, 7, 8], the paper does not specify how tuning $\\\\delta$ (or $\\\\alpha$) would alter the specific categories of strategies (Apple, Corner, Edge, Chase) discovered and listed in Table 1 for the Monster-Hunt game [1]. The discussion pertaining to Table 1 primarily focuses on the necessity of reward switching and intrinsic rewards [9].\",null,[\"da135b09-5e1e-4967-953c-f09c5905f5b1\",\"75e2ca76-929e-4d1a-9d23-943f2151aece\",155088721],null,[[[[0,148,[[[0,148,[\"The provided sources do not directly detail the impact of adjusting $\\\\delta$ on the specific results presented in Table 1 for the Monster-Hunt game.\"]]]]],[148,252,[[[148,252,[\"However, the sources do explain the general role of $\\\\delta$ and its associated hyperparameter $\\\\alpha$:\"]]]]],[252,511,[[[252,279,[\"$\\\\delta$ is defined as the \"]],[279,296,[\"novelty threshold\",[true]]],[296,511,[\" in the iterative constrained optimization problem, where a policy $\\\\pi_k$ must be sufficiently distinct from previously discovered policies $\\\\pi_1, \\\\dots, \\\\pi_{k-1}$ by a distance greater than or equal to $\\\\delta$.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[511,738,[[[511,533,[\"The paper utilizes an \"]],[533,562,[\"automatic threshold selection\",[true]]],[562,738,[\" where $\\\\delta_j \\u003d \\\\alpha \\\\cdot D(\\\\pi_{rnd}, \\\\pi_j)$, with $\\\\alpha$ being a task-specific hyperparameter. This means $\\\\alpha$ directly controls the effective value of $\\\\delta$.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[738,910,[[[738,794,[\"The sensitivity analysis indicates that $\\\\alpha$ is the \"]],[794,823,[\"most important hyperparameter\",[true]]],[823,854,[\" in RSPO because it determines \"]],[854,896,[\"which trajectories in a batch are accepted\",[true]]],[896,910,[\" for training.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]],[910,1163,[[[910,912,[\"A \"]],[912,926,[\"small $\\\\alpha$\",[true]]],[926,1163,[\" (and thus $\\\\delta$) might cause RSPO to accept too many trajectories initially, potentially leading to a final policy that is similar to previously discovered ones, offering no better performance than a vanilla policy gradient baseline.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,3]]],[1163,1440,[[[1163,1165,[\"A \"]],[1165,1179,[\"large $\\\\alpha$\",[true]]],[1179,1440,[\" (and thus $\\\\delta$) might cause RSPO to reject too many trajectories in the early stages, leading to a severe wasting of samples and potentially breaking learning due to a lack of feasible trajectories, which sacrifices training time for the gain in diversity.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,5,4]]],[1440,1900,[[[1440,1900,[\"While the general impact of $\\\\alpha$ on data efficiency and population diversity is discussed and evaluated for other environments like Humanoid, the paper does not specify how tuning $\\\\delta$ (or $\\\\alpha$) would alter the specific categories of strategies (Apple, Corner, Edge, Chase) discovered and listed in Table 1 for the Monster-Hunt game. The discussion pertaining to Table 1 primarily focuses on the necessity of reward switching and intrinsic rewards.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,6,5]]]],[[[\"62dd6a98-60b1-41e3-ba6e-0c1508cbbf92\"],[null,0,147]],[[\"12b949ab-782f-4e87-a05b-7999850161a4\"],[null,297,510]],[[\"9f842d8b-1ccf-42d7-b3f2-b2988d49559c\"],[null,297,510]],[[\"333e0760-3fac-4056-8ac5-0890fb3bea8e\"],[null,563,666]],[[\"84486d82-b228-438c-8314-895893581bfc\"],[null,897,909]],[[\"84486d82-b228-438c-8314-895893581bfc\"],[null,927,1162]],[[\"84486d82-b228-438c-8314-895893581bfc\"],[null,1180,1439]],[[\"c7184c02-054c-45be-85a3-f870fe4aca8b\"],[null,1180,1439]],[[\"84486d82-b228-438c-8314-895893581bfc\"],[null,1440,1584]],[[\"d471fc1c-3585-42e2-86ea-48004dbd02c7\"],[null,1440,1584]],[[\"f486c02d-1bb5-40ca-a11c-f579f362034b\"],[null,1440,1584]],[[\"62dd6a98-60b1-41e3-ba6e-0c1508cbbf92\"],[null,1584,1784]],[[\"f5ad3c6a-6ec8-40af-9922-934c336e258f\"],[null,1784,1899]]]],null,null,[[[\"62dd6a98-60b1-41e3-ba6e-0c1508cbbf92\"],[null,null,0.6243664204182673,[[null,30725,31258]],[[[30725,31028,[[[30725,31028,[\"We adopt both behavior-driven and reward-driven intrinsic rewards in RSPO to tackle Monster-Hunt. Fig. 4 illustrates all the discovered strategies by RSPO over 20 iterations, which covers a wide range of human-interpretable strategies, including the non-cooperative apple-eating strategy as well as the \"]]]]],[31028,31028,[]],[31028,31029,[[[31028,31029,[\" \"]]]]],[31029,31121,[[[31029,31121,[\"Table 1: Types of strategies discovered by each methods in Monster-Hunt over 20 iterations. \"]]]]],[31121,31145,[[[31121,31145,[\"Apple Corner Edge Chase \"]]]]],[31145,31154,[[[31145,31154,[\"Ablation \"]]]]],[31154,31202,[[[31154,31202,[\"RSPO X X X X - No switch X X - No rint X - rint \"]]]]],[31202,31215,[[[31202,31215,[\"B only X X X \"]]]]],[31215,31258,[[[31215,31258,[\"Baseline RPG X X X MAVEN X X PG/DIPG/RND X \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"62dd6a98-60b1-41e3-ba6e-0c1508cbbf92\"]]],[[\"12b949ab-782f-4e87-a05b-7999850161a4\"],[null,null,0.6412522816745847,[[null,12609,13127]],[[[12609,12865,[[[12609,12865,[\"Rather than learning a single solution for J(θ), we aim to discover a diverse set of M policies, i.e, {πθk |1 ≤ k ≤ M}, such that all of these polices are locally optimized under J(θ) and mutually distinct w.r.t. some distance measure D(πθi , πθj ), i.e., \"]]]]],[12865,12872,[[[12865,12872,[\"max θk \"]]]]],[12872,12938,[[[12872,12938,[\"J(θk) ∀1 ≤ k ≤M, subject to D(πθi , πθj ) ≥ δ, ∀1 ≤ i \\u003c j ≤M. (1) \"]]]]],[12938,13127,[[[12938,13127,[\"Here D(·, ·) measures how different two policies are and δ is the novelty threshold. For conciseness, in the following content, we omit θ and use πk to denote the policy with parameter θk. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"12b949ab-782f-4e87-a05b-7999850161a4\"]]],[[\"9f842d8b-1ccf-42d7-b3f2-b2988d49559c\"],[null,null,0.6301980528025974,[[null,13785,14650]],[[[13785,13848,[[[13785,13848,[\"θk \\u003d arg max θ J(θ), subject to D(πθ, πj) ≥ δ, ∀1 ≤ j \\u003c k. (2) \"]]]]],[13848,13848,[]],[13848,13849,[[[13848,13849,[\" \"]]]]],[13849,14650,[[[13849,14650,[\"Eq. (2) reduces the population-based objective to a standard constrained optimization problem for a single policy, which is much easier to solve. Such an iterative procedure does not require a large population size M as is typically necessary in population-based methods. And, in practice, only a few iterations could result in a sufficiently diverse collection of policies. We remark that, in theory, directly solving the constraint problem in Eq. (2) may lead to a solution that is not a local optimum w.r.t. the unconstrained objective J(θ). It is because a solution in Eq. (2) can be located on the boundary of the constraint space (i.e., D(πθ, πj) \\u003d δ), which is undesirable according to our original goal. However, this issue can be often alleviated by properly setting the novelty threshold δ. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"9f842d8b-1ccf-42d7-b3f2-b2988d49559c\"]]],[[\"333e0760-3fac-4056-8ac5-0890fb3bea8e\"],[null,null,0.6628766568710132,[[null,22458,24108]],[[[22458,22646,[[[22458,22646,[\"In addition to the aforementioned RSPO algorithm, we also introduce two implementation enhance-ments for better empirical performances, especially in some performance-sensitive scenarios. \"]]]]],[22646,22646,[]],[22646,22647,[[[22646,22647,[\" \"]]]]],[22647,24108,[[[22647,24108,[\"Automatic threshold selection. We provide an empirical way of adjusting δ. In some environments, δ is sensitive to each reference policy. Instead of tuning δ for each reference policy, we choose its corresponding threshold by δj \\u003d α · D(πrnd, πj), where πrnd is a fully random policy and α is a task-specific hyperparameter. We remark that α is a constant parameter across training iterations and is much easier to choose than manually tuning δ, which requires subtle variation throughout multiple training iterations. We use automatic threshold selection by default. Detailed values of α and the methodology of tuning α can be found in App. D.1 and App. B.3 respectively. Smoothed-switching for intrinsic rewards. Intrinsic rewards have multiple switching indicators, i.e., φ1, . . . , φk−1. Moreover, for different trajectories, different subsets of indicators will be turned on and off, which may result in a varying scale of intrinsic rewards and hurt training stability. Therefore, in some constraint-sensitive cases, we propose a smoothed switching mechanism which could further improve practical performance. Specifically, we maintain a running average φ̃j over all the sampled trajectories for each indicator φj(τ), and use these smoothed indicators to compute intrinsic rewards defined in Eq. (9). Smoothed-switching empirically improves training stability when a large number of reference policies exist, such as in stag-hunt games (see Section 4.2). \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"333e0760-3fac-4056-8ac5-0890fb3bea8e\"]]],[[\"84486d82-b228-438c-8314-895893581bfc\"],[null,null,0.7099275325715608,[[null,58846,60324]],[[[58846,58871,[[[58846,58871,[\"B.3 SENSITIVITY ANALYSIS \"]]]]],[58871,59058,[[[58871,59058,[\"We have performed a sensitivity analysis over α, λintB and λintR since they are the critical to the performance RSPO. The default values used in our experiments can be found in Table 12. \"]]]]],[59058,60324,[[[59058,60324,[\"α is the most important hyperparameter in RSPO because it determines what trajectories in a batch to be accepted. We focus on the data efficiency, i.e., the proportion of accepted trajectories in a batch. In the sensitivity analysis, we run the second iteration of RSPO in Humanoid with α \\u003d 0.5, 1.0, 1.5, 2 respectively and compute the population diversity score of the 2 resulting policies. The result is shown in Fig. 12 and the left part of Table 9. The result accords with our heuristic to adjust α: with a small α (α \\u003d 0.5), RSPO may accept all the trajectories at the beginning and lead to a similar policy after convergence, which is no better than the PG baseline; with a large α (α \\u003d 1.5 and α \\u003d 2), RSPO may reject too many trajectories at the early stage of training and spend quite a lot of time on exploration, which sacrifices training time for the gain in the diversity score. In practice, we suggest starting with α \\u003d 1 and adjusting it such that the acceptance rate can drop at the start of training and then quickly and smoothly converge to 1, as shown in Fig. 12 (α \\u003d 1) and Fig. 5. α should be decreased if too much data is rejected at the beginning of training and increased if data efficiency always stays high in the early stage of training. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"84486d82-b228-438c-8314-895893581bfc\"]]],[[\"c7184c02-054c-45be-85a3-f870fe4aca8b\"],[null,null,0.638765286182543,[[null,17046,17934]],[[[17046,17065,[[[17046,17065,[\"Jfilter(θ) \\u003d Eτ∼πθ \"]]]]],[17065,17072,[[[17065,17072,[\"[ φ(τ) \"]]]]],[17072,17076,[[[17072,17076,[\"∑ t \"]]]]],[17076,17081,[[[17076,17081,[\"γtrt \"]]]]],[17081,17099,[[[17081,17099,[\"] , where φ(τ) :\\u003d \"]]]]],[17099,17108,[[[17099,17108,[\"k−1∏ j\\u003d1 \"]]]]],[17108,17131,[[[17108,17131,[\"I[NLL(τ ;πj) ≥ δ]. (5) \"]]]]],[17131,17478,[[[17131,17478,[\"We call the objective in Eq. (5) a filtering objective. We show in App. G that solving Eq. (5) is equivalent to solving Eq. (2) with an even stronger diversity constraint. In addition, we also remark that trajectory filtering shares a conceptually similar motivation with the clipping term in Proximal Policy Optimization (Schulman et al., 2017). \"]]]]],[17478,17526,[[[17478,17526,[\"3.4 INTRINSIC REWARDS FOR DIVERSITY EXPLORATION \"]]]]],[17526,17934,[[[17526,17934,[\"The main issue in Eq. (5) is that trajectory filtering may reject a significant number of trajectories, especially in the early stage of policy learning since the policy is typically initialized to the a random policy. Hence, it is often the case that most of the data in a batch are abandoned, which leads to a severe wasting of samples and may even break learning due to the lack of feasible trajectories. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"c7184c02-054c-45be-85a3-f870fe4aca8b\"]]],[[\"d471fc1c-3585-42e2-86ea-48004dbd02c7\"],[null,null,0.6599198382183982,[[null,57350,57853]],[[[57350,57416,[[[57350,57416,[\"Table 8: Population Diversity on the hard map 2c_vs_64zg in SMAC. \"]]]]],[57416,57469,[[[57416,57469,[\"Algorithm # Distinct Strategies Population Diversity \"]]]]],[57469,57495,[[[57469,57495,[\"PG 1 0.981 PBT-CE 2 1.000 \"]]]]],[57495,57507,[[[57495,57507,[\"DvD 3 1.000 \"]]]]],[57507,57520,[[[57507,57520,[\"RSPO 4 1.000 \"]]]]],[57520,57520,[]],[57520,57521,[[[57520,57521,[\" \"]]]]],[57521,57552,[[[57521,57552,[\"0 500 1000 1500 2000 2500 3000 \"]]]]],[57552,57574,[[[57552,57574,[\"Training Update Steps \"]]],[null,4]]],[57574,57578,[[[57574,57578,[\"0.0 \"]]]]],[57578,57582,[[[57578,57582,[\"0.2 \"]]]]],[57582,57586,[[[57582,57586,[\"0.4 \"]]]]],[57586,57590,[[[57586,57590,[\"0.6 \"]]]]],[57590,57594,[[[57590,57594,[\"0.8 \"]]]]],[57594,57598,[[[57594,57598,[\"1.0 \"]]]]],[57598,57603,[[[57598,57603,[\"D at \"]]],[null,4]]],[57603,57607,[[[57603,57607,[\"a E \"]]],[null,4]]],[57607,57614,[[[57607,57614,[\"ffi ci \"]]],[null,4]]],[57614,57620,[[[57614,57620,[\"en cy \"]]],[null,4]]],[57620,57656,[[[57620,57656,[\"alpha\\u003d0.5 alpha\\u003d1 alpha\\u003d1.5 alpha\\u003d2 \"]]],[null,6]]],[57656,57713,[[[57656,57713,[\"Figure 12: Data efficiency with different α in Humanoid. \"]]]]],[57713,57752,[[[57713,57752,[\"0 250 500 750 1000 1250 1500 1750 2000 \"]]]]],[57752,57774,[[[57752,57774,[\"Training Update Steps \"]]],[null,4]]],[57774,57778,[[[57774,57778,[\"0.0 \"]]]]],[57778,57782,[[[57778,57782,[\"0.2 \"]]]]],[57782,57786,[[[57782,57786,[\"0.4 \"]]]]],[57786,57790,[[[57786,57790,[\"0.6 \"]]]]],[57790,57794,[[[57790,57794,[\"0.8 \"]]]]],[57794,57798,[[[57794,57798,[\"1.0 \"]]]]],[57798,57803,[[[57798,57803,[\"W in \"]]],[null,4]]],[57803,57809,[[[57803,57809,[\"ni ng \"]]],[null,4]]],[57809,57816,[[[57809,57816,[\" ra te \"]]],[null,4]]],[57816,57853,[[[57816,57853,[\"lambdaR\\u003d0.2 lambdaR\\u003d0.05 lambdaR\\u003d0.0 \"]]],[null,6]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"d471fc1c-3585-42e2-86ea-48004dbd02c7\"]]],[[\"f486c02d-1bb5-40ca-a11c-f579f362034b\"],[null,null,0.6563970223842246,[[null,57853,58846]],[[[57853,57929,[[[57853,57929,[\"Figure 13: Learning curve with different λintR on the 2m_vs_1z map in SMAC. \"]]]]],[57929,57979,[[[57929,57979,[\"α Population Diversity λintB Population Diversity \"]]]]],[57979,58060,[[[57979,58060,[\"0.5 0.604 0.5 0.640 1.0 0.676 1.0 0.621 1.5 0.687 5.0 0.697 2.0 0.748 10.0 0.697 \"]]]]],[58060,58324,[[[58060,58324,[\"Table 9: Population Diversity scores of the first 2 policies with different hyperparameters in Humanoid. We have scaled the denominator of the RBF kernel in the Population Diversity matrix by a factor 10, such that the difference can be demonstrated more clearly. \"]]]]],[58324,58846,[[[58324,58846,[\"To the best of our knowledge, a commonly accepted policy diversity metric for complex MARL games remains an open question in the existing literature. In our practice, rendering and visualizing the evaluation trajectories remains the best approach to distinguish different learned strategies. We emphasize that qualitatively, we are so far the first paper that ever reports such a visually diverse collection of winning strategies on a hard map in SMAC. Please check our website for policy visualizations (see Appendix A). \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"f486c02d-1bb5-40ca-a11c-f579f362034b\"]]],[[\"f5ad3c6a-6ec8-40af-9922-934c336e258f\"],[null,null,0.6810832532897884,[[null,32492,34064]],[[[32492,32659,[[[32492,32659,[\"We perform ablation studies on RSPO by turning off reward switching (No Switch) or intrinsic reward (No rint) or only using the behavior-driven intrinsic reward (rint \"]]]]],[32659,34064,[[[32659,34064,[\"B only), and evaluate the performances of many baseline methods, including vanilla PG with restarts (PG), DIPG, RND, a popular multi-agent exploration method MAVEN (Mahajan et al., 2019) and reward-randomized policy gradient (RPG) (Tang et al., 2021). We summarize the categories of discovered strategies by all these baselines in Table 1. Apple denotes the non-cooperative apple-eating NE; Chase denotes the optimal NE where both agents actively chase the monster; Corner and Edge denote the sub-optimal cooperative NE where both agents passively wait for the monster at a corner or an edge respectively. Regarding the baselines, PG, DIPG, and RND never discover any strategy beyond the non-cooperative Apple NE. For RPG, even using the domain knowledge to change the reward structure of the game, it never discovers the Edge NE. Regarding the RSPO variants, both reward switching and intrinsic rewards are necessary. Fig. 5 shows that when the intrinsic reward is turned off, the proportion of accepted trajectories per batch stays low throughout training. This implies that the learning policy failed to escape the infeasible subspace. Besides, as shown in Table 1, using behavior-driven exploration alone fails to discover the optimal NE, which suggests the necessity of reward-driven exploration to maximize the divergence of both states and actions in problems with massive equivalent local optima. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"f5ad3c6a-6ec8-40af-9922-934c336e258f\"]]]]]],[[null,null,0.6243664204182673,[[null,30725,31258]],[[[30725,31028,[[[30725,31028,[\"We adopt both behavior-driven and reward-driven intrinsic rewards in RSPO to tackle Monster-Hunt. Fig. 4 illustrates all the discovered strategies by RSPO over 20 iterations, which covers a wide range of human-interpretable strategies, including the non-cooperative apple-eating strategy as well as the \"]]]]],[31028,31028,[]],[31028,31029,[[[31028,31029,[\" \"]]]]],[31029,31121,[[[31029,31121,[\"Table 1: Types of strategies discovered by each methods in Monster-Hunt over 20 iterations. \"]]]]],[31121,31145,[[[31121,31145,[\"Apple Corner Edge Chase \"]]]]],[31145,31154,[[[31145,31154,[\"Ablation \"]]]]],[31154,31202,[[[31154,31202,[\"RSPO X X X X - No switch X X - No rint X - rint \"]]]]],[31202,31215,[[[31202,31215,[\"B only X X X \"]]]]],[31215,31258,[[[31215,31258,[\"Baseline RPG X X X MAVEN X X PG/DIPG/RND X \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"62dd6a98-60b1-41e3-ba6e-0c1508cbbf92\"]],[null,null,0.6412522816745847,[[null,12609,13127]],[[[12609,12865,[[[12609,12865,[\"Rather than learning a single solution for J(θ), we aim to discover a diverse set of M policies, i.e, {πθk |1 ≤ k ≤ M}, such that all of these polices are locally optimized under J(θ) and mutually distinct w.r.t. some distance measure D(πθi , πθj ), i.e., \"]]]]],[12865,12872,[[[12865,12872,[\"max θk \"]]]]],[12872,12938,[[[12872,12938,[\"J(θk) ∀1 ≤ k ≤M, subject to D(πθi , πθj ) ≥ δ, ∀1 ≤ i \\u003c j ≤M. (1) \"]]]]],[12938,13127,[[[12938,13127,[\"Here D(·, ·) measures how different two policies are and δ is the novelty threshold. For conciseness, in the following content, we omit θ and use πk to denote the policy with parameter θk. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"12b949ab-782f-4e87-a05b-7999850161a4\"]],[null,null,0.6301980528025974,[[null,13785,14650]],[[[13785,13848,[[[13785,13848,[\"θk \\u003d arg max θ J(θ), subject to D(πθ, πj) ≥ δ, ∀1 ≤ j \\u003c k. (2) \"]]]]],[13848,13848,[]],[13848,13849,[[[13848,13849,[\" \"]]]]],[13849,14650,[[[13849,14650,[\"Eq. (2) reduces the population-based objective to a standard constrained optimization problem for a single policy, which is much easier to solve. Such an iterative procedure does not require a large population size M as is typically necessary in population-based methods. And, in practice, only a few iterations could result in a sufficiently diverse collection of policies. We remark that, in theory, directly solving the constraint problem in Eq. (2) may lead to a solution that is not a local optimum w.r.t. the unconstrained objective J(θ). It is because a solution in Eq. (2) can be located on the boundary of the constraint space (i.e., D(πθ, πj) \\u003d δ), which is undesirable according to our original goal. However, this issue can be often alleviated by properly setting the novelty threshold δ. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"9f842d8b-1ccf-42d7-b3f2-b2988d49559c\"]],[null,null,0.6628766568710132,[[null,22458,24108]],[[[22458,22646,[[[22458,22646,[\"In addition to the aforementioned RSPO algorithm, we also introduce two implementation enhance-ments for better empirical performances, especially in some performance-sensitive scenarios. \"]]]]],[22646,22646,[]],[22646,22647,[[[22646,22647,[\" \"]]]]],[22647,24108,[[[22647,24108,[\"Automatic threshold selection. We provide an empirical way of adjusting δ. In some environments, δ is sensitive to each reference policy. Instead of tuning δ for each reference policy, we choose its corresponding threshold by δj \\u003d α · D(πrnd, πj), where πrnd is a fully random policy and α is a task-specific hyperparameter. We remark that α is a constant parameter across training iterations and is much easier to choose than manually tuning δ, which requires subtle variation throughout multiple training iterations. We use automatic threshold selection by default. Detailed values of α and the methodology of tuning α can be found in App. D.1 and App. B.3 respectively. Smoothed-switching for intrinsic rewards. Intrinsic rewards have multiple switching indicators, i.e., φ1, . . . , φk−1. Moreover, for different trajectories, different subsets of indicators will be turned on and off, which may result in a varying scale of intrinsic rewards and hurt training stability. Therefore, in some constraint-sensitive cases, we propose a smoothed switching mechanism which could further improve practical performance. Specifically, we maintain a running average φ̃j over all the sampled trajectories for each indicator φj(τ), and use these smoothed indicators to compute intrinsic rewards defined in Eq. (9). Smoothed-switching empirically improves training stability when a large number of reference policies exist, such as in stag-hunt games (see Section 4.2). \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"333e0760-3fac-4056-8ac5-0890fb3bea8e\"]],[null,null,0.7099275325715608,[[null,58846,60324]],[[[58846,58871,[[[58846,58871,[\"B.3 SENSITIVITY ANALYSIS \"]]]]],[58871,59058,[[[58871,59058,[\"We have performed a sensitivity analysis over α, λintB and λintR since they are the critical to the performance RSPO. The default values used in our experiments can be found in Table 12. \"]]]]],[59058,60324,[[[59058,60324,[\"α is the most important hyperparameter in RSPO because it determines what trajectories in a batch to be accepted. We focus on the data efficiency, i.e., the proportion of accepted trajectories in a batch. In the sensitivity analysis, we run the second iteration of RSPO in Humanoid with α \\u003d 0.5, 1.0, 1.5, 2 respectively and compute the population diversity score of the 2 resulting policies. The result is shown in Fig. 12 and the left part of Table 9. The result accords with our heuristic to adjust α: with a small α (α \\u003d 0.5), RSPO may accept all the trajectories at the beginning and lead to a similar policy after convergence, which is no better than the PG baseline; with a large α (α \\u003d 1.5 and α \\u003d 2), RSPO may reject too many trajectories at the early stage of training and spend quite a lot of time on exploration, which sacrifices training time for the gain in the diversity score. In practice, we suggest starting with α \\u003d 1 and adjusting it such that the acceptance rate can drop at the start of training and then quickly and smoothly converge to 1, as shown in Fig. 12 (α \\u003d 1) and Fig. 5. α should be decreased if too much data is rejected at the beginning of training and increased if data efficiency always stays high in the early stage of training. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"84486d82-b228-438c-8314-895893581bfc\"]],[null,null,0.638765286182543,[[null,17046,17934]],[[[17046,17065,[[[17046,17065,[\"Jfilter(θ) \\u003d Eτ∼πθ \"]]]]],[17065,17072,[[[17065,17072,[\"[ φ(τ) \"]]]]],[17072,17076,[[[17072,17076,[\"∑ t \"]]]]],[17076,17081,[[[17076,17081,[\"γtrt \"]]]]],[17081,17099,[[[17081,17099,[\"] , where φ(τ) :\\u003d \"]]]]],[17099,17108,[[[17099,17108,[\"k−1∏ j\\u003d1 \"]]]]],[17108,17131,[[[17108,17131,[\"I[NLL(τ ;πj) ≥ δ]. (5) \"]]]]],[17131,17478,[[[17131,17478,[\"We call the objective in Eq. (5) a filtering objective. We show in App. G that solving Eq. (5) is equivalent to solving Eq. (2) with an even stronger diversity constraint. In addition, we also remark that trajectory filtering shares a conceptually similar motivation with the clipping term in Proximal Policy Optimization (Schulman et al., 2017). \"]]]]],[17478,17526,[[[17478,17526,[\"3.4 INTRINSIC REWARDS FOR DIVERSITY EXPLORATION \"]]]]],[17526,17934,[[[17526,17934,[\"The main issue in Eq. (5) is that trajectory filtering may reject a significant number of trajectories, especially in the early stage of policy learning since the policy is typically initialized to the a random policy. Hence, it is often the case that most of the data in a batch are abandoned, which leads to a severe wasting of samples and may even break learning due to the lack of feasible trajectories. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"c7184c02-054c-45be-85a3-f870fe4aca8b\"]],[null,null,0.6599198382183982,[[null,57350,57853]],[[[57350,57416,[[[57350,57416,[\"Table 8: Population Diversity on the hard map 2c_vs_64zg in SMAC. \"]]]]],[57416,57469,[[[57416,57469,[\"Algorithm # Distinct Strategies Population Diversity \"]]]]],[57469,57495,[[[57469,57495,[\"PG 1 0.981 PBT-CE 2 1.000 \"]]]]],[57495,57507,[[[57495,57507,[\"DvD 3 1.000 \"]]]]],[57507,57520,[[[57507,57520,[\"RSPO 4 1.000 \"]]]]],[57520,57520,[]],[57520,57521,[[[57520,57521,[\" \"]]]]],[57521,57552,[[[57521,57552,[\"0 500 1000 1500 2000 2500 3000 \"]]]]],[57552,57574,[[[57552,57574,[\"Training Update Steps \"]]],[null,4]]],[57574,57578,[[[57574,57578,[\"0.0 \"]]]]],[57578,57582,[[[57578,57582,[\"0.2 \"]]]]],[57582,57586,[[[57582,57586,[\"0.4 \"]]]]],[57586,57590,[[[57586,57590,[\"0.6 \"]]]]],[57590,57594,[[[57590,57594,[\"0.8 \"]]]]],[57594,57598,[[[57594,57598,[\"1.0 \"]]]]],[57598,57603,[[[57598,57603,[\"D at \"]]],[null,4]]],[57603,57607,[[[57603,57607,[\"a E \"]]],[null,4]]],[57607,57614,[[[57607,57614,[\"ffi ci \"]]],[null,4]]],[57614,57620,[[[57614,57620,[\"en cy \"]]],[null,4]]],[57620,57656,[[[57620,57656,[\"alpha\\u003d0.5 alpha\\u003d1 alpha\\u003d1.5 alpha\\u003d2 \"]]],[null,6]]],[57656,57713,[[[57656,57713,[\"Figure 12: Data efficiency with different α in Humanoid. \"]]]]],[57713,57752,[[[57713,57752,[\"0 250 500 750 1000 1250 1500 1750 2000 \"]]]]],[57752,57774,[[[57752,57774,[\"Training Update Steps \"]]],[null,4]]],[57774,57778,[[[57774,57778,[\"0.0 \"]]]]],[57778,57782,[[[57778,57782,[\"0.2 \"]]]]],[57782,57786,[[[57782,57786,[\"0.4 \"]]]]],[57786,57790,[[[57786,57790,[\"0.6 \"]]]]],[57790,57794,[[[57790,57794,[\"0.8 \"]]]]],[57794,57798,[[[57794,57798,[\"1.0 \"]]]]],[57798,57803,[[[57798,57803,[\"W in \"]]],[null,4]]],[57803,57809,[[[57803,57809,[\"ni ng \"]]],[null,4]]],[57809,57816,[[[57809,57816,[\" ra te \"]]],[null,4]]],[57816,57853,[[[57816,57853,[\"lambdaR\\u003d0.2 lambdaR\\u003d0.05 lambdaR\\u003d0.0 \"]]],[null,6]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"d471fc1c-3585-42e2-86ea-48004dbd02c7\"]],[null,null,0.6563970223842246,[[null,57853,58846]],[[[57853,57929,[[[57853,57929,[\"Figure 13: Learning curve with different λintR on the 2m_vs_1z map in SMAC. \"]]]]],[57929,57979,[[[57929,57979,[\"α Population Diversity λintB Population Diversity \"]]]]],[57979,58060,[[[57979,58060,[\"0.5 0.604 0.5 0.640 1.0 0.676 1.0 0.621 1.5 0.687 5.0 0.697 2.0 0.748 10.0 0.697 \"]]]]],[58060,58324,[[[58060,58324,[\"Table 9: Population Diversity scores of the first 2 policies with different hyperparameters in Humanoid. We have scaled the denominator of the RBF kernel in the Population Diversity matrix by a factor 10, such that the difference can be demonstrated more clearly. \"]]]]],[58324,58846,[[[58324,58846,[\"To the best of our knowledge, a commonly accepted policy diversity metric for complex MARL games remains an open question in the existing literature. In our practice, rendering and visualizing the evaluation trajectories remains the best approach to distinguish different learned strategies. We emphasize that qualitatively, we are so far the first paper that ever reports such a visually diverse collection of winning strategies on a hard map in SMAC. Please check our website for policy visualizations (see Appendix A). \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"f486c02d-1bb5-40ca-a11c-f579f362034b\"]],[null,null,0.6810832532897884,[[null,32492,34064]],[[[32492,32659,[[[32492,32659,[\"We perform ablation studies on RSPO by turning off reward switching (No Switch) or intrinsic reward (No rint) or only using the behavior-driven intrinsic reward (rint \"]]]]],[32659,34064,[[[32659,34064,[\"B only), and evaluate the performances of many baseline methods, including vanilla PG with restarts (PG), DIPG, RND, a popular multi-agent exploration method MAVEN (Mahajan et al., 2019) and reward-randomized policy gradient (RPG) (Tang et al., 2021). We summarize the categories of discovered strategies by all these baselines in Table 1. Apple denotes the non-cooperative apple-eating NE; Chase denotes the optimal NE where both agents actively chase the monster; Corner and Edge denote the sub-optimal cooperative NE where both agents passively wait for the monster at a corner or an edge respectively. Regarding the baselines, PG, DIPG, and RND never discover any strategy beyond the non-cooperative Apple NE. For RPG, even using the domain knowledge to change the reward structure of the game, it never discovers the Edge NE. Regarding the RSPO variants, both reward switching and intrinsic rewards are necessary. Fig. 5 shows that when the intrinsic reward is turned off, the proportion of accepted trajectories per batch stays low throughout training. This implies that the learning policy failed to escape the infeasible subspace. Besides, as shown in Table 1, using behavior-driven exploration alone fails to discover the optimal NE, which suggests the necessity of reward-driven exploration to maximize the divergence of both states and actions in problems with massive equivalent local optima. \"]]]]]]],[[[\"b2debaec-7788-42a7-9812-969c77f82186\"],\"7715f411-ee43-4963-a041-4b33838dc238\"]],[\"f5ad3c6a-6ec8-40af-9922-934c336e258f\"]]],[[[null,0,147],[0]],[[null,297,510],[1,2]],[[null,563,666],[3]],[[null,897,909],[4]],[[null,927,1162],[4]],[[null,1180,1439],[4,5]],[[null,1440,1584],[4,6,7]],[[null,1584,1784],[0]],[[null,1784,1899],[8]]],[[\"What is RSPO's core contribution?\",\"What is the Monster-Hunt game?\",\"What are two SMAC maps?\"]]]"]]
59
[["di",9020],["af.httprm",9019,"4221577016338925822",61]]
28
[["e",8,null,null,119995]]
