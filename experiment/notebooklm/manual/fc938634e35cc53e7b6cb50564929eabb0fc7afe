)]}'

3933
[["wrb.fr",null,"[[\"\\\"Production versus depth curves\\\" represent the **non-linear relationship between carbonate producer growth rate and water depth** [1]. These curves are user-defined values added to each carbonate sediment class within the Dionisos model [1]. For example, the\",null,[\"97cd01b7-3d6b-4db0-9b2f-b6821daf81a4\",\"e50f8b2a-8fb1-4143-a9c7-8c04551c72d9\",2929115240],null,[[null,[[[\"f443895d-3d94-40cc-ba94-78f11530881c\"],[null,125,125]],[[\"f443895d-3d94-40cc-ba94-78f11530881c\"],[null,125,228]]]],null,null,[[[\"f443895d-3d94-40cc-ba94-78f11530881c\"],[null,null,0.6208599509547273,[[null,16591,17820]],[[[16591,16640,[[[16591,16640,[\"Table 3. Model hydrodynamic boundary conditions. \"]]]]],[16640,16656,[[[16640,16656,[\"Parameter Value \"]]]]],[16656,16860,[[[16656,16860,[\"Mean Wave base (m) 6.0 Propagation azimuth (◦) 30 Wave energy flux (kWm−1) 14.02 Wave height (m) 2.15 Storm Wave base (m) 19 Propagation azimuth (◦) 30 Wave energy flux (kWm−1) 19.90 Wave height (m) 6.82 \"]]]]],[16860,16985,[[[16860,16985,[\"model was extracted and added to the model at a 10 % yearly occurrence rate. The wave parameters are summarized in Ta-ble 3. \"]]]]],[16985,17036,[[[16985,17036,[\"2.5 Carbonate production and facies identification \"]]]]],[17036,17820,[[[17036,17820,[\"Carbonate production is naturally the largest variable in a carbonate forward stratigraphic model. Several factors di-rectly control the dominate coral species and rate of growth: water temperature, turbidity, wave energy, and water depth (e.g., Montaggioni and Braithwaite, 2009). In order to ad-dress this, Dionisos classifies each carbonate producer under the four component sediment class definitions from Sect. 2.3 and then adds a user-defined production-versus-depth value to each carbonate sediment class. This production-versus-depth curve represents the non-linear relationship between carbonate producer growth rate and depth. To streamline this process, Dionisos provides growth curves for several fre-quently used carbonate sediment classes as defined in the lit-erature. \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"f443895d-3d94-40cc-ba94-78f11530881c\"]]]]]],[[null,null,0.6208599509547273,[[null,16591,17820]],[[[16591,16640,[[[16591,16640,[\"Table 3. Model hydrodynamic boundary conditions. \"]]]]],[16640,16656,[[[16640,16656,[\"Parameter Value \"]]]]],[16656,16860,[[[16656,16860,[\"Mean Wave base (m) 6.0 Propagation azimuth (◦) 30 Wave energy flux (kWm−1) 14.02 Wave height (m) 2.15 Storm Wave base (m) 19 Propagation azimuth (◦) 30 Wave energy flux (kWm−1) 19.90 Wave height (m) 6.82 \"]]]]],[16860,16985,[[[16860,16985,[\"model was extracted and added to the model at a 10 % yearly occurrence rate. The wave parameters are summarized in Ta-ble 3. \"]]]]],[16985,17036,[[[16985,17036,[\"2.5 Carbonate production and facies identification \"]]]]],[17036,17820,[[[17036,17820,[\"Carbonate production is naturally the largest variable in a carbonate forward stratigraphic model. Several factors di-rectly control the dominate coral species and rate of growth: water temperature, turbidity, wave energy, and water depth (e.g., Montaggioni and Braithwaite, 2009). In order to ad-dress this, Dionisos classifies each carbonate producer under the four component sediment class definitions from Sect. 2.3 and then adds a user-defined production-versus-depth value to each carbonate sediment class. This production-versus-depth curve represents the non-linear relationship between carbonate producer growth rate and depth. To streamline this process, Dionisos provides growth curves for several fre-quently used carbonate sediment classes as defined in the lit-erature. \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"f443895d-3d94-40cc-ba94-78f11530881c\"]]],[[[null,125,125],[0]],[[null,125,228],[0]]]]"]]
8074
[["wrb.fr",null,"[[\"\\\"Production versus depth curves\\\" represent the **non-linear relationship between carbonate producer growth rate and water depth** [1]. These curves are user-defined values added to each carbonate sediment class within the Dionisos model [1]. For example, the study utilized growth curves from existing literature for various coral-based facies, tabular corals, encrusting corals, and carbonate sand and mud [2]. In the simulations for Lembetabe, constant maximum growth rates, such as 2500 m Myr−1 for coral facies and 1000 m Myr−1 for carbonate sand and mud, were used throughout the model run [3].\",null,[\"97cd01b7-3d6b-4db0-9b2f-b6821daf81a4\",\"e50f8b2a-8fb1-4143-a9c7-8c04551c72d9\",2929115240],null,[[null,[[[\"424d6b3c-cca7-42b5-8325-02c4a2a08854\"],[null,125,125]],[[\"424d6b3c-cca7-42b5-8325-02c4a2a08854\"],[null,125,228]],[[\"9a85c7ca-929c-4b3b-bc28-3f6fc0f491e8\"],[null,228,394]],[[\"009fef08-14eb-4be6-80c1-8674acaca6a1\"],[null,394,578]]]],null,null,[[[\"424d6b3c-cca7-42b5-8325-02c4a2a08854\"],[null,null,0.6208599509547273,[[null,16591,17820]],[[[16591,16640,[[[16591,16640,[\"Table 3. Model hydrodynamic boundary conditions. \"]]]]],[16640,16656,[[[16640,16656,[\"Parameter Value \"]]]]],[16656,16860,[[[16656,16860,[\"Mean Wave base (m) 6.0 Propagation azimuth (◦) 30 Wave energy flux (kWm−1) 14.02 Wave height (m) 2.15 Storm Wave base (m) 19 Propagation azimuth (◦) 30 Wave energy flux (kWm−1) 19.90 Wave height (m) 6.82 \"]]]]],[16860,16985,[[[16860,16985,[\"model was extracted and added to the model at a 10 % yearly occurrence rate. The wave parameters are summarized in Ta-ble 3. \"]]]]],[16985,17036,[[[16985,17036,[\"2.5 Carbonate production and facies identification \"]]]]],[17036,17820,[[[17036,17820,[\"Carbonate production is naturally the largest variable in a carbonate forward stratigraphic model. Several factors di-rectly control the dominate coral species and rate of growth: water temperature, turbidity, wave energy, and water depth (e.g., Montaggioni and Braithwaite, 2009). In order to ad-dress this, Dionisos classifies each carbonate producer under the four component sediment class definitions from Sect. 2.3 and then adds a user-defined production-versus-depth value to each carbonate sediment class. This production-versus-depth curve represents the non-linear relationship between carbonate producer growth rate and depth. To streamline this process, Dionisos provides growth curves for several fre-quently used carbonate sediment classes as defined in the lit-erature. \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"424d6b3c-cca7-42b5-8325-02c4a2a08854\"]]],[[\"9a85c7ca-929c-4b3b-bc28-3f6fc0f491e8\"],[null,null,0.637162987903787,[[null,17820,18439]],[[[17820,18235,[[[17820,18235,[\"For the purposes of this study, we utilize the growth curves described by Montaggioni et al. (2015) for our coral-based facies, the tabular coral growth curve from Lanteaume et al. (2018), the encrusting coral growth curve from Kolodka et al. (2016), and the carbonate sand and mud curves from Burgess and Pollitt (2012). In the model, production is con-trolled as either a constant or is linear per time interval. \"]]]]],[18235,18236,[[[18235,18236,[\" \"]]]]],[18236,18318,[[[18236,18318,[\"P. Boyden et al.: Refining patterns of melt with forward stratigraphic models 921 \"]]]]],[18318,18439,[[[18318,18439,[\"Figure 2. Production rate (m Myr−1) as a function of water depth for each sediment class included in the Dionisos model. \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"9a85c7ca-929c-4b3b-bc28-3f6fc0f491e8\"]]],[[\"009fef08-14eb-4be6-80c1-8674acaca6a1\"],[null,null,0.563926379357099,[[null,18439,18988]],[[[18439,18839,[[[18439,18839,[\"For example, Montaggioni et al. (2015) experimented with varying the growth rates through time and achieved signif-icantly different results. However, because for Lembetabe we lack the seismic data and well logs available to Mon-taggioni et al. (2015), our simulations are run with con-stant maximum growth rates from the literature mentioned above, e.g., 2500 m Myr−1 for coral facies, 1500 m Myr−1 \"]]]]],[18839,18988,[[[18839,18988,[\"for encrusting carbonates, 2000 m Myr−1 for tabular corals, and 1000 m Myr−1 for carbonate sand and carbonate mud throughout the model run (Fig. 2). \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"009fef08-14eb-4be6-80c1-8674acaca6a1\"]]]]]],[[null,null,0.6208599509547273,[[null,16591,17820]],[[[16591,16640,[[[16591,16640,[\"Table 3. Model hydrodynamic boundary conditions. \"]]]]],[16640,16656,[[[16640,16656,[\"Parameter Value \"]]]]],[16656,16860,[[[16656,16860,[\"Mean Wave base (m) 6.0 Propagation azimuth (◦) 30 Wave energy flux (kWm−1) 14.02 Wave height (m) 2.15 Storm Wave base (m) 19 Propagation azimuth (◦) 30 Wave energy flux (kWm−1) 19.90 Wave height (m) 6.82 \"]]]]],[16860,16985,[[[16860,16985,[\"model was extracted and added to the model at a 10 % yearly occurrence rate. The wave parameters are summarized in Ta-ble 3. \"]]]]],[16985,17036,[[[16985,17036,[\"2.5 Carbonate production and facies identification \"]]]]],[17036,17820,[[[17036,17820,[\"Carbonate production is naturally the largest variable in a carbonate forward stratigraphic model. Several factors di-rectly control the dominate coral species and rate of growth: water temperature, turbidity, wave energy, and water depth (e.g., Montaggioni and Braithwaite, 2009). In order to ad-dress this, Dionisos classifies each carbonate producer under the four component sediment class definitions from Sect. 2.3 and then adds a user-defined production-versus-depth value to each carbonate sediment class. This production-versus-depth curve represents the non-linear relationship between carbonate producer growth rate and depth. To streamline this process, Dionisos provides growth curves for several fre-quently used carbonate sediment classes as defined in the lit-erature. \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"424d6b3c-cca7-42b5-8325-02c4a2a08854\"]],[null,null,0.637162987903787,[[null,17820,18439]],[[[17820,18235,[[[17820,18235,[\"For the purposes of this study, we utilize the growth curves described by Montaggioni et al. (2015) for our coral-based facies, the tabular coral growth curve from Lanteaume et al. (2018), the encrusting coral growth curve from Kolodka et al. (2016), and the carbonate sand and mud curves from Burgess and Pollitt (2012). In the model, production is con-trolled as either a constant or is linear per time interval. \"]]]]],[18235,18236,[[[18235,18236,[\" \"]]]]],[18236,18318,[[[18236,18318,[\"P. Boyden et al.: Refining patterns of melt with forward stratigraphic models 921 \"]]]]],[18318,18439,[[[18318,18439,[\"Figure 2. Production rate (m Myr−1) as a function of water depth for each sediment class included in the Dionisos model. \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"9a85c7ca-929c-4b3b-bc28-3f6fc0f491e8\"]],[null,null,0.563926379357099,[[null,18439,18988]],[[[18439,18839,[[[18439,18839,[\"For example, Montaggioni et al. (2015) experimented with varying the growth rates through time and achieved signif-icantly different results. However, because for Lembetabe we lack the seismic data and well logs available to Mon-taggioni et al. (2015), our simulations are run with con-stant maximum growth rates from the literature mentioned above, e.g., 2500 m Myr−1 for coral facies, 1500 m Myr−1 \"]]]]],[18839,18988,[[[18839,18988,[\"for encrusting carbonates, 2000 m Myr−1 for tabular corals, and 1000 m Myr−1 for carbonate sand and carbonate mud throughout the model run (Fig. 2). \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"009fef08-14eb-4be6-80c1-8674acaca6a1\"]]],[[[null,125,125],[0]],[[null,125,228],[0]],[[null,228,394],[1]],[[null,394,578],[2]]]]"]]
8722
[["wrb.fr",null,"[[\"\\\"Production versus depth curves\\\" represent the **non-linear relationship between carbonate producer growth rate and water depth** [1]. These curves are user-defined values added to each carbonate sediment class within the Dionisos model [1]. For example, the study utilized growth curves from existing literature for various coral-based facies, tabular corals, encrusting corals, and carbonate sand and mud [2]. In the simulations for Lembetabe, constant maximum growth rates, such as 2500 m Myr−1 for coral facies and 1000 m Myr−1 for carbonate sand and mud, were used throughout the model run [3].\",null,[\"97cd01b7-3d6b-4db0-9b2f-b6821daf81a4\",\"e50f8b2a-8fb1-4143-a9c7-8c04551c72d9\",2929115240],null,[[[[0,579,[[[0,47,[\"\\\"Production versus depth curves\\\" represent the \"]],[47,125,[\"non-linear relationship between carbonate producer growth rate and water depth\",[true]]],[125,579,[\". These curves are user-defined values added to each carbonate sediment class within the Dionisos model. For example, the study utilized growth curves from existing literature for various coral-based facies, tabular corals, encrusting corals, and carbonate sand and mud. In the simulations for Lembetabe, constant maximum growth rates, such as 2500 m Myr−1 for coral facies and 1000 m Myr−1 for carbonate sand and mud, were used throughout the model run.\"]]]]]],[[[\"424d6b3c-cca7-42b5-8325-02c4a2a08854\"],[null,125,125]],[[\"424d6b3c-cca7-42b5-8325-02c4a2a08854\"],[null,125,228]],[[\"9a85c7ca-929c-4b3b-bc28-3f6fc0f491e8\"],[null,228,394]],[[\"009fef08-14eb-4be6-80c1-8674acaca6a1\"],[null,394,578]]]],null,null,[[[\"424d6b3c-cca7-42b5-8325-02c4a2a08854\"],[null,null,0.6208599509547273,[[null,16591,17820]],[[[16591,16640,[[[16591,16640,[\"Table 3. Model hydrodynamic boundary conditions. \"]]]]],[16640,16656,[[[16640,16656,[\"Parameter Value \"]]]]],[16656,16860,[[[16656,16860,[\"Mean Wave base (m) 6.0 Propagation azimuth (◦) 30 Wave energy flux (kWm−1) 14.02 Wave height (m) 2.15 Storm Wave base (m) 19 Propagation azimuth (◦) 30 Wave energy flux (kWm−1) 19.90 Wave height (m) 6.82 \"]]]]],[16860,16985,[[[16860,16985,[\"model was extracted and added to the model at a 10 % yearly occurrence rate. The wave parameters are summarized in Ta-ble 3. \"]]]]],[16985,17036,[[[16985,17036,[\"2.5 Carbonate production and facies identification \"]]]]],[17036,17820,[[[17036,17820,[\"Carbonate production is naturally the largest variable in a carbonate forward stratigraphic model. Several factors di-rectly control the dominate coral species and rate of growth: water temperature, turbidity, wave energy, and water depth (e.g., Montaggioni and Braithwaite, 2009). In order to ad-dress this, Dionisos classifies each carbonate producer under the four component sediment class definitions from Sect. 2.3 and then adds a user-defined production-versus-depth value to each carbonate sediment class. This production-versus-depth curve represents the non-linear relationship between carbonate producer growth rate and depth. To streamline this process, Dionisos provides growth curves for several fre-quently used carbonate sediment classes as defined in the lit-erature. \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"424d6b3c-cca7-42b5-8325-02c4a2a08854\"]]],[[\"9a85c7ca-929c-4b3b-bc28-3f6fc0f491e8\"],[null,null,0.637162987903787,[[null,17820,18439]],[[[17820,18235,[[[17820,18235,[\"For the purposes of this study, we utilize the growth curves described by Montaggioni et al. (2015) for our coral-based facies, the tabular coral growth curve from Lanteaume et al. (2018), the encrusting coral growth curve from Kolodka et al. (2016), and the carbonate sand and mud curves from Burgess and Pollitt (2012). In the model, production is con-trolled as either a constant or is linear per time interval. \"]]]]],[18235,18236,[[[18235,18236,[\" \"]]]]],[18236,18318,[[[18236,18318,[\"P. Boyden et al.: Refining patterns of melt with forward stratigraphic models 921 \"]]]]],[18318,18439,[[[18318,18439,[\"Figure 2. Production rate (m Myr−1) as a function of water depth for each sediment class included in the Dionisos model. \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"9a85c7ca-929c-4b3b-bc28-3f6fc0f491e8\"]]],[[\"009fef08-14eb-4be6-80c1-8674acaca6a1\"],[null,null,0.563926379357099,[[null,18439,18988]],[[[18439,18839,[[[18439,18839,[\"For example, Montaggioni et al. (2015) experimented with varying the growth rates through time and achieved signif-icantly different results. However, because for Lembetabe we lack the seismic data and well logs available to Mon-taggioni et al. (2015), our simulations are run with con-stant maximum growth rates from the literature mentioned above, e.g., 2500 m Myr−1 for coral facies, 1500 m Myr−1 \"]]]]],[18839,18988,[[[18839,18988,[\"for encrusting carbonates, 2000 m Myr−1 for tabular corals, and 1000 m Myr−1 for carbonate sand and carbonate mud throughout the model run (Fig. 2). \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"009fef08-14eb-4be6-80c1-8674acaca6a1\"]]]]]],[[null,null,0.6208599509547273,[[null,16591,17820]],[[[16591,16640,[[[16591,16640,[\"Table 3. Model hydrodynamic boundary conditions. \"]]]]],[16640,16656,[[[16640,16656,[\"Parameter Value \"]]]]],[16656,16860,[[[16656,16860,[\"Mean Wave base (m) 6.0 Propagation azimuth (◦) 30 Wave energy flux (kWm−1) 14.02 Wave height (m) 2.15 Storm Wave base (m) 19 Propagation azimuth (◦) 30 Wave energy flux (kWm−1) 19.90 Wave height (m) 6.82 \"]]]]],[16860,16985,[[[16860,16985,[\"model was extracted and added to the model at a 10 % yearly occurrence rate. The wave parameters are summarized in Ta-ble 3. \"]]]]],[16985,17036,[[[16985,17036,[\"2.5 Carbonate production and facies identification \"]]]]],[17036,17820,[[[17036,17820,[\"Carbonate production is naturally the largest variable in a carbonate forward stratigraphic model. Several factors di-rectly control the dominate coral species and rate of growth: water temperature, turbidity, wave energy, and water depth (e.g., Montaggioni and Braithwaite, 2009). In order to ad-dress this, Dionisos classifies each carbonate producer under the four component sediment class definitions from Sect. 2.3 and then adds a user-defined production-versus-depth value to each carbonate sediment class. This production-versus-depth curve represents the non-linear relationship between carbonate producer growth rate and depth. To streamline this process, Dionisos provides growth curves for several fre-quently used carbonate sediment classes as defined in the lit-erature. \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"424d6b3c-cca7-42b5-8325-02c4a2a08854\"]],[null,null,0.637162987903787,[[null,17820,18439]],[[[17820,18235,[[[17820,18235,[\"For the purposes of this study, we utilize the growth curves described by Montaggioni et al. (2015) for our coral-based facies, the tabular coral growth curve from Lanteaume et al. (2018), the encrusting coral growth curve from Kolodka et al. (2016), and the carbonate sand and mud curves from Burgess and Pollitt (2012). In the model, production is con-trolled as either a constant or is linear per time interval. \"]]]]],[18235,18236,[[[18235,18236,[\" \"]]]]],[18236,18318,[[[18236,18318,[\"P. Boyden et al.: Refining patterns of melt with forward stratigraphic models 921 \"]]]]],[18318,18439,[[[18318,18439,[\"Figure 2. Production rate (m Myr−1) as a function of water depth for each sediment class included in the Dionisos model. \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"9a85c7ca-929c-4b3b-bc28-3f6fc0f491e8\"]],[null,null,0.563926379357099,[[null,18439,18988]],[[[18439,18839,[[[18439,18839,[\"For example, Montaggioni et al. (2015) experimented with varying the growth rates through time and achieved signif-icantly different results. However, because for Lembetabe we lack the seismic data and well logs available to Mon-taggioni et al. (2015), our simulations are run with con-stant maximum growth rates from the literature mentioned above, e.g., 2500 m Myr−1 for coral facies, 1500 m Myr−1 \"]]]]],[18839,18988,[[[18839,18988,[\"for encrusting carbonates, 2000 m Myr−1 for tabular corals, and 1000 m Myr−1 for carbonate sand and carbonate mud throughout the model run (Fig. 2). \"]]]]]]],[[[\"67c7c6c9-8681-42b3-97cf-0d3d61c1a60d\"],\"ed6ebb66-76ae-4136-a23e-e3d60539ecdc\"]],[\"009fef08-14eb-4be6-80c1-8674acaca6a1\"]]],[[[null,125,125],[0]],[[null,125,228],[0]],[[null,228,394],[1]],[[null,394,578],[2]]]]"]]
59
[["di",3793],["af.httprm",3793,"2642117648262498653",73]]
27
[["e",6,null,null,20937]]
