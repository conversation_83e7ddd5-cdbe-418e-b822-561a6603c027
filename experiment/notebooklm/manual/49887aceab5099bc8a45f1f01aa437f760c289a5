)]}'

285
[["wrb.fr",null,"[[\"The 90%\",null,[\"981f15dc-2b7b-4273-ae50-765ced97c3e4\",\"ac2c4976-0542-4a08-bbb3-69df072bdfc3\",2822032413],null,[[]]],null,null,[[\"What is NInGa's primary purpose?\",\"What is a \\\"Gold Standard\\\" model?\",\"How does Bayesian GS improve estimates?\"]]]"]]
10771
[["wrb.fr",null,"[[\"The 90% Normalized Information Gain (NInGa) performance achieved by the mixture model is specific to the real neural data example it was evaluated on [1, 2].\\n\\nWhile NInGa is designed as an interpretable evaluation metric for likelihood-based models that is desirable for \\\"comparing models that are trained on different datasets, which can exhibit different levels of achievable performance\\\" [3], the specific value of 90% is a result from that particular dataset. Datasets can vary in their characteristics, such as the number of repeated trials, levels of (shared) variability, and sparsity of responses, which can influence the \\\"achievable performance\\\" (\",null,[\"981f15dc-2b7b-4273-ae50-765ced97c3e4\",\"ac2c4976-0542-4a08-bbb3-69df072bdfc3\",2822032413],null,[[[[0,150,[[[0,150,[\"The 90% Normalized Information Gain (NInGa) performance achieved by the mixture model is specific to the real neural data example it was evaluated on.\"]]]]]],[[[\"043adc22-022c-4f7b-b8e1-c5840f7e4e12\"],[null,0,149]],[[\"9a3c19df-be93-45f6-9673-06040cc91c59\"],[null,0,149]],[[\"cf546a75-74ee-4e4e-90dd-9bec66b4d895\"],[null,150,381]]]],null,null,[[[\"043adc22-022c-4f7b-b8e1-c5840f7e4e12\"],[null,null,0.7345387794427887,[[null,0,2270]],[[[0,1,[[[0,1,[\" \"]]]]],[1,42,[[[1,42,[\"BAYESIAN ORACLE FOR BOUNDING INFORMATION \"]]],[null,6]]],[42,73,[[[42,73,[\"GAIN IN NEURAL ENCODING MODELS \"]]]]],[73,453,[[[73,453,[\"Konstantin-Klemens Lurz1,*, Mohammad Bashiri1,*, Edgar Y. Walker2, Fabian H. Sinz1,3,† 1 Institute for Bioinformatics and Medical Informatics, University of Tübingen, Germany 2 Department of Physiology, Computational Neuroscience Center, University of Washington, USA 3 Department of Computer Science, University Göttingen, Germany *equal contribution, †<EMAIL> \"]]]]],[453,462,[[[453,462,[\"ABSTRACT \"]]]]],[462,2270,[[[462,2270,[\"In recent years, deep learning models have set new standards in predicting neural population responses. Most of these models currently focus on predicting the mean response of each neuron for a given input. However, neural variability around this mean is not just noise and plays a central role in several theories on neural computation. To capture this variability, we need models that predict full response distributions for a given stimulus. However, to measure the quality of such models, commonly used correlation-based metrics are not sufficient as they mainly care about the mean of the response distribution. An interpretable alternative evaluation metric for likelihood-based models is Normalized Information Gain (NInGa) which evaluates the likelihood of a model relative to a lower and upper bound. However, while a lower bound is usually easy to obtain, constructing an upper bound turns out to be challenging for neural recordings with relatively low numbers of repeated trials, high (shared) variability, and sparse responses. In this work, we generalize the jack-knife oracle estimator for the mean—commonly used for correlation metrics—to a flexible Bayesian oracle estimator for NInGa based on posterior predictive distributions. We describe and address the challenges that arise when estimating the lower and upper bounds from small datasets. We then show that our upper bound estimate is data-efficient and robust even in the case of sparse responses and low signal-to-noise ratio. We further provide the derivation of the upper bound estimator for a variety of common distributions including the state-of-the-art zero-inflated mixture models, and relate NInGa to common mean-based metrics. Finally, we use our approach to evaluate such a mixture model resulting in 90% NInGa performance. \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"043adc22-022c-4f7b-b8e1-c5840f7e4e12\"]]],[[\"9a3c19df-be93-45f6-9673-06040cc91c59\"],[null,null,0.7265228735225631,[[null,24041,24721]],[[[24041,24721,[[[24041,24721,[\"Encoding model performance is at 90% NInGa The final NInGa value of the trained model using the Null model and the GS model with optimized prior hyper-parameters can be seen in Fig. 4 c. It performs remarkably well at 90% NInGa (blue bar), which corresponds to a likelihood of 7.00 bits per image and neuron (printed value above the bar). The effect of the parameter matching (grey bars) is more emphasized and suggests that the largest performance gain can be achieved in future models by improving the prediction of the parameter q. We performed additional analyses on multiple datasets to show how NInGa facilitates model comparison across different datasets (see Appendix J). \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"9a3c19df-be93-45f6-9673-06040cc91c59\"]]],[[\"cf546a75-74ee-4e4e-90dd-9bec66b4d895\"],[null,null,0.7002078526824659,[[null,4292,5178]],[[[4292,5178,[[[4292,5178,[\"trial-to-trial fluctuations. Therefore, model correlation is often normalized by an upper bound oracle estimator [19; 16], which is commonly obtained by computing point estimates of the conditional mean using the responses to repeated presentations of the same stimulus. For a likelihood-based metric, a similar normalization to a bounded and interpretable scale would be desirable, especially for: 1) Assessing whether a model has achieved its “best possible” performance for a given dataset, and 2) comparing models that are trained on different datasets, which can exhibit different levels of achievable performance. To this end, one can use Normalized Information Gain (NInGa) [14], which uses an estimate of both upper and lower bound, to put the likelihood between two meaningful values. However, the challenge lies in how these bounds can be obtained for noisy neural responses. \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"cf546a75-74ee-4e4e-90dd-9bec66b4d895\"]]]]]],[[null,null,0.7345387794427887,[[null,0,2270]],[[[0,1,[[[0,1,[\" \"]]]]],[1,42,[[[1,42,[\"BAYESIAN ORACLE FOR BOUNDING INFORMATION \"]]],[null,6]]],[42,73,[[[42,73,[\"GAIN IN NEURAL ENCODING MODELS \"]]]]],[73,453,[[[73,453,[\"Konstantin-Klemens Lurz1,*, Mohammad Bashiri1,*, Edgar Y. Walker2, Fabian H. Sinz1,3,† 1 Institute for Bioinformatics and Medical Informatics, University of Tübingen, Germany 2 Department of Physiology, Computational Neuroscience Center, University of Washington, USA 3 Department of Computer Science, University Göttingen, Germany *equal contribution, †<EMAIL> \"]]]]],[453,462,[[[453,462,[\"ABSTRACT \"]]]]],[462,2270,[[[462,2270,[\"In recent years, deep learning models have set new standards in predicting neural population responses. Most of these models currently focus on predicting the mean response of each neuron for a given input. However, neural variability around this mean is not just noise and plays a central role in several theories on neural computation. To capture this variability, we need models that predict full response distributions for a given stimulus. However, to measure the quality of such models, commonly used correlation-based metrics are not sufficient as they mainly care about the mean of the response distribution. An interpretable alternative evaluation metric for likelihood-based models is Normalized Information Gain (NInGa) which evaluates the likelihood of a model relative to a lower and upper bound. However, while a lower bound is usually easy to obtain, constructing an upper bound turns out to be challenging for neural recordings with relatively low numbers of repeated trials, high (shared) variability, and sparse responses. In this work, we generalize the jack-knife oracle estimator for the mean—commonly used for correlation metrics—to a flexible Bayesian oracle estimator for NInGa based on posterior predictive distributions. We describe and address the challenges that arise when estimating the lower and upper bounds from small datasets. We then show that our upper bound estimate is data-efficient and robust even in the case of sparse responses and low signal-to-noise ratio. We further provide the derivation of the upper bound estimator for a variety of common distributions including the state-of-the-art zero-inflated mixture models, and relate NInGa to common mean-based metrics. Finally, we use our approach to evaluate such a mixture model resulting in 90% NInGa performance. \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"043adc22-022c-4f7b-b8e1-c5840f7e4e12\"]],[null,null,0.7265228735225631,[[null,24041,24721]],[[[24041,24721,[[[24041,24721,[\"Encoding model performance is at 90% NInGa The final NInGa value of the trained model using the Null model and the GS model with optimized prior hyper-parameters can be seen in Fig. 4 c. It performs remarkably well at 90% NInGa (blue bar), which corresponds to a likelihood of 7.00 bits per image and neuron (printed value above the bar). The effect of the parameter matching (grey bars) is more emphasized and suggests that the largest performance gain can be achieved in future models by improving the prediction of the parameter q. We performed additional analyses on multiple datasets to show how NInGa facilitates model comparison across different datasets (see Appendix J). \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"9a3c19df-be93-45f6-9673-06040cc91c59\"]],[null,null,0.7002078526824659,[[null,4292,5178]],[[[4292,5178,[[[4292,5178,[\"trial-to-trial fluctuations. Therefore, model correlation is often normalized by an upper bound oracle estimator [19; 16], which is commonly obtained by computing point estimates of the conditional mean using the responses to repeated presentations of the same stimulus. For a likelihood-based metric, a similar normalization to a bounded and interpretable scale would be desirable, especially for: 1) Assessing whether a model has achieved its “best possible” performance for a given dataset, and 2) comparing models that are trained on different datasets, which can exhibit different levels of achievable performance. To this end, one can use Normalized Information Gain (NInGa) [14], which uses an estimate of both upper and lower bound, to put the likelihood between two meaningful values. However, the challenge lies in how these bounds can be obtained for noisy neural responses. \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"cf546a75-74ee-4e4e-90dd-9bec66b4d895\"]]],[[[null,0,149],[0,1]],[[null,150,381],[2]]],[[\"What is NInGa's primary purpose?\",\"What is a \\\"Gold Standard\\\" model?\",\"How does Bayesian GS improve estimates?\"]]]"]]
12006
[["wrb.fr",null,"[[\"The 90% Normalized Information Gain (NInGa) performance achieved by the mixture model is specific to the real neural data example it was evaluated on [1, 2].\\n\\nWhile NInGa is designed as an interpretable evaluation metric for likelihood-based models that is desirable for \\\"comparing models that are trained on different datasets, which can exhibit different levels of achievable performance\\\" [3], the specific value of 90% is a result from that particular dataset. Datasets can vary in their characteristics, such as the number of repeated trials, levels of (shared) variability, and sparsity of responses, which can influence the \\\"achievable performance\\\" (upper bound) [1, 3].\\n\\nTherefore, while NInGa **facilitates model comparison across different datasets** [2, 3], the 90% NInGa achieved on one dataset does not inherently indicate that the model will achieve the same numerical performance on other datasets, as the potential upper bound for performance can vary [3].\",null,[\"981f15dc-2b7b-4273-ae50-765ced97c3e4\",\"ac2c4976-0542-4a08-bbb3-69df072bdfc3\",2822032413],null,[[[[0,150,[[[0,150,[\"The 90% Normalized Information Gain (NInGa) performance achieved by the mixture model is specific to the real neural data example it was evaluated on.\"]]]]],[150,656,[[[150,656,[\"While NInGa is designed as an interpretable evaluation metric for likelihood-based models that is desirable for \\\"comparing models that are trained on different datasets, which can exhibit different levels of achievable performance\\\", the specific value of 90% is a result from that particular dataset. Datasets can vary in their characteristics, such as the number of repeated trials, levels of (shared) variability, and sparsity of responses, which can influence the \\\"achievable performance\\\" (upper bound).\"]]]]]],[[[\"8427270e-eaaa-4eee-881a-048c62fa2a43\"],[null,0,149]],[[\"0259c17f-6738-4e7b-b174-490ba19b3cda\"],[null,0,149]],[[\"02134707-380d-406f-b904-c9ce42059031\"],[null,150,381]],[[\"8427270e-eaaa-4eee-881a-048c62fa2a43\"],[null,381,655]],[[\"02134707-380d-406f-b904-c9ce42059031\"],[null,381,655]],[[\"0259c17f-6738-4e7b-b174-490ba19b3cda\"],[null,733,733]],[[\"02134707-380d-406f-b904-c9ce42059031\"],[null,733,733]],[[\"02134707-380d-406f-b904-c9ce42059031\"],[null,733,933]]]],null,null,[[[\"8427270e-eaaa-4eee-881a-048c62fa2a43\"],[null,null,0.7345387794427887,[[null,0,2270]],[[[0,1,[[[0,1,[\" \"]]]]],[1,42,[[[1,42,[\"BAYESIAN ORACLE FOR BOUNDING INFORMATION \"]]],[null,6]]],[42,73,[[[42,73,[\"GAIN IN NEURAL ENCODING MODELS \"]]]]],[73,453,[[[73,453,[\"Konstantin-Klemens Lurz1,*, Mohammad Bashiri1,*, Edgar Y. Walker2, Fabian H. Sinz1,3,† 1 Institute for Bioinformatics and Medical Informatics, University of Tübingen, Germany 2 Department of Physiology, Computational Neuroscience Center, University of Washington, USA 3 Department of Computer Science, University Göttingen, Germany *equal contribution, †<EMAIL> \"]]]]],[453,462,[[[453,462,[\"ABSTRACT \"]]]]],[462,2270,[[[462,2270,[\"In recent years, deep learning models have set new standards in predicting neural population responses. Most of these models currently focus on predicting the mean response of each neuron for a given input. However, neural variability around this mean is not just noise and plays a central role in several theories on neural computation. To capture this variability, we need models that predict full response distributions for a given stimulus. However, to measure the quality of such models, commonly used correlation-based metrics are not sufficient as they mainly care about the mean of the response distribution. An interpretable alternative evaluation metric for likelihood-based models is Normalized Information Gain (NInGa) which evaluates the likelihood of a model relative to a lower and upper bound. However, while a lower bound is usually easy to obtain, constructing an upper bound turns out to be challenging for neural recordings with relatively low numbers of repeated trials, high (shared) variability, and sparse responses. In this work, we generalize the jack-knife oracle estimator for the mean—commonly used for correlation metrics—to a flexible Bayesian oracle estimator for NInGa based on posterior predictive distributions. We describe and address the challenges that arise when estimating the lower and upper bounds from small datasets. We then show that our upper bound estimate is data-efficient and robust even in the case of sparse responses and low signal-to-noise ratio. We further provide the derivation of the upper bound estimator for a variety of common distributions including the state-of-the-art zero-inflated mixture models, and relate NInGa to common mean-based metrics. Finally, we use our approach to evaluate such a mixture model resulting in 90% NInGa performance. \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"8427270e-eaaa-4eee-881a-048c62fa2a43\"]]],[[\"0259c17f-6738-4e7b-b174-490ba19b3cda\"],[null,null,0.7265228735225631,[[null,24041,24721]],[[[24041,24721,[[[24041,24721,[\"Encoding model performance is at 90% NInGa The final NInGa value of the trained model using the Null model and the GS model with optimized prior hyper-parameters can be seen in Fig. 4 c. It performs remarkably well at 90% NInGa (blue bar), which corresponds to a likelihood of 7.00 bits per image and neuron (printed value above the bar). The effect of the parameter matching (grey bars) is more emphasized and suggests that the largest performance gain can be achieved in future models by improving the prediction of the parameter q. We performed additional analyses on multiple datasets to show how NInGa facilitates model comparison across different datasets (see Appendix J). \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"0259c17f-6738-4e7b-b174-490ba19b3cda\"]]],[[\"02134707-380d-406f-b904-c9ce42059031\"],[null,null,0.7002078526824659,[[null,4292,5178]],[[[4292,5178,[[[4292,5178,[\"trial-to-trial fluctuations. Therefore, model correlation is often normalized by an upper bound oracle estimator [19; 16], which is commonly obtained by computing point estimates of the conditional mean using the responses to repeated presentations of the same stimulus. For a likelihood-based metric, a similar normalization to a bounded and interpretable scale would be desirable, especially for: 1) Assessing whether a model has achieved its “best possible” performance for a given dataset, and 2) comparing models that are trained on different datasets, which can exhibit different levels of achievable performance. To this end, one can use Normalized Information Gain (NInGa) [14], which uses an estimate of both upper and lower bound, to put the likelihood between two meaningful values. However, the challenge lies in how these bounds can be obtained for noisy neural responses. \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"02134707-380d-406f-b904-c9ce42059031\"]]]]]],[[null,null,0.7345387794427887,[[null,0,2270]],[[[0,1,[[[0,1,[\" \"]]]]],[1,42,[[[1,42,[\"BAYESIAN ORACLE FOR BOUNDING INFORMATION \"]]],[null,6]]],[42,73,[[[42,73,[\"GAIN IN NEURAL ENCODING MODELS \"]]]]],[73,453,[[[73,453,[\"Konstantin-Klemens Lurz1,*, Mohammad Bashiri1,*, Edgar Y. Walker2, Fabian H. Sinz1,3,† 1 Institute for Bioinformatics and Medical Informatics, University of Tübingen, Germany 2 Department of Physiology, Computational Neuroscience Center, University of Washington, USA 3 Department of Computer Science, University Göttingen, Germany *equal contribution, †<EMAIL> \"]]]]],[453,462,[[[453,462,[\"ABSTRACT \"]]]]],[462,2270,[[[462,2270,[\"In recent years, deep learning models have set new standards in predicting neural population responses. Most of these models currently focus on predicting the mean response of each neuron for a given input. However, neural variability around this mean is not just noise and plays a central role in several theories on neural computation. To capture this variability, we need models that predict full response distributions for a given stimulus. However, to measure the quality of such models, commonly used correlation-based metrics are not sufficient as they mainly care about the mean of the response distribution. An interpretable alternative evaluation metric for likelihood-based models is Normalized Information Gain (NInGa) which evaluates the likelihood of a model relative to a lower and upper bound. However, while a lower bound is usually easy to obtain, constructing an upper bound turns out to be challenging for neural recordings with relatively low numbers of repeated trials, high (shared) variability, and sparse responses. In this work, we generalize the jack-knife oracle estimator for the mean—commonly used for correlation metrics—to a flexible Bayesian oracle estimator for NInGa based on posterior predictive distributions. We describe and address the challenges that arise when estimating the lower and upper bounds from small datasets. We then show that our upper bound estimate is data-efficient and robust even in the case of sparse responses and low signal-to-noise ratio. We further provide the derivation of the upper bound estimator for a variety of common distributions including the state-of-the-art zero-inflated mixture models, and relate NInGa to common mean-based metrics. Finally, we use our approach to evaluate such a mixture model resulting in 90% NInGa performance. \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"8427270e-eaaa-4eee-881a-048c62fa2a43\"]],[null,null,0.7265228735225631,[[null,24041,24721]],[[[24041,24721,[[[24041,24721,[\"Encoding model performance is at 90% NInGa The final NInGa value of the trained model using the Null model and the GS model with optimized prior hyper-parameters can be seen in Fig. 4 c. It performs remarkably well at 90% NInGa (blue bar), which corresponds to a likelihood of 7.00 bits per image and neuron (printed value above the bar). The effect of the parameter matching (grey bars) is more emphasized and suggests that the largest performance gain can be achieved in future models by improving the prediction of the parameter q. We performed additional analyses on multiple datasets to show how NInGa facilitates model comparison across different datasets (see Appendix J). \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"0259c17f-6738-4e7b-b174-490ba19b3cda\"]],[null,null,0.7002078526824659,[[null,4292,5178]],[[[4292,5178,[[[4292,5178,[\"trial-to-trial fluctuations. Therefore, model correlation is often normalized by an upper bound oracle estimator [19; 16], which is commonly obtained by computing point estimates of the conditional mean using the responses to repeated presentations of the same stimulus. For a likelihood-based metric, a similar normalization to a bounded and interpretable scale would be desirable, especially for: 1) Assessing whether a model has achieved its “best possible” performance for a given dataset, and 2) comparing models that are trained on different datasets, which can exhibit different levels of achievable performance. To this end, one can use Normalized Information Gain (NInGa) [14], which uses an estimate of both upper and lower bound, to put the likelihood between two meaningful values. However, the challenge lies in how these bounds can be obtained for noisy neural responses. \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"02134707-380d-406f-b904-c9ce42059031\"]]],[[[null,0,149],[0,1]],[[null,150,381],[2]],[[null,381,655],[0,2]],[[null,733,733],[1,2]],[[null,733,933],[2]]],[[\"What is NInGa's primary purpose?\",\"What is a \\\"Gold Standard\\\" model?\",\"How does Bayesian GS improve estimates?\"]]]"]]
12356
[["wrb.fr",null,"[[\"The 90% Normalized Information Gain (NInGa) performance achieved by the mixture model is specific to the real neural data example it was evaluated on [1, 2].\\n\\nWhile NInGa is designed as an interpretable evaluation metric for likelihood-based models that is desirable for \\\"comparing models that are trained on different datasets, which can exhibit different levels of achievable performance\\\" [3], the specific value of 90% is a result from that particular dataset. Datasets can vary in their characteristics, such as the number of repeated trials, levels of (shared) variability, and sparsity of responses, which can influence the \\\"achievable performance\\\" (upper bound) [1, 3].\\n\\nTherefore, while NInGa **facilitates model comparison across different datasets** [2, 3], the 90% NInGa achieved on one dataset does not inherently indicate that the model will achieve the same numerical performance on other datasets, as the potential upper bound for performance can vary [3].\",null,[\"981f15dc-2b7b-4273-ae50-765ced97c3e4\",\"ac2c4976-0542-4a08-bbb3-69df072bdfc3\",2822032413],null,[[[[0,150,[[[0,150,[\"The 90% Normalized Information Gain (NInGa) performance achieved by the mixture model is specific to the real neural data example it was evaluated on.\"]]]]],[150,656,[[[150,656,[\"While NInGa is designed as an interpretable evaluation metric for likelihood-based models that is desirable for \\\"comparing models that are trained on different datasets, which can exhibit different levels of achievable performance\\\", the specific value of 90% is a result from that particular dataset. Datasets can vary in their characteristics, such as the number of repeated trials, levels of (shared) variability, and sparsity of responses, which can influence the \\\"achievable performance\\\" (upper bound).\"]]]]],[656,934,[[[656,679,[\"Therefore, while NInGa \"]],[679,733,[\"facilitates model comparison across different datasets\",[true]]],[733,934,[\", the 90% NInGa achieved on one dataset does not inherently indicate that the model will achieve the same numerical performance on other datasets, as the potential upper bound for performance can vary.\"]]]]]],[[[\"8427270e-eaaa-4eee-881a-048c62fa2a43\"],[null,0,149]],[[\"0259c17f-6738-4e7b-b174-490ba19b3cda\"],[null,0,149]],[[\"02134707-380d-406f-b904-c9ce42059031\"],[null,150,381]],[[\"8427270e-eaaa-4eee-881a-048c62fa2a43\"],[null,381,655]],[[\"02134707-380d-406f-b904-c9ce42059031\"],[null,381,655]],[[\"0259c17f-6738-4e7b-b174-490ba19b3cda\"],[null,733,733]],[[\"02134707-380d-406f-b904-c9ce42059031\"],[null,733,733]],[[\"02134707-380d-406f-b904-c9ce42059031\"],[null,733,933]]]],null,null,[[[\"8427270e-eaaa-4eee-881a-048c62fa2a43\"],[null,null,0.7345387794427887,[[null,0,2270]],[[[0,1,[[[0,1,[\" \"]]]]],[1,42,[[[1,42,[\"BAYESIAN ORACLE FOR BOUNDING INFORMATION \"]]],[null,6]]],[42,73,[[[42,73,[\"GAIN IN NEURAL ENCODING MODELS \"]]]]],[73,453,[[[73,453,[\"Konstantin-Klemens Lurz1,*, Mohammad Bashiri1,*, Edgar Y. Walker2, Fabian H. Sinz1,3,† 1 Institute for Bioinformatics and Medical Informatics, University of Tübingen, Germany 2 Department of Physiology, Computational Neuroscience Center, University of Washington, USA 3 Department of Computer Science, University Göttingen, Germany *equal contribution, †<EMAIL> \"]]]]],[453,462,[[[453,462,[\"ABSTRACT \"]]]]],[462,2270,[[[462,2270,[\"In recent years, deep learning models have set new standards in predicting neural population responses. Most of these models currently focus on predicting the mean response of each neuron for a given input. However, neural variability around this mean is not just noise and plays a central role in several theories on neural computation. To capture this variability, we need models that predict full response distributions for a given stimulus. However, to measure the quality of such models, commonly used correlation-based metrics are not sufficient as they mainly care about the mean of the response distribution. An interpretable alternative evaluation metric for likelihood-based models is Normalized Information Gain (NInGa) which evaluates the likelihood of a model relative to a lower and upper bound. However, while a lower bound is usually easy to obtain, constructing an upper bound turns out to be challenging for neural recordings with relatively low numbers of repeated trials, high (shared) variability, and sparse responses. In this work, we generalize the jack-knife oracle estimator for the mean—commonly used for correlation metrics—to a flexible Bayesian oracle estimator for NInGa based on posterior predictive distributions. We describe and address the challenges that arise when estimating the lower and upper bounds from small datasets. We then show that our upper bound estimate is data-efficient and robust even in the case of sparse responses and low signal-to-noise ratio. We further provide the derivation of the upper bound estimator for a variety of common distributions including the state-of-the-art zero-inflated mixture models, and relate NInGa to common mean-based metrics. Finally, we use our approach to evaluate such a mixture model resulting in 90% NInGa performance. \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"8427270e-eaaa-4eee-881a-048c62fa2a43\"]]],[[\"0259c17f-6738-4e7b-b174-490ba19b3cda\"],[null,null,0.7265228735225631,[[null,24041,24721]],[[[24041,24721,[[[24041,24721,[\"Encoding model performance is at 90% NInGa The final NInGa value of the trained model using the Null model and the GS model with optimized prior hyper-parameters can be seen in Fig. 4 c. It performs remarkably well at 90% NInGa (blue bar), which corresponds to a likelihood of 7.00 bits per image and neuron (printed value above the bar). The effect of the parameter matching (grey bars) is more emphasized and suggests that the largest performance gain can be achieved in future models by improving the prediction of the parameter q. We performed additional analyses on multiple datasets to show how NInGa facilitates model comparison across different datasets (see Appendix J). \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"0259c17f-6738-4e7b-b174-490ba19b3cda\"]]],[[\"02134707-380d-406f-b904-c9ce42059031\"],[null,null,0.7002078526824659,[[null,4292,5178]],[[[4292,5178,[[[4292,5178,[\"trial-to-trial fluctuations. Therefore, model correlation is often normalized by an upper bound oracle estimator [19; 16], which is commonly obtained by computing point estimates of the conditional mean using the responses to repeated presentations of the same stimulus. For a likelihood-based metric, a similar normalization to a bounded and interpretable scale would be desirable, especially for: 1) Assessing whether a model has achieved its “best possible” performance for a given dataset, and 2) comparing models that are trained on different datasets, which can exhibit different levels of achievable performance. To this end, one can use Normalized Information Gain (NInGa) [14], which uses an estimate of both upper and lower bound, to put the likelihood between two meaningful values. However, the challenge lies in how these bounds can be obtained for noisy neural responses. \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"02134707-380d-406f-b904-c9ce42059031\"]]]]]],[[null,null,0.7345387794427887,[[null,0,2270]],[[[0,1,[[[0,1,[\" \"]]]]],[1,42,[[[1,42,[\"BAYESIAN ORACLE FOR BOUNDING INFORMATION \"]]],[null,6]]],[42,73,[[[42,73,[\"GAIN IN NEURAL ENCODING MODELS \"]]]]],[73,453,[[[73,453,[\"Konstantin-Klemens Lurz1,*, Mohammad Bashiri1,*, Edgar Y. Walker2, Fabian H. Sinz1,3,† 1 Institute for Bioinformatics and Medical Informatics, University of Tübingen, Germany 2 Department of Physiology, Computational Neuroscience Center, University of Washington, USA 3 Department of Computer Science, University Göttingen, Germany *equal contribution, †<EMAIL> \"]]]]],[453,462,[[[453,462,[\"ABSTRACT \"]]]]],[462,2270,[[[462,2270,[\"In recent years, deep learning models have set new standards in predicting neural population responses. Most of these models currently focus on predicting the mean response of each neuron for a given input. However, neural variability around this mean is not just noise and plays a central role in several theories on neural computation. To capture this variability, we need models that predict full response distributions for a given stimulus. However, to measure the quality of such models, commonly used correlation-based metrics are not sufficient as they mainly care about the mean of the response distribution. An interpretable alternative evaluation metric for likelihood-based models is Normalized Information Gain (NInGa) which evaluates the likelihood of a model relative to a lower and upper bound. However, while a lower bound is usually easy to obtain, constructing an upper bound turns out to be challenging for neural recordings with relatively low numbers of repeated trials, high (shared) variability, and sparse responses. In this work, we generalize the jack-knife oracle estimator for the mean—commonly used for correlation metrics—to a flexible Bayesian oracle estimator for NInGa based on posterior predictive distributions. We describe and address the challenges that arise when estimating the lower and upper bounds from small datasets. We then show that our upper bound estimate is data-efficient and robust even in the case of sparse responses and low signal-to-noise ratio. We further provide the derivation of the upper bound estimator for a variety of common distributions including the state-of-the-art zero-inflated mixture models, and relate NInGa to common mean-based metrics. Finally, we use our approach to evaluate such a mixture model resulting in 90% NInGa performance. \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"8427270e-eaaa-4eee-881a-048c62fa2a43\"]],[null,null,0.7265228735225631,[[null,24041,24721]],[[[24041,24721,[[[24041,24721,[\"Encoding model performance is at 90% NInGa The final NInGa value of the trained model using the Null model and the GS model with optimized prior hyper-parameters can be seen in Fig. 4 c. It performs remarkably well at 90% NInGa (blue bar), which corresponds to a likelihood of 7.00 bits per image and neuron (printed value above the bar). The effect of the parameter matching (grey bars) is more emphasized and suggests that the largest performance gain can be achieved in future models by improving the prediction of the parameter q. We performed additional analyses on multiple datasets to show how NInGa facilitates model comparison across different datasets (see Appendix J). \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"0259c17f-6738-4e7b-b174-490ba19b3cda\"]],[null,null,0.7002078526824659,[[null,4292,5178]],[[[4292,5178,[[[4292,5178,[\"trial-to-trial fluctuations. Therefore, model correlation is often normalized by an upper bound oracle estimator [19; 16], which is commonly obtained by computing point estimates of the conditional mean using the responses to repeated presentations of the same stimulus. For a likelihood-based metric, a similar normalization to a bounded and interpretable scale would be desirable, especially for: 1) Assessing whether a model has achieved its “best possible” performance for a given dataset, and 2) comparing models that are trained on different datasets, which can exhibit different levels of achievable performance. To this end, one can use Normalized Information Gain (NInGa) [14], which uses an estimate of both upper and lower bound, to put the likelihood between two meaningful values. However, the challenge lies in how these bounds can be obtained for noisy neural responses. \"]]]]]]],[[[\"676bb0b5-6ae7-4e29-af86-c3cd083acd65\"],\"95368c85-1dbb-425c-be35-5ec04d99a1ed\"]],[\"02134707-380d-406f-b904-c9ce42059031\"]]],[[[null,0,149],[0,1]],[[null,150,381],[2]],[[null,381,655],[0,2]],[[null,733,733],[1,2]],[[null,733,933],[2]]],[[\"What is NInGa's primary purpose?\",\"What is a \\\"Gold Standard\\\" model?\",\"How does Bayesian GS improve estimates?\"]]]"]]
59
[["di",6908],["af.httprm",6908,"8373335844251350576",53]]
27
[["e",7,null,null,35616]]
