)]}'

257
[["wrb.fr",null,"[[\"**\",null,[\"3024ed14-289d-4163-a2c2-ad1198dbe204\",\"53627997-b228-4045-93ae-623842f5371b\",1660633321],null,[[]]],null,null,[[\"What is reporting bias?\",\"Name five knowledge dimensions.\",\"What is the VERBPHYSICS dataset?\"]]]"]]
12980
[["wrb.fr",null,"[[\"**Action frames** represent the physical implications of action verbs when applied to object pairs, viewed through a frame-centric perspective [1, 2]. They capture the implied knowledge a verb holds along a specific physical attribute (such as size, weight, strength, rigidness, or speed) between any two objects that might be used within that frame [3, 4]. These frames are enumerated for each verb using syntactic patterns that extract core components of an action, such as the subject (agent), direct object (theme), and prepositional object (goal) [5].\",null,[\"3024ed14-289d-4163-a2c2-ad1198dbe204\",\"53627997-b228-4045-93ae-623842f5371b\",1660633321],null,[[null,[[[\"49e678ee-f68c-4ac0-a6aa-406df82531b7\"],[null,14,138]],[[\"a89abdb8-7205-42d9-a853-da91b2da6054\"],[null,14,138]],[[\"4e127932-d51b-4bdd-9e3a-0a4bc516f0f5\"],[null,138,338]],[[\"f3ea5e93-caf1-4cf2-a2fa-26f1a195797a\"],[null,138,338]],[[\"70fdbb7b-9890-4092-a0e3-db9c51fcdf02\"],[null,338,533]]]],null,null,[[[\"49e678ee-f68c-4ac0-a6aa-406df82531b7\"],[null,null,0.6212294663428691,[[null,7404,7909]],[[[7404,7407,[[[7404,7407,[\"267\"]]]]],[7407,7424,[[[7407,7424,[\"approx max width \"]]]]],[7424,7431,[[[7424,7431,[\"action \"]]]]],[7431,7450,[[[7431,7450,[\"theme Iagent,theme \"]]]]],[7450,7473,[[[7450,7473,[\"agent goal Itheme,goal \",[null,true]]]]]],[7473,7485,[[[7473,7485,[\"Iagent,goal \"]]]]],[7485,7505,[[[7485,7505,[\"“He threw the ball” \"]]]]],[7505,7518,[[[7505,7518,[\"Iagent,theme \"]]]]],[7518,7528,[[[7518,7528,[\"x threw y \"]]]]],[7528,7598,[[[7528,7598,[\"⇒ x  is larger than  y ⇒ x  is heavier than  y ⇒ x  is slower than  y \",[null,true]]]]]],[7598,7625,[[[7598,7625,[\"“We walked into the house” \"]]]]],[7625,7641,[[[7625,7641,[\"x walked into y \"]]]]],[7641,7712,[[[7641,7712,[\"⇒ x  is smaller than  y ⇒ x  is lighter than  y ⇒ x  is faster than  y \",[null,true]]]]]],[7712,7758,[[[7712,7758,[\"Iagent,goal “I squashed the bug with my boot” \",[null,true]]]]]],[7758,7776,[[[7758,7776,[\"squashed x with y \"]]]]],[7776,7824,[[[7776,7824,[\"⇒ x  is smaller than  y ⇒ x  is lighter than  y \",[null,true]]]]]],[7824,7847,[[[7824,7847,[\"⇒ x  is weaker than  y \"]]]]],[7847,7859,[[[7847,7859,[\"Itheme,goal \"]]]]],[7859,7886,[[[7859,7886,[\"⇒ x  is less rigid than  y \"]]]]],[7886,7909,[[[7886,7909,[\"⇒ x  is slower than  y \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"49e678ee-f68c-4ac0-a6aa-406df82531b7\"]]],[[\"a89abdb8-7205-42d9-a853-da91b2da6054\"],[null,null,0.6697063249661586,[[null,9863,10528]],[[[9863,9904,[[[9863,9904,[\"vt . We use this notation going forward. \"]]]]],[9904,10528,[[[9904,10528,[\"Frame Perspective on Verb Implications: Fig-ure 2 illustrates the frame-centric view of physical implication knowledge we aim to learn. Impor-tantly, the key insight of our work is inspired by Fillmore’s original manuscript on frame seman-tics (Fillmore, 1976). Fillmore has argued that “frames”—the contexts in which utterances are situated—should be considered as a third primi-tive of describing a language, along with a gram-mar and lexicon. While existing frame annotations such as FrameNet (Baker et al., 1998), PropBank (Palmer et al., 2005), and VerbNet (Kipper et al., 2000) provide rich frame knowledge associated \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"a89abdb8-7205-42d9-a853-da91b2da6054\"]]],[[\"4e127932-d51b-4bdd-9e3a-0a4bc516f0f5\"],[null,null,0.***************,[[null,8963,9863]],[[[8963,9103,[[[8963,9103,[\"v to denote the impli-cation of the action verb v when applied over its arguments x and y with respect to a knowledge di-mension a so that: \"]]]]],[9103,9152,[[[9103,9152,[\"P(F size threw \\u003d \\u003e) :\\u003d P(“x threw y”⇒ x \\u003esize y) \"]]]]],[9152,9198,[[[9152,9198,[\"P(Fwgt threw \\u003d \\u003e) :\\u003d P(“x threw y”⇒ x \\u003ewgt y) \"]]]]],[9198,9248,[[[9198,9248,[\"where the range of F size threw is {\\u003e, \\u003c,'}. Intu-\"]]]]],[9248,9307,[[[9248,9307,[\"itively, F size threw represents the likely first order re-\"]]]]],[9307,9380,[[[9307,9380,[\"lation implied by “throw” over ungrounded (i.e., variable) object pairs. \"]]]]],[9380,9863,[[[9380,9863,[\"The above definition assumes that there is only a single implication relation for any given verb with respect to a specific knowledge dimension. This is generally not true, since a verb, especially a common action verb, can often invoke a number of different frames according to frame semantics (Fillmore, 1976). Thus, given a number of differ-ent frame relations v1...vT associated with a verb v, we define random variables F with respect to a specific frame relation vt, i.e., F a \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"4e127932-d51b-4bdd-9e3a-0a4bc516f0f5\"]]],[[\"f3ea5e93-caf1-4cf2-a2fa-26f1a195797a\"],[null,null,0.6847871365205493,[[null,17199,17885]],[[[17199,17316,[[[17199,17316,[\"x,y, the value represents the belief about the relation be-tween x and y along the attribute a. For a frame node F a \"]]]]],[17316,17455,[[[17316,17455,[\"vt , the value represents the belief about the relation along the attribute a between any two ob-jects that might be used in the frame vt. \"]]]]],[17455,17544,[[[17455,17544,[\"We denote the sets of all object pair and frame random variables O and F , respectively. \"]]]]],[17544,17576,[[[17544,17576,[\"4.2 Action–Object Compatibility \"]]]]],[17576,17885,[[[17576,17885,[\"The key aspect of our work is to reason about two types of knowledge simultaneously: relative knowledge of grounded object pairs, and implica-tions of actions related to those objects. Thus we connect the verb subgraphs and object subgraphs through selectional preference factors ψs between two such nodes Oa \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"f3ea5e93-caf1-4cf2-a2fa-26f1a195797a\"]]],[[\"70fdbb7b-9890-4092-a0e3-db9c51fcdf02\"],[null,null,0.7322107859813535,[[null,11329,12057]],[[[11329,12057,[[[11329,12057,[\"Action Frames: Figure 2 illustrates examples of action frame relations. Because we consider implications over pairwise argument relations for each frame, there are sometimes multiple frame relations we consider for a single frame. To enu-merate action frame relations for each verb, we use syntactic patterns based on dependency parse by extracting the core components (subject, verb, di-rect object, prepositional object) of an action, then map the subject to an agent, the direct object to a theme, and the prepositional object to a goal.3 For those frames that involve an argument in a prepo-sitional phrase, we create a separate frame for each preposition based on the statistics observed in the Google Syntax Ngram corpus. \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"70fdbb7b-9890-4092-a0e3-db9c51fcdf02\"]]]]]],[[null,null,0.6212294663428691,[[null,7404,7909]],[[[7404,7407,[[[7404,7407,[\"267\"]]]]],[7407,7424,[[[7407,7424,[\"approx max width \"]]]]],[7424,7431,[[[7424,7431,[\"action \"]]]]],[7431,7450,[[[7431,7450,[\"theme Iagent,theme \"]]]]],[7450,7473,[[[7450,7473,[\"agent goal Itheme,goal \",[null,true]]]]]],[7473,7485,[[[7473,7485,[\"Iagent,goal \"]]]]],[7485,7505,[[[7485,7505,[\"“He threw the ball” \"]]]]],[7505,7518,[[[7505,7518,[\"Iagent,theme \"]]]]],[7518,7528,[[[7518,7528,[\"x threw y \"]]]]],[7528,7598,[[[7528,7598,[\"⇒ x  is larger than  y ⇒ x  is heavier than  y ⇒ x  is slower than  y \",[null,true]]]]]],[7598,7625,[[[7598,7625,[\"“We walked into the house” \"]]]]],[7625,7641,[[[7625,7641,[\"x walked into y \"]]]]],[7641,7712,[[[7641,7712,[\"⇒ x  is smaller than  y ⇒ x  is lighter than  y ⇒ x  is faster than  y \",[null,true]]]]]],[7712,7758,[[[7712,7758,[\"Iagent,goal “I squashed the bug with my boot” \",[null,true]]]]]],[7758,7776,[[[7758,7776,[\"squashed x with y \"]]]]],[7776,7824,[[[7776,7824,[\"⇒ x  is smaller than  y ⇒ x  is lighter than  y \",[null,true]]]]]],[7824,7847,[[[7824,7847,[\"⇒ x  is weaker than  y \"]]]]],[7847,7859,[[[7847,7859,[\"Itheme,goal \"]]]]],[7859,7886,[[[7859,7886,[\"⇒ x  is less rigid than  y \"]]]]],[7886,7909,[[[7886,7909,[\"⇒ x  is slower than  y \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"49e678ee-f68c-4ac0-a6aa-406df82531b7\"]],[null,null,0.6697063249661586,[[null,9863,10528]],[[[9863,9904,[[[9863,9904,[\"vt . We use this notation going forward. \"]]]]],[9904,10528,[[[9904,10528,[\"Frame Perspective on Verb Implications: Fig-ure 2 illustrates the frame-centric view of physical implication knowledge we aim to learn. Impor-tantly, the key insight of our work is inspired by Fillmore’s original manuscript on frame seman-tics (Fillmore, 1976). Fillmore has argued that “frames”—the contexts in which utterances are situated—should be considered as a third primi-tive of describing a language, along with a gram-mar and lexicon. While existing frame annotations such as FrameNet (Baker et al., 1998), PropBank (Palmer et al., 2005), and VerbNet (Kipper et al., 2000) provide rich frame knowledge associated \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"a89abdb8-7205-42d9-a853-da91b2da6054\"]],[null,null,0.***************,[[null,8963,9863]],[[[8963,9103,[[[8963,9103,[\"v to denote the impli-cation of the action verb v when applied over its arguments x and y with respect to a knowledge di-mension a so that: \"]]]]],[9103,9152,[[[9103,9152,[\"P(F size threw \\u003d \\u003e) :\\u003d P(“x threw y”⇒ x \\u003esize y) \"]]]]],[9152,9198,[[[9152,9198,[\"P(Fwgt threw \\u003d \\u003e) :\\u003d P(“x threw y”⇒ x \\u003ewgt y) \"]]]]],[9198,9248,[[[9198,9248,[\"where the range of F size threw is {\\u003e, \\u003c,'}. Intu-\"]]]]],[9248,9307,[[[9248,9307,[\"itively, F size threw represents the likely first order re-\"]]]]],[9307,9380,[[[9307,9380,[\"lation implied by “throw” over ungrounded (i.e., variable) object pairs. \"]]]]],[9380,9863,[[[9380,9863,[\"The above definition assumes that there is only a single implication relation for any given verb with respect to a specific knowledge dimension. This is generally not true, since a verb, especially a common action verb, can often invoke a number of different frames according to frame semantics (Fillmore, 1976). Thus, given a number of differ-ent frame relations v1...vT associated with a verb v, we define random variables F with respect to a specific frame relation vt, i.e., F a \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"4e127932-d51b-4bdd-9e3a-0a4bc516f0f5\"]],[null,null,0.6847871365205493,[[null,17199,17885]],[[[17199,17316,[[[17199,17316,[\"x,y, the value represents the belief about the relation be-tween x and y along the attribute a. For a frame node F a \"]]]]],[17316,17455,[[[17316,17455,[\"vt , the value represents the belief about the relation along the attribute a between any two ob-jects that might be used in the frame vt. \"]]]]],[17455,17544,[[[17455,17544,[\"We denote the sets of all object pair and frame random variables O and F , respectively. \"]]]]],[17544,17576,[[[17544,17576,[\"4.2 Action–Object Compatibility \"]]]]],[17576,17885,[[[17576,17885,[\"The key aspect of our work is to reason about two types of knowledge simultaneously: relative knowledge of grounded object pairs, and implica-tions of actions related to those objects. Thus we connect the verb subgraphs and object subgraphs through selectional preference factors ψs between two such nodes Oa \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"f3ea5e93-caf1-4cf2-a2fa-26f1a195797a\"]],[null,null,0.7322107859813535,[[null,11329,12057]],[[[11329,12057,[[[11329,12057,[\"Action Frames: Figure 2 illustrates examples of action frame relations. Because we consider implications over pairwise argument relations for each frame, there are sometimes multiple frame relations we consider for a single frame. To enu-merate action frame relations for each verb, we use syntactic patterns based on dependency parse by extracting the core components (subject, verb, di-rect object, prepositional object) of an action, then map the subject to an agent, the direct object to a theme, and the prepositional object to a goal.3 For those frames that involve an argument in a prepo-sitional phrase, we create a separate frame for each preposition based on the statistics observed in the Google Syntax Ngram corpus. \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"70fdbb7b-9890-4092-a0e3-db9c51fcdf02\"]]],[[[null,14,138],[0,1]],[[null,138,338],[2,3]],[[null,338,533],[4]]],[[\"What is reporting bias?\",\"Name five knowledge dimensions.\",\"What is the VERBPHYSICS dataset?\"]]]"]]
13560
[["wrb.fr",null,"[[\"**Action frames** represent the physical implications of action verbs when applied to object pairs, viewed through a frame-centric perspective [1, 2]. They capture the implied knowledge a verb holds along a specific physical attribute (such as size, weight, strength, rigidness, or speed) between any two objects that might be used within that frame [3, 4]. These frames are enumerated for each verb using syntactic patterns that extract core components of an action, such as the subject (agent), direct object (theme), and prepositional object (goal) [5].\",null,[\"3024ed14-289d-4163-a2c2-ad1198dbe204\",\"53627997-b228-4045-93ae-623842f5371b\",1660633321],null,[[[[0,534,[[[0,13,[\"Action frames\",[true]]],[13,534,[\" represent the physical implications of action verbs when applied to object pairs, viewed through a frame-centric perspective. They capture the implied knowledge a verb holds along a specific physical attribute (such as size, weight, strength, rigidness, or speed) between any two objects that might be used within that frame. These frames are enumerated for each verb using syntactic patterns that extract core components of an action, such as the subject (agent), direct object (theme), and prepositional object (goal).\"]]]]]],[[[\"49e678ee-f68c-4ac0-a6aa-406df82531b7\"],[null,14,138]],[[\"a89abdb8-7205-42d9-a853-da91b2da6054\"],[null,14,138]],[[\"4e127932-d51b-4bdd-9e3a-0a4bc516f0f5\"],[null,138,338]],[[\"f3ea5e93-caf1-4cf2-a2fa-26f1a195797a\"],[null,138,338]],[[\"70fdbb7b-9890-4092-a0e3-db9c51fcdf02\"],[null,338,533]]]],null,null,[[[\"49e678ee-f68c-4ac0-a6aa-406df82531b7\"],[null,null,0.6212294663428691,[[null,7404,7909]],[[[7404,7407,[[[7404,7407,[\"267\"]]]]],[7407,7424,[[[7407,7424,[\"approx max width \"]]]]],[7424,7431,[[[7424,7431,[\"action \"]]]]],[7431,7450,[[[7431,7450,[\"theme Iagent,theme \"]]]]],[7450,7473,[[[7450,7473,[\"agent goal Itheme,goal \",[null,true]]]]]],[7473,7485,[[[7473,7485,[\"Iagent,goal \"]]]]],[7485,7505,[[[7485,7505,[\"“He threw the ball” \"]]]]],[7505,7518,[[[7505,7518,[\"Iagent,theme \"]]]]],[7518,7528,[[[7518,7528,[\"x threw y \"]]]]],[7528,7598,[[[7528,7598,[\"⇒ x  is larger than  y ⇒ x  is heavier than  y ⇒ x  is slower than  y \",[null,true]]]]]],[7598,7625,[[[7598,7625,[\"“We walked into the house” \"]]]]],[7625,7641,[[[7625,7641,[\"x walked into y \"]]]]],[7641,7712,[[[7641,7712,[\"⇒ x  is smaller than  y ⇒ x  is lighter than  y ⇒ x  is faster than  y \",[null,true]]]]]],[7712,7758,[[[7712,7758,[\"Iagent,goal “I squashed the bug with my boot” \",[null,true]]]]]],[7758,7776,[[[7758,7776,[\"squashed x with y \"]]]]],[7776,7824,[[[7776,7824,[\"⇒ x  is smaller than  y ⇒ x  is lighter than  y \",[null,true]]]]]],[7824,7847,[[[7824,7847,[\"⇒ x  is weaker than  y \"]]]]],[7847,7859,[[[7847,7859,[\"Itheme,goal \"]]]]],[7859,7886,[[[7859,7886,[\"⇒ x  is less rigid than  y \"]]]]],[7886,7909,[[[7886,7909,[\"⇒ x  is slower than  y \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"49e678ee-f68c-4ac0-a6aa-406df82531b7\"]]],[[\"a89abdb8-7205-42d9-a853-da91b2da6054\"],[null,null,0.6697063249661586,[[null,9863,10528]],[[[9863,9904,[[[9863,9904,[\"vt . We use this notation going forward. \"]]]]],[9904,10528,[[[9904,10528,[\"Frame Perspective on Verb Implications: Fig-ure 2 illustrates the frame-centric view of physical implication knowledge we aim to learn. Impor-tantly, the key insight of our work is inspired by Fillmore’s original manuscript on frame seman-tics (Fillmore, 1976). Fillmore has argued that “frames”—the contexts in which utterances are situated—should be considered as a third primi-tive of describing a language, along with a gram-mar and lexicon. While existing frame annotations such as FrameNet (Baker et al., 1998), PropBank (Palmer et al., 2005), and VerbNet (Kipper et al., 2000) provide rich frame knowledge associated \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"a89abdb8-7205-42d9-a853-da91b2da6054\"]]],[[\"4e127932-d51b-4bdd-9e3a-0a4bc516f0f5\"],[null,null,0.***************,[[null,8963,9863]],[[[8963,9103,[[[8963,9103,[\"v to denote the impli-cation of the action verb v when applied over its arguments x and y with respect to a knowledge di-mension a so that: \"]]]]],[9103,9152,[[[9103,9152,[\"P(F size threw \\u003d \\u003e) :\\u003d P(“x threw y”⇒ x \\u003esize y) \"]]]]],[9152,9198,[[[9152,9198,[\"P(Fwgt threw \\u003d \\u003e) :\\u003d P(“x threw y”⇒ x \\u003ewgt y) \"]]]]],[9198,9248,[[[9198,9248,[\"where the range of F size threw is {\\u003e, \\u003c,'}. Intu-\"]]]]],[9248,9307,[[[9248,9307,[\"itively, F size threw represents the likely first order re-\"]]]]],[9307,9380,[[[9307,9380,[\"lation implied by “throw” over ungrounded (i.e., variable) object pairs. \"]]]]],[9380,9863,[[[9380,9863,[\"The above definition assumes that there is only a single implication relation for any given verb with respect to a specific knowledge dimension. This is generally not true, since a verb, especially a common action verb, can often invoke a number of different frames according to frame semantics (Fillmore, 1976). Thus, given a number of differ-ent frame relations v1...vT associated with a verb v, we define random variables F with respect to a specific frame relation vt, i.e., F a \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"4e127932-d51b-4bdd-9e3a-0a4bc516f0f5\"]]],[[\"f3ea5e93-caf1-4cf2-a2fa-26f1a195797a\"],[null,null,0.6847871365205493,[[null,17199,17885]],[[[17199,17316,[[[17199,17316,[\"x,y, the value represents the belief about the relation be-tween x and y along the attribute a. For a frame node F a \"]]]]],[17316,17455,[[[17316,17455,[\"vt , the value represents the belief about the relation along the attribute a between any two ob-jects that might be used in the frame vt. \"]]]]],[17455,17544,[[[17455,17544,[\"We denote the sets of all object pair and frame random variables O and F , respectively. \"]]]]],[17544,17576,[[[17544,17576,[\"4.2 Action–Object Compatibility \"]]]]],[17576,17885,[[[17576,17885,[\"The key aspect of our work is to reason about two types of knowledge simultaneously: relative knowledge of grounded object pairs, and implica-tions of actions related to those objects. Thus we connect the verb subgraphs and object subgraphs through selectional preference factors ψs between two such nodes Oa \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"f3ea5e93-caf1-4cf2-a2fa-26f1a195797a\"]]],[[\"70fdbb7b-9890-4092-a0e3-db9c51fcdf02\"],[null,null,0.7322107859813535,[[null,11329,12057]],[[[11329,12057,[[[11329,12057,[\"Action Frames: Figure 2 illustrates examples of action frame relations. Because we consider implications over pairwise argument relations for each frame, there are sometimes multiple frame relations we consider for a single frame. To enu-merate action frame relations for each verb, we use syntactic patterns based on dependency parse by extracting the core components (subject, verb, di-rect object, prepositional object) of an action, then map the subject to an agent, the direct object to a theme, and the prepositional object to a goal.3 For those frames that involve an argument in a prepo-sitional phrase, we create a separate frame for each preposition based on the statistics observed in the Google Syntax Ngram corpus. \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"70fdbb7b-9890-4092-a0e3-db9c51fcdf02\"]]]]]],[[null,null,0.6212294663428691,[[null,7404,7909]],[[[7404,7407,[[[7404,7407,[\"267\"]]]]],[7407,7424,[[[7407,7424,[\"approx max width \"]]]]],[7424,7431,[[[7424,7431,[\"action \"]]]]],[7431,7450,[[[7431,7450,[\"theme Iagent,theme \"]]]]],[7450,7473,[[[7450,7473,[\"agent goal Itheme,goal \",[null,true]]]]]],[7473,7485,[[[7473,7485,[\"Iagent,goal \"]]]]],[7485,7505,[[[7485,7505,[\"“He threw the ball” \"]]]]],[7505,7518,[[[7505,7518,[\"Iagent,theme \"]]]]],[7518,7528,[[[7518,7528,[\"x threw y \"]]]]],[7528,7598,[[[7528,7598,[\"⇒ x  is larger than  y ⇒ x  is heavier than  y ⇒ x  is slower than  y \",[null,true]]]]]],[7598,7625,[[[7598,7625,[\"“We walked into the house” \"]]]]],[7625,7641,[[[7625,7641,[\"x walked into y \"]]]]],[7641,7712,[[[7641,7712,[\"⇒ x  is smaller than  y ⇒ x  is lighter than  y ⇒ x  is faster than  y \",[null,true]]]]]],[7712,7758,[[[7712,7758,[\"Iagent,goal “I squashed the bug with my boot” \",[null,true]]]]]],[7758,7776,[[[7758,7776,[\"squashed x with y \"]]]]],[7776,7824,[[[7776,7824,[\"⇒ x  is smaller than  y ⇒ x  is lighter than  y \",[null,true]]]]]],[7824,7847,[[[7824,7847,[\"⇒ x  is weaker than  y \"]]]]],[7847,7859,[[[7847,7859,[\"Itheme,goal \"]]]]],[7859,7886,[[[7859,7886,[\"⇒ x  is less rigid than  y \"]]]]],[7886,7909,[[[7886,7909,[\"⇒ x  is slower than  y \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"49e678ee-f68c-4ac0-a6aa-406df82531b7\"]],[null,null,0.6697063249661586,[[null,9863,10528]],[[[9863,9904,[[[9863,9904,[\"vt . We use this notation going forward. \"]]]]],[9904,10528,[[[9904,10528,[\"Frame Perspective on Verb Implications: Fig-ure 2 illustrates the frame-centric view of physical implication knowledge we aim to learn. Impor-tantly, the key insight of our work is inspired by Fillmore’s original manuscript on frame seman-tics (Fillmore, 1976). Fillmore has argued that “frames”—the contexts in which utterances are situated—should be considered as a third primi-tive of describing a language, along with a gram-mar and lexicon. While existing frame annotations such as FrameNet (Baker et al., 1998), PropBank (Palmer et al., 2005), and VerbNet (Kipper et al., 2000) provide rich frame knowledge associated \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"a89abdb8-7205-42d9-a853-da91b2da6054\"]],[null,null,0.***************,[[null,8963,9863]],[[[8963,9103,[[[8963,9103,[\"v to denote the impli-cation of the action verb v when applied over its arguments x and y with respect to a knowledge di-mension a so that: \"]]]]],[9103,9152,[[[9103,9152,[\"P(F size threw \\u003d \\u003e) :\\u003d P(“x threw y”⇒ x \\u003esize y) \"]]]]],[9152,9198,[[[9152,9198,[\"P(Fwgt threw \\u003d \\u003e) :\\u003d P(“x threw y”⇒ x \\u003ewgt y) \"]]]]],[9198,9248,[[[9198,9248,[\"where the range of F size threw is {\\u003e, \\u003c,'}. Intu-\"]]]]],[9248,9307,[[[9248,9307,[\"itively, F size threw represents the likely first order re-\"]]]]],[9307,9380,[[[9307,9380,[\"lation implied by “throw” over ungrounded (i.e., variable) object pairs. \"]]]]],[9380,9863,[[[9380,9863,[\"The above definition assumes that there is only a single implication relation for any given verb with respect to a specific knowledge dimension. This is generally not true, since a verb, especially a common action verb, can often invoke a number of different frames according to frame semantics (Fillmore, 1976). Thus, given a number of differ-ent frame relations v1...vT associated with a verb v, we define random variables F with respect to a specific frame relation vt, i.e., F a \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"4e127932-d51b-4bdd-9e3a-0a4bc516f0f5\"]],[null,null,0.6847871365205493,[[null,17199,17885]],[[[17199,17316,[[[17199,17316,[\"x,y, the value represents the belief about the relation be-tween x and y along the attribute a. For a frame node F a \"]]]]],[17316,17455,[[[17316,17455,[\"vt , the value represents the belief about the relation along the attribute a between any two ob-jects that might be used in the frame vt. \"]]]]],[17455,17544,[[[17455,17544,[\"We denote the sets of all object pair and frame random variables O and F , respectively. \"]]]]],[17544,17576,[[[17544,17576,[\"4.2 Action–Object Compatibility \"]]]]],[17576,17885,[[[17576,17885,[\"The key aspect of our work is to reason about two types of knowledge simultaneously: relative knowledge of grounded object pairs, and implica-tions of actions related to those objects. Thus we connect the verb subgraphs and object subgraphs through selectional preference factors ψs between two such nodes Oa \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"f3ea5e93-caf1-4cf2-a2fa-26f1a195797a\"]],[null,null,0.7322107859813535,[[null,11329,12057]],[[[11329,12057,[[[11329,12057,[\"Action Frames: Figure 2 illustrates examples of action frame relations. Because we consider implications over pairwise argument relations for each frame, there are sometimes multiple frame relations we consider for a single frame. To enu-merate action frame relations for each verb, we use syntactic patterns based on dependency parse by extracting the core components (subject, verb, di-rect object, prepositional object) of an action, then map the subject to an agent, the direct object to a theme, and the prepositional object to a goal.3 For those frames that involve an argument in a prepo-sitional phrase, we create a separate frame for each preposition based on the statistics observed in the Google Syntax Ngram corpus. \"]]]]]]],[[[\"c1d8e0b6-9208-493d-8bf1-1ab3a6022faf\"],\"1bf05688-cefd-4826-9552-bef9c8a3dbd8\"]],[\"70fdbb7b-9890-4092-a0e3-db9c51fcdf02\"]]],[[[null,14,138],[0,1]],[[null,138,338],[2,3]],[[null,338,533],[4]]],[[\"What is reporting bias?\",\"Name five knowledge dimensions.\",\"What is the VERBPHYSICS dataset?\"]]]"]]
60
[["di",4402],["af.httprm",4401,"-7700143133730595172",57]]
27
[["e",6,null,null,27159]]
