)]}'

2959
[["wrb.fr",null,"[[\"Images were excluded, meaning their radius (R') was considered \\\"too big,\\\" if **R′ was more than 33% bigger than the certified radius R of g** [1].\",null,[\"32261cb6-8293-4553-ae54-ac56e49ced0d\",\"485dba58-5ea8-4ca5-b4f0-8b8e74cff6cd\",3675337728],null,[[null,[[[\"ec1fb9d2-4b81-412e-8edb-bc402e0307a1\"],[null,137,137]]]],null,null,[[[\"ec1fb9d2-4b81-412e-8edb-bc402e0307a1\"],[null,null,0.7384786227843471,[[null,59423,60332]],[[[59423,59423,[]],[59423,59424,[[[59423,59424,[\" \"]]]]],[59424,60332,[[[59424,60332,[\"Selecting R′ In this paragraph, we detail how the adversarial distance R′ is chosen. We choose it heuristically, for each individual x. For each x, we select R′ by searching for the smallest adversarial distance on f , where at least 10% of 500 attacks withA on f succeed but exclude images x, whose R′ is more than 33% bigger than the certified radius R of g. This procedure allows us to select the adversarial distance R′, that is neither too small, nor too big, which is important to avoid making the problem trivial, as outlined in Section 2.3. In particular, for this choice of R′ the smoothed classifier g is likely to be attackable, since f contains enough attacks for a high density input adversarial region to exist. On top of that, the exclusion of images x with too big adversarial distance R′ experimentally allowed us to exclude trivial attacks, for which x is attackable for most classes on g. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"ec1fb9d2-4b81-412e-8edb-bc402e0307a1\"]]]]]],[[null,null,0.7384786227843471,[[null,59423,60332]],[[[59423,59423,[]],[59423,59424,[[[59423,59424,[\" \"]]]]],[59424,60332,[[[59424,60332,[\"Selecting R′ In this paragraph, we detail how the adversarial distance R′ is chosen. We choose it heuristically, for each individual x. For each x, we select R′ by searching for the smallest adversarial distance on f , where at least 10% of 500 attacks withA on f succeed but exclude images x, whose R′ is more than 33% bigger than the certified radius R of g. This procedure allows us to select the adversarial distance R′, that is neither too small, nor too big, which is important to avoid making the problem trivial, as outlined in Section 2.3. In particular, for this choice of R′ the smoothed classifier g is likely to be attackable, since f contains enough attacks for a high density input adversarial region to exist. On top of that, the exclusion of images x with too big adversarial distance R′ experimentally allowed us to exclude trivial attacks, for which x is attackable for most classes on g. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"ec1fb9d2-4b81-412e-8edb-bc402e0307a1\"]]],[[[null,137,137],[0]]],[[\"What is PARADE's primary purpose?\",\"What is a provably robust adversarial example?\",\"What is randomized smoothing's purpose?\"]]]"]]
3166
[["wrb.fr",null,"[[\"Images were excluded, meaning their radius (R') was considered \\\"too big,\\\" if **R′ was more than 33% bigger than the certified radius R of g** [1].\",null,[\"32261cb6-8293-4553-ae54-ac56e49ced0d\",\"485dba58-5ea8-4ca5-b4f0-8b8e74cff6cd\",3675337728],null,[[[[0,138,[[[0,77,[\"Images were excluded, meaning their radius (R') was considered \\\"too big,\\\" if \"]],[77,137,[\"R′ was more than 33% bigger than the certified radius R of g\",[true]]],[137,138,[\".\"]]]]]],[[[\"ec1fb9d2-4b81-412e-8edb-bc402e0307a1\"],[null,137,137]]]],null,null,[[[\"ec1fb9d2-4b81-412e-8edb-bc402e0307a1\"],[null,null,0.7384786227843471,[[null,59423,60332]],[[[59423,59423,[]],[59423,59424,[[[59423,59424,[\" \"]]]]],[59424,60332,[[[59424,60332,[\"Selecting R′ In this paragraph, we detail how the adversarial distance R′ is chosen. We choose it heuristically, for each individual x. For each x, we select R′ by searching for the smallest adversarial distance on f , where at least 10% of 500 attacks withA on f succeed but exclude images x, whose R′ is more than 33% bigger than the certified radius R of g. This procedure allows us to select the adversarial distance R′, that is neither too small, nor too big, which is important to avoid making the problem trivial, as outlined in Section 2.3. In particular, for this choice of R′ the smoothed classifier g is likely to be attackable, since f contains enough attacks for a high density input adversarial region to exist. On top of that, the exclusion of images x with too big adversarial distance R′ experimentally allowed us to exclude trivial attacks, for which x is attackable for most classes on g. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"ec1fb9d2-4b81-412e-8edb-bc402e0307a1\"]]]]]],[[null,null,0.7384786227843471,[[null,59423,60332]],[[[59423,59423,[]],[59423,59424,[[[59423,59424,[\" \"]]]]],[59424,60332,[[[59424,60332,[\"Selecting R′ In this paragraph, we detail how the adversarial distance R′ is chosen. We choose it heuristically, for each individual x. For each x, we select R′ by searching for the smallest adversarial distance on f , where at least 10% of 500 attacks withA on f succeed but exclude images x, whose R′ is more than 33% bigger than the certified radius R of g. This procedure allows us to select the adversarial distance R′, that is neither too small, nor too big, which is important to avoid making the problem trivial, as outlined in Section 2.3. In particular, for this choice of R′ the smoothed classifier g is likely to be attackable, since f contains enough attacks for a high density input adversarial region to exist. On top of that, the exclusion of images x with too big adversarial distance R′ experimentally allowed us to exclude trivial attacks, for which x is attackable for most classes on g. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"ec1fb9d2-4b81-412e-8edb-bc402e0307a1\"]]],[[[null,137,137],[0]]],[[\"What is PARADE's primary purpose?\",\"What is a provably robust adversarial example?\",\"What is randomized smoothing's purpose?\"]]]"]]
59
[["di",2937],["af.httprm",2937,"6924799068510025390",59]]
26
[["e",5,null,null,6290]]
