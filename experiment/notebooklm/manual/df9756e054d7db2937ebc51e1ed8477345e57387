)]}'

6689
[["wrb.fr",null,"[[\"The logical atom **\\\"nsubj:noun-verb-obj:noun\\\"** refers to a **double-link property** derived from Universal Dependencies treebanks [1]. It specifically denotes a grammatical construction where the **nominal subject (a noun)** precedes the **verb (the head)**, and the **object (a noun)** follows the verb [1, 2]. This represents the relative frequency of the **Subject-Verb-Object (SVO)** word order in a language for\",null,[\"8a75c4c9-bc3d-4a34-bc1a-18a616c7b216\",\"ee0ba998-0b9d-43fb-b3fa-22dbca6a239d\",*********],null,[[null,[[[\"cd53a4fd-a13f-4300-90b6-1f5c8ade23d1\"],[null,77,122]],[[\"cd53a4fd-a13f-4300-90b6-1f5c8ade23d1\"],[null,264,280]],[[\"6e373ba3-8e04-462a-9960-aa92954b587a\"],[null,264,280]]]],null,null,[[[\"cd53a4fd-a13f-4300-90b6-1f5c8ade23d1\"],[null,null,0.****************,[[null,9453,10235]],[[[9453,9482,[[[9453,9482,[\"v`verb-nsubj:noun-obj:noun \\u003d \"]]]]],[9482,9484,[[[9482,9484,[\"# \"]]]]],[9484,9503,[[[9484,9503,[\" VERB NOUN NOUN \"]]]]],[9503,9509,[[[9503,9509,[\"nsubj \"]]]]],[9509,9520,[[[9509,9520,[\"obj (`) \"]]]]],[9520,9522,[[[9520,9522,[\"# \"]]]]],[9522,9541,[[[9522,9541,[\" VERB NOUN NOUN \"]]]]],[9541,9547,[[[9541,9547,[\"nsubj \"]]]]],[9547,9569,[[[9547,9569,[\"obj (`) + . . .+ # \"]]]]],[9569,9588,[[[9569,9588,[\" NOUN NOUN VERB \"]]]]],[9588,9592,[[[9588,9592,[\"obj \"]]]]],[9592,9598,[[[9592,9598,[\"nsubj \"]]]]],[9598,9605,[[[9598,9605,[\"(`) \"]]]]],[9605,9630,[[[9605,9630,[\"︸ ︷︷ ︸ six possibilities \"]]]]],[9630,9633,[[[9630,9633,[\"(7)\"]]]]],[9633,9638,[[[9633,9638,[\"3993 \"]]]]],[9638,9943,[[[9638,9943,[\"Here, #[∗](`) returns how often the construction ∗ appears for `. The subscripts of v` represent the constructions: hyphens connect words, the head is represented by its part-of-speech tag, and the dependents are represented by their dependency relation and their part-of-speech tag, combined by a colon. \"]]]]],[9943,10235,[[[9943,10235,[\"2.2 Fuzzy logic The relative frequencies are in the interval [ 0, 1 ], hence our valuation function has to define logical connectives for a real-valued logic. A common example for real-valued logic is fuzzy logic (Zadeh, 1965), which defines negation, conjunction and disjunction as follows: \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"cd53a4fd-a13f-4300-90b6-1f5c8ade23d1\"]]],[[\"6e373ba3-8e04-462a-9960-aa92954b587a\"],[null,null,0.***************,[[null,19581,20542]],[[[19581,19971,[[[19581,19971,[\"7Addition is not a logical connective but used here to calculate the value of properties which cannot be directly extracted from the UD treebanks. For example, the degree to which a language has S-O order is the percentage occupied by V-S-O, S-V-O and S-O-V constructions. Thus, the value v`nsubj:noun-obj:noun can be calculated as v`verb-nsubj:noun-obj:noun + v`nsubj:noun-verb-obj:noun + \"]]]]],[19971,20542,[[[19971,20542,[\"v`nsubj:noun-obj:noun-verb. Addition is restricted to variables that are mutually exclusive, i.e. that are calculated with the same de-nominator (compare eq. (7)). That said, (out-of-logic) addition in our framework is comparable to addition of probabilities of disjoint events in probability theory; it is not as powerful and does not serve the same purpose as (in-logic) addition (“strong dis-junction”) in certain real-valued logics, e.g. Łukasiewicz logic (Łukasiewicz and Tarski (1930); cf. Bergmann (2008, p. 179)), where it is possible to add arbitrary variables. \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"6e373ba3-8e04-462a-9960-aa92954b587a\"]]]]]],[[null,null,0.****************,[[null,9453,10235]],[[[9453,9482,[[[9453,9482,[\"v`verb-nsubj:noun-obj:noun \\u003d \"]]]]],[9482,9484,[[[9482,9484,[\"# \"]]]]],[9484,9503,[[[9484,9503,[\" VERB NOUN NOUN \"]]]]],[9503,9509,[[[9503,9509,[\"nsubj \"]]]]],[9509,9520,[[[9509,9520,[\"obj (`) \"]]]]],[9520,9522,[[[9520,9522,[\"# \"]]]]],[9522,9541,[[[9522,9541,[\" VERB NOUN NOUN \"]]]]],[9541,9547,[[[9541,9547,[\"nsubj \"]]]]],[9547,9569,[[[9547,9569,[\"obj (`) + . . .+ # \"]]]]],[9569,9588,[[[9569,9588,[\" NOUN NOUN VERB \"]]]]],[9588,9592,[[[9588,9592,[\"obj \"]]]]],[9592,9598,[[[9592,9598,[\"nsubj \"]]]]],[9598,9605,[[[9598,9605,[\"(`) \"]]]]],[9605,9630,[[[9605,9630,[\"︸ ︷︷ ︸ six possibilities \"]]]]],[9630,9633,[[[9630,9633,[\"(7)\"]]]]],[9633,9638,[[[9633,9638,[\"3993 \"]]]]],[9638,9943,[[[9638,9943,[\"Here, #[∗](`) returns how often the construction ∗ appears for `. The subscripts of v` represent the constructions: hyphens connect words, the head is represented by its part-of-speech tag, and the dependents are represented by their dependency relation and their part-of-speech tag, combined by a colon. \"]]]]],[9943,10235,[[[9943,10235,[\"2.2 Fuzzy logic The relative frequencies are in the interval [ 0, 1 ], hence our valuation function has to define logical connectives for a real-valued logic. A common example for real-valued logic is fuzzy logic (Zadeh, 1965), which defines negation, conjunction and disjunction as follows: \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"cd53a4fd-a13f-4300-90b6-1f5c8ade23d1\"]],[null,null,0.***************,[[null,19581,20542]],[[[19581,19971,[[[19581,19971,[\"7Addition is not a logical connective but used here to calculate the value of properties which cannot be directly extracted from the UD treebanks. For example, the degree to which a language has S-O order is the percentage occupied by V-S-O, S-V-O and S-O-V constructions. Thus, the value v`nsubj:noun-obj:noun can be calculated as v`verb-nsubj:noun-obj:noun + v`nsubj:noun-verb-obj:noun + \"]]]]],[19971,20542,[[[19971,20542,[\"v`nsubj:noun-obj:noun-verb. Addition is restricted to variables that are mutually exclusive, i.e. that are calculated with the same de-nominator (compare eq. (7)). That said, (out-of-logic) addition in our framework is comparable to addition of probabilities of disjoint events in probability theory; it is not as powerful and does not serve the same purpose as (in-logic) addition (“strong dis-junction”) in certain real-valued logics, e.g. Łukasiewicz logic (Łukasiewicz and Tarski (1930); cf. Bergmann (2008, p. 179)), where it is possible to add arbitrary variables. \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"6e373ba3-8e04-462a-9960-aa92954b587a\"]]],[[[null,77,122],[0]],[[null,264,280],[0,1]]],[[\"How are typological universals modeled?\",\"What are the two property types?\",\"What is the weighting function's purpose?\"]]]"]]
6857
[["wrb.fr",null,"[[\"The logical atom **\\\"nsubj:noun-verb-obj:noun\\\"** refers to a **double-link property** derived from Universal Dependencies treebanks [1]. It specifically denotes a grammatical construction where the **nominal subject (a noun)** precedes the **verb (the head)**, and the **object (a noun)** follows the verb [1, 2]. This represents the relative frequency of the **Subject-Verb-Object (SVO)** word order in a language for constructions involving a verb, its nominal subject, and its object, all as nouns [2].\",null,[\"8a75c4c9-bc3d-4a34-bc1a-18a616c7b216\",\"ee0ba998-0b9d-43fb-b3fa-22dbca6a239d\",*********],null,[[null,[[[\"15b0c534-9140-42b5-b1ce-5940925dd9d5\"],[null,77,122]],[[\"15b0c534-9140-42b5-b1ce-5940925dd9d5\"],[null,264,280]],[[\"4d675eaf-9dec-4ac7-8bd3-69c3a1cc51a5\"],[null,264,280]],[[\"4d675eaf-9dec-4ac7-8bd3-69c3a1cc51a5\"],[null,354,464]]]],null,null,[[[\"15b0c534-9140-42b5-b1ce-5940925dd9d5\"],[null,null,0.****************,[[null,9453,10235]],[[[9453,9482,[[[9453,9482,[\"v`verb-nsubj:noun-obj:noun \\u003d \"]]]]],[9482,9484,[[[9482,9484,[\"# \"]]]]],[9484,9503,[[[9484,9503,[\" VERB NOUN NOUN \"]]]]],[9503,9509,[[[9503,9509,[\"nsubj \"]]]]],[9509,9520,[[[9509,9520,[\"obj (`) \"]]]]],[9520,9522,[[[9520,9522,[\"# \"]]]]],[9522,9541,[[[9522,9541,[\" VERB NOUN NOUN \"]]]]],[9541,9547,[[[9541,9547,[\"nsubj \"]]]]],[9547,9569,[[[9547,9569,[\"obj (`) + . . .+ # \"]]]]],[9569,9588,[[[9569,9588,[\" NOUN NOUN VERB \"]]]]],[9588,9592,[[[9588,9592,[\"obj \"]]]]],[9592,9598,[[[9592,9598,[\"nsubj \"]]]]],[9598,9605,[[[9598,9605,[\"(`) \"]]]]],[9605,9630,[[[9605,9630,[\"︸ ︷︷ ︸ six possibilities \"]]]]],[9630,9633,[[[9630,9633,[\"(7)\"]]]]],[9633,9638,[[[9633,9638,[\"3993 \"]]]]],[9638,9943,[[[9638,9943,[\"Here, #[∗](`) returns how often the construction ∗ appears for `. The subscripts of v` represent the constructions: hyphens connect words, the head is represented by its part-of-speech tag, and the dependents are represented by their dependency relation and their part-of-speech tag, combined by a colon. \"]]]]],[9943,10235,[[[9943,10235,[\"2.2 Fuzzy logic The relative frequencies are in the interval [ 0, 1 ], hence our valuation function has to define logical connectives for a real-valued logic. A common example for real-valued logic is fuzzy logic (Zadeh, 1965), which defines negation, conjunction and disjunction as follows: \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"15b0c534-9140-42b5-b1ce-5940925dd9d5\"]]],[[\"4d675eaf-9dec-4ac7-8bd3-69c3a1cc51a5\"],[null,null,0.***************,[[null,19581,20542]],[[[19581,19971,[[[19581,19971,[\"7Addition is not a logical connective but used here to calculate the value of properties which cannot be directly extracted from the UD treebanks. For example, the degree to which a language has S-O order is the percentage occupied by V-S-O, S-V-O and S-O-V constructions. Thus, the value v`nsubj:noun-obj:noun can be calculated as v`verb-nsubj:noun-obj:noun + v`nsubj:noun-verb-obj:noun + \"]]]]],[19971,20542,[[[19971,20542,[\"v`nsubj:noun-obj:noun-verb. Addition is restricted to variables that are mutually exclusive, i.e. that are calculated with the same de-nominator (compare eq. (7)). That said, (out-of-logic) addition in our framework is comparable to addition of probabilities of disjoint events in probability theory; it is not as powerful and does not serve the same purpose as (in-logic) addition (“strong dis-junction”) in certain real-valued logics, e.g. Łukasiewicz logic (Łukasiewicz and Tarski (1930); cf. Bergmann (2008, p. 179)), where it is possible to add arbitrary variables. \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"4d675eaf-9dec-4ac7-8bd3-69c3a1cc51a5\"]]]]]],[[null,null,0.****************,[[null,9453,10235]],[[[9453,9482,[[[9453,9482,[\"v`verb-nsubj:noun-obj:noun \\u003d \"]]]]],[9482,9484,[[[9482,9484,[\"# \"]]]]],[9484,9503,[[[9484,9503,[\" VERB NOUN NOUN \"]]]]],[9503,9509,[[[9503,9509,[\"nsubj \"]]]]],[9509,9520,[[[9509,9520,[\"obj (`) \"]]]]],[9520,9522,[[[9520,9522,[\"# \"]]]]],[9522,9541,[[[9522,9541,[\" VERB NOUN NOUN \"]]]]],[9541,9547,[[[9541,9547,[\"nsubj \"]]]]],[9547,9569,[[[9547,9569,[\"obj (`) + . . .+ # \"]]]]],[9569,9588,[[[9569,9588,[\" NOUN NOUN VERB \"]]]]],[9588,9592,[[[9588,9592,[\"obj \"]]]]],[9592,9598,[[[9592,9598,[\"nsubj \"]]]]],[9598,9605,[[[9598,9605,[\"(`) \"]]]]],[9605,9630,[[[9605,9630,[\"︸ ︷︷ ︸ six possibilities \"]]]]],[9630,9633,[[[9630,9633,[\"(7)\"]]]]],[9633,9638,[[[9633,9638,[\"3993 \"]]]]],[9638,9943,[[[9638,9943,[\"Here, #[∗](`) returns how often the construction ∗ appears for `. The subscripts of v` represent the constructions: hyphens connect words, the head is represented by its part-of-speech tag, and the dependents are represented by their dependency relation and their part-of-speech tag, combined by a colon. \"]]]]],[9943,10235,[[[9943,10235,[\"2.2 Fuzzy logic The relative frequencies are in the interval [ 0, 1 ], hence our valuation function has to define logical connectives for a real-valued logic. A common example for real-valued logic is fuzzy logic (Zadeh, 1965), which defines negation, conjunction and disjunction as follows: \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"15b0c534-9140-42b5-b1ce-5940925dd9d5\"]],[null,null,0.***************,[[null,19581,20542]],[[[19581,19971,[[[19581,19971,[\"7Addition is not a logical connective but used here to calculate the value of properties which cannot be directly extracted from the UD treebanks. For example, the degree to which a language has S-O order is the percentage occupied by V-S-O, S-V-O and S-O-V constructions. Thus, the value v`nsubj:noun-obj:noun can be calculated as v`verb-nsubj:noun-obj:noun + v`nsubj:noun-verb-obj:noun + \"]]]]],[19971,20542,[[[19971,20542,[\"v`nsubj:noun-obj:noun-verb. Addition is restricted to variables that are mutually exclusive, i.e. that are calculated with the same de-nominator (compare eq. (7)). That said, (out-of-logic) addition in our framework is comparable to addition of probabilities of disjoint events in probability theory; it is not as powerful and does not serve the same purpose as (in-logic) addition (“strong dis-junction”) in certain real-valued logics, e.g. Łukasiewicz logic (Łukasiewicz and Tarski (1930); cf. Bergmann (2008, p. 179)), where it is possible to add arbitrary variables. \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"4d675eaf-9dec-4ac7-8bd3-69c3a1cc51a5\"]]],[[[null,77,122],[0]],[[null,264,280],[0,1]],[[null,354,464],[1]]],[[\"How are typological universals modeled?\",\"What are the two property types?\",\"What is the weighting function's purpose?\"]]]"]]
7590
[["wrb.fr",null,"[[\"The logical atom **\\\"nsubj:noun-verb-obj:noun\\\"** refers to a **double-link property** derived from Universal Dependencies treebanks [1]. It specifically denotes a grammatical construction where the **nominal subject (a noun)** precedes the **verb (the head)**, and the **object (a noun)** follows the verb [1, 2]. This represents the relative frequency of the **Subject-Verb-Object (SVO)** word order in a language for constructions involving a verb, its nominal subject, and its object, all as nouns [2].\",null,[\"8a75c4c9-bc3d-4a34-bc1a-18a616c7b216\",\"ee0ba998-0b9d-43fb-b3fa-22dbca6a239d\",*********],null,[[[[0,465,[[[0,17,[\"The logical atom \"]],[17,43,[\"\\\"nsubj:noun-verb-obj:noun\\\"\",[true]]],[43,56,[\" refers to a \"]],[56,76,[\"double-link property\",[true]]],[76,185,[\" derived from Universal Dependencies treebanks. It specifically denotes a grammatical construction where the \"]],[185,209,[\"nominal subject (a noun)\",[true]]],[209,223,[\" precedes the \"]],[223,238,[\"verb (the head)\",[true]]],[238,248,[\", and the \"]],[248,263,[\"object (a noun)\",[true]]],[263,328,[\" follows the verb. This represents the relative frequency of the \"]],[328,353,[\"Subject-Verb-Object (SVO)\",[true]]],[353,465,[\" word order in a language for constructions involving a verb, its nominal subject, and its object, all as nouns.\"]]]]]],[[[\"15b0c534-9140-42b5-b1ce-5940925dd9d5\"],[null,77,122]],[[\"15b0c534-9140-42b5-b1ce-5940925dd9d5\"],[null,264,280]],[[\"4d675eaf-9dec-4ac7-8bd3-69c3a1cc51a5\"],[null,264,280]],[[\"4d675eaf-9dec-4ac7-8bd3-69c3a1cc51a5\"],[null,354,464]]]],null,null,[[[\"15b0c534-9140-42b5-b1ce-5940925dd9d5\"],[null,null,0.****************,[[null,9453,10235]],[[[9453,9482,[[[9453,9482,[\"v`verb-nsubj:noun-obj:noun \\u003d \"]]]]],[9482,9484,[[[9482,9484,[\"# \"]]]]],[9484,9503,[[[9484,9503,[\" VERB NOUN NOUN \"]]]]],[9503,9509,[[[9503,9509,[\"nsubj \"]]]]],[9509,9520,[[[9509,9520,[\"obj (`) \"]]]]],[9520,9522,[[[9520,9522,[\"# \"]]]]],[9522,9541,[[[9522,9541,[\" VERB NOUN NOUN \"]]]]],[9541,9547,[[[9541,9547,[\"nsubj \"]]]]],[9547,9569,[[[9547,9569,[\"obj (`) + . . .+ # \"]]]]],[9569,9588,[[[9569,9588,[\" NOUN NOUN VERB \"]]]]],[9588,9592,[[[9588,9592,[\"obj \"]]]]],[9592,9598,[[[9592,9598,[\"nsubj \"]]]]],[9598,9605,[[[9598,9605,[\"(`) \"]]]]],[9605,9630,[[[9605,9630,[\"︸ ︷︷ ︸ six possibilities \"]]]]],[9630,9633,[[[9630,9633,[\"(7)\"]]]]],[9633,9638,[[[9633,9638,[\"3993 \"]]]]],[9638,9943,[[[9638,9943,[\"Here, #[∗](`) returns how often the construction ∗ appears for `. The subscripts of v` represent the constructions: hyphens connect words, the head is represented by its part-of-speech tag, and the dependents are represented by their dependency relation and their part-of-speech tag, combined by a colon. \"]]]]],[9943,10235,[[[9943,10235,[\"2.2 Fuzzy logic The relative frequencies are in the interval [ 0, 1 ], hence our valuation function has to define logical connectives for a real-valued logic. A common example for real-valued logic is fuzzy logic (Zadeh, 1965), which defines negation, conjunction and disjunction as follows: \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"15b0c534-9140-42b5-b1ce-5940925dd9d5\"]]],[[\"4d675eaf-9dec-4ac7-8bd3-69c3a1cc51a5\"],[null,null,0.***************,[[null,19581,20542]],[[[19581,19971,[[[19581,19971,[\"7Addition is not a logical connective but used here to calculate the value of properties which cannot be directly extracted from the UD treebanks. For example, the degree to which a language has S-O order is the percentage occupied by V-S-O, S-V-O and S-O-V constructions. Thus, the value v`nsubj:noun-obj:noun can be calculated as v`verb-nsubj:noun-obj:noun + v`nsubj:noun-verb-obj:noun + \"]]]]],[19971,20542,[[[19971,20542,[\"v`nsubj:noun-obj:noun-verb. Addition is restricted to variables that are mutually exclusive, i.e. that are calculated with the same de-nominator (compare eq. (7)). That said, (out-of-logic) addition in our framework is comparable to addition of probabilities of disjoint events in probability theory; it is not as powerful and does not serve the same purpose as (in-logic) addition (“strong dis-junction”) in certain real-valued logics, e.g. Łukasiewicz logic (Łukasiewicz and Tarski (1930); cf. Bergmann (2008, p. 179)), where it is possible to add arbitrary variables. \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"4d675eaf-9dec-4ac7-8bd3-69c3a1cc51a5\"]]]]]],[[null,null,0.****************,[[null,9453,10235]],[[[9453,9482,[[[9453,9482,[\"v`verb-nsubj:noun-obj:noun \\u003d \"]]]]],[9482,9484,[[[9482,9484,[\"# \"]]]]],[9484,9503,[[[9484,9503,[\" VERB NOUN NOUN \"]]]]],[9503,9509,[[[9503,9509,[\"nsubj \"]]]]],[9509,9520,[[[9509,9520,[\"obj (`) \"]]]]],[9520,9522,[[[9520,9522,[\"# \"]]]]],[9522,9541,[[[9522,9541,[\" VERB NOUN NOUN \"]]]]],[9541,9547,[[[9541,9547,[\"nsubj \"]]]]],[9547,9569,[[[9547,9569,[\"obj (`) + . . .+ # \"]]]]],[9569,9588,[[[9569,9588,[\" NOUN NOUN VERB \"]]]]],[9588,9592,[[[9588,9592,[\"obj \"]]]]],[9592,9598,[[[9592,9598,[\"nsubj \"]]]]],[9598,9605,[[[9598,9605,[\"(`) \"]]]]],[9605,9630,[[[9605,9630,[\"︸ ︷︷ ︸ six possibilities \"]]]]],[9630,9633,[[[9630,9633,[\"(7)\"]]]]],[9633,9638,[[[9633,9638,[\"3993 \"]]]]],[9638,9943,[[[9638,9943,[\"Here, #[∗](`) returns how often the construction ∗ appears for `. The subscripts of v` represent the constructions: hyphens connect words, the head is represented by its part-of-speech tag, and the dependents are represented by their dependency relation and their part-of-speech tag, combined by a colon. \"]]]]],[9943,10235,[[[9943,10235,[\"2.2 Fuzzy logic The relative frequencies are in the interval [ 0, 1 ], hence our valuation function has to define logical connectives for a real-valued logic. A common example for real-valued logic is fuzzy logic (Zadeh, 1965), which defines negation, conjunction and disjunction as follows: \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"15b0c534-9140-42b5-b1ce-5940925dd9d5\"]],[null,null,0.***************,[[null,19581,20542]],[[[19581,19971,[[[19581,19971,[\"7Addition is not a logical connective but used here to calculate the value of properties which cannot be directly extracted from the UD treebanks. For example, the degree to which a language has S-O order is the percentage occupied by V-S-O, S-V-O and S-O-V constructions. Thus, the value v`nsubj:noun-obj:noun can be calculated as v`verb-nsubj:noun-obj:noun + v`nsubj:noun-verb-obj:noun + \"]]]]],[19971,20542,[[[19971,20542,[\"v`nsubj:noun-obj:noun-verb. Addition is restricted to variables that are mutually exclusive, i.e. that are calculated with the same de-nominator (compare eq. (7)). That said, (out-of-logic) addition in our framework is comparable to addition of probabilities of disjoint events in probability theory; it is not as powerful and does not serve the same purpose as (in-logic) addition (“strong dis-junction”) in certain real-valued logics, e.g. Łukasiewicz logic (Łukasiewicz and Tarski (1930); cf. Bergmann (2008, p. 179)), where it is possible to add arbitrary variables. \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"4d675eaf-9dec-4ac7-8bd3-69c3a1cc51a5\"]]],[[[null,77,122],[0]],[[null,264,280],[0,1]],[[null,354,464],[1]]],[[\"How are typological universals modeled?\",\"What are the two property types?\",\"What is the weighting function's purpose?\"]]]"]]
59
[["di",9128],["af.httprm",9128,"3249489591936418229",63]]
27
[["e",6,null,null,21568]]
