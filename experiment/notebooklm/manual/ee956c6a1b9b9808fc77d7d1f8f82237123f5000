)]}'

3236
[["wrb.fr",null,"[[\"\\\"Edit boundaries might be unusual\\\" refers to the fact that the **start and end boundaries of edits were determined automatically** by ERRANT's system, specifically during the edit extraction stage [1]. Human experts who were evaluating the appropriateness of the predicted error types were **warned that these automatically determined boundaries might not always align with what they would consider a typical or \\\"\",null,[\"d806de55-457c-4285-9a86-4b49f4dd866b\",\"96636577-4b03-4363-88f1-ba1421396259\",3149512694],null,[[null,[[[\"335405f5-3035-4513-9e42-bcde8baafade\"],[null,127,192]]]],null,null,[[[\"335405f5-3035-4513-9e42-bcde8baafade\"],[null,null,0.7078662334461099,[[null,12056,13003]],[[[12056,12082,[[[12056,12082,[\"3.5 Classifier Evaluation \"]]]]],[12082,13003,[[[12082,13003,[\"As our new error scheme is based solely on au-tomatically obtained properties of the data, there are no gold standard labels against which to evalu-ate classifier performance. For this reason, we in-stead carried out a small-scale manual evaluation, where we simply asked 5 GEC researchers to rate the appropriateness of the predicted error types for 200 randomly chosen edits in context (100 from FCE-test and 100 from CoNLL-2014) as “Good”, “Acceptable” or “Bad”. “Good’ meant the chosen type was the most appropriate for the given edit, “Acceptable” meant the chosen type was appropri-ate, but probably not optimum, while “Bad” meant the chosen type was not appropriate for the edit. Raters were warned that the edit boundaries had been determined automatically and hence might be unusual, but that they should focus on the appropriateness of the error type regardless of whether they agreed with the boundary or not. \"]]]]]]],[[[\"4cc9dc57-037d-4222-9d12-cc5755e4270b\"],\"bea084d9-43bf-468b-acb1-1efa3dc7af4e\"]],[\"335405f5-3035-4513-9e42-bcde8baafade\"]]]]]],[[null,null,0.7078662334461099,[[null,12056,13003]],[[[12056,12082,[[[12056,12082,[\"3.5 Classifier Evaluation \"]]]]],[12082,13003,[[[12082,13003,[\"As our new error scheme is based solely on au-tomatically obtained properties of the data, there are no gold standard labels against which to evalu-ate classifier performance. For this reason, we in-stead carried out a small-scale manual evaluation, where we simply asked 5 GEC researchers to rate the appropriateness of the predicted error types for 200 randomly chosen edits in context (100 from FCE-test and 100 from CoNLL-2014) as “Good”, “Acceptable” or “Bad”. “Good’ meant the chosen type was the most appropriate for the given edit, “Acceptable” meant the chosen type was appropri-ate, but probably not optimum, while “Bad” meant the chosen type was not appropriate for the edit. Raters were warned that the edit boundaries had been determined automatically and hence might be unusual, but that they should focus on the appropriateness of the error type regardless of whether they agreed with the boundary or not. \"]]]]]]],[[[\"4cc9dc57-037d-4222-9d12-cc5755e4270b\"],\"bea084d9-43bf-468b-acb1-1efa3dc7af4e\"]],[\"335405f5-3035-4513-9e42-bcde8baafade\"]]],[[[null,127,192],[0]]],[[\"Why was ERRANT developed?\",\"How does ERRANT classify edits?\",\"What is a multi-token edit?\"]]]"]]
3596
[["wrb.fr",null,"[[\"\\\"Edit boundaries might be unusual\\\" refers to the fact that the **start and end boundaries of edits were determined automatically** by ERRANT's system, specifically during the edit extraction stage [1]. Human experts who were evaluating the appropriateness of the predicted error types were **warned that these automatically determined boundaries might not always align with what they would consider a typical or \\\"usual\\\" edit span** [1]. Despite this, they were instructed to **focus solely on the appropriateness of the error type**, regardless of whether they agreed with the automatically set boundary [1].\",null,[\"d806de55-457c-4285-9a86-4b49f4dd866b\",\"96636577-4b03-4363-88f1-ba1421396259\",3149512694],null,[[null,[[[\"7203cf0a-189d-443a-9a39-86186a38de78\"],[null,127,192]],[[\"7203cf0a-189d-443a-9a39-86186a38de78\"],[null,419,419]],[[\"7203cf0a-189d-443a-9a39-86186a38de78\"],[null,512,583]]]],null,null,[[[\"7203cf0a-189d-443a-9a39-86186a38de78\"],[null,null,0.7078662334461099,[[null,12056,13003]],[[[12056,12082,[[[12056,12082,[\"3.5 Classifier Evaluation \"]]]]],[12082,13003,[[[12082,13003,[\"As our new error scheme is based solely on au-tomatically obtained properties of the data, there are no gold standard labels against which to evalu-ate classifier performance. For this reason, we in-stead carried out a small-scale manual evaluation, where we simply asked 5 GEC researchers to rate the appropriateness of the predicted error types for 200 randomly chosen edits in context (100 from FCE-test and 100 from CoNLL-2014) as “Good”, “Acceptable” or “Bad”. “Good’ meant the chosen type was the most appropriate for the given edit, “Acceptable” meant the chosen type was appropri-ate, but probably not optimum, while “Bad” meant the chosen type was not appropriate for the edit. Raters were warned that the edit boundaries had been determined automatically and hence might be unusual, but that they should focus on the appropriateness of the error type regardless of whether they agreed with the boundary or not. \"]]]]]]],[[[\"4cc9dc57-037d-4222-9d12-cc5755e4270b\"],\"bea084d9-43bf-468b-acb1-1efa3dc7af4e\"]],[\"7203cf0a-189d-443a-9a39-86186a38de78\"]]]]]],[[null,null,0.7078662334461099,[[null,12056,13003]],[[[12056,12082,[[[12056,12082,[\"3.5 Classifier Evaluation \"]]]]],[12082,13003,[[[12082,13003,[\"As our new error scheme is based solely on au-tomatically obtained properties of the data, there are no gold standard labels against which to evalu-ate classifier performance. For this reason, we in-stead carried out a small-scale manual evaluation, where we simply asked 5 GEC researchers to rate the appropriateness of the predicted error types for 200 randomly chosen edits in context (100 from FCE-test and 100 from CoNLL-2014) as “Good”, “Acceptable” or “Bad”. “Good’ meant the chosen type was the most appropriate for the given edit, “Acceptable” meant the chosen type was appropri-ate, but probably not optimum, while “Bad” meant the chosen type was not appropriate for the edit. Raters were warned that the edit boundaries had been determined automatically and hence might be unusual, but that they should focus on the appropriateness of the error type regardless of whether they agreed with the boundary or not. \"]]]]]]],[[[\"4cc9dc57-037d-4222-9d12-cc5755e4270b\"],\"bea084d9-43bf-468b-acb1-1efa3dc7af4e\"]],[\"7203cf0a-189d-443a-9a39-86186a38de78\"]]],[[[null,127,192],[0]],[[null,419,419],[0]],[[null,512,583],[0]]],[[\"Why was ERRANT developed?\",\"How does ERRANT classify edits?\",\"What is a multi-token edit?\"]]]"]]
4337
[["wrb.fr",null,"[[\"\\\"Edit boundaries might be unusual\\\" refers to the fact that the **start and end boundaries of edits were determined automatically** by ERRANT's system, specifically during the edit extraction stage [1]. Human experts who were evaluating the appropriateness of the predicted error types were **warned that these automatically determined boundaries might not always align with what they would consider a typical or \\\"usual\\\" edit span** [1]. Despite this, they were instructed to **focus solely on the appropriateness of the error type**, regardless of whether they agreed with the automatically set boundary [1].\",null,[\"d806de55-457c-4285-9a86-4b49f4dd866b\",\"96636577-4b03-4363-88f1-ba1421396259\",3149512694],null,[[[[0,584,[[[0,63,[\"\\\"Edit boundaries might be unusual\\\" refers to the fact that the \"]],[63,126,[\"start and end boundaries of edits were determined automatically\",[true]]],[126,282,[\" by ERRANT's system, specifically during the edit extraction stage. Human experts who were evaluating the appropriateness of the predicted error types were \"]],[282,419,[\"warned that these automatically determined boundaries might not always align with what they would consider a typical or \\\"usual\\\" edit span\",[true]]],[419,459,[\". Despite this, they were instructed to \"]],[459,512,[\"focus solely on the appropriateness of the error type\",[true]]],[512,584,[\", regardless of whether they agreed with the automatically set boundary.\"]]]]]],[[[\"7203cf0a-189d-443a-9a39-86186a38de78\"],[null,127,192]],[[\"7203cf0a-189d-443a-9a39-86186a38de78\"],[null,419,419]],[[\"7203cf0a-189d-443a-9a39-86186a38de78\"],[null,512,583]]]],null,null,[[[\"7203cf0a-189d-443a-9a39-86186a38de78\"],[null,null,0.7078662334461099,[[null,12056,13003]],[[[12056,12082,[[[12056,12082,[\"3.5 Classifier Evaluation \"]]]]],[12082,13003,[[[12082,13003,[\"As our new error scheme is based solely on au-tomatically obtained properties of the data, there are no gold standard labels against which to evalu-ate classifier performance. For this reason, we in-stead carried out a small-scale manual evaluation, where we simply asked 5 GEC researchers to rate the appropriateness of the predicted error types for 200 randomly chosen edits in context (100 from FCE-test and 100 from CoNLL-2014) as “Good”, “Acceptable” or “Bad”. “Good’ meant the chosen type was the most appropriate for the given edit, “Acceptable” meant the chosen type was appropri-ate, but probably not optimum, while “Bad” meant the chosen type was not appropriate for the edit. Raters were warned that the edit boundaries had been determined automatically and hence might be unusual, but that they should focus on the appropriateness of the error type regardless of whether they agreed with the boundary or not. \"]]]]]]],[[[\"4cc9dc57-037d-4222-9d12-cc5755e4270b\"],\"bea084d9-43bf-468b-acb1-1efa3dc7af4e\"]],[\"7203cf0a-189d-443a-9a39-86186a38de78\"]]]]]],[[null,null,0.7078662334461099,[[null,12056,13003]],[[[12056,12082,[[[12056,12082,[\"3.5 Classifier Evaluation \"]]]]],[12082,13003,[[[12082,13003,[\"As our new error scheme is based solely on au-tomatically obtained properties of the data, there are no gold standard labels against which to evalu-ate classifier performance. For this reason, we in-stead carried out a small-scale manual evaluation, where we simply asked 5 GEC researchers to rate the appropriateness of the predicted error types for 200 randomly chosen edits in context (100 from FCE-test and 100 from CoNLL-2014) as “Good”, “Acceptable” or “Bad”. “Good’ meant the chosen type was the most appropriate for the given edit, “Acceptable” meant the chosen type was appropri-ate, but probably not optimum, while “Bad” meant the chosen type was not appropriate for the edit. Raters were warned that the edit boundaries had been determined automatically and hence might be unusual, but that they should focus on the appropriateness of the error type regardless of whether they agreed with the boundary or not. \"]]]]]]],[[[\"4cc9dc57-037d-4222-9d12-cc5755e4270b\"],\"bea084d9-43bf-468b-acb1-1efa3dc7af4e\"]],[\"7203cf0a-189d-443a-9a39-86186a38de78\"]]],[[[null,127,192],[0]],[[null,419,419],[0]],[[null,512,583],[0]]],[[\"Why was ERRANT developed?\",\"How does ERRANT classify edits?\",\"What is a multi-token edit?\"]]]"]]
60
[["di",3958],["af.httprm",3958,"-3938419414534137416",58]]
27
[["e",6,null,null,11422]]
