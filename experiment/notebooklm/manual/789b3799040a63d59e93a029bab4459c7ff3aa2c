)]}'

15102
[["wrb.fr",null,"[[\"\\\"Information exchanges between the two parts of the network\\\" in Fig. 1 refers to the **mutual influence and updates** between the **backbone** (feature extractor) and the **task head** (the final layer of the network responsible for predictions) when their parameters are **updated together during finetuning** [1-3]. This interaction determines how features are adapted from the pretrained network for downstream tasks [4].\",null,[\"b2253aed-3804-499b-bcf2-6e7e668d0056\",\"7d5c9b2f-c3a0-46bd-adeb-6fecf25f0e74\",2627347831],null,[[null,[[[\"17808886-4c81-41bb-ba1b-8105158820f1\"],[null,294,294]],[[\"522e7fa6-6920-4531-b4f0-4dc95f33125f\"],[null,294,294]],[[\"c3c9e31d-bcc0-468b-87e2-e40733e6bfc9\"],[null,294,294]],[[\"399026ff-1a3d-4464-a8fc-cce7436b231d\"],[null,294,397]]]],null,null,[[[\"17808886-4c81-41bb-ba1b-8105158820f1\"],[null,null,0.6459477321007119,[[null,2543,3340]],[[[2543,3340,[[[2543,3340,[\"Our work builds significantly off the analysis of Kumar et al. (2022), who study the interactions between the “task head” (the final layer of the network, usually randomly initialized) and the “back-bone” (usually copied from the pretrained model). Kumar et al. claim that the standard finetuning method, randomly initializing a task head then updating all parameters of the whole network, can distort the pretrained features and hence can deteriorate the generalization ability if (as they assume) the previous backbone features were optimal for the downstream task. By analyzing an overparam-eterized linear model, they prove that linear probing (i.e., only updating the parameters of the task head) first, followed by finetuning the whole network, leads to better performance in their setting. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"17808886-4c81-41bb-ba1b-8105158820f1\"]]],[[\"522e7fa6-6920-4531-b4f0-4dc95f33125f\"],[null,null,0.6849546830407934,[[null,7646,8760]],[[[7646,7689,[[[7646,7689,[\"3.1 PROBLEM SETTING AND TWO STAGE TRAINING \"]]]]],[7689,7782,[[[7689,7782,[\"When training a model for a downstream task, our goal is to find a predictor f ◦ g : Rd → Rk \"]]]]],[7782,8760,[[[7782,8760,[\"that maps a high-dimensional input signal x ∈ Rd to a task-related prediction q ∈ Rk. As depicted by the left-bottom panel of Figure 1, we split the predictor into two parts: the backbone f(x;B) : Rd → Rh which maps the input signal to intermediate representations z ∈ Rh, and the task head g(z; v) : Rh → Rk which gives the prediction vector q ∈ Rk (e.g. logits in classification tasks using cross-entropy loss). Usually, the backbone f is parameterized by B and initialized by copying from a pretrained model. The task head g parameterized by v, on the other hand, is usually randomly initialized and might be a complex non-linear function. The training has two distinct stages: 1) head probing (HP) where we fix f and only update the parameters v of g for τ epochs; 2) finetuning (FT) where the parameters {v,B} of f ◦ g are updated together until convergence. In this work, we analyze how the FT stage is influenced by the architecture and v at the beginning of finetuning. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"522e7fa6-6920-4531-b4f0-4dc95f33125f\"]]],[[\"c3c9e31d-bcc0-468b-87e2-e40733e6bfc9\"],[null,null,0.6826719406097176,[[null,9863,10422]],[[[9863,9863,[]],[9863,9990,[[[9863,9990,[\" Strong: the pretrained features are far from the optimal ones for downstream task, so we need substantial feature adaptation. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[9990,9990,[]],[9990,10076,[[[9990,10076,[\" Mild: f(x;B) is reasonably good, but adaptation to the downstream domain is helpful. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[10076,10076,[]],[10076,10159,[[[10076,10159,[\" Tiny: the pretrained f(x;B) is near optimal and only need to be slightly adapted. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[10159,10422,[[[10159,10422,[\"In the rest of this paper, we first analyze how f(x;B) and g(z; v) interact with each other, under both the general case and the simplified case. Based on our observations, we propose several practical principles in Section 5.1 for better downstream performance. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"c3c9e31d-bcc0-468b-87e2-e40733e6bfc9\"]]],[[\"399026ff-1a3d-4464-a8fc-cce7436b231d\"],[null,null,0.6925216170929267,[[null,0,1601]],[[[0,1,[[[0,1,[\" \"]]]]],[1,46,[[[1,46,[\"HOW TO PREPARE YOUR TASK HEAD FOR FINETUNING \"]]],[null,6]]],[46,107,[[[46,107,[\"Yi Ren University of <NAME_EMAIL> \"]]]]],[107,159,[[[107,159,[\"Shangmin Guo University <NAME_EMAIL> \"]]]]],[159,216,[[[159,216,[\"Wonho Bae University of <NAME_EMAIL> \"]]]]],[216,291,[[[216,291,[\"Danica J. Sutherland University of British Columbia \\u0026 Amii <EMAIL> \"]]]]],[291,300,[[[291,300,[\"ABSTRACT \"]]]]],[300,1601,[[[300,1601,[\"In deep learning, transferring information from a pretrained network to a down-stream task by finetuning has many benefits. The choice of task head plays an important role in fine-tuning, as the pretrained and downstream tasks are usually different. Although there exist many different designs for finetuning, a full under-standing of when and why these algorithms work has been elusive. We analyze how the choice of task head controls feature adaptation and hence influences the downstream performance. By decomposing the learning dynamics of adaptation, we find that the key aspect is the training accuracy and loss at the beginning of finetuning, which determines the “energy” available for the feature’s adaptation. We identify a significant trend in the effect of changes in this initial energy on the resulting features after finetuning. Specifically, as the energy increases, the Eu-clidean and cosine distances between the resulting and original features increase, while their dot products (and the resulting features’ norm) first increase then de-crease. Inspired by this, we give several practical principles that lead to better downstream performance. We analytically prove this trend in an overparamter-ized linear setting, and verify its applicability to different experimental settings. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"399026ff-1a3d-4464-a8fc-cce7436b231d\"]]]]]],[[null,null,0.6459477321007119,[[null,2543,3340]],[[[2543,3340,[[[2543,3340,[\"Our work builds significantly off the analysis of Kumar et al. (2022), who study the interactions between the “task head” (the final layer of the network, usually randomly initialized) and the “back-bone” (usually copied from the pretrained model). Kumar et al. claim that the standard finetuning method, randomly initializing a task head then updating all parameters of the whole network, can distort the pretrained features and hence can deteriorate the generalization ability if (as they assume) the previous backbone features were optimal for the downstream task. By analyzing an overparam-eterized linear model, they prove that linear probing (i.e., only updating the parameters of the task head) first, followed by finetuning the whole network, leads to better performance in their setting. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"17808886-4c81-41bb-ba1b-8105158820f1\"]],[null,null,0.6849546830407934,[[null,7646,8760]],[[[7646,7689,[[[7646,7689,[\"3.1 PROBLEM SETTING AND TWO STAGE TRAINING \"]]]]],[7689,7782,[[[7689,7782,[\"When training a model for a downstream task, our goal is to find a predictor f ◦ g : Rd → Rk \"]]]]],[7782,8760,[[[7782,8760,[\"that maps a high-dimensional input signal x ∈ Rd to a task-related prediction q ∈ Rk. As depicted by the left-bottom panel of Figure 1, we split the predictor into two parts: the backbone f(x;B) : Rd → Rh which maps the input signal to intermediate representations z ∈ Rh, and the task head g(z; v) : Rh → Rk which gives the prediction vector q ∈ Rk (e.g. logits in classification tasks using cross-entropy loss). Usually, the backbone f is parameterized by B and initialized by copying from a pretrained model. The task head g parameterized by v, on the other hand, is usually randomly initialized and might be a complex non-linear function. The training has two distinct stages: 1) head probing (HP) where we fix f and only update the parameters v of g for τ epochs; 2) finetuning (FT) where the parameters {v,B} of f ◦ g are updated together until convergence. In this work, we analyze how the FT stage is influenced by the architecture and v at the beginning of finetuning. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"522e7fa6-6920-4531-b4f0-4dc95f33125f\"]],[null,null,0.6826719406097176,[[null,9863,10422]],[[[9863,9863,[]],[9863,9990,[[[9863,9990,[\" Strong: the pretrained features are far from the optimal ones for downstream task, so we need substantial feature adaptation. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[9990,9990,[]],[9990,10076,[[[9990,10076,[\" Mild: f(x;B) is reasonably good, but adaptation to the downstream domain is helpful. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[10076,10076,[]],[10076,10159,[[[10076,10159,[\" Tiny: the pretrained f(x;B) is near optimal and only need to be slightly adapted. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[10159,10422,[[[10159,10422,[\"In the rest of this paper, we first analyze how f(x;B) and g(z; v) interact with each other, under both the general case and the simplified case. Based on our observations, we propose several practical principles in Section 5.1 for better downstream performance. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"c3c9e31d-bcc0-468b-87e2-e40733e6bfc9\"]],[null,null,0.6925216170929267,[[null,0,1601]],[[[0,1,[[[0,1,[\" \"]]]]],[1,46,[[[1,46,[\"HOW TO PREPARE YOUR TASK HEAD FOR FINETUNING \"]]],[null,6]]],[46,107,[[[46,107,[\"Yi Ren University of <NAME_EMAIL> \"]]]]],[107,159,[[[107,159,[\"Shangmin Guo University <NAME_EMAIL> \"]]]]],[159,216,[[[159,216,[\"Wonho Bae University of <NAME_EMAIL> \"]]]]],[216,291,[[[216,291,[\"Danica J. Sutherland University of British Columbia \\u0026 Amii <EMAIL> \"]]]]],[291,300,[[[291,300,[\"ABSTRACT \"]]]]],[300,1601,[[[300,1601,[\"In deep learning, transferring information from a pretrained network to a down-stream task by finetuning has many benefits. The choice of task head plays an important role in fine-tuning, as the pretrained and downstream tasks are usually different. Although there exist many different designs for finetuning, a full under-standing of when and why these algorithms work has been elusive. We analyze how the choice of task head controls feature adaptation and hence influences the downstream performance. By decomposing the learning dynamics of adaptation, we find that the key aspect is the training accuracy and loss at the beginning of finetuning, which determines the “energy” available for the feature’s adaptation. We identify a significant trend in the effect of changes in this initial energy on the resulting features after finetuning. Specifically, as the energy increases, the Eu-clidean and cosine distances between the resulting and original features increase, while their dot products (and the resulting features’ norm) first increase then de-crease. Inspired by this, we give several practical principles that lead to better downstream performance. We analytically prove this trend in an overparamter-ized linear setting, and verify its applicability to different experimental settings. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"399026ff-1a3d-4464-a8fc-cce7436b231d\"]]],[[[null,294,294],[0,1,2]],[[null,294,397],[3]]],[[\"Who are the authors of this paper?\",\"What is the purpose of \\\"head probing\\\"?\",\"When does label smoothing help?\"]]]"]]
15102
[["wrb.fr",null,"[[\"\\\"Information exchanges between the two parts of the network\\\" in Fig. 1 refers to the **mutual influence and updates** between the **backbone** (feature extractor) and the **task head** (the final layer of the network responsible for predictions) when their parameters are **updated together during finetuning** [1-3]. This interaction determines how features are adapted from the pretrained network for downstream tasks [4].\",null,[\"b2253aed-3804-499b-bcf2-6e7e668d0056\",\"7d5c9b2f-c3a0-46bd-adeb-6fecf25f0e74\",2627347831],null,[[null,[[[\"30d131e0-fa95-4ad9-88fb-9750761f553c\"],[null,294,294]],[[\"9d39fdb6-10f4-4add-b669-22c862855d29\"],[null,294,294]],[[\"9cf1353e-a6c0-430d-a51a-2612fabaa5c8\"],[null,294,294]],[[\"dd8ad5be-75f7-4ff3-abbd-5611afed1239\"],[null,294,397]]]],null,null,[[[\"30d131e0-fa95-4ad9-88fb-9750761f553c\"],[null,null,0.6459477321007119,[[null,2543,3340]],[[[2543,3340,[[[2543,3340,[\"Our work builds significantly off the analysis of Kumar et al. (2022), who study the interactions between the “task head” (the final layer of the network, usually randomly initialized) and the “back-bone” (usually copied from the pretrained model). Kumar et al. claim that the standard finetuning method, randomly initializing a task head then updating all parameters of the whole network, can distort the pretrained features and hence can deteriorate the generalization ability if (as they assume) the previous backbone features were optimal for the downstream task. By analyzing an overparam-eterized linear model, they prove that linear probing (i.e., only updating the parameters of the task head) first, followed by finetuning the whole network, leads to better performance in their setting. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"30d131e0-fa95-4ad9-88fb-9750761f553c\"]]],[[\"9d39fdb6-10f4-4add-b669-22c862855d29\"],[null,null,0.6849546830407934,[[null,7646,8760]],[[[7646,7689,[[[7646,7689,[\"3.1 PROBLEM SETTING AND TWO STAGE TRAINING \"]]]]],[7689,7782,[[[7689,7782,[\"When training a model for a downstream task, our goal is to find a predictor f ◦ g : Rd → Rk \"]]]]],[7782,8760,[[[7782,8760,[\"that maps a high-dimensional input signal x ∈ Rd to a task-related prediction q ∈ Rk. As depicted by the left-bottom panel of Figure 1, we split the predictor into two parts: the backbone f(x;B) : Rd → Rh which maps the input signal to intermediate representations z ∈ Rh, and the task head g(z; v) : Rh → Rk which gives the prediction vector q ∈ Rk (e.g. logits in classification tasks using cross-entropy loss). Usually, the backbone f is parameterized by B and initialized by copying from a pretrained model. The task head g parameterized by v, on the other hand, is usually randomly initialized and might be a complex non-linear function. The training has two distinct stages: 1) head probing (HP) where we fix f and only update the parameters v of g for τ epochs; 2) finetuning (FT) where the parameters {v,B} of f ◦ g are updated together until convergence. In this work, we analyze how the FT stage is influenced by the architecture and v at the beginning of finetuning. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"9d39fdb6-10f4-4add-b669-22c862855d29\"]]],[[\"9cf1353e-a6c0-430d-a51a-2612fabaa5c8\"],[null,null,0.6826719406097176,[[null,9863,10422]],[[[9863,9863,[]],[9863,9990,[[[9863,9990,[\" Strong: the pretrained features are far from the optimal ones for downstream task, so we need substantial feature adaptation. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[9990,9990,[]],[9990,10076,[[[9990,10076,[\" Mild: f(x;B) is reasonably good, but adaptation to the downstream domain is helpful. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[10076,10076,[]],[10076,10159,[[[10076,10159,[\" Tiny: the pretrained f(x;B) is near optimal and only need to be slightly adapted. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[10159,10422,[[[10159,10422,[\"In the rest of this paper, we first analyze how f(x;B) and g(z; v) interact with each other, under both the general case and the simplified case. Based on our observations, we propose several practical principles in Section 5.1 for better downstream performance. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"9cf1353e-a6c0-430d-a51a-2612fabaa5c8\"]]],[[\"dd8ad5be-75f7-4ff3-abbd-5611afed1239\"],[null,null,0.6925216170929267,[[null,0,1601]],[[[0,1,[[[0,1,[\" \"]]]]],[1,46,[[[1,46,[\"HOW TO PREPARE YOUR TASK HEAD FOR FINETUNING \"]]],[null,6]]],[46,107,[[[46,107,[\"Yi Ren University of <NAME_EMAIL> \"]]]]],[107,159,[[[107,159,[\"Shangmin Guo University <NAME_EMAIL> \"]]]]],[159,216,[[[159,216,[\"Wonho Bae University of <NAME_EMAIL> \"]]]]],[216,291,[[[216,291,[\"Danica J. Sutherland University of British Columbia \\u0026 Amii <EMAIL> \"]]]]],[291,300,[[[291,300,[\"ABSTRACT \"]]]]],[300,1601,[[[300,1601,[\"In deep learning, transferring information from a pretrained network to a down-stream task by finetuning has many benefits. The choice of task head plays an important role in fine-tuning, as the pretrained and downstream tasks are usually different. Although there exist many different designs for finetuning, a full under-standing of when and why these algorithms work has been elusive. We analyze how the choice of task head controls feature adaptation and hence influences the downstream performance. By decomposing the learning dynamics of adaptation, we find that the key aspect is the training accuracy and loss at the beginning of finetuning, which determines the “energy” available for the feature’s adaptation. We identify a significant trend in the effect of changes in this initial energy on the resulting features after finetuning. Specifically, as the energy increases, the Eu-clidean and cosine distances between the resulting and original features increase, while their dot products (and the resulting features’ norm) first increase then de-crease. Inspired by this, we give several practical principles that lead to better downstream performance. We analytically prove this trend in an overparamter-ized linear setting, and verify its applicability to different experimental settings. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"dd8ad5be-75f7-4ff3-abbd-5611afed1239\"]]]]]],[[null,null,0.6459477321007119,[[null,2543,3340]],[[[2543,3340,[[[2543,3340,[\"Our work builds significantly off the analysis of Kumar et al. (2022), who study the interactions between the “task head” (the final layer of the network, usually randomly initialized) and the “back-bone” (usually copied from the pretrained model). Kumar et al. claim that the standard finetuning method, randomly initializing a task head then updating all parameters of the whole network, can distort the pretrained features and hence can deteriorate the generalization ability if (as they assume) the previous backbone features were optimal for the downstream task. By analyzing an overparam-eterized linear model, they prove that linear probing (i.e., only updating the parameters of the task head) first, followed by finetuning the whole network, leads to better performance in their setting. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"30d131e0-fa95-4ad9-88fb-9750761f553c\"]],[null,null,0.6849546830407934,[[null,7646,8760]],[[[7646,7689,[[[7646,7689,[\"3.1 PROBLEM SETTING AND TWO STAGE TRAINING \"]]]]],[7689,7782,[[[7689,7782,[\"When training a model for a downstream task, our goal is to find a predictor f ◦ g : Rd → Rk \"]]]]],[7782,8760,[[[7782,8760,[\"that maps a high-dimensional input signal x ∈ Rd to a task-related prediction q ∈ Rk. As depicted by the left-bottom panel of Figure 1, we split the predictor into two parts: the backbone f(x;B) : Rd → Rh which maps the input signal to intermediate representations z ∈ Rh, and the task head g(z; v) : Rh → Rk which gives the prediction vector q ∈ Rk (e.g. logits in classification tasks using cross-entropy loss). Usually, the backbone f is parameterized by B and initialized by copying from a pretrained model. The task head g parameterized by v, on the other hand, is usually randomly initialized and might be a complex non-linear function. The training has two distinct stages: 1) head probing (HP) where we fix f and only update the parameters v of g for τ epochs; 2) finetuning (FT) where the parameters {v,B} of f ◦ g are updated together until convergence. In this work, we analyze how the FT stage is influenced by the architecture and v at the beginning of finetuning. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"9d39fdb6-10f4-4add-b669-22c862855d29\"]],[null,null,0.6826719406097176,[[null,9863,10422]],[[[9863,9863,[]],[9863,9990,[[[9863,9990,[\" Strong: the pretrained features are far from the optimal ones for downstream task, so we need substantial feature adaptation. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[9990,9990,[]],[9990,10076,[[[9990,10076,[\" Mild: f(x;B) is reasonably good, but adaptation to the downstream domain is helpful. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[10076,10076,[]],[10076,10159,[[[10076,10159,[\" Tiny: the pretrained f(x;B) is near optimal and only need to be slightly adapted. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[10159,10422,[[[10159,10422,[\"In the rest of this paper, we first analyze how f(x;B) and g(z; v) interact with each other, under both the general case and the simplified case. Based on our observations, we propose several practical principles in Section 5.1 for better downstream performance. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"9cf1353e-a6c0-430d-a51a-2612fabaa5c8\"]],[null,null,0.6925216170929267,[[null,0,1601]],[[[0,1,[[[0,1,[\" \"]]]]],[1,46,[[[1,46,[\"HOW TO PREPARE YOUR TASK HEAD FOR FINETUNING \"]]],[null,6]]],[46,107,[[[46,107,[\"Yi Ren University of <NAME_EMAIL> \"]]]]],[107,159,[[[107,159,[\"Shangmin Guo University <NAME_EMAIL> \"]]]]],[159,216,[[[159,216,[\"Wonho Bae University of <NAME_EMAIL> \"]]]]],[216,291,[[[216,291,[\"Danica J. Sutherland University of British Columbia \\u0026 Amii <EMAIL> \"]]]]],[291,300,[[[291,300,[\"ABSTRACT \"]]]]],[300,1601,[[[300,1601,[\"In deep learning, transferring information from a pretrained network to a down-stream task by finetuning has many benefits. The choice of task head plays an important role in fine-tuning, as the pretrained and downstream tasks are usually different. Although there exist many different designs for finetuning, a full under-standing of when and why these algorithms work has been elusive. We analyze how the choice of task head controls feature adaptation and hence influences the downstream performance. By decomposing the learning dynamics of adaptation, we find that the key aspect is the training accuracy and loss at the beginning of finetuning, which determines the “energy” available for the feature’s adaptation. We identify a significant trend in the effect of changes in this initial energy on the resulting features after finetuning. Specifically, as the energy increases, the Eu-clidean and cosine distances between the resulting and original features increase, while their dot products (and the resulting features’ norm) first increase then de-crease. Inspired by this, we give several practical principles that lead to better downstream performance. We analytically prove this trend in an overparamter-ized linear setting, and verify its applicability to different experimental settings. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"dd8ad5be-75f7-4ff3-abbd-5611afed1239\"]]],[[[null,294,294],[0,1,2]],[[null,294,397],[3]]],[[\"Who are the authors of this paper?\",\"What is the purpose of \\\"head probing\\\"?\",\"When does label smoothing help?\"]]]"]]
15692
[["wrb.fr",null,"[[\"\\\"Information exchanges between the two parts of the network\\\" in Fig. 1 refers to the **mutual influence and updates** between the **backbone** (feature extractor) and the **task head** (the final layer of the network responsible for predictions) when their parameters are **updated together during finetuning** [1-3]. This interaction determines how features are adapted from the pretrained network for downstream tasks [4].\",null,[\"b2253aed-3804-499b-bcf2-6e7e668d0056\",\"7d5c9b2f-c3a0-46bd-adeb-6fecf25f0e74\",2627347831],null,[[[[0,398,[[[0,85,[\"\\\"Information exchanges between the two parts of the network\\\" in Fig. 1 refers to the \"]],[85,113,[\"mutual influence and updates\",[true]]],[113,126,[\" between the \"]],[126,134,[\"backbone\",[true]]],[134,163,[\" (feature extractor) and the \"]],[163,172,[\"task head\",[true]]],[172,260,[\" (the final layer of the network responsible for predictions) when their parameters are \"]],[260,294,[\"updated together during finetuning\",[true]]],[294,398,[\". This interaction determines how features are adapted from the pretrained network for downstream tasks.\"]]]]]],[[[\"30d131e0-fa95-4ad9-88fb-9750761f553c\"],[null,294,294]],[[\"9d39fdb6-10f4-4add-b669-22c862855d29\"],[null,294,294]],[[\"9cf1353e-a6c0-430d-a51a-2612fabaa5c8\"],[null,294,294]],[[\"dd8ad5be-75f7-4ff3-abbd-5611afed1239\"],[null,294,397]]]],null,null,[[[\"30d131e0-fa95-4ad9-88fb-9750761f553c\"],[null,null,0.6459477321007119,[[null,2543,3340]],[[[2543,3340,[[[2543,3340,[\"Our work builds significantly off the analysis of Kumar et al. (2022), who study the interactions between the “task head” (the final layer of the network, usually randomly initialized) and the “back-bone” (usually copied from the pretrained model). Kumar et al. claim that the standard finetuning method, randomly initializing a task head then updating all parameters of the whole network, can distort the pretrained features and hence can deteriorate the generalization ability if (as they assume) the previous backbone features were optimal for the downstream task. By analyzing an overparam-eterized linear model, they prove that linear probing (i.e., only updating the parameters of the task head) first, followed by finetuning the whole network, leads to better performance in their setting. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"30d131e0-fa95-4ad9-88fb-9750761f553c\"]]],[[\"9d39fdb6-10f4-4add-b669-22c862855d29\"],[null,null,0.6849546830407934,[[null,7646,8760]],[[[7646,7689,[[[7646,7689,[\"3.1 PROBLEM SETTING AND TWO STAGE TRAINING \"]]]]],[7689,7782,[[[7689,7782,[\"When training a model for a downstream task, our goal is to find a predictor f ◦ g : Rd → Rk \"]]]]],[7782,8760,[[[7782,8760,[\"that maps a high-dimensional input signal x ∈ Rd to a task-related prediction q ∈ Rk. As depicted by the left-bottom panel of Figure 1, we split the predictor into two parts: the backbone f(x;B) : Rd → Rh which maps the input signal to intermediate representations z ∈ Rh, and the task head g(z; v) : Rh → Rk which gives the prediction vector q ∈ Rk (e.g. logits in classification tasks using cross-entropy loss). Usually, the backbone f is parameterized by B and initialized by copying from a pretrained model. The task head g parameterized by v, on the other hand, is usually randomly initialized and might be a complex non-linear function. The training has two distinct stages: 1) head probing (HP) where we fix f and only update the parameters v of g for τ epochs; 2) finetuning (FT) where the parameters {v,B} of f ◦ g are updated together until convergence. In this work, we analyze how the FT stage is influenced by the architecture and v at the beginning of finetuning. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"9d39fdb6-10f4-4add-b669-22c862855d29\"]]],[[\"9cf1353e-a6c0-430d-a51a-2612fabaa5c8\"],[null,null,0.6826719406097176,[[null,9863,10422]],[[[9863,9863,[]],[9863,9990,[[[9863,9990,[\" Strong: the pretrained features are far from the optimal ones for downstream task, so we need substantial feature adaptation. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[9990,9990,[]],[9990,10076,[[[9990,10076,[\" Mild: f(x;B) is reasonably good, but adaptation to the downstream domain is helpful. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[10076,10076,[]],[10076,10159,[[[10076,10159,[\" Tiny: the pretrained f(x;B) is near optimal and only need to be slightly adapted. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[10159,10422,[[[10159,10422,[\"In the rest of this paper, we first analyze how f(x;B) and g(z; v) interact with each other, under both the general case and the simplified case. Based on our observations, we propose several practical principles in Section 5.1 for better downstream performance. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"9cf1353e-a6c0-430d-a51a-2612fabaa5c8\"]]],[[\"dd8ad5be-75f7-4ff3-abbd-5611afed1239\"],[null,null,0.6925216170929267,[[null,0,1601]],[[[0,1,[[[0,1,[\" \"]]]]],[1,46,[[[1,46,[\"HOW TO PREPARE YOUR TASK HEAD FOR FINETUNING \"]]],[null,6]]],[46,107,[[[46,107,[\"Yi Ren University of <NAME_EMAIL> \"]]]]],[107,159,[[[107,159,[\"Shangmin Guo University <NAME_EMAIL> \"]]]]],[159,216,[[[159,216,[\"Wonho Bae University of <NAME_EMAIL> \"]]]]],[216,291,[[[216,291,[\"Danica J. Sutherland University of British Columbia \\u0026 Amii <EMAIL> \"]]]]],[291,300,[[[291,300,[\"ABSTRACT \"]]]]],[300,1601,[[[300,1601,[\"In deep learning, transferring information from a pretrained network to a down-stream task by finetuning has many benefits. The choice of task head plays an important role in fine-tuning, as the pretrained and downstream tasks are usually different. Although there exist many different designs for finetuning, a full under-standing of when and why these algorithms work has been elusive. We analyze how the choice of task head controls feature adaptation and hence influences the downstream performance. By decomposing the learning dynamics of adaptation, we find that the key aspect is the training accuracy and loss at the beginning of finetuning, which determines the “energy” available for the feature’s adaptation. We identify a significant trend in the effect of changes in this initial energy on the resulting features after finetuning. Specifically, as the energy increases, the Eu-clidean and cosine distances between the resulting and original features increase, while their dot products (and the resulting features’ norm) first increase then de-crease. Inspired by this, we give several practical principles that lead to better downstream performance. We analytically prove this trend in an overparamter-ized linear setting, and verify its applicability to different experimental settings. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"dd8ad5be-75f7-4ff3-abbd-5611afed1239\"]]]]]],[[null,null,0.6459477321007119,[[null,2543,3340]],[[[2543,3340,[[[2543,3340,[\"Our work builds significantly off the analysis of Kumar et al. (2022), who study the interactions between the “task head” (the final layer of the network, usually randomly initialized) and the “back-bone” (usually copied from the pretrained model). Kumar et al. claim that the standard finetuning method, randomly initializing a task head then updating all parameters of the whole network, can distort the pretrained features and hence can deteriorate the generalization ability if (as they assume) the previous backbone features were optimal for the downstream task. By analyzing an overparam-eterized linear model, they prove that linear probing (i.e., only updating the parameters of the task head) first, followed by finetuning the whole network, leads to better performance in their setting. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"30d131e0-fa95-4ad9-88fb-9750761f553c\"]],[null,null,0.6849546830407934,[[null,7646,8760]],[[[7646,7689,[[[7646,7689,[\"3.1 PROBLEM SETTING AND TWO STAGE TRAINING \"]]]]],[7689,7782,[[[7689,7782,[\"When training a model for a downstream task, our goal is to find a predictor f ◦ g : Rd → Rk \"]]]]],[7782,8760,[[[7782,8760,[\"that maps a high-dimensional input signal x ∈ Rd to a task-related prediction q ∈ Rk. As depicted by the left-bottom panel of Figure 1, we split the predictor into two parts: the backbone f(x;B) : Rd → Rh which maps the input signal to intermediate representations z ∈ Rh, and the task head g(z; v) : Rh → Rk which gives the prediction vector q ∈ Rk (e.g. logits in classification tasks using cross-entropy loss). Usually, the backbone f is parameterized by B and initialized by copying from a pretrained model. The task head g parameterized by v, on the other hand, is usually randomly initialized and might be a complex non-linear function. The training has two distinct stages: 1) head probing (HP) where we fix f and only update the parameters v of g for τ epochs; 2) finetuning (FT) where the parameters {v,B} of f ◦ g are updated together until convergence. In this work, we analyze how the FT stage is influenced by the architecture and v at the beginning of finetuning. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"9d39fdb6-10f4-4add-b669-22c862855d29\"]],[null,null,0.6826719406097176,[[null,9863,10422]],[[[9863,9863,[]],[9863,9990,[[[9863,9990,[\" Strong: the pretrained features are far from the optimal ones for downstream task, so we need substantial feature adaptation. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[9990,9990,[]],[9990,10076,[[[9990,10076,[\" Mild: f(x;B) is reasonably good, but adaptation to the downstream domain is helpful. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[10076,10076,[]],[10076,10159,[[[10076,10159,[\" Tiny: the pretrained f(x;B) is near optimal and only need to be slightly adapted. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[10159,10422,[[[10159,10422,[\"In the rest of this paper, we first analyze how f(x;B) and g(z; v) interact with each other, under both the general case and the simplified case. Based on our observations, we propose several practical principles in Section 5.1 for better downstream performance. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"9cf1353e-a6c0-430d-a51a-2612fabaa5c8\"]],[null,null,0.6925216170929267,[[null,0,1601]],[[[0,1,[[[0,1,[\" \"]]]]],[1,46,[[[1,46,[\"HOW TO PREPARE YOUR TASK HEAD FOR FINETUNING \"]]],[null,6]]],[46,107,[[[46,107,[\"Yi Ren University of <NAME_EMAIL> \"]]]]],[107,159,[[[107,159,[\"Shangmin Guo University <NAME_EMAIL> \"]]]]],[159,216,[[[159,216,[\"Wonho Bae University of <NAME_EMAIL> \"]]]]],[216,291,[[[216,291,[\"Danica J. Sutherland University of British Columbia \\u0026 Amii <EMAIL> \"]]]]],[291,300,[[[291,300,[\"ABSTRACT \"]]]]],[300,1601,[[[300,1601,[\"In deep learning, transferring information from a pretrained network to a down-stream task by finetuning has many benefits. The choice of task head plays an important role in fine-tuning, as the pretrained and downstream tasks are usually different. Although there exist many different designs for finetuning, a full under-standing of when and why these algorithms work has been elusive. We analyze how the choice of task head controls feature adaptation and hence influences the downstream performance. By decomposing the learning dynamics of adaptation, we find that the key aspect is the training accuracy and loss at the beginning of finetuning, which determines the “energy” available for the feature’s adaptation. We identify a significant trend in the effect of changes in this initial energy on the resulting features after finetuning. Specifically, as the energy increases, the Eu-clidean and cosine distances between the resulting and original features increase, while their dot products (and the resulting features’ norm) first increase then de-crease. Inspired by this, we give several practical principles that lead to better downstream performance. We analytically prove this trend in an overparamter-ized linear setting, and verify its applicability to different experimental settings. \"]]]]]]],[[[\"e5b684b5-3ef2-45cc-b46c-011879fbefe5\"],\"f92f7338-536f-46ac-a998-bbd509f33ab1\"]],[\"dd8ad5be-75f7-4ff3-abbd-5611afed1239\"]]],[[[null,294,294],[0,1,2]],[[null,294,397],[3]]],[[\"Who are the authors of this paper?\",\"What is the purpose of \\\"head probing\\\"?\",\"When does label smoothing help?\"]]]"]]
59
[["di",6595],["af.httprm",6595,"7530975316948005800",61]]
27
[["e",6,null,null,46253]]
