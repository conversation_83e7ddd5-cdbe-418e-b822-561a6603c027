)]}'

2374
[["wrb.fr",null,"[[\"With more candidate images, the task naturally becomes **harder** [1]. This is reflected in the **random expected Jaccard index**,\",null,[\"93d755ef-f44e-4a42-93d0-3b0fb15ee01d\",\"227f3144-f882-4e58-9279-9e6bb4e146d6\",2765451006],null,[[null,[[[\"b80737a1-25d9-4918-bc7e-17ade04794be\"],[null,61,61]]]],null,null,[[[\"b80737a1-25d9-4918-bc7e-17ade04794be\"],[null,null,0.7265139898093562,[[null,11200,11915]],[[[11200,11221,[[[11200,11221,[\"2.2 Human Annotation \"]]]]],[11221,11915,[[[11221,11915,[\"We hire Amazon Mechanical Turk workers to play the WinoGAViL game. We develop qualification tests to select high-quality annotators and collect the annotators’ demographic information. Spymas-ters screen example is presented in Figure 3; See Appendix A for more details.5 We have several options for the total number of candidates: 5, 6, 10 or 12. With more candidates, the task naturally becomes harder. The spymasters are allowed to select between 2-5 images. Full annotation results and statistics are presented in Table 1. The scores of both humans and models is the Jaccard index of between their created associations instances. The annotation task includes three steps, elaborated below. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"b80737a1-25d9-4918-bc7e-17ade04794be\"]]]]]],[[null,null,0.7265139898093562,[[null,11200,11915]],[[[11200,11221,[[[11200,11221,[\"2.2 Human Annotation \"]]]]],[11221,11915,[[[11221,11915,[\"We hire Amazon Mechanical Turk workers to play the WinoGAViL game. We develop qualification tests to select high-quality annotators and collect the annotators’ demographic information. Spymas-ters screen example is presented in Figure 3; See Appendix A for more details.5 We have several options for the total number of candidates: 5, 6, 10 or 12. With more candidates, the task naturally becomes harder. The spymasters are allowed to select between 2-5 images. Full annotation results and statistics are presented in Table 1. The scores of both humans and models is the Jaccard index of between their created associations instances. The annotation task includes three steps, elaborated below. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"b80737a1-25d9-4918-bc7e-17ade04794be\"]]],[[[null,61,61],[0]]]]"]]
8877
[["wrb.fr",null,"[[\"With more candidate images, the task naturally becomes **harder** [1]. This is reflected in the **random expected Jaccard index**, which decreases significantly with more candidates: for 5 candidates it's 38%, for 6 candidates it's 34%, for 10 candidates it's 24%, and for 12 candidates it's 17% [2]. While this increased difficulty leads to a significant performance drop for most AI models, human accuracy remains high, indicating human robustness to this increased difficulty level [3].\",null,[\"93d755ef-f44e-4a42-93d0-3b0fb15ee01d\",\"227f3144-f882-4e58-9279-9e6bb4e146d6\",2765451006],null,[[null,[[[\"34e5b69e-5f5b-4d7b-ac86-3d079c5e2f12\"],[null,61,61]],[[\"41c141be-6b16-428d-9d05-4c799a0cfb66\"],[null,117,283]],[[\"90632553-d9a9-4e3e-940b-6e5cb35b899a\"],[null,283,468]]]],null,null,[[[\"34e5b69e-5f5b-4d7b-ac86-3d079c5e2f12\"],[null,null,0.7265139898093562,[[null,11200,11915]],[[[11200,11221,[[[11200,11221,[\"2.2 Human Annotation \"]]]]],[11221,11915,[[[11221,11915,[\"We hire Amazon Mechanical Turk workers to play the WinoGAViL game. We develop qualification tests to select high-quality annotators and collect the annotators’ demographic information. Spymas-ters screen example is presented in Figure 3; See Appendix A for more details.5 We have several options for the total number of candidates: 5, 6, 10 or 12. With more candidates, the task naturally becomes harder. The spymasters are allowed to select between 2-5 images. Full annotation results and statistics are presented in Table 1. The scores of both humans and models is the Jaccard index of between their created associations instances. The annotation task includes three steps, elaborated below. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"34e5b69e-5f5b-4d7b-ac86-3d079c5e2f12\"]]],[[\"41c141be-6b16-428d-9d05-4c799a0cfb66\"],[null,null,0.7280073562996616,[[null,5997,7000]],[[[5997,5998,[[[5997,5998,[\"2\"]]]]],[5998,6130,[[[5998,6130,[\"data generated by WinoGAViL is much more challenging to machines, highlighting the value of our gamified data collection framework. \"]]]]],[6130,6156,[[[6130,6156,[\"2 The WinoGAViL Benchmark \"]]]]],[6156,6398,[[[6156,6398,[\"We start by presenting the game as a framework for collecting challenging associations (§2.1). Second, we describe how we crowd-source a test set using the game (§2.2). Finally, we analyze the collected dataset and provide statistics (§2.3). \"]]]]],[6398,7000,[[[6398,7000,[\"Throughout this paper we use the Jaccard index, which is the intersection of selected candidates divided by the union of selected candidates.3 This metric does not reward random guesses highly. The random expected Jaccard index is 38%, 34%, 24%, 17% with 5/6/10/12 candidates respectively. For example, in Figure 1c the Jaccard index (‘Human score’) of the solvers is 100%, since the intersection of the selections is the same as the union. In Figure 1b the AI model selection is 1/3, so the Jaccard index (‘Model score’) is 33%: there are three images in the union, and one image in the intersection. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"41c141be-6b16-428d-9d05-4c799a0cfb66\"]]],[[\"90632553-d9a9-4e3e-940b-6e5cb35b899a\"],[null,null,0.7284902849189898,[[null,24390,25495]],[[[24390,24406,[[[24390,24406,[\"Model Game SWOW \"]]]]],[24406,24439,[[[24406,24439,[\"# Candidates 10 \\u0026 12 5 \\u0026 6 5 \\u0026 6 \"]]]]],[24439,24529,[[[24439,24529,[\"CLIP-RN50x64/14 38 50 70 CLIP-VIT-L/14 40 53 74 CLIP-VIT-B/32 41 53 74 CLIP-RN50 35 50 73 \"]]]]],[24529,24576,[[[24529,24576,[\"CLIP-ViL 15 47 66 ViLT 52 55 59 X-VLM 46 53 68 \"]]]]],[24576,24592,[[[24576,24592,[\"Humans 90 92 95 \"]]]]],[24592,24820,[[[24592,24820,[\"Zero-shot results on WinoGAViL dataset and the SWOW vision baseline dataset are presented in Table 4. Table 10 (Appendix A) shows full statis-tics and performance for the different number of candidates and created associations. \"]]]]],[24820,25495,[[[24820,25495,[\"The game allows collection of associations that are easy for humans and challenging for models. Performance on the data collected via the game is 15–52% with 10-12 candidates, and 47–55% with 5-6 candidates. All models’ per-formances are far below human performance (90% and 92%, see last row). We highlight that although our rival AI model is CLIP with RN50, the created data is still challenging even for mod-els order-of-magnitude larger. We also see a significant performance drop with most mod-els when increasing the number of candidates without hurting human accuracy, indicating that humans are robust to the increased difficulty level while models struggle with it. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"90632553-d9a9-4e3e-940b-6e5cb35b899a\"]]]]]],[[null,null,0.7265139898093562,[[null,11200,11915]],[[[11200,11221,[[[11200,11221,[\"2.2 Human Annotation \"]]]]],[11221,11915,[[[11221,11915,[\"We hire Amazon Mechanical Turk workers to play the WinoGAViL game. We develop qualification tests to select high-quality annotators and collect the annotators’ demographic information. Spymas-ters screen example is presented in Figure 3; See Appendix A for more details.5 We have several options for the total number of candidates: 5, 6, 10 or 12. With more candidates, the task naturally becomes harder. The spymasters are allowed to select between 2-5 images. Full annotation results and statistics are presented in Table 1. The scores of both humans and models is the Jaccard index of between their created associations instances. The annotation task includes three steps, elaborated below. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"34e5b69e-5f5b-4d7b-ac86-3d079c5e2f12\"]],[null,null,0.7280073562996616,[[null,5997,7000]],[[[5997,5998,[[[5997,5998,[\"2\"]]]]],[5998,6130,[[[5998,6130,[\"data generated by WinoGAViL is much more challenging to machines, highlighting the value of our gamified data collection framework. \"]]]]],[6130,6156,[[[6130,6156,[\"2 The WinoGAViL Benchmark \"]]]]],[6156,6398,[[[6156,6398,[\"We start by presenting the game as a framework for collecting challenging associations (§2.1). Second, we describe how we crowd-source a test set using the game (§2.2). Finally, we analyze the collected dataset and provide statistics (§2.3). \"]]]]],[6398,7000,[[[6398,7000,[\"Throughout this paper we use the Jaccard index, which is the intersection of selected candidates divided by the union of selected candidates.3 This metric does not reward random guesses highly. The random expected Jaccard index is 38%, 34%, 24%, 17% with 5/6/10/12 candidates respectively. For example, in Figure 1c the Jaccard index (‘Human score’) of the solvers is 100%, since the intersection of the selections is the same as the union. In Figure 1b the AI model selection is 1/3, so the Jaccard index (‘Model score’) is 33%: there are three images in the union, and one image in the intersection. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"41c141be-6b16-428d-9d05-4c799a0cfb66\"]],[null,null,0.7284902849189898,[[null,24390,25495]],[[[24390,24406,[[[24390,24406,[\"Model Game SWOW \"]]]]],[24406,24439,[[[24406,24439,[\"# Candidates 10 \\u0026 12 5 \\u0026 6 5 \\u0026 6 \"]]]]],[24439,24529,[[[24439,24529,[\"CLIP-RN50x64/14 38 50 70 CLIP-VIT-L/14 40 53 74 CLIP-VIT-B/32 41 53 74 CLIP-RN50 35 50 73 \"]]]]],[24529,24576,[[[24529,24576,[\"CLIP-ViL 15 47 66 ViLT 52 55 59 X-VLM 46 53 68 \"]]]]],[24576,24592,[[[24576,24592,[\"Humans 90 92 95 \"]]]]],[24592,24820,[[[24592,24820,[\"Zero-shot results on WinoGAViL dataset and the SWOW vision baseline dataset are presented in Table 4. Table 10 (Appendix A) shows full statis-tics and performance for the different number of candidates and created associations. \"]]]]],[24820,25495,[[[24820,25495,[\"The game allows collection of associations that are easy for humans and challenging for models. Performance on the data collected via the game is 15–52% with 10-12 candidates, and 47–55% with 5-6 candidates. All models’ per-formances are far below human performance (90% and 92%, see last row). We highlight that although our rival AI model is CLIP with RN50, the created data is still challenging even for mod-els order-of-magnitude larger. We also see a significant performance drop with most mod-els when increasing the number of candidates without hurting human accuracy, indicating that humans are robust to the increased difficulty level while models struggle with it. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"90632553-d9a9-4e3e-940b-6e5cb35b899a\"]]],[[[null,61,61],[0]],[[null,117,283],[1]],[[null,283,468],[2]]]]"]]
9446
[["wrb.fr",null,"[[\"With more candidate images, the task naturally becomes **harder** [1]. This is reflected in the **random expected Jaccard index**, which decreases significantly with more candidates: for 5 candidates it's 38%, for 6 candidates it's 34%, for 10 candidates it's 24%, and for 12 candidates it's 17% [2]. While this increased difficulty leads to a significant performance drop for most AI models, human accuracy remains high, indicating human robustness to this increased difficulty level [3].\",null,[\"93d755ef-f44e-4a42-93d0-3b0fb15ee01d\",\"227f3144-f882-4e58-9279-9e6bb4e146d6\",2765451006],null,[[[[0,469,[[[0,55,[\"With more candidate images, the task naturally becomes \"]],[55,61,[\"harder\",[true]]],[61,88,[\". This is reflected in the \"]],[88,117,[\"random expected Jaccard index\",[true]]],[117,469,[\", which decreases significantly with more candidates: for 5 candidates it's 38%, for 6 candidates it's 34%, for 10 candidates it's 24%, and for 12 candidates it's 17%. While this increased difficulty leads to a significant performance drop for most AI models, human accuracy remains high, indicating human robustness to this increased difficulty level.\"]]]]]],[[[\"34e5b69e-5f5b-4d7b-ac86-3d079c5e2f12\"],[null,61,61]],[[\"41c141be-6b16-428d-9d05-4c799a0cfb66\"],[null,117,283]],[[\"90632553-d9a9-4e3e-940b-6e5cb35b899a\"],[null,283,468]]]],null,null,[[[\"34e5b69e-5f5b-4d7b-ac86-3d079c5e2f12\"],[null,null,0.7265139898093562,[[null,11200,11915]],[[[11200,11221,[[[11200,11221,[\"2.2 Human Annotation \"]]]]],[11221,11915,[[[11221,11915,[\"We hire Amazon Mechanical Turk workers to play the WinoGAViL game. We develop qualification tests to select high-quality annotators and collect the annotators’ demographic information. Spymas-ters screen example is presented in Figure 3; See Appendix A for more details.5 We have several options for the total number of candidates: 5, 6, 10 or 12. With more candidates, the task naturally becomes harder. The spymasters are allowed to select between 2-5 images. Full annotation results and statistics are presented in Table 1. The scores of both humans and models is the Jaccard index of between their created associations instances. The annotation task includes three steps, elaborated below. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"34e5b69e-5f5b-4d7b-ac86-3d079c5e2f12\"]]],[[\"41c141be-6b16-428d-9d05-4c799a0cfb66\"],[null,null,0.7280073562996616,[[null,5997,7000]],[[[5997,5998,[[[5997,5998,[\"2\"]]]]],[5998,6130,[[[5998,6130,[\"data generated by WinoGAViL is much more challenging to machines, highlighting the value of our gamified data collection framework. \"]]]]],[6130,6156,[[[6130,6156,[\"2 The WinoGAViL Benchmark \"]]]]],[6156,6398,[[[6156,6398,[\"We start by presenting the game as a framework for collecting challenging associations (§2.1). Second, we describe how we crowd-source a test set using the game (§2.2). Finally, we analyze the collected dataset and provide statistics (§2.3). \"]]]]],[6398,7000,[[[6398,7000,[\"Throughout this paper we use the Jaccard index, which is the intersection of selected candidates divided by the union of selected candidates.3 This metric does not reward random guesses highly. The random expected Jaccard index is 38%, 34%, 24%, 17% with 5/6/10/12 candidates respectively. For example, in Figure 1c the Jaccard index (‘Human score’) of the solvers is 100%, since the intersection of the selections is the same as the union. In Figure 1b the AI model selection is 1/3, so the Jaccard index (‘Model score’) is 33%: there are three images in the union, and one image in the intersection. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"41c141be-6b16-428d-9d05-4c799a0cfb66\"]]],[[\"90632553-d9a9-4e3e-940b-6e5cb35b899a\"],[null,null,0.7284902849189898,[[null,24390,25495]],[[[24390,24406,[[[24390,24406,[\"Model Game SWOW \"]]]]],[24406,24439,[[[24406,24439,[\"# Candidates 10 \\u0026 12 5 \\u0026 6 5 \\u0026 6 \"]]]]],[24439,24529,[[[24439,24529,[\"CLIP-RN50x64/14 38 50 70 CLIP-VIT-L/14 40 53 74 CLIP-VIT-B/32 41 53 74 CLIP-RN50 35 50 73 \"]]]]],[24529,24576,[[[24529,24576,[\"CLIP-ViL 15 47 66 ViLT 52 55 59 X-VLM 46 53 68 \"]]]]],[24576,24592,[[[24576,24592,[\"Humans 90 92 95 \"]]]]],[24592,24820,[[[24592,24820,[\"Zero-shot results on WinoGAViL dataset and the SWOW vision baseline dataset are presented in Table 4. Table 10 (Appendix A) shows full statis-tics and performance for the different number of candidates and created associations. \"]]]]],[24820,25495,[[[24820,25495,[\"The game allows collection of associations that are easy for humans and challenging for models. Performance on the data collected via the game is 15–52% with 10-12 candidates, and 47–55% with 5-6 candidates. All models’ per-formances are far below human performance (90% and 92%, see last row). We highlight that although our rival AI model is CLIP with RN50, the created data is still challenging even for mod-els order-of-magnitude larger. We also see a significant performance drop with most mod-els when increasing the number of candidates without hurting human accuracy, indicating that humans are robust to the increased difficulty level while models struggle with it. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"90632553-d9a9-4e3e-940b-6e5cb35b899a\"]]]]]],[[null,null,0.7265139898093562,[[null,11200,11915]],[[[11200,11221,[[[11200,11221,[\"2.2 Human Annotation \"]]]]],[11221,11915,[[[11221,11915,[\"We hire Amazon Mechanical Turk workers to play the WinoGAViL game. We develop qualification tests to select high-quality annotators and collect the annotators’ demographic information. Spymas-ters screen example is presented in Figure 3; See Appendix A for more details.5 We have several options for the total number of candidates: 5, 6, 10 or 12. With more candidates, the task naturally becomes harder. The spymasters are allowed to select between 2-5 images. Full annotation results and statistics are presented in Table 1. The scores of both humans and models is the Jaccard index of between their created associations instances. The annotation task includes three steps, elaborated below. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"34e5b69e-5f5b-4d7b-ac86-3d079c5e2f12\"]],[null,null,0.7280073562996616,[[null,5997,7000]],[[[5997,5998,[[[5997,5998,[\"2\"]]]]],[5998,6130,[[[5998,6130,[\"data generated by WinoGAViL is much more challenging to machines, highlighting the value of our gamified data collection framework. \"]]]]],[6130,6156,[[[6130,6156,[\"2 The WinoGAViL Benchmark \"]]]]],[6156,6398,[[[6156,6398,[\"We start by presenting the game as a framework for collecting challenging associations (§2.1). Second, we describe how we crowd-source a test set using the game (§2.2). Finally, we analyze the collected dataset and provide statistics (§2.3). \"]]]]],[6398,7000,[[[6398,7000,[\"Throughout this paper we use the Jaccard index, which is the intersection of selected candidates divided by the union of selected candidates.3 This metric does not reward random guesses highly. The random expected Jaccard index is 38%, 34%, 24%, 17% with 5/6/10/12 candidates respectively. For example, in Figure 1c the Jaccard index (‘Human score’) of the solvers is 100%, since the intersection of the selections is the same as the union. In Figure 1b the AI model selection is 1/3, so the Jaccard index (‘Model score’) is 33%: there are three images in the union, and one image in the intersection. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"41c141be-6b16-428d-9d05-4c799a0cfb66\"]],[null,null,0.7284902849189898,[[null,24390,25495]],[[[24390,24406,[[[24390,24406,[\"Model Game SWOW \"]]]]],[24406,24439,[[[24406,24439,[\"# Candidates 10 \\u0026 12 5 \\u0026 6 5 \\u0026 6 \"]]]]],[24439,24529,[[[24439,24529,[\"CLIP-RN50x64/14 38 50 70 CLIP-VIT-L/14 40 53 74 CLIP-VIT-B/32 41 53 74 CLIP-RN50 35 50 73 \"]]]]],[24529,24576,[[[24529,24576,[\"CLIP-ViL 15 47 66 ViLT 52 55 59 X-VLM 46 53 68 \"]]]]],[24576,24592,[[[24576,24592,[\"Humans 90 92 95 \"]]]]],[24592,24820,[[[24592,24820,[\"Zero-shot results on WinoGAViL dataset and the SWOW vision baseline dataset are presented in Table 4. Table 10 (Appendix A) shows full statis-tics and performance for the different number of candidates and created associations. \"]]]]],[24820,25495,[[[24820,25495,[\"The game allows collection of associations that are easy for humans and challenging for models. Performance on the data collected via the game is 15–52% with 10-12 candidates, and 47–55% with 5-6 candidates. All models’ per-formances are far below human performance (90% and 92%, see last row). We highlight that although our rival AI model is CLIP with RN50, the created data is still challenging even for mod-els order-of-magnitude larger. We also see a significant performance drop with most mod-els when increasing the number of candidates without hurting human accuracy, indicating that humans are robust to the increased difficulty level while models struggle with it. \"]]]]]]],[[[\"680edbb5-ba8f-4fb4-9ebb-d85b7ed5a052\"],\"3dd0cd6d-c531-4f8b-a27d-8fdd48699fd1\"]],[\"90632553-d9a9-4e3e-940b-6e5cb35b899a\"]]],[[[null,61,61],[0]],[[null,117,283],[1]],[[null,283,468],[2]]]]"]]
60
[["di",5988],["af.httprm",5988,"-4919251665808391464",49]]
27
[["e",6,null,null,20886]]
