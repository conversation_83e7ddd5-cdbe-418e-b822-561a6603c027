)]}'

6438
[["wrb.fr",null,"[[\"The task described in the paper, hypernym prediction, involves **finding the correct point to attach a new synset (node) to the existing WordNet taxonomy** by predicting its direct hypernym [1, 2].\\n\\nTaxonomy induction, on the other hand, is the broader task of **constructing or\",null,[\"aba55f55-e54a-479f-a24e-69750bde0de0\",\"f5b6d5ee-5504-4eca-ade4-bf6ef2247862\",*********],null,[[[[0,186,[[[0,63,[\"The task described in the paper, hypernym prediction, involves \"]],[63,151,[\"finding the correct point to attach a new synset (node) to the existing WordNet taxonomy\",[true]]],[151,186,[\" by predicting its direct hypernym.\"]]]]]],[[[\"e9357e86-b479-4e69-bd53-39789c7e0094\"],[null,152,185]],[[\"333d117d-5999-4a97-8e93-f2547d283cf0\"],[null,152,185]]]],null,null,[[[\"e9357e86-b479-4e69-bd53-39789c7e0094\"],[null,null,0.6943858785848589,[[null,1526,2927]],[[[1526,1925,[[[1526,1925,[\"Hypernymy prediction is often evaluated against a given taxonomy, typically WordNet (Fellbaum, 1998). The main hypothesis that we pursue in this paper is that knowledge of this taxonomy, in particular of taxonomy paths, will be helpful for hypernymy prediction. So we introduce two simple encoder-decoder based models for hypernym prediction that make use of information in the full taxonomy paths. \"]]]]],[1925,2927,[[[1925,2927,[\"There has been much recent work on modeling lexical relations based on distributed representations (Pinter and Eisenstein, 2018; Bernier-Colborne and Barriere, 2018; Nickel and Kiela, 2018). However, the task formulations and evaluation datasets have differed widely, making it hard to compare different approaches. We focus on evaluating on hypernymy, rather than jointly on many relations, which can mask strong performance differences across relations. We evaluate our encoder-decoder models against several previous models that have not been evaluated in the same setting before. Like many other approaches, we use WordNet as the basis for our experiments. We formulate the task as the task of finding the correct point to attach a new node (synset) to the WordNet taxonomy. We build on the existing WN18RR dataset (Dettmers et al., 2018), but filter its hypernymy pairs to produce WN18RR-hp, a subset that is leak-free with respect to approaches that use taxonomy paths during training, as we do. \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"e9357e86-b479-4e69-bd53-39789c7e0094\"]]],[[\"333d117d-5999-4a97-8e93-f2547d283cf0\"],[null,null,0.6655935301243988,[[null,5744,6587]],[[[5744,6587,[[[5744,6587,[\"Like previous work in knowledge base completion (Bordes et al., 2013; Nickel and Kiela, 2017; Pinter and Eisenstein, 2018; Balažević et al., 2019), we take WordNet as our experimental space, so we learn hypernymy between synsets rather than raw lemmas. A synset is a basic lexical unit in WordNet, defined as a set of lemmas that are synonymous to each other. A synset thus also functions as one of the senses for each of the lemmas in the set. For example, the synset mark.n.01 (a number or letter indicating quality) consists of the three lemmas mark, grade, and score. Given a new synset, which we call the source node, our task is to predict its direct hypernym or target node from among the synsets in WordNet. For example, for the source node woolly daisy.n.01, the model should identify wildflower.n.01 as the target node in the graph. \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"333d117d-5999-4a97-8e93-f2547d283cf0\"]]]]]],[[null,null,0.6943858785848589,[[null,1526,2927]],[[[1526,1925,[[[1526,1925,[\"Hypernymy prediction is often evaluated against a given taxonomy, typically WordNet (Fellbaum, 1998). The main hypothesis that we pursue in this paper is that knowledge of this taxonomy, in particular of taxonomy paths, will be helpful for hypernymy prediction. So we introduce two simple encoder-decoder based models for hypernym prediction that make use of information in the full taxonomy paths. \"]]]]],[1925,2927,[[[1925,2927,[\"There has been much recent work on modeling lexical relations based on distributed representations (Pinter and Eisenstein, 2018; Bernier-Colborne and Barriere, 2018; Nickel and Kiela, 2018). However, the task formulations and evaluation datasets have differed widely, making it hard to compare different approaches. We focus on evaluating on hypernymy, rather than jointly on many relations, which can mask strong performance differences across relations. We evaluate our encoder-decoder models against several previous models that have not been evaluated in the same setting before. Like many other approaches, we use WordNet as the basis for our experiments. We formulate the task as the task of finding the correct point to attach a new node (synset) to the WordNet taxonomy. We build on the existing WN18RR dataset (Dettmers et al., 2018), but filter its hypernymy pairs to produce WN18RR-hp, a subset that is leak-free with respect to approaches that use taxonomy paths during training, as we do. \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"e9357e86-b479-4e69-bd53-39789c7e0094\"]],[null,null,0.6655935301243988,[[null,5744,6587]],[[[5744,6587,[[[5744,6587,[\"Like previous work in knowledge base completion (Bordes et al., 2013; Nickel and Kiela, 2017; Pinter and Eisenstein, 2018; Balažević et al., 2019), we take WordNet as our experimental space, so we learn hypernymy between synsets rather than raw lemmas. A synset is a basic lexical unit in WordNet, defined as a set of lemmas that are synonymous to each other. A synset thus also functions as one of the senses for each of the lemmas in the set. For example, the synset mark.n.01 (a number or letter indicating quality) consists of the three lemmas mark, grade, and score. Given a new synset, which we call the source node, our task is to predict its direct hypernym or target node from among the synsets in WordNet. For example, for the source node woolly daisy.n.01, the model should identify wildflower.n.01 as the target node in the graph. \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"333d117d-5999-4a97-8e93-f2547d283cf0\"]]],[[[null,152,185],[0,1]]],[[\"What is hypernymy?\",\"What is WordNet?\",\"How is H@1 calculated?\"]]]"]]
9364
[["wrb.fr",null,"[[\"The task described in the paper, hypernym prediction, involves **finding the correct point to attach a new synset (node) to the existing WordNet taxonomy** by predicting its direct hypernym [1, 2].\\n\\nTaxonomy induction, on the other hand, is the broader task of **constructing or extending a taxonomy** [3].\",null,[\"aba55f55-e54a-479f-a24e-69750bde0de0\",\"f5b6d5ee-5504-4eca-ade4-bf6ef2247862\",*********],null,[[[[0,186,[[[0,63,[\"The task described in the paper, hypernym prediction, involves \"]],[63,151,[\"finding the correct point to attach a new synset (node) to the existing WordNet taxonomy\",[true]]],[151,186,[\" by predicting its direct hypernym.\"]]]]]],[[[\"673ba455-b28b-4c4c-893d-825710f6efb5\"],[null,152,185]],[[\"c686d8e1-4fe0-4df7-a0ac-06b0f6103aa5\"],[null,152,185]],[[\"54de5749-06e1-4bb2-b8c9-3f6879df5dc4\"],[null,284,284]]]],null,null,[[[\"673ba455-b28b-4c4c-893d-825710f6efb5\"],[null,null,0.6943858785848589,[[null,1526,2927]],[[[1526,1925,[[[1526,1925,[\"Hypernymy prediction is often evaluated against a given taxonomy, typically WordNet (Fellbaum, 1998). The main hypothesis that we pursue in this paper is that knowledge of this taxonomy, in particular of taxonomy paths, will be helpful for hypernymy prediction. So we introduce two simple encoder-decoder based models for hypernym prediction that make use of information in the full taxonomy paths. \"]]]]],[1925,2927,[[[1925,2927,[\"There has been much recent work on modeling lexical relations based on distributed representations (Pinter and Eisenstein, 2018; Bernier-Colborne and Barriere, 2018; Nickel and Kiela, 2018). However, the task formulations and evaluation datasets have differed widely, making it hard to compare different approaches. We focus on evaluating on hypernymy, rather than jointly on many relations, which can mask strong performance differences across relations. We evaluate our encoder-decoder models against several previous models that have not been evaluated in the same setting before. Like many other approaches, we use WordNet as the basis for our experiments. We formulate the task as the task of finding the correct point to attach a new node (synset) to the WordNet taxonomy. We build on the existing WN18RR dataset (Dettmers et al., 2018), but filter its hypernymy pairs to produce WN18RR-hp, a subset that is leak-free with respect to approaches that use taxonomy paths during training, as we do. \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"673ba455-b28b-4c4c-893d-825710f6efb5\"]]],[[\"c686d8e1-4fe0-4df7-a0ac-06b0f6103aa5\"],[null,null,0.6655935301243988,[[null,5744,6587]],[[[5744,6587,[[[5744,6587,[\"Like previous work in knowledge base completion (Bordes et al., 2013; Nickel and Kiela, 2017; Pinter and Eisenstein, 2018; Balažević et al., 2019), we take WordNet as our experimental space, so we learn hypernymy between synsets rather than raw lemmas. A synset is a basic lexical unit in WordNet, defined as a set of lemmas that are synonymous to each other. A synset thus also functions as one of the senses for each of the lemmas in the set. For example, the synset mark.n.01 (a number or letter indicating quality) consists of the three lemmas mark, grade, and score. Given a new synset, which we call the source node, our task is to predict its direct hypernym or target node from among the synsets in WordNet. For example, for the source node woolly daisy.n.01, the model should identify wildflower.n.01 as the target node in the graph. \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"c686d8e1-4fe0-4df7-a0ac-06b0f6103aa5\"]]],[[\"54de5749-06e1-4bb2-b8c9-3f6879df5dc4\"],[null,null,0.741922219981587,[[null,4103,5200]],[[[4103,4509,[[[4103,4509,[\"Table 1: We frame hypernym prediction as a sequence generation problem. Given a query hyponym (e.g., pizza.n.01), the hypo2path rev model generates its taxonomy path, from its direct hypernym (dish.n.02) to the root node (entity.n.01). 3 and 7 indicate a correct and an incorrect prediction, respectively. In each example, an underlined synset corresponds to what the model predicted as a direct hypernym. \"]]]]],[4509,4531,[[[4509,4531,[\"2 Hypernym Prediction \"]]]]],[4531,5200,[[[4531,5200,[\"Several tasks related to hypernymy have been proposed under different names: extracting is-a relations from text (hypernym discovery) (Hearst, 1992; Snow et al., 2005; Camacho-Collados et al., 2018), binary classification of whether two given words are in a hypernym relation (hypernym detection) (Weeds et al., 2014; Shwartz et al., 2016; Roller et al., 2018), and constructing or extending a taxonomy (taxonomy induction) (Snow et al., 2006; Jurgens and Pilehvar, 2016). Another recently introduced task is hierarchi-cal path completion (Alsuhaibani et al., 2019), where, given a hypernym path of length 4 from WordNet, the task is to predict the correct hyponym(s). \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"54de5749-06e1-4bb2-b8c9-3f6879df5dc4\"]]]]]],[[null,null,0.6943858785848589,[[null,1526,2927]],[[[1526,1925,[[[1526,1925,[\"Hypernymy prediction is often evaluated against a given taxonomy, typically WordNet (Fellbaum, 1998). The main hypothesis that we pursue in this paper is that knowledge of this taxonomy, in particular of taxonomy paths, will be helpful for hypernymy prediction. So we introduce two simple encoder-decoder based models for hypernym prediction that make use of information in the full taxonomy paths. \"]]]]],[1925,2927,[[[1925,2927,[\"There has been much recent work on modeling lexical relations based on distributed representations (Pinter and Eisenstein, 2018; Bernier-Colborne and Barriere, 2018; Nickel and Kiela, 2018). However, the task formulations and evaluation datasets have differed widely, making it hard to compare different approaches. We focus on evaluating on hypernymy, rather than jointly on many relations, which can mask strong performance differences across relations. We evaluate our encoder-decoder models against several previous models that have not been evaluated in the same setting before. Like many other approaches, we use WordNet as the basis for our experiments. We formulate the task as the task of finding the correct point to attach a new node (synset) to the WordNet taxonomy. We build on the existing WN18RR dataset (Dettmers et al., 2018), but filter its hypernymy pairs to produce WN18RR-hp, a subset that is leak-free with respect to approaches that use taxonomy paths during training, as we do. \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"673ba455-b28b-4c4c-893d-825710f6efb5\"]],[null,null,0.6655935301243988,[[null,5744,6587]],[[[5744,6587,[[[5744,6587,[\"Like previous work in knowledge base completion (Bordes et al., 2013; Nickel and Kiela, 2017; Pinter and Eisenstein, 2018; Balažević et al., 2019), we take WordNet as our experimental space, so we learn hypernymy between synsets rather than raw lemmas. A synset is a basic lexical unit in WordNet, defined as a set of lemmas that are synonymous to each other. A synset thus also functions as one of the senses for each of the lemmas in the set. For example, the synset mark.n.01 (a number or letter indicating quality) consists of the three lemmas mark, grade, and score. Given a new synset, which we call the source node, our task is to predict its direct hypernym or target node from among the synsets in WordNet. For example, for the source node woolly daisy.n.01, the model should identify wildflower.n.01 as the target node in the graph. \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"c686d8e1-4fe0-4df7-a0ac-06b0f6103aa5\"]],[null,null,0.741922219981587,[[null,4103,5200]],[[[4103,4509,[[[4103,4509,[\"Table 1: We frame hypernym prediction as a sequence generation problem. Given a query hyponym (e.g., pizza.n.01), the hypo2path rev model generates its taxonomy path, from its direct hypernym (dish.n.02) to the root node (entity.n.01). 3 and 7 indicate a correct and an incorrect prediction, respectively. In each example, an underlined synset corresponds to what the model predicted as a direct hypernym. \"]]]]],[4509,4531,[[[4509,4531,[\"2 Hypernym Prediction \"]]]]],[4531,5200,[[[4531,5200,[\"Several tasks related to hypernymy have been proposed under different names: extracting is-a relations from text (hypernym discovery) (Hearst, 1992; Snow et al., 2005; Camacho-Collados et al., 2018), binary classification of whether two given words are in a hypernym relation (hypernym detection) (Weeds et al., 2014; Shwartz et al., 2016; Roller et al., 2018), and constructing or extending a taxonomy (taxonomy induction) (Snow et al., 2006; Jurgens and Pilehvar, 2016). Another recently introduced task is hierarchi-cal path completion (Alsuhaibani et al., 2019), where, given a hypernym path of length 4 from WordNet, the task is to predict the correct hyponym(s). \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"54de5749-06e1-4bb2-b8c9-3f6879df5dc4\"]]],[[[null,152,185],[0,1]],[[null,284,284],[2]]],[[\"What is hypernymy?\",\"What is WordNet?\",\"How is H@1 calculated?\"]]]"]]
9535
[["wrb.fr",null,"[[\"The task described in the paper, hypernym prediction, involves **finding the correct point to attach a new synset (node) to the existing WordNet taxonomy** by predicting its direct hypernym [1, 2].\\n\\nTaxonomy induction, on the other hand, is the broader task of **constructing or extending a taxonomy** [3].\",null,[\"aba55f55-e54a-479f-a24e-69750bde0de0\",\"f5b6d5ee-5504-4eca-ade4-bf6ef2247862\",*********],null,[[[[0,186,[[[0,63,[\"The task described in the paper, hypernym prediction, involves \"]],[63,151,[\"finding the correct point to attach a new synset (node) to the existing WordNet taxonomy\",[true]]],[151,186,[\" by predicting its direct hypernym.\"]]]]],[186,285,[[[186,248,[\"Taxonomy induction, on the other hand, is the broader task of \"]],[248,284,[\"constructing or extending a taxonomy\",[true]]],[284,285,[\".\"]]]]]],[[[\"673ba455-b28b-4c4c-893d-825710f6efb5\"],[null,152,185]],[[\"c686d8e1-4fe0-4df7-a0ac-06b0f6103aa5\"],[null,152,185]],[[\"54de5749-06e1-4bb2-b8c9-3f6879df5dc4\"],[null,284,284]]]],null,null,[[[\"673ba455-b28b-4c4c-893d-825710f6efb5\"],[null,null,0.6943858785848589,[[null,1526,2927]],[[[1526,1925,[[[1526,1925,[\"Hypernymy prediction is often evaluated against a given taxonomy, typically WordNet (Fellbaum, 1998). The main hypothesis that we pursue in this paper is that knowledge of this taxonomy, in particular of taxonomy paths, will be helpful for hypernymy prediction. So we introduce two simple encoder-decoder based models for hypernym prediction that make use of information in the full taxonomy paths. \"]]]]],[1925,2927,[[[1925,2927,[\"There has been much recent work on modeling lexical relations based on distributed representations (Pinter and Eisenstein, 2018; Bernier-Colborne and Barriere, 2018; Nickel and Kiela, 2018). However, the task formulations and evaluation datasets have differed widely, making it hard to compare different approaches. We focus on evaluating on hypernymy, rather than jointly on many relations, which can mask strong performance differences across relations. We evaluate our encoder-decoder models against several previous models that have not been evaluated in the same setting before. Like many other approaches, we use WordNet as the basis for our experiments. We formulate the task as the task of finding the correct point to attach a new node (synset) to the WordNet taxonomy. We build on the existing WN18RR dataset (Dettmers et al., 2018), but filter its hypernymy pairs to produce WN18RR-hp, a subset that is leak-free with respect to approaches that use taxonomy paths during training, as we do. \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"673ba455-b28b-4c4c-893d-825710f6efb5\"]]],[[\"c686d8e1-4fe0-4df7-a0ac-06b0f6103aa5\"],[null,null,0.6655935301243988,[[null,5744,6587]],[[[5744,6587,[[[5744,6587,[\"Like previous work in knowledge base completion (Bordes et al., 2013; Nickel and Kiela, 2017; Pinter and Eisenstein, 2018; Balažević et al., 2019), we take WordNet as our experimental space, so we learn hypernymy between synsets rather than raw lemmas. A synset is a basic lexical unit in WordNet, defined as a set of lemmas that are synonymous to each other. A synset thus also functions as one of the senses for each of the lemmas in the set. For example, the synset mark.n.01 (a number or letter indicating quality) consists of the three lemmas mark, grade, and score. Given a new synset, which we call the source node, our task is to predict its direct hypernym or target node from among the synsets in WordNet. For example, for the source node woolly daisy.n.01, the model should identify wildflower.n.01 as the target node in the graph. \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"c686d8e1-4fe0-4df7-a0ac-06b0f6103aa5\"]]],[[\"54de5749-06e1-4bb2-b8c9-3f6879df5dc4\"],[null,null,0.741922219981587,[[null,4103,5200]],[[[4103,4509,[[[4103,4509,[\"Table 1: We frame hypernym prediction as a sequence generation problem. Given a query hyponym (e.g., pizza.n.01), the hypo2path rev model generates its taxonomy path, from its direct hypernym (dish.n.02) to the root node (entity.n.01). 3 and 7 indicate a correct and an incorrect prediction, respectively. In each example, an underlined synset corresponds to what the model predicted as a direct hypernym. \"]]]]],[4509,4531,[[[4509,4531,[\"2 Hypernym Prediction \"]]]]],[4531,5200,[[[4531,5200,[\"Several tasks related to hypernymy have been proposed under different names: extracting is-a relations from text (hypernym discovery) (Hearst, 1992; Snow et al., 2005; Camacho-Collados et al., 2018), binary classification of whether two given words are in a hypernym relation (hypernym detection) (Weeds et al., 2014; Shwartz et al., 2016; Roller et al., 2018), and constructing or extending a taxonomy (taxonomy induction) (Snow et al., 2006; Jurgens and Pilehvar, 2016). Another recently introduced task is hierarchi-cal path completion (Alsuhaibani et al., 2019), where, given a hypernym path of length 4 from WordNet, the task is to predict the correct hyponym(s). \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"54de5749-06e1-4bb2-b8c9-3f6879df5dc4\"]]]]]],[[null,null,0.6943858785848589,[[null,1526,2927]],[[[1526,1925,[[[1526,1925,[\"Hypernymy prediction is often evaluated against a given taxonomy, typically WordNet (Fellbaum, 1998). The main hypothesis that we pursue in this paper is that knowledge of this taxonomy, in particular of taxonomy paths, will be helpful for hypernymy prediction. So we introduce two simple encoder-decoder based models for hypernym prediction that make use of information in the full taxonomy paths. \"]]]]],[1925,2927,[[[1925,2927,[\"There has been much recent work on modeling lexical relations based on distributed representations (Pinter and Eisenstein, 2018; Bernier-Colborne and Barriere, 2018; Nickel and Kiela, 2018). However, the task formulations and evaluation datasets have differed widely, making it hard to compare different approaches. We focus on evaluating on hypernymy, rather than jointly on many relations, which can mask strong performance differences across relations. We evaluate our encoder-decoder models against several previous models that have not been evaluated in the same setting before. Like many other approaches, we use WordNet as the basis for our experiments. We formulate the task as the task of finding the correct point to attach a new node (synset) to the WordNet taxonomy. We build on the existing WN18RR dataset (Dettmers et al., 2018), but filter its hypernymy pairs to produce WN18RR-hp, a subset that is leak-free with respect to approaches that use taxonomy paths during training, as we do. \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"673ba455-b28b-4c4c-893d-825710f6efb5\"]],[null,null,0.6655935301243988,[[null,5744,6587]],[[[5744,6587,[[[5744,6587,[\"Like previous work in knowledge base completion (Bordes et al., 2013; Nickel and Kiela, 2017; Pinter and Eisenstein, 2018; Balažević et al., 2019), we take WordNet as our experimental space, so we learn hypernymy between synsets rather than raw lemmas. A synset is a basic lexical unit in WordNet, defined as a set of lemmas that are synonymous to each other. A synset thus also functions as one of the senses for each of the lemmas in the set. For example, the synset mark.n.01 (a number or letter indicating quality) consists of the three lemmas mark, grade, and score. Given a new synset, which we call the source node, our task is to predict its direct hypernym or target node from among the synsets in WordNet. For example, for the source node woolly daisy.n.01, the model should identify wildflower.n.01 as the target node in the graph. \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"c686d8e1-4fe0-4df7-a0ac-06b0f6103aa5\"]],[null,null,0.741922219981587,[[null,4103,5200]],[[[4103,4509,[[[4103,4509,[\"Table 1: We frame hypernym prediction as a sequence generation problem. Given a query hyponym (e.g., pizza.n.01), the hypo2path rev model generates its taxonomy path, from its direct hypernym (dish.n.02) to the root node (entity.n.01). 3 and 7 indicate a correct and an incorrect prediction, respectively. In each example, an underlined synset corresponds to what the model predicted as a direct hypernym. \"]]]]],[4509,4531,[[[4509,4531,[\"2 Hypernym Prediction \"]]]]],[4531,5200,[[[4531,5200,[\"Several tasks related to hypernymy have been proposed under different names: extracting is-a relations from text (hypernym discovery) (Hearst, 1992; Snow et al., 2005; Camacho-Collados et al., 2018), binary classification of whether two given words are in a hypernym relation (hypernym detection) (Weeds et al., 2014; Shwartz et al., 2016; Roller et al., 2018), and constructing or extending a taxonomy (taxonomy induction) (Snow et al., 2006; Jurgens and Pilehvar, 2016). Another recently introduced task is hierarchi-cal path completion (Alsuhaibani et al., 2019), where, given a hypernym path of length 4 from WordNet, the task is to predict the correct hyponym(s). \"]]]]]]],[[[\"ebfd2830-b4b0-481f-8ec3-c897335d2748\"],\"7c22d0b6-f690-40aa-8161-8c80c18a813a\"]],[\"54de5749-06e1-4bb2-b8c9-3f6879df5dc4\"]]],[[[null,152,185],[0,1]],[[null,284,284],[2]]],[[\"What is hypernymy?\",\"What is WordNet?\",\"How is H@1 calculated?\"]]]"]]
59
[["di",4235],["af.httprm",4235,"5745939873524372175",49]]
27
[["e",6,null,null,25457]]
