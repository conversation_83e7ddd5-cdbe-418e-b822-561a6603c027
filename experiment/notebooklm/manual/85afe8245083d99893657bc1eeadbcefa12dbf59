)]}'

10589
[["wrb.fr",null,"[[\"\\\"1 psnr\\\" refers to a **1 dB (decibel) change in PSNR**, where PSNR (Peak Signal-to-Noise Ratio) is a measure of quality (i.e., negative distortion) in image compression [1-3]. For instance, the sources indicate theoretical room for improving image compression methods by at least **one dB in PSNR**\",null,[\"9d9b7258-a585-4e81-8008-82f9a2b880c8\",\"272f3ec4-4e83-4486-af12-602e3f0e1dc4\",978106763],null,[[null,[[[\"ead7d91f-9342-4723-8a9f-7258b92b9fab\"],[null,50,164]],[[\"b8cffb9b-1ba5-483e-9f38-7d27e09db98f\"],[null,50,164]],[[\"1efbdbaf-314f-4b07-9374-f537bc24e142\"],[null,50,164]]]],null,null,[[[\"ead7d91f-9342-4723-8a9f-7258b92b9fab\"],[null,null,0.6442762686374874,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"ead7d91f-9342-4723-8a9f-7258b92b9fab\"]]],[[\"b8cffb9b-1ba5-483e-9f38-7d27e09db98f\"],[null,null,0.6001184697857914,[[null,5362,5898]],[[[5362,5622,[[[5362,5622,[\"4. Our estimated R-D upper bounds on high-resolution natural images (evaluated on the standard Kodak and Tecnick datasets) indicate theoretical room for improvement of state-of-the-art image compression methods by at least one dB in PSNR, at various bitrates. \"]]]]],[5622,5898,[[[5622,5898,[\"We begin by reviewing the prerequisite rate-distortion theory in Section 2, then describe our upper and lower bound algorithms in Section 3 and Section 4, respectively. We discuss related work in Section 5, report experimental results in Section 6, and conclude in Section 7. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"b8cffb9b-1ba5-483e-9f38-7d27e09db98f\"]]],[[\"1efbdbaf-314f-4b07-9374-f537bc24e142\"],[null,null,0.6338094564526744,[[null,36120,38217]],[[[36120,36139,[[[36120,36139,[\"6.4 NATURAL IMAGES \"]]]]],[36139,38217,[[[36139,38217,[\"To establish upper bounds on the R-D function of natural images, we define variational distributions (QZ , QZ|X) on a Euclidean latent space for simplicity, and parameterize them as well as a learned decoder ω via hierarchical VAEs. We consider two VAE architectures: 1. we borrow the convolutional autoencoder architecture of a state-of-the-art image compression model (Minnen \\u0026 Singh, 2020), but use factorized Gaussians for the variational distributions (we still keep the deep factorized hyperprior, but no longer convolve it with a uniform prior); 2. we also reuse our ResNet-VAE architecture with 6 stochastic layers from the GAN experiments (Sec. 6.3). We trained the models with mean-squared error (MSE) distortion and various λ on the COCO 2017 (Lin et al., 2014) images, and evaluated them on the Kodak (1993) and Tecnick (Asuni \\u0026 Giachetti, 2014) datasets. Following image compression conventions, we report the rate in bits-per-pixel, and the quality (i.e., negative distortion) in PSNR averaged over the images for each (λ, model) pair 2. The resulting quality-rate (Q-R) curves can be interpreted as giving upper bounds on the R-D functions of the image-generating distributions. We plot them in Fig. 3, along with the Q-R performance (in actual bitrate) of various traditional and learned image compression methods (Ballé et al., 2017; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), for the Kodak dataset (see similar results on Tecnick in Appendix Fig. 11). Our β-VAE version of (Minnen \\u0026 Singh, 2020) (orange) lies on average 0.7 dB higher than the Q-R curves of the original compression model (red) and VTM (green). With a deeper latent hierarchy, our ResNet-(β-)VAE gives a higher Q-R curve (blue) that is on average 1.1 dB above the state-of-the-art Q-R curves (gap shaded in cyan). We leave it to future work to investigate which choice of autoencoder architecture and variational distributions are most effective, as well as how the theoretical R-D performance of such a β-VAE can be realized by a practical compression algorithm (see discussions in Sec. 5). \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"1efbdbaf-314f-4b07-9374-f537bc24e142\"]]]]]],[[null,null,0.6442762686374874,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"ead7d91f-9342-4723-8a9f-7258b92b9fab\"]],[null,null,0.6001184697857914,[[null,5362,5898]],[[[5362,5622,[[[5362,5622,[\"4. Our estimated R-D upper bounds on high-resolution natural images (evaluated on the standard Kodak and Tecnick datasets) indicate theoretical room for improvement of state-of-the-art image compression methods by at least one dB in PSNR, at various bitrates. \"]]]]],[5622,5898,[[[5622,5898,[\"We begin by reviewing the prerequisite rate-distortion theory in Section 2, then describe our upper and lower bound algorithms in Section 3 and Section 4, respectively. We discuss related work in Section 5, report experimental results in Section 6, and conclude in Section 7. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"b8cffb9b-1ba5-483e-9f38-7d27e09db98f\"]],[null,null,0.6338094564526744,[[null,36120,38217]],[[[36120,36139,[[[36120,36139,[\"6.4 NATURAL IMAGES \"]]]]],[36139,38217,[[[36139,38217,[\"To establish upper bounds on the R-D function of natural images, we define variational distributions (QZ , QZ|X) on a Euclidean latent space for simplicity, and parameterize them as well as a learned decoder ω via hierarchical VAEs. We consider two VAE architectures: 1. we borrow the convolutional autoencoder architecture of a state-of-the-art image compression model (Minnen \\u0026 Singh, 2020), but use factorized Gaussians for the variational distributions (we still keep the deep factorized hyperprior, but no longer convolve it with a uniform prior); 2. we also reuse our ResNet-VAE architecture with 6 stochastic layers from the GAN experiments (Sec. 6.3). We trained the models with mean-squared error (MSE) distortion and various λ on the COCO 2017 (Lin et al., 2014) images, and evaluated them on the Kodak (1993) and Tecnick (Asuni \\u0026 Giachetti, 2014) datasets. Following image compression conventions, we report the rate in bits-per-pixel, and the quality (i.e., negative distortion) in PSNR averaged over the images for each (λ, model) pair 2. The resulting quality-rate (Q-R) curves can be interpreted as giving upper bounds on the R-D functions of the image-generating distributions. We plot them in Fig. 3, along with the Q-R performance (in actual bitrate) of various traditional and learned image compression methods (Ballé et al., 2017; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), for the Kodak dataset (see similar results on Tecnick in Appendix Fig. 11). Our β-VAE version of (Minnen \\u0026 Singh, 2020) (orange) lies on average 0.7 dB higher than the Q-R curves of the original compression model (red) and VTM (green). With a deeper latent hierarchy, our ResNet-(β-)VAE gives a higher Q-R curve (blue) that is on average 1.1 dB above the state-of-the-art Q-R curves (gap shaded in cyan). We leave it to future work to investigate which choice of autoencoder architecture and variational distributions are most effective, as well as how the theoretical R-D performance of such a β-VAE can be realized by a practical compression algorithm (see discussions in Sec. 5). \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"1efbdbaf-314f-4b07-9374-f537bc24e142\"]]],[[[null,50,164],[0,1,2]]]]"]]
10740
[["wrb.fr",null,"[[\"\\\"1 psnr\\\" refers to a **1 dB (decibel) change in PSNR**, where PSNR (Peak Signal-to-Noise Ratio) is a measure of quality (i.e., negative distortion) in image compression [1-3]. For instance, the sources indicate theoretical room for improving image compression methods by at least **one dB in PSNR** [1, 2].\",null,[\"9d9b7258-a585-4e81-8008-82f9a2b880c8\",\"272f3ec4-4e83-4486-af12-602e3f0e1dc4\",978106763],null,[[null,[[[\"c7978e6a-87e8-44f2-9220-4ea0c490bc79\"],[null,50,164]],[[\"71761b0e-0c7a-4294-9d3a-7c44b8c82389\"],[null,50,164]],[[\"a1e92624-9d7d-477d-9d0c-e7e4437ec798\"],[null,50,164]],[[\"c7978e6a-87e8-44f2-9220-4ea0c490bc79\"],[null,284,284]],[[\"71761b0e-0c7a-4294-9d3a-7c44b8c82389\"],[null,284,284]]]],null,null,[[[\"c7978e6a-87e8-44f2-9220-4ea0c490bc79\"],[null,null,0.6442762686374874,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"c7978e6a-87e8-44f2-9220-4ea0c490bc79\"]]],[[\"71761b0e-0c7a-4294-9d3a-7c44b8c82389\"],[null,null,0.6001184697857914,[[null,5362,5898]],[[[5362,5622,[[[5362,5622,[\"4. Our estimated R-D upper bounds on high-resolution natural images (evaluated on the standard Kodak and Tecnick datasets) indicate theoretical room for improvement of state-of-the-art image compression methods by at least one dB in PSNR, at various bitrates. \"]]]]],[5622,5898,[[[5622,5898,[\"We begin by reviewing the prerequisite rate-distortion theory in Section 2, then describe our upper and lower bound algorithms in Section 3 and Section 4, respectively. We discuss related work in Section 5, report experimental results in Section 6, and conclude in Section 7. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"71761b0e-0c7a-4294-9d3a-7c44b8c82389\"]]],[[\"a1e92624-9d7d-477d-9d0c-e7e4437ec798\"],[null,null,0.6338094564526744,[[null,36120,38217]],[[[36120,36139,[[[36120,36139,[\"6.4 NATURAL IMAGES \"]]]]],[36139,38217,[[[36139,38217,[\"To establish upper bounds on the R-D function of natural images, we define variational distributions (QZ , QZ|X) on a Euclidean latent space for simplicity, and parameterize them as well as a learned decoder ω via hierarchical VAEs. We consider two VAE architectures: 1. we borrow the convolutional autoencoder architecture of a state-of-the-art image compression model (Minnen \\u0026 Singh, 2020), but use factorized Gaussians for the variational distributions (we still keep the deep factorized hyperprior, but no longer convolve it with a uniform prior); 2. we also reuse our ResNet-VAE architecture with 6 stochastic layers from the GAN experiments (Sec. 6.3). We trained the models with mean-squared error (MSE) distortion and various λ on the COCO 2017 (Lin et al., 2014) images, and evaluated them on the Kodak (1993) and Tecnick (Asuni \\u0026 Giachetti, 2014) datasets. Following image compression conventions, we report the rate in bits-per-pixel, and the quality (i.e., negative distortion) in PSNR averaged over the images for each (λ, model) pair 2. The resulting quality-rate (Q-R) curves can be interpreted as giving upper bounds on the R-D functions of the image-generating distributions. We plot them in Fig. 3, along with the Q-R performance (in actual bitrate) of various traditional and learned image compression methods (Ballé et al., 2017; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), for the Kodak dataset (see similar results on Tecnick in Appendix Fig. 11). Our β-VAE version of (Minnen \\u0026 Singh, 2020) (orange) lies on average 0.7 dB higher than the Q-R curves of the original compression model (red) and VTM (green). With a deeper latent hierarchy, our ResNet-(β-)VAE gives a higher Q-R curve (blue) that is on average 1.1 dB above the state-of-the-art Q-R curves (gap shaded in cyan). We leave it to future work to investigate which choice of autoencoder architecture and variational distributions are most effective, as well as how the theoretical R-D performance of such a β-VAE can be realized by a practical compression algorithm (see discussions in Sec. 5). \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"a1e92624-9d7d-477d-9d0c-e7e4437ec798\"]]]]]],[[null,null,0.6442762686374874,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"c7978e6a-87e8-44f2-9220-4ea0c490bc79\"]],[null,null,0.6001184697857914,[[null,5362,5898]],[[[5362,5622,[[[5362,5622,[\"4. Our estimated R-D upper bounds on high-resolution natural images (evaluated on the standard Kodak and Tecnick datasets) indicate theoretical room for improvement of state-of-the-art image compression methods by at least one dB in PSNR, at various bitrates. \"]]]]],[5622,5898,[[[5622,5898,[\"We begin by reviewing the prerequisite rate-distortion theory in Section 2, then describe our upper and lower bound algorithms in Section 3 and Section 4, respectively. We discuss related work in Section 5, report experimental results in Section 6, and conclude in Section 7. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"71761b0e-0c7a-4294-9d3a-7c44b8c82389\"]],[null,null,0.6338094564526744,[[null,36120,38217]],[[[36120,36139,[[[36120,36139,[\"6.4 NATURAL IMAGES \"]]]]],[36139,38217,[[[36139,38217,[\"To establish upper bounds on the R-D function of natural images, we define variational distributions (QZ , QZ|X) on a Euclidean latent space for simplicity, and parameterize them as well as a learned decoder ω via hierarchical VAEs. We consider two VAE architectures: 1. we borrow the convolutional autoencoder architecture of a state-of-the-art image compression model (Minnen \\u0026 Singh, 2020), but use factorized Gaussians for the variational distributions (we still keep the deep factorized hyperprior, but no longer convolve it with a uniform prior); 2. we also reuse our ResNet-VAE architecture with 6 stochastic layers from the GAN experiments (Sec. 6.3). We trained the models with mean-squared error (MSE) distortion and various λ on the COCO 2017 (Lin et al., 2014) images, and evaluated them on the Kodak (1993) and Tecnick (Asuni \\u0026 Giachetti, 2014) datasets. Following image compression conventions, we report the rate in bits-per-pixel, and the quality (i.e., negative distortion) in PSNR averaged over the images for each (λ, model) pair 2. The resulting quality-rate (Q-R) curves can be interpreted as giving upper bounds on the R-D functions of the image-generating distributions. We plot them in Fig. 3, along with the Q-R performance (in actual bitrate) of various traditional and learned image compression methods (Ballé et al., 2017; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), for the Kodak dataset (see similar results on Tecnick in Appendix Fig. 11). Our β-VAE version of (Minnen \\u0026 Singh, 2020) (orange) lies on average 0.7 dB higher than the Q-R curves of the original compression model (red) and VTM (green). With a deeper latent hierarchy, our ResNet-(β-)VAE gives a higher Q-R curve (blue) that is on average 1.1 dB above the state-of-the-art Q-R curves (gap shaded in cyan). We leave it to future work to investigate which choice of autoencoder architecture and variational distributions are most effective, as well as how the theoretical R-D performance of such a β-VAE can be realized by a practical compression algorithm (see discussions in Sec. 5). \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"a1e92624-9d7d-477d-9d0c-e7e4437ec798\"]]],[[[null,50,164],[0,1,2]],[[null,284,284],[0,1]]]]"]]
11133
[["wrb.fr",null,"[[\"\\\"1 psnr\\\" refers to a **1 dB (decibel) change in PSNR**, where PSNR (Peak Signal-to-Noise Ratio) is a measure of quality (i.e., negative distortion) in image compression [1-3]. For instance, the sources indicate theoretical room for improving image compression methods by at least **one dB in PSNR** [1, 2].\",null,[\"9d9b7258-a585-4e81-8008-82f9a2b880c8\",\"272f3ec4-4e83-4486-af12-602e3f0e1dc4\",978106763],null,[[[[0,285,[[[0,21,[\"\\\"1 psnr\\\" refers to a \"]],[21,50,[\"1 dB (decibel) change in PSNR\",[true]]],[50,270,[\", where PSNR (Peak Signal-to-Noise Ratio) is a measure of quality (i.e., negative distortion) in image compression. For instance, the sources indicate theoretical room for improving image compression methods by at least \"]],[270,284,[\"one dB in PSNR\",[true]]],[284,285,[\".\"]]]]]],[[[\"c7978e6a-87e8-44f2-9220-4ea0c490bc79\"],[null,50,164]],[[\"71761b0e-0c7a-4294-9d3a-7c44b8c82389\"],[null,50,164]],[[\"a1e92624-9d7d-477d-9d0c-e7e4437ec798\"],[null,50,164]],[[\"c7978e6a-87e8-44f2-9220-4ea0c490bc79\"],[null,284,284]],[[\"71761b0e-0c7a-4294-9d3a-7c44b8c82389\"],[null,284,284]]]],null,null,[[[\"c7978e6a-87e8-44f2-9220-4ea0c490bc79\"],[null,null,0.6442762686374874,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"c7978e6a-87e8-44f2-9220-4ea0c490bc79\"]]],[[\"71761b0e-0c7a-4294-9d3a-7c44b8c82389\"],[null,null,0.6001184697857914,[[null,5362,5898]],[[[5362,5622,[[[5362,5622,[\"4. Our estimated R-D upper bounds on high-resolution natural images (evaluated on the standard Kodak and Tecnick datasets) indicate theoretical room for improvement of state-of-the-art image compression methods by at least one dB in PSNR, at various bitrates. \"]]]]],[5622,5898,[[[5622,5898,[\"We begin by reviewing the prerequisite rate-distortion theory in Section 2, then describe our upper and lower bound algorithms in Section 3 and Section 4, respectively. We discuss related work in Section 5, report experimental results in Section 6, and conclude in Section 7. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"71761b0e-0c7a-4294-9d3a-7c44b8c82389\"]]],[[\"a1e92624-9d7d-477d-9d0c-e7e4437ec798\"],[null,null,0.6338094564526744,[[null,36120,38217]],[[[36120,36139,[[[36120,36139,[\"6.4 NATURAL IMAGES \"]]]]],[36139,38217,[[[36139,38217,[\"To establish upper bounds on the R-D function of natural images, we define variational distributions (QZ , QZ|X) on a Euclidean latent space for simplicity, and parameterize them as well as a learned decoder ω via hierarchical VAEs. We consider two VAE architectures: 1. we borrow the convolutional autoencoder architecture of a state-of-the-art image compression model (Minnen \\u0026 Singh, 2020), but use factorized Gaussians for the variational distributions (we still keep the deep factorized hyperprior, but no longer convolve it with a uniform prior); 2. we also reuse our ResNet-VAE architecture with 6 stochastic layers from the GAN experiments (Sec. 6.3). We trained the models with mean-squared error (MSE) distortion and various λ on the COCO 2017 (Lin et al., 2014) images, and evaluated them on the Kodak (1993) and Tecnick (Asuni \\u0026 Giachetti, 2014) datasets. Following image compression conventions, we report the rate in bits-per-pixel, and the quality (i.e., negative distortion) in PSNR averaged over the images for each (λ, model) pair 2. The resulting quality-rate (Q-R) curves can be interpreted as giving upper bounds on the R-D functions of the image-generating distributions. We plot them in Fig. 3, along with the Q-R performance (in actual bitrate) of various traditional and learned image compression methods (Ballé et al., 2017; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), for the Kodak dataset (see similar results on Tecnick in Appendix Fig. 11). Our β-VAE version of (Minnen \\u0026 Singh, 2020) (orange) lies on average 0.7 dB higher than the Q-R curves of the original compression model (red) and VTM (green). With a deeper latent hierarchy, our ResNet-(β-)VAE gives a higher Q-R curve (blue) that is on average 1.1 dB above the state-of-the-art Q-R curves (gap shaded in cyan). We leave it to future work to investigate which choice of autoencoder architecture and variational distributions are most effective, as well as how the theoretical R-D performance of such a β-VAE can be realized by a practical compression algorithm (see discussions in Sec. 5). \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"a1e92624-9d7d-477d-9d0c-e7e4437ec798\"]]]]]],[[null,null,0.6442762686374874,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"c7978e6a-87e8-44f2-9220-4ea0c490bc79\"]],[null,null,0.6001184697857914,[[null,5362,5898]],[[[5362,5622,[[[5362,5622,[\"4. Our estimated R-D upper bounds on high-resolution natural images (evaluated on the standard Kodak and Tecnick datasets) indicate theoretical room for improvement of state-of-the-art image compression methods by at least one dB in PSNR, at various bitrates. \"]]]]],[5622,5898,[[[5622,5898,[\"We begin by reviewing the prerequisite rate-distortion theory in Section 2, then describe our upper and lower bound algorithms in Section 3 and Section 4, respectively. We discuss related work in Section 5, report experimental results in Section 6, and conclude in Section 7. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"71761b0e-0c7a-4294-9d3a-7c44b8c82389\"]],[null,null,0.6338094564526744,[[null,36120,38217]],[[[36120,36139,[[[36120,36139,[\"6.4 NATURAL IMAGES \"]]]]],[36139,38217,[[[36139,38217,[\"To establish upper bounds on the R-D function of natural images, we define variational distributions (QZ , QZ|X) on a Euclidean latent space for simplicity, and parameterize them as well as a learned decoder ω via hierarchical VAEs. We consider two VAE architectures: 1. we borrow the convolutional autoencoder architecture of a state-of-the-art image compression model (Minnen \\u0026 Singh, 2020), but use factorized Gaussians for the variational distributions (we still keep the deep factorized hyperprior, but no longer convolve it with a uniform prior); 2. we also reuse our ResNet-VAE architecture with 6 stochastic layers from the GAN experiments (Sec. 6.3). We trained the models with mean-squared error (MSE) distortion and various λ on the COCO 2017 (Lin et al., 2014) images, and evaluated them on the Kodak (1993) and Tecnick (Asuni \\u0026 Giachetti, 2014) datasets. Following image compression conventions, we report the rate in bits-per-pixel, and the quality (i.e., negative distortion) in PSNR averaged over the images for each (λ, model) pair 2. The resulting quality-rate (Q-R) curves can be interpreted as giving upper bounds on the R-D functions of the image-generating distributions. We plot them in Fig. 3, along with the Q-R performance (in actual bitrate) of various traditional and learned image compression methods (Ballé et al., 2017; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), for the Kodak dataset (see similar results on Tecnick in Appendix Fig. 11). Our β-VAE version of (Minnen \\u0026 Singh, 2020) (orange) lies on average 0.7 dB higher than the Q-R curves of the original compression model (red) and VTM (green). With a deeper latent hierarchy, our ResNet-(β-)VAE gives a higher Q-R curve (blue) that is on average 1.1 dB above the state-of-the-art Q-R curves (gap shaded in cyan). We leave it to future work to investigate which choice of autoencoder architecture and variational distributions are most effective, as well as how the theoretical R-D performance of such a β-VAE can be realized by a practical compression algorithm (see discussions in Sec. 5). \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"a1e92624-9d7d-477d-9d0c-e7e4437ec798\"]]],[[[null,50,164],[0,1,2]],[[null,284,284],[0,1]]]]"]]
59
[["di",5348],["af.httprm",5348,"3133846351781878397",55]]
27
[["e",6,null,null,32621]]
