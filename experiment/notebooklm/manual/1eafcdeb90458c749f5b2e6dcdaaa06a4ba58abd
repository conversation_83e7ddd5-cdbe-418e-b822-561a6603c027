)]}'

6360
[["wrb.fr",null,"[[\"The phrase \\\"but train the detector head with $r(\\\\cdot)$ online in a single stage\\\" means that the F-VLM method **trains only the detector head** [1] in a **simplified, single training phase** [1, 2]. This approach **eliminates the need for multi-stage training pipelines**, such as those involving knowledge distillation,\",null,[\"8e95a0da-b191-484a-9abb-2843a6b9c822\",\"8d358e03-02bd-4e5f-9f5c-d361aba97b78\",155934402],null,[[null,[[[\"e5769df7-3ddd-4ce5-b516-ff5b3404062b\"],[null,139,139]],[[\"e5769df7-3ddd-4ce5-b516-ff5b3404062b\"],[null,178,178]],[[\"892af10f-37a3-4699-85ce-54b30781759a\"],[null,178,178]]]],null,null,[[[\"e5769df7-3ddd-4ce5-b516-ff5b3404062b\"],[null,null,0.7452614778134843,[[null,2815,3748]],[[[2815,3748,[[[2815,3748,[\"We propose F-VLM – a simple and scalable open-vocabulary detection approach built upon frozen VLMs. For localization, we simply attach a detector head to predict object regions. For open-vocabulary recognition, we apply the VLM feature pooler (e.g., a self-attention layer) on the region features from frozen backbones at test time. We train only the detector head upon a frozen VLM backbone, and combine the detection scores with the corresponding VLM predictions at test time. Our recipe reduces the training complexity of an open-vocabulary detector to below that of a stan-dard detector, obviating the need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. By preserving the knowledge of pretrained VLMs completely, F-VLM main-tains a similar philosophy as ViTDet (Li et al., 2022c) to decouple the detector-specific learning from the more task-agnostic vision knowledge in the backbone. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"e5769df7-3ddd-4ce5-b516-ff5b3404062b\"]]],[[\"892af10f-37a3-4699-85ce-54b30781759a\"],[null,null,0.7398836624402914,[[null,0,1202]],[[[0,1,[[[0,1,[\" \"]]]]],[1,41,[[[1,41,[\"F-VLM: OPEN-VOCABULARY OBJECT DETECTION \"]]],[null,6]]],[41,80,[[[41,80,[\"UPON FROZEN VISION AND LANGUAGE MODELS \"]]],[null,6]]],[80,267,[[[80,267,[\"Weicheng Kuo?, Yin Cui†, Xiuye Gu†, AJ Piergiovanni?, Anelia Angelova? ?Google Research, Brain Team; †Google Research, Perception {weicheng, yincui, xiuyegu, ajpiergi, anelia}@google.com \"]]]]],[267,276,[[[267,276,[\"ABSTRACT \"]]]]],[276,1202,[[[276,1202,[\"We present F-VLM, a simple open-vocabulary object detection method built upon Frozen Vision and Language Models. F-VLM simplifies the current multi-stage training pipeline by eliminating the need for knowledge distillation or detection-tailored pretraining. Surprisingly, we observe that a frozen VLM: 1) retains the locality-sensitive features necessary for detection, and 2) is a strong region clas-sifier. We finetune only the detector head and combine the detector and VLM outputs for each region at inference time. F-VLM shows compelling scaling be-havior and achieves +6.5 mask AP improvement over the previous state-of-the-art on LVIS open-vocabulary detection benchmark at system level. In addition, we demonstrate very competitive results on COCO open-vocabulary detection bench-mark and cross-dataset transfer detection, in addition to significant training speed-up and compute savings. The code will be released 1. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"892af10f-37a3-4699-85ce-54b30781759a\"]]]]]],[[null,null,0.7452614778134843,[[null,2815,3748]],[[[2815,3748,[[[2815,3748,[\"We propose F-VLM – a simple and scalable open-vocabulary detection approach built upon frozen VLMs. For localization, we simply attach a detector head to predict object regions. For open-vocabulary recognition, we apply the VLM feature pooler (e.g., a self-attention layer) on the region features from frozen backbones at test time. We train only the detector head upon a frozen VLM backbone, and combine the detection scores with the corresponding VLM predictions at test time. Our recipe reduces the training complexity of an open-vocabulary detector to below that of a stan-dard detector, obviating the need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. By preserving the knowledge of pretrained VLMs completely, F-VLM main-tains a similar philosophy as ViTDet (Li et al., 2022c) to decouple the detector-specific learning from the more task-agnostic vision knowledge in the backbone. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"e5769df7-3ddd-4ce5-b516-ff5b3404062b\"]],[null,null,0.7398836624402914,[[null,0,1202]],[[[0,1,[[[0,1,[\" \"]]]]],[1,41,[[[1,41,[\"F-VLM: OPEN-VOCABULARY OBJECT DETECTION \"]]],[null,6]]],[41,80,[[[41,80,[\"UPON FROZEN VISION AND LANGUAGE MODELS \"]]],[null,6]]],[80,267,[[[80,267,[\"Weicheng Kuo?, Yin Cui†, Xiuye Gu†, AJ Piergiovanni?, Anelia Angelova? ?Google Research, Brain Team; †Google Research, Perception {weicheng, yincui, xiuyegu, ajpiergi, anelia}@google.com \"]]]]],[267,276,[[[267,276,[\"ABSTRACT \"]]]]],[276,1202,[[[276,1202,[\"We present F-VLM, a simple open-vocabulary object detection method built upon Frozen Vision and Language Models. F-VLM simplifies the current multi-stage training pipeline by eliminating the need for knowledge distillation or detection-tailored pretraining. Surprisingly, we observe that a frozen VLM: 1) retains the locality-sensitive features necessary for detection, and 2) is a strong region clas-sifier. We finetune only the detector head and combine the detector and VLM outputs for each region at inference time. F-VLM shows compelling scaling be-havior and achieves +6.5 mask AP improvement over the previous state-of-the-art on LVIS open-vocabulary detection benchmark at system level. In addition, we demonstrate very competitive results on COCO open-vocabulary detection bench-mark and cross-dataset transfer detection, in addition to significant training speed-up and compute savings. The code will be released 1. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"892af10f-37a3-4699-85ce-54b30781759a\"]]],[[[null,139,139],[0]],[[null,178,178],[0,1]]],[[\"How does F-VLM simplify training?\",\"What is F-VLM's key advantage?\",\"How are VLM scores combined?\"]]]"]]
15624
[["wrb.fr",null,"[[\"The phrase \\\"but train the detector head with $r(\\\\cdot)$ online in a single stage\\\" means that the F-VLM method **trains only the detector head** [1] in a **simplified, single training phase** [1, 2]. This approach **eliminates the need for multi-stage training pipelines**, such as those involving knowledge distillation, detection-tailored pretraining, or weakly supervised learning [1-3].\\n\\nThe term \\\"online\\\" indicates that the training process **does not involve cropping and resizing RGB image regions and caching their embeddings in a separate offline process**, which is a simplification compared to existing works [4]. The `r(·)` refers to **ROI-Align** [4, 5]. While ROI-Align (`R(·)`) is used as part of the `Q(·)` function to generate region embeddings (`rb`) for training the detector head [5], the VLM region features (`vb`) that\",null,[\"8e95a0da-b191-484a-9abb-2843a6b9c822\",\"8d358e03-02bd-4e5f-9f5c-d361aba97b78\",155934402],null,[[[[0,360,[[[0,110,[\"The phrase \\\"but train the detector head with $r(\\\\cdot)$ online in a single stage\\\" means that the F-VLM method \"]],[110,139,[\"trains only the detector head\",[true]]],[139,145,[\" in a \"]],[145,178,[\"simplified, single training phase\",[true]]],[178,194,[\". This approach \"]],[194,248,[\"eliminates the need for multi-stage training pipelines\",[true]]],[248,360,[\", such as those involving knowledge distillation, detection-tailored pretraining, or weakly supervised learning.\"]]]]]],[[[\"099b8cb4-3ed9-460c-bdbd-ae93d8da9b17\"],[null,139,139]],[[\"099b8cb4-3ed9-460c-bdbd-ae93d8da9b17\"],[null,178,178]],[[\"76f9a34f-0cab-4d50-9fde-64cc81404262\"],[null,178,178]],[[\"099b8cb4-3ed9-460c-bdbd-ae93d8da9b17\"],[null,248,359]],[[\"76f9a34f-0cab-4d50-9fde-64cc81404262\"],[null,248,359]],[[\"586934de-528b-4ada-953b-45a9b183a734\"],[null,248,359]],[[\"2b0be037-bf6a-4b99-b0cc-ed1c43bcacc4\"],[null,529,583]],[[\"2b0be037-bf6a-4b99-b0cc-ed1c43bcacc4\"],[null,613,613]],[[\"0c5d4b84-35b1-4e36-9321-a93c784a67ab\"],[null,613,613]],[[\"0c5d4b84-35b1-4e36-9321-a93c784a67ab\"],[null,708,740]]]],null,null,[[[\"099b8cb4-3ed9-460c-bdbd-ae93d8da9b17\"],[null,null,0.7452614778134843,[[null,2815,3748]],[[[2815,3748,[[[2815,3748,[\"We propose F-VLM – a simple and scalable open-vocabulary detection approach built upon frozen VLMs. For localization, we simply attach a detector head to predict object regions. For open-vocabulary recognition, we apply the VLM feature pooler (e.g., a self-attention layer) on the region features from frozen backbones at test time. We train only the detector head upon a frozen VLM backbone, and combine the detection scores with the corresponding VLM predictions at test time. Our recipe reduces the training complexity of an open-vocabulary detector to below that of a stan-dard detector, obviating the need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. By preserving the knowledge of pretrained VLMs completely, F-VLM main-tains a similar philosophy as ViTDet (Li et al., 2022c) to decouple the detector-specific learning from the more task-agnostic vision knowledge in the backbone. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"099b8cb4-3ed9-460c-bdbd-ae93d8da9b17\"]]],[[\"76f9a34f-0cab-4d50-9fde-64cc81404262\"],[null,null,0.7398836624402914,[[null,0,1202]],[[[0,1,[[[0,1,[\" \"]]]]],[1,41,[[[1,41,[\"F-VLM: OPEN-VOCABULARY OBJECT DETECTION \"]]],[null,6]]],[41,80,[[[41,80,[\"UPON FROZEN VISION AND LANGUAGE MODELS \"]]],[null,6]]],[80,267,[[[80,267,[\"Weicheng Kuo?, Yin Cui†, Xiuye Gu†, AJ Piergiovanni?, Anelia Angelova? ?Google Research, Brain Team; †Google Research, Perception {weicheng, yincui, xiuyegu, ajpiergi, anelia}@google.com \"]]]]],[267,276,[[[267,276,[\"ABSTRACT \"]]]]],[276,1202,[[[276,1202,[\"We present F-VLM, a simple open-vocabulary object detection method built upon Frozen Vision and Language Models. F-VLM simplifies the current multi-stage training pipeline by eliminating the need for knowledge distillation or detection-tailored pretraining. Surprisingly, we observe that a frozen VLM: 1) retains the locality-sensitive features necessary for detection, and 2) is a strong region clas-sifier. We finetune only the detector head and combine the detector and VLM outputs for each region at inference time. F-VLM shows compelling scaling be-havior and achieves +6.5 mask AP improvement over the previous state-of-the-art on LVIS open-vocabulary detection benchmark at system level. In addition, we demonstrate very competitive results on COCO open-vocabulary detection bench-mark and cross-dataset transfer detection, in addition to significant training speed-up and compute savings. The code will be released 1. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"76f9a34f-0cab-4d50-9fde-64cc81404262\"]]],[[\"586934de-528b-4ada-953b-45a9b183a734\"],[null,null,0.7124015140812012,[[null,3748,4499]],[[[3748,3804,[[[3748,3804,[\"1Project page: https://sites.google.com/view/f-vlm/home \"]]]]],[3804,3804,[]],[3804,3805,[[[3804,3805,[\" \"]]]]],[3805,4499,[[[3805,4499,[\"Figure 1: We explore the potential of frozen VLM (e.g., CLIP) features for open-vocabulary de-tection. The feature grouping reveals rich semantic and locality-sensitive information where object boundaries are nicely delineated (col. 2, see Appendix C for more details). The same frozen features can classify groundtruth regions well without finetuning (col. 3). Therefore, we propose to build a open-vocabulary detector on top of a frozen VLM (col. 4) without a need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. F-VLM significantly reduces training complexity and compute requirement, and achieves the state-of-the-art performance at system level. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"586934de-528b-4ada-953b-45a9b183a734\"]]],[[\"2b0be037-bf6a-4b99-b0cc-ed1c43bcacc4\"],[null,null,0.7422914309744433,[[null,16159,17274]],[[[16159,16191,[[[16159,16191,[\"3.4 OPEN-VOCABULARY RECOGNITION \"]]]]],[16191,16494,[[[16191,16494,[\"The ability to perform open-vocabulary recognition at region level is integral to F-VLM. Since the backbone features are frozen, they do not overfit to the base categories and can be directly cropped for region-level classification. F-VLM performs this open-vocabulary classification only at test time. \"]]]]],[16494,17274,[[[16494,17274,[\"To obtain the features for a region b, we apply the VLM pooling layer P(·) on the cropped backbone output features F(I) (see Sec. 3.2 for notations). Because the pooling layer requires fixed-size inputs, e.g. 7x7 for R50 (Radford et al., 2021), we crop and resize the region features with ROI-Align R(·) (He et al., 2017) (see Fig. 2b). Unlike existing works (Gu et al., 2022; Du et al., 2022), we do not crop and resize the RGB image regions and cache their embeddings in a separate offline process, but train the detector head in one stage. This is simpler and more space-efficient. In addition, we do not crop VLM region features with R(·) during training because the backbone features are frozen. Using the notations from equation 1, we obtain the VLM region embedding vb by: \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"2b0be037-bf6a-4b99-b0cc-ed1c43bcacc4\"]]],[[\"0c5d4b84-35b1-4e36-9321-a93c784a67ab\"],[null,null,0.7249287816845053,[[null,14167,15130]],[[[14167,14204,[[[14167,14204,[\"3.3 TEXT-EMBEDDING REGION CLASSIFIER \"]]]]],[14204,14524,[[[14204,14524,[\"Notations: Let’s define I as the input image, F(I) the backbone features from the image encoder. Let Q(·) be the function that yields a region embedding rb from F(I) and a given box region proposal b, which involves FPN (Lin et al., 2017), ROI-Align (He et al., 2017), and Faster R-CNN head (Ren et al., 2015). We have: \"]]]]],[14524,14544,[[[14524,14544,[\"rb \\u003d Q(F(I), b) (1) \"]]]]],[14544,15130,[[[14544,15130,[\"Standard detectors use K-way classifier because the training and test time categories are the same. This design does not support the open-vocabulary settings which require new categories to be added at test time. To accommodate this, we replace the last fully connected layer with the text embeddings of base categories (see Fig. 2a). At inference time, we can simply expand the text embeddings to include novel categories for open-vocabulary detection (see Fig. 2b). An advantage of such design is that the system can generalize to the novel categories near CB in the embedding space. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"0c5d4b84-35b1-4e36-9321-a93c784a67ab\"]]]]]],[[null,null,0.7452614778134843,[[null,2815,3748]],[[[2815,3748,[[[2815,3748,[\"We propose F-VLM – a simple and scalable open-vocabulary detection approach built upon frozen VLMs. For localization, we simply attach a detector head to predict object regions. For open-vocabulary recognition, we apply the VLM feature pooler (e.g., a self-attention layer) on the region features from frozen backbones at test time. We train only the detector head upon a frozen VLM backbone, and combine the detection scores with the corresponding VLM predictions at test time. Our recipe reduces the training complexity of an open-vocabulary detector to below that of a stan-dard detector, obviating the need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. By preserving the knowledge of pretrained VLMs completely, F-VLM main-tains a similar philosophy as ViTDet (Li et al., 2022c) to decouple the detector-specific learning from the more task-agnostic vision knowledge in the backbone. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"099b8cb4-3ed9-460c-bdbd-ae93d8da9b17\"]],[null,null,0.7398836624402914,[[null,0,1202]],[[[0,1,[[[0,1,[\" \"]]]]],[1,41,[[[1,41,[\"F-VLM: OPEN-VOCABULARY OBJECT DETECTION \"]]],[null,6]]],[41,80,[[[41,80,[\"UPON FROZEN VISION AND LANGUAGE MODELS \"]]],[null,6]]],[80,267,[[[80,267,[\"Weicheng Kuo?, Yin Cui†, Xiuye Gu†, AJ Piergiovanni?, Anelia Angelova? ?Google Research, Brain Team; †Google Research, Perception {weicheng, yincui, xiuyegu, ajpiergi, anelia}@google.com \"]]]]],[267,276,[[[267,276,[\"ABSTRACT \"]]]]],[276,1202,[[[276,1202,[\"We present F-VLM, a simple open-vocabulary object detection method built upon Frozen Vision and Language Models. F-VLM simplifies the current multi-stage training pipeline by eliminating the need for knowledge distillation or detection-tailored pretraining. Surprisingly, we observe that a frozen VLM: 1) retains the locality-sensitive features necessary for detection, and 2) is a strong region clas-sifier. We finetune only the detector head and combine the detector and VLM outputs for each region at inference time. F-VLM shows compelling scaling be-havior and achieves +6.5 mask AP improvement over the previous state-of-the-art on LVIS open-vocabulary detection benchmark at system level. In addition, we demonstrate very competitive results on COCO open-vocabulary detection bench-mark and cross-dataset transfer detection, in addition to significant training speed-up and compute savings. The code will be released 1. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"76f9a34f-0cab-4d50-9fde-64cc81404262\"]],[null,null,0.7124015140812012,[[null,3748,4499]],[[[3748,3804,[[[3748,3804,[\"1Project page: https://sites.google.com/view/f-vlm/home \"]]]]],[3804,3804,[]],[3804,3805,[[[3804,3805,[\" \"]]]]],[3805,4499,[[[3805,4499,[\"Figure 1: We explore the potential of frozen VLM (e.g., CLIP) features for open-vocabulary de-tection. The feature grouping reveals rich semantic and locality-sensitive information where object boundaries are nicely delineated (col. 2, see Appendix C for more details). The same frozen features can classify groundtruth regions well without finetuning (col. 3). Therefore, we propose to build a open-vocabulary detector on top of a frozen VLM (col. 4) without a need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. F-VLM significantly reduces training complexity and compute requirement, and achieves the state-of-the-art performance at system level. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"586934de-528b-4ada-953b-45a9b183a734\"]],[null,null,0.7422914309744433,[[null,16159,17274]],[[[16159,16191,[[[16159,16191,[\"3.4 OPEN-VOCABULARY RECOGNITION \"]]]]],[16191,16494,[[[16191,16494,[\"The ability to perform open-vocabulary recognition at region level is integral to F-VLM. Since the backbone features are frozen, they do not overfit to the base categories and can be directly cropped for region-level classification. F-VLM performs this open-vocabulary classification only at test time. \"]]]]],[16494,17274,[[[16494,17274,[\"To obtain the features for a region b, we apply the VLM pooling layer P(·) on the cropped backbone output features F(I) (see Sec. 3.2 for notations). Because the pooling layer requires fixed-size inputs, e.g. 7x7 for R50 (Radford et al., 2021), we crop and resize the region features with ROI-Align R(·) (He et al., 2017) (see Fig. 2b). Unlike existing works (Gu et al., 2022; Du et al., 2022), we do not crop and resize the RGB image regions and cache their embeddings in a separate offline process, but train the detector head in one stage. This is simpler and more space-efficient. In addition, we do not crop VLM region features with R(·) during training because the backbone features are frozen. Using the notations from equation 1, we obtain the VLM region embedding vb by: \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"2b0be037-bf6a-4b99-b0cc-ed1c43bcacc4\"]],[null,null,0.7249287816845053,[[null,14167,15130]],[[[14167,14204,[[[14167,14204,[\"3.3 TEXT-EMBEDDING REGION CLASSIFIER \"]]]]],[14204,14524,[[[14204,14524,[\"Notations: Let’s define I as the input image, F(I) the backbone features from the image encoder. Let Q(·) be the function that yields a region embedding rb from F(I) and a given box region proposal b, which involves FPN (Lin et al., 2017), ROI-Align (He et al., 2017), and Faster R-CNN head (Ren et al., 2015). We have: \"]]]]],[14524,14544,[[[14524,14544,[\"rb \\u003d Q(F(I), b) (1) \"]]]]],[14544,15130,[[[14544,15130,[\"Standard detectors use K-way classifier because the training and test time categories are the same. This design does not support the open-vocabulary settings which require new categories to be added at test time. To accommodate this, we replace the last fully connected layer with the text embeddings of base categories (see Fig. 2a). At inference time, we can simply expand the text embeddings to include novel categories for open-vocabulary detection (see Fig. 2b). An advantage of such design is that the system can generalize to the novel categories near CB in the embedding space. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"0c5d4b84-35b1-4e36-9321-a93c784a67ab\"]]],[[[null,139,139],[0]],[[null,178,178],[0,1]],[[null,248,359],[0,1,2]],[[null,529,583],[3]],[[null,613,613],[3,4]],[[null,708,740],[4]]],[[\"How does F-VLM simplify training?\",\"What is F-VLM's key advantage?\",\"How are VLM scores combined?\"]]]"]]
18234
[["wrb.fr",null,"[[\"The phrase \\\"but train the detector head with $r(\\\\cdot)$ online in a single stage\\\" means that the F-VLM method **trains only the detector head** [1] in a **simplified, single training phase** [1, 2]. This approach **eliminates the need for multi-stage training pipelines**, such as those involving knowledge distillation, detection-tailored pretraining, or weakly supervised learning [1-3].\\n\\nThe term \\\"online\\\" indicates that the training process **does not involve cropping and resizing RGB image regions and caching their embeddings in a separate offline process**, which is a simplification compared to existing works [4]. The `r(·)` refers to **ROI-Align** [4, 5]. While ROI-Align (`R(·)`) is used as part of the `Q(·)` function to generate region embeddings (`rb`) for training the detector head [5], the VLM region features (`vb`) that also utilize `R(·)` are specifically noted to **not be cropped or used during training**, but only at test time [4, 6].\",null,[\"8e95a0da-b191-484a-9abb-2843a6b9c822\",\"8d358e03-02bd-4e5f-9f5c-d361aba97b78\",155934402],null,[[[[0,360,[[[0,110,[\"The phrase \\\"but train the detector head with $r(\\\\cdot)$ online in a single stage\\\" means that the F-VLM method \"]],[110,139,[\"trains only the detector head\",[true]]],[139,145,[\" in a \"]],[145,178,[\"simplified, single training phase\",[true]]],[178,194,[\". This approach \"]],[194,248,[\"eliminates the need for multi-stage training pipelines\",[true]]],[248,360,[\", such as those involving knowledge distillation, detection-tailored pretraining, or weakly supervised learning.\"]]]]]],[[[\"e1d0c4d1-ab01-49ed-a619-4fe0fd462521\"],[null,139,139]],[[\"e1d0c4d1-ab01-49ed-a619-4fe0fd462521\"],[null,178,178]],[[\"25b20281-de48-4bff-9826-5f7ab6ef46bd\"],[null,178,178]],[[\"e1d0c4d1-ab01-49ed-a619-4fe0fd462521\"],[null,248,359]],[[\"25b20281-de48-4bff-9826-5f7ab6ef46bd\"],[null,248,359]],[[\"cf541b37-7759-4174-a6c9-2d23f42b1c15\"],[null,248,359]],[[\"f43235c3-ab58-4f2e-b4fc-b1470a54650b\"],[null,529,583]],[[\"f43235c3-ab58-4f2e-b4fc-b1470a54650b\"],[null,613,613]],[[\"dd6fa657-2432-4799-90bc-4481bb7095d0\"],[null,613,613]],[[\"dd6fa657-2432-4799-90bc-4481bb7095d0\"],[null,708,740]],[[\"f43235c3-ab58-4f2e-b4fc-b1470a54650b\"],[null,858,881]],[[\"108e1a32-7313-48e7-9e57-9f3c27777b62\"],[null,858,881]]]],null,null,[[[\"e1d0c4d1-ab01-49ed-a619-4fe0fd462521\"],[null,null,0.7452614778134843,[[null,2815,3748]],[[[2815,3748,[[[2815,3748,[\"We propose F-VLM – a simple and scalable open-vocabulary detection approach built upon frozen VLMs. For localization, we simply attach a detector head to predict object regions. For open-vocabulary recognition, we apply the VLM feature pooler (e.g., a self-attention layer) on the region features from frozen backbones at test time. We train only the detector head upon a frozen VLM backbone, and combine the detection scores with the corresponding VLM predictions at test time. Our recipe reduces the training complexity of an open-vocabulary detector to below that of a stan-dard detector, obviating the need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. By preserving the knowledge of pretrained VLMs completely, F-VLM main-tains a similar philosophy as ViTDet (Li et al., 2022c) to decouple the detector-specific learning from the more task-agnostic vision knowledge in the backbone. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"e1d0c4d1-ab01-49ed-a619-4fe0fd462521\"]]],[[\"25b20281-de48-4bff-9826-5f7ab6ef46bd\"],[null,null,0.7398836624402914,[[null,0,1202]],[[[0,1,[[[0,1,[\" \"]]]]],[1,41,[[[1,41,[\"F-VLM: OPEN-VOCABULARY OBJECT DETECTION \"]]],[null,6]]],[41,80,[[[41,80,[\"UPON FROZEN VISION AND LANGUAGE MODELS \"]]],[null,6]]],[80,267,[[[80,267,[\"Weicheng Kuo?, Yin Cui†, Xiuye Gu†, AJ Piergiovanni?, Anelia Angelova? ?Google Research, Brain Team; †Google Research, Perception {weicheng, yincui, xiuyegu, ajpiergi, anelia}@google.com \"]]]]],[267,276,[[[267,276,[\"ABSTRACT \"]]]]],[276,1202,[[[276,1202,[\"We present F-VLM, a simple open-vocabulary object detection method built upon Frozen Vision and Language Models. F-VLM simplifies the current multi-stage training pipeline by eliminating the need for knowledge distillation or detection-tailored pretraining. Surprisingly, we observe that a frozen VLM: 1) retains the locality-sensitive features necessary for detection, and 2) is a strong region clas-sifier. We finetune only the detector head and combine the detector and VLM outputs for each region at inference time. F-VLM shows compelling scaling be-havior and achieves +6.5 mask AP improvement over the previous state-of-the-art on LVIS open-vocabulary detection benchmark at system level. In addition, we demonstrate very competitive results on COCO open-vocabulary detection bench-mark and cross-dataset transfer detection, in addition to significant training speed-up and compute savings. The code will be released 1. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"25b20281-de48-4bff-9826-5f7ab6ef46bd\"]]],[[\"cf541b37-7759-4174-a6c9-2d23f42b1c15\"],[null,null,0.7124015140812012,[[null,3748,4499]],[[[3748,3804,[[[3748,3804,[\"1Project page: https://sites.google.com/view/f-vlm/home \"]]]]],[3804,3804,[]],[3804,3805,[[[3804,3805,[\" \"]]]]],[3805,4499,[[[3805,4499,[\"Figure 1: We explore the potential of frozen VLM (e.g., CLIP) features for open-vocabulary de-tection. The feature grouping reveals rich semantic and locality-sensitive information where object boundaries are nicely delineated (col. 2, see Appendix C for more details). The same frozen features can classify groundtruth regions well without finetuning (col. 3). Therefore, we propose to build a open-vocabulary detector on top of a frozen VLM (col. 4) without a need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. F-VLM significantly reduces training complexity and compute requirement, and achieves the state-of-the-art performance at system level. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"cf541b37-7759-4174-a6c9-2d23f42b1c15\"]]],[[\"f43235c3-ab58-4f2e-b4fc-b1470a54650b\"],[null,null,0.7422914309744433,[[null,16159,17274]],[[[16159,16191,[[[16159,16191,[\"3.4 OPEN-VOCABULARY RECOGNITION \"]]]]],[16191,16494,[[[16191,16494,[\"The ability to perform open-vocabulary recognition at region level is integral to F-VLM. Since the backbone features are frozen, they do not overfit to the base categories and can be directly cropped for region-level classification. F-VLM performs this open-vocabulary classification only at test time. \"]]]]],[16494,17274,[[[16494,17274,[\"To obtain the features for a region b, we apply the VLM pooling layer P(·) on the cropped backbone output features F(I) (see Sec. 3.2 for notations). Because the pooling layer requires fixed-size inputs, e.g. 7x7 for R50 (Radford et al., 2021), we crop and resize the region features with ROI-Align R(·) (He et al., 2017) (see Fig. 2b). Unlike existing works (Gu et al., 2022; Du et al., 2022), we do not crop and resize the RGB image regions and cache their embeddings in a separate offline process, but train the detector head in one stage. This is simpler and more space-efficient. In addition, we do not crop VLM region features with R(·) during training because the backbone features are frozen. Using the notations from equation 1, we obtain the VLM region embedding vb by: \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"f43235c3-ab58-4f2e-b4fc-b1470a54650b\"]]],[[\"dd6fa657-2432-4799-90bc-4481bb7095d0\"],[null,null,0.7249287816845053,[[null,14167,15130]],[[[14167,14204,[[[14167,14204,[\"3.3 TEXT-EMBEDDING REGION CLASSIFIER \"]]]]],[14204,14524,[[[14204,14524,[\"Notations: Let’s define I as the input image, F(I) the backbone features from the image encoder. Let Q(·) be the function that yields a region embedding rb from F(I) and a given box region proposal b, which involves FPN (Lin et al., 2017), ROI-Align (He et al., 2017), and Faster R-CNN head (Ren et al., 2015). We have: \"]]]]],[14524,14544,[[[14524,14544,[\"rb \\u003d Q(F(I), b) (1) \"]]]]],[14544,15130,[[[14544,15130,[\"Standard detectors use K-way classifier because the training and test time categories are the same. This design does not support the open-vocabulary settings which require new categories to be added at test time. To accommodate this, we replace the last fully connected layer with the text embeddings of base categories (see Fig. 2a). At inference time, we can simply expand the text embeddings to include novel categories for open-vocabulary detection (see Fig. 2b). An advantage of such design is that the system can generalize to the novel categories near CB in the embedding space. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"dd6fa657-2432-4799-90bc-4481bb7095d0\"]]],[[\"108e1a32-7313-48e7-9e57-9f3c27777b62\"],[null,null,0.7133383996419858,[[null,17274,17953]],[[[17274,17297,[[[17274,17297,[\"vb \\u003d P(R(F(I), b)) (3) \"]]]]],[17297,17492,[[[17297,17492,[\"where b denotes the box region and vb corresponds to v1, ..., vk in Fig. 2b. NoteR(·) is used at test time only. Similar to equation 2, we compute the VLM scores by cosine similarity as follows: \"]]]]],[17492,17511,[[[17492,17511,[\"w(vb) \\u003d Softmax( 1 \"]]]]],[17511,17513,[[[17511,17513,[\"T \"]]]]],[17513,17568,[[[17513,17568,[\"[ cos(vb, tbg), cos(vb, t1), · · · , cos(vb, t|CB∪N |) \"]]]]],[17568,17576,[[[17568,17576,[\"] ) (4) \"]]]]],[17576,17953,[[[17576,17953,[\"where T is a fixed temperature and the text embeddings include both the CB and CN at inference time (see Fig. 2b). We use a fixed temperature to adjust the scale of VLM scores relative to the detection scores in equation 2. In the special case when the region b is equal to the whole image, the VLM scores w(vb) becomes equivalent to the zero-shot image classification scores. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"108e1a32-7313-48e7-9e57-9f3c27777b62\"]]]]]],[[null,null,0.7452614778134843,[[null,2815,3748]],[[[2815,3748,[[[2815,3748,[\"We propose F-VLM – a simple and scalable open-vocabulary detection approach built upon frozen VLMs. For localization, we simply attach a detector head to predict object regions. For open-vocabulary recognition, we apply the VLM feature pooler (e.g., a self-attention layer) on the region features from frozen backbones at test time. We train only the detector head upon a frozen VLM backbone, and combine the detection scores with the corresponding VLM predictions at test time. Our recipe reduces the training complexity of an open-vocabulary detector to below that of a stan-dard detector, obviating the need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. By preserving the knowledge of pretrained VLMs completely, F-VLM main-tains a similar philosophy as ViTDet (Li et al., 2022c) to decouple the detector-specific learning from the more task-agnostic vision knowledge in the backbone. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"e1d0c4d1-ab01-49ed-a619-4fe0fd462521\"]],[null,null,0.7398836624402914,[[null,0,1202]],[[[0,1,[[[0,1,[\" \"]]]]],[1,41,[[[1,41,[\"F-VLM: OPEN-VOCABULARY OBJECT DETECTION \"]]],[null,6]]],[41,80,[[[41,80,[\"UPON FROZEN VISION AND LANGUAGE MODELS \"]]],[null,6]]],[80,267,[[[80,267,[\"Weicheng Kuo?, Yin Cui†, Xiuye Gu†, AJ Piergiovanni?, Anelia Angelova? ?Google Research, Brain Team; †Google Research, Perception {weicheng, yincui, xiuyegu, ajpiergi, anelia}@google.com \"]]]]],[267,276,[[[267,276,[\"ABSTRACT \"]]]]],[276,1202,[[[276,1202,[\"We present F-VLM, a simple open-vocabulary object detection method built upon Frozen Vision and Language Models. F-VLM simplifies the current multi-stage training pipeline by eliminating the need for knowledge distillation or detection-tailored pretraining. Surprisingly, we observe that a frozen VLM: 1) retains the locality-sensitive features necessary for detection, and 2) is a strong region clas-sifier. We finetune only the detector head and combine the detector and VLM outputs for each region at inference time. F-VLM shows compelling scaling be-havior and achieves +6.5 mask AP improvement over the previous state-of-the-art on LVIS open-vocabulary detection benchmark at system level. In addition, we demonstrate very competitive results on COCO open-vocabulary detection bench-mark and cross-dataset transfer detection, in addition to significant training speed-up and compute savings. The code will be released 1. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"25b20281-de48-4bff-9826-5f7ab6ef46bd\"]],[null,null,0.7124015140812012,[[null,3748,4499]],[[[3748,3804,[[[3748,3804,[\"1Project page: https://sites.google.com/view/f-vlm/home \"]]]]],[3804,3804,[]],[3804,3805,[[[3804,3805,[\" \"]]]]],[3805,4499,[[[3805,4499,[\"Figure 1: We explore the potential of frozen VLM (e.g., CLIP) features for open-vocabulary de-tection. The feature grouping reveals rich semantic and locality-sensitive information where object boundaries are nicely delineated (col. 2, see Appendix C for more details). The same frozen features can classify groundtruth regions well without finetuning (col. 3). Therefore, we propose to build a open-vocabulary detector on top of a frozen VLM (col. 4) without a need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. F-VLM significantly reduces training complexity and compute requirement, and achieves the state-of-the-art performance at system level. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"cf541b37-7759-4174-a6c9-2d23f42b1c15\"]],[null,null,0.7422914309744433,[[null,16159,17274]],[[[16159,16191,[[[16159,16191,[\"3.4 OPEN-VOCABULARY RECOGNITION \"]]]]],[16191,16494,[[[16191,16494,[\"The ability to perform open-vocabulary recognition at region level is integral to F-VLM. Since the backbone features are frozen, they do not overfit to the base categories and can be directly cropped for region-level classification. F-VLM performs this open-vocabulary classification only at test time. \"]]]]],[16494,17274,[[[16494,17274,[\"To obtain the features for a region b, we apply the VLM pooling layer P(·) on the cropped backbone output features F(I) (see Sec. 3.2 for notations). Because the pooling layer requires fixed-size inputs, e.g. 7x7 for R50 (Radford et al., 2021), we crop and resize the region features with ROI-Align R(·) (He et al., 2017) (see Fig. 2b). Unlike existing works (Gu et al., 2022; Du et al., 2022), we do not crop and resize the RGB image regions and cache their embeddings in a separate offline process, but train the detector head in one stage. This is simpler and more space-efficient. In addition, we do not crop VLM region features with R(·) during training because the backbone features are frozen. Using the notations from equation 1, we obtain the VLM region embedding vb by: \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"f43235c3-ab58-4f2e-b4fc-b1470a54650b\"]],[null,null,0.7249287816845053,[[null,14167,15130]],[[[14167,14204,[[[14167,14204,[\"3.3 TEXT-EMBEDDING REGION CLASSIFIER \"]]]]],[14204,14524,[[[14204,14524,[\"Notations: Let’s define I as the input image, F(I) the backbone features from the image encoder. Let Q(·) be the function that yields a region embedding rb from F(I) and a given box region proposal b, which involves FPN (Lin et al., 2017), ROI-Align (He et al., 2017), and Faster R-CNN head (Ren et al., 2015). We have: \"]]]]],[14524,14544,[[[14524,14544,[\"rb \\u003d Q(F(I), b) (1) \"]]]]],[14544,15130,[[[14544,15130,[\"Standard detectors use K-way classifier because the training and test time categories are the same. This design does not support the open-vocabulary settings which require new categories to be added at test time. To accommodate this, we replace the last fully connected layer with the text embeddings of base categories (see Fig. 2a). At inference time, we can simply expand the text embeddings to include novel categories for open-vocabulary detection (see Fig. 2b). An advantage of such design is that the system can generalize to the novel categories near CB in the embedding space. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"dd6fa657-2432-4799-90bc-4481bb7095d0\"]],[null,null,0.7133383996419858,[[null,17274,17953]],[[[17274,17297,[[[17274,17297,[\"vb \\u003d P(R(F(I), b)) (3) \"]]]]],[17297,17492,[[[17297,17492,[\"where b denotes the box region and vb corresponds to v1, ..., vk in Fig. 2b. NoteR(·) is used at test time only. Similar to equation 2, we compute the VLM scores by cosine similarity as follows: \"]]]]],[17492,17511,[[[17492,17511,[\"w(vb) \\u003d Softmax( 1 \"]]]]],[17511,17513,[[[17511,17513,[\"T \"]]]]],[17513,17568,[[[17513,17568,[\"[ cos(vb, tbg), cos(vb, t1), · · · , cos(vb, t|CB∪N |) \"]]]]],[17568,17576,[[[17568,17576,[\"] ) (4) \"]]]]],[17576,17953,[[[17576,17953,[\"where T is a fixed temperature and the text embeddings include both the CB and CN at inference time (see Fig. 2b). We use a fixed temperature to adjust the scale of VLM scores relative to the detection scores in equation 2. In the special case when the region b is equal to the whole image, the VLM scores w(vb) becomes equivalent to the zero-shot image classification scores. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"108e1a32-7313-48e7-9e57-9f3c27777b62\"]]],[[[null,139,139],[0]],[[null,178,178],[0,1]],[[null,248,359],[0,1,2]],[[null,529,583],[3]],[[null,613,613],[3,4]],[[null,708,740],[4]],[[null,858,881],[3,5]]],[[\"How does F-VLM simplify training?\",\"What is F-VLM's key advantage?\",\"How are VLM scores combined?\"]]]"]]
19372
[["wrb.fr",null,"[[\"The phrase \\\"but train the detector head with $r(\\\\cdot)$ online in a single stage\\\" means that the F-VLM method **trains only the detector head** [1] in a **simplified, single training phase** [1, 2]. This approach **eliminates the need for multi-stage training pipelines**, such as those involving knowledge distillation, detection-tailored pretraining, or weakly supervised learning [1-3].\\n\\nThe term \\\"online\\\" indicates that the training process **does not involve cropping and resizing RGB image regions and caching their embeddings in a separate offline process**, which is a simplification compared to existing works [4]. The `r(·)` refers to **ROI-Align** [4, 5]. While ROI-Align (`R(·)`) is used as part of the `Q(·)` function to generate region embeddings (`rb`) for training the detector head [5], the VLM region features (`vb`) that also utilize `R(·)` are specifically noted to **not be cropped or used during training**, but only at test time [4, 6].\",null,[\"8e95a0da-b191-484a-9abb-2843a6b9c822\",\"8d358e03-02bd-4e5f-9f5c-d361aba97b78\",155934402],null,[[[[0,360,[[[0,110,[\"The phrase \\\"but train the detector head with $r(\\\\cdot)$ online in a single stage\\\" means that the F-VLM method \"]],[110,139,[\"trains only the detector head\",[true]]],[139,145,[\" in a \"]],[145,178,[\"simplified, single training phase\",[true]]],[178,194,[\". This approach \"]],[194,248,[\"eliminates the need for multi-stage training pipelines\",[true]]],[248,360,[\", such as those involving knowledge distillation, detection-tailored pretraining, or weakly supervised learning.\"]]]]],[360,882,[[[360,414,[\"The term \\\"online\\\" indicates that the training process \"]],[414,529,[\"does not involve cropping and resizing RGB image regions and caching their embeddings in a separate offline process\",[true]]],[529,589,[\", which is a simplification compared to existing works. The \"]],[589,593,[\"r(·)\",[null,null,null,null,null,null,null,true]]],[593,604,[\" refers to \"]],[604,613,[\"ROI-Align\",[true]]],[613,632,[\". While ROI-Align (\"]],[632,636,[\"R(·)\",[null,null,null,null,null,null,null,true]]],[636,661,[\") is used as part of the \"]],[661,665,[\"Q(·)\",[null,null,null,null,null,null,null,true]]],[665,706,[\" function to generate region embeddings (\"]],[706,708,[\"rb\",[null,null,null,null,null,null,null,true]]],[708,767,[\") for training the detector head, the VLM region features (\"]],[767,769,[\"vb\",[null,null,null,null,null,null,null,true]]],[769,789,[\") that also utilize \"]],[789,793,[\"R(·)\",[null,null,null,null,null,null,null,true]]],[793,820,[\" are specifically noted to \"]],[820,858,[\"not be cropped or used during training\",[true]]],[858,882,[\", but only at test time.\"]]]]]],[[[\"e1d0c4d1-ab01-49ed-a619-4fe0fd462521\"],[null,139,139]],[[\"e1d0c4d1-ab01-49ed-a619-4fe0fd462521\"],[null,178,178]],[[\"25b20281-de48-4bff-9826-5f7ab6ef46bd\"],[null,178,178]],[[\"e1d0c4d1-ab01-49ed-a619-4fe0fd462521\"],[null,248,359]],[[\"25b20281-de48-4bff-9826-5f7ab6ef46bd\"],[null,248,359]],[[\"cf541b37-7759-4174-a6c9-2d23f42b1c15\"],[null,248,359]],[[\"f43235c3-ab58-4f2e-b4fc-b1470a54650b\"],[null,529,583]],[[\"f43235c3-ab58-4f2e-b4fc-b1470a54650b\"],[null,613,613]],[[\"dd6fa657-2432-4799-90bc-4481bb7095d0\"],[null,613,613]],[[\"dd6fa657-2432-4799-90bc-4481bb7095d0\"],[null,708,740]],[[\"f43235c3-ab58-4f2e-b4fc-b1470a54650b\"],[null,858,881]],[[\"108e1a32-7313-48e7-9e57-9f3c27777b62\"],[null,858,881]]]],null,null,[[[\"e1d0c4d1-ab01-49ed-a619-4fe0fd462521\"],[null,null,0.7452614778134843,[[null,2815,3748]],[[[2815,3748,[[[2815,3748,[\"We propose F-VLM – a simple and scalable open-vocabulary detection approach built upon frozen VLMs. For localization, we simply attach a detector head to predict object regions. For open-vocabulary recognition, we apply the VLM feature pooler (e.g., a self-attention layer) on the region features from frozen backbones at test time. We train only the detector head upon a frozen VLM backbone, and combine the detection scores with the corresponding VLM predictions at test time. Our recipe reduces the training complexity of an open-vocabulary detector to below that of a stan-dard detector, obviating the need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. By preserving the knowledge of pretrained VLMs completely, F-VLM main-tains a similar philosophy as ViTDet (Li et al., 2022c) to decouple the detector-specific learning from the more task-agnostic vision knowledge in the backbone. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"e1d0c4d1-ab01-49ed-a619-4fe0fd462521\"]]],[[\"25b20281-de48-4bff-9826-5f7ab6ef46bd\"],[null,null,0.7398836624402914,[[null,0,1202]],[[[0,1,[[[0,1,[\" \"]]]]],[1,41,[[[1,41,[\"F-VLM: OPEN-VOCABULARY OBJECT DETECTION \"]]],[null,6]]],[41,80,[[[41,80,[\"UPON FROZEN VISION AND LANGUAGE MODELS \"]]],[null,6]]],[80,267,[[[80,267,[\"Weicheng Kuo?, Yin Cui†, Xiuye Gu†, AJ Piergiovanni?, Anelia Angelova? ?Google Research, Brain Team; †Google Research, Perception {weicheng, yincui, xiuyegu, ajpiergi, anelia}@google.com \"]]]]],[267,276,[[[267,276,[\"ABSTRACT \"]]]]],[276,1202,[[[276,1202,[\"We present F-VLM, a simple open-vocabulary object detection method built upon Frozen Vision and Language Models. F-VLM simplifies the current multi-stage training pipeline by eliminating the need for knowledge distillation or detection-tailored pretraining. Surprisingly, we observe that a frozen VLM: 1) retains the locality-sensitive features necessary for detection, and 2) is a strong region clas-sifier. We finetune only the detector head and combine the detector and VLM outputs for each region at inference time. F-VLM shows compelling scaling be-havior and achieves +6.5 mask AP improvement over the previous state-of-the-art on LVIS open-vocabulary detection benchmark at system level. In addition, we demonstrate very competitive results on COCO open-vocabulary detection bench-mark and cross-dataset transfer detection, in addition to significant training speed-up and compute savings. The code will be released 1. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"25b20281-de48-4bff-9826-5f7ab6ef46bd\"]]],[[\"cf541b37-7759-4174-a6c9-2d23f42b1c15\"],[null,null,0.7124015140812012,[[null,3748,4499]],[[[3748,3804,[[[3748,3804,[\"1Project page: https://sites.google.com/view/f-vlm/home \"]]]]],[3804,3804,[]],[3804,3805,[[[3804,3805,[\" \"]]]]],[3805,4499,[[[3805,4499,[\"Figure 1: We explore the potential of frozen VLM (e.g., CLIP) features for open-vocabulary de-tection. The feature grouping reveals rich semantic and locality-sensitive information where object boundaries are nicely delineated (col. 2, see Appendix C for more details). The same frozen features can classify groundtruth regions well without finetuning (col. 3). Therefore, we propose to build a open-vocabulary detector on top of a frozen VLM (col. 4) without a need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. F-VLM significantly reduces training complexity and compute requirement, and achieves the state-of-the-art performance at system level. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"cf541b37-7759-4174-a6c9-2d23f42b1c15\"]]],[[\"f43235c3-ab58-4f2e-b4fc-b1470a54650b\"],[null,null,0.7422914309744433,[[null,16159,17274]],[[[16159,16191,[[[16159,16191,[\"3.4 OPEN-VOCABULARY RECOGNITION \"]]]]],[16191,16494,[[[16191,16494,[\"The ability to perform open-vocabulary recognition at region level is integral to F-VLM. Since the backbone features are frozen, they do not overfit to the base categories and can be directly cropped for region-level classification. F-VLM performs this open-vocabulary classification only at test time. \"]]]]],[16494,17274,[[[16494,17274,[\"To obtain the features for a region b, we apply the VLM pooling layer P(·) on the cropped backbone output features F(I) (see Sec. 3.2 for notations). Because the pooling layer requires fixed-size inputs, e.g. 7x7 for R50 (Radford et al., 2021), we crop and resize the region features with ROI-Align R(·) (He et al., 2017) (see Fig. 2b). Unlike existing works (Gu et al., 2022; Du et al., 2022), we do not crop and resize the RGB image regions and cache their embeddings in a separate offline process, but train the detector head in one stage. This is simpler and more space-efficient. In addition, we do not crop VLM region features with R(·) during training because the backbone features are frozen. Using the notations from equation 1, we obtain the VLM region embedding vb by: \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"f43235c3-ab58-4f2e-b4fc-b1470a54650b\"]]],[[\"dd6fa657-2432-4799-90bc-4481bb7095d0\"],[null,null,0.7249287816845053,[[null,14167,15130]],[[[14167,14204,[[[14167,14204,[\"3.3 TEXT-EMBEDDING REGION CLASSIFIER \"]]]]],[14204,14524,[[[14204,14524,[\"Notations: Let’s define I as the input image, F(I) the backbone features from the image encoder. Let Q(·) be the function that yields a region embedding rb from F(I) and a given box region proposal b, which involves FPN (Lin et al., 2017), ROI-Align (He et al., 2017), and Faster R-CNN head (Ren et al., 2015). We have: \"]]]]],[14524,14544,[[[14524,14544,[\"rb \\u003d Q(F(I), b) (1) \"]]]]],[14544,15130,[[[14544,15130,[\"Standard detectors use K-way classifier because the training and test time categories are the same. This design does not support the open-vocabulary settings which require new categories to be added at test time. To accommodate this, we replace the last fully connected layer with the text embeddings of base categories (see Fig. 2a). At inference time, we can simply expand the text embeddings to include novel categories for open-vocabulary detection (see Fig. 2b). An advantage of such design is that the system can generalize to the novel categories near CB in the embedding space. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"dd6fa657-2432-4799-90bc-4481bb7095d0\"]]],[[\"108e1a32-7313-48e7-9e57-9f3c27777b62\"],[null,null,0.7133383996419858,[[null,17274,17953]],[[[17274,17297,[[[17274,17297,[\"vb \\u003d P(R(F(I), b)) (3) \"]]]]],[17297,17492,[[[17297,17492,[\"where b denotes the box region and vb corresponds to v1, ..., vk in Fig. 2b. NoteR(·) is used at test time only. Similar to equation 2, we compute the VLM scores by cosine similarity as follows: \"]]]]],[17492,17511,[[[17492,17511,[\"w(vb) \\u003d Softmax( 1 \"]]]]],[17511,17513,[[[17511,17513,[\"T \"]]]]],[17513,17568,[[[17513,17568,[\"[ cos(vb, tbg), cos(vb, t1), · · · , cos(vb, t|CB∪N |) \"]]]]],[17568,17576,[[[17568,17576,[\"] ) (4) \"]]]]],[17576,17953,[[[17576,17953,[\"where T is a fixed temperature and the text embeddings include both the CB and CN at inference time (see Fig. 2b). We use a fixed temperature to adjust the scale of VLM scores relative to the detection scores in equation 2. In the special case when the region b is equal to the whole image, the VLM scores w(vb) becomes equivalent to the zero-shot image classification scores. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"108e1a32-7313-48e7-9e57-9f3c27777b62\"]]]]]],[[null,null,0.7452614778134843,[[null,2815,3748]],[[[2815,3748,[[[2815,3748,[\"We propose F-VLM – a simple and scalable open-vocabulary detection approach built upon frozen VLMs. For localization, we simply attach a detector head to predict object regions. For open-vocabulary recognition, we apply the VLM feature pooler (e.g., a self-attention layer) on the region features from frozen backbones at test time. We train only the detector head upon a frozen VLM backbone, and combine the detection scores with the corresponding VLM predictions at test time. Our recipe reduces the training complexity of an open-vocabulary detector to below that of a stan-dard detector, obviating the need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. By preserving the knowledge of pretrained VLMs completely, F-VLM main-tains a similar philosophy as ViTDet (Li et al., 2022c) to decouple the detector-specific learning from the more task-agnostic vision knowledge in the backbone. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"e1d0c4d1-ab01-49ed-a619-4fe0fd462521\"]],[null,null,0.7398836624402914,[[null,0,1202]],[[[0,1,[[[0,1,[\" \"]]]]],[1,41,[[[1,41,[\"F-VLM: OPEN-VOCABULARY OBJECT DETECTION \"]]],[null,6]]],[41,80,[[[41,80,[\"UPON FROZEN VISION AND LANGUAGE MODELS \"]]],[null,6]]],[80,267,[[[80,267,[\"Weicheng Kuo?, Yin Cui†, Xiuye Gu†, AJ Piergiovanni?, Anelia Angelova? ?Google Research, Brain Team; †Google Research, Perception {weicheng, yincui, xiuyegu, ajpiergi, anelia}@google.com \"]]]]],[267,276,[[[267,276,[\"ABSTRACT \"]]]]],[276,1202,[[[276,1202,[\"We present F-VLM, a simple open-vocabulary object detection method built upon Frozen Vision and Language Models. F-VLM simplifies the current multi-stage training pipeline by eliminating the need for knowledge distillation or detection-tailored pretraining. Surprisingly, we observe that a frozen VLM: 1) retains the locality-sensitive features necessary for detection, and 2) is a strong region clas-sifier. We finetune only the detector head and combine the detector and VLM outputs for each region at inference time. F-VLM shows compelling scaling be-havior and achieves +6.5 mask AP improvement over the previous state-of-the-art on LVIS open-vocabulary detection benchmark at system level. In addition, we demonstrate very competitive results on COCO open-vocabulary detection bench-mark and cross-dataset transfer detection, in addition to significant training speed-up and compute savings. The code will be released 1. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"25b20281-de48-4bff-9826-5f7ab6ef46bd\"]],[null,null,0.7124015140812012,[[null,3748,4499]],[[[3748,3804,[[[3748,3804,[\"1Project page: https://sites.google.com/view/f-vlm/home \"]]]]],[3804,3804,[]],[3804,3805,[[[3804,3805,[\" \"]]]]],[3805,4499,[[[3805,4499,[\"Figure 1: We explore the potential of frozen VLM (e.g., CLIP) features for open-vocabulary de-tection. The feature grouping reveals rich semantic and locality-sensitive information where object boundaries are nicely delineated (col. 2, see Appendix C for more details). The same frozen features can classify groundtruth regions well without finetuning (col. 3). Therefore, we propose to build a open-vocabulary detector on top of a frozen VLM (col. 4) without a need for knowledge distillation, detection-tailored pretraining, or weakly supervised learning. F-VLM significantly reduces training complexity and compute requirement, and achieves the state-of-the-art performance at system level. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"cf541b37-7759-4174-a6c9-2d23f42b1c15\"]],[null,null,0.7422914309744433,[[null,16159,17274]],[[[16159,16191,[[[16159,16191,[\"3.4 OPEN-VOCABULARY RECOGNITION \"]]]]],[16191,16494,[[[16191,16494,[\"The ability to perform open-vocabulary recognition at region level is integral to F-VLM. Since the backbone features are frozen, they do not overfit to the base categories and can be directly cropped for region-level classification. F-VLM performs this open-vocabulary classification only at test time. \"]]]]],[16494,17274,[[[16494,17274,[\"To obtain the features for a region b, we apply the VLM pooling layer P(·) on the cropped backbone output features F(I) (see Sec. 3.2 for notations). Because the pooling layer requires fixed-size inputs, e.g. 7x7 for R50 (Radford et al., 2021), we crop and resize the region features with ROI-Align R(·) (He et al., 2017) (see Fig. 2b). Unlike existing works (Gu et al., 2022; Du et al., 2022), we do not crop and resize the RGB image regions and cache their embeddings in a separate offline process, but train the detector head in one stage. This is simpler and more space-efficient. In addition, we do not crop VLM region features with R(·) during training because the backbone features are frozen. Using the notations from equation 1, we obtain the VLM region embedding vb by: \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"f43235c3-ab58-4f2e-b4fc-b1470a54650b\"]],[null,null,0.7249287816845053,[[null,14167,15130]],[[[14167,14204,[[[14167,14204,[\"3.3 TEXT-EMBEDDING REGION CLASSIFIER \"]]]]],[14204,14524,[[[14204,14524,[\"Notations: Let’s define I as the input image, F(I) the backbone features from the image encoder. Let Q(·) be the function that yields a region embedding rb from F(I) and a given box region proposal b, which involves FPN (Lin et al., 2017), ROI-Align (He et al., 2017), and Faster R-CNN head (Ren et al., 2015). We have: \"]]]]],[14524,14544,[[[14524,14544,[\"rb \\u003d Q(F(I), b) (1) \"]]]]],[14544,15130,[[[14544,15130,[\"Standard detectors use K-way classifier because the training and test time categories are the same. This design does not support the open-vocabulary settings which require new categories to be added at test time. To accommodate this, we replace the last fully connected layer with the text embeddings of base categories (see Fig. 2a). At inference time, we can simply expand the text embeddings to include novel categories for open-vocabulary detection (see Fig. 2b). An advantage of such design is that the system can generalize to the novel categories near CB in the embedding space. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"dd6fa657-2432-4799-90bc-4481bb7095d0\"]],[null,null,0.7133383996419858,[[null,17274,17953]],[[[17274,17297,[[[17274,17297,[\"vb \\u003d P(R(F(I), b)) (3) \"]]]]],[17297,17492,[[[17297,17492,[\"where b denotes the box region and vb corresponds to v1, ..., vk in Fig. 2b. NoteR(·) is used at test time only. Similar to equation 2, we compute the VLM scores by cosine similarity as follows: \"]]]]],[17492,17511,[[[17492,17511,[\"w(vb) \\u003d Softmax( 1 \"]]]]],[17511,17513,[[[17511,17513,[\"T \"]]]]],[17513,17568,[[[17513,17568,[\"[ cos(vb, tbg), cos(vb, t1), · · · , cos(vb, t|CB∪N |) \"]]]]],[17568,17576,[[[17568,17576,[\"] ) (4) \"]]]]],[17576,17953,[[[17576,17953,[\"where T is a fixed temperature and the text embeddings include both the CB and CN at inference time (see Fig. 2b). We use a fixed temperature to adjust the scale of VLM scores relative to the detection scores in equation 2. In the special case when the region b is equal to the whole image, the VLM scores w(vb) becomes equivalent to the zero-shot image classification scores. \"]]]]]]],[[[\"340079b7-5239-4802-972a-32649f56ab2e\"],\"20048601-9146-4bc5-869b-a403031cc1ca\"]],[\"108e1a32-7313-48e7-9e57-9f3c27777b62\"]]],[[[null,139,139],[0]],[[null,178,178],[0,1]],[[null,248,359],[0,1,2]],[[null,529,583],[3]],[[null,613,613],[3,4]],[[null,708,740],[4]],[[null,858,881],[3,5]]],[[\"How does F-VLM simplify training?\",\"What is F-VLM's key advantage?\",\"How are VLM scores combined?\"]]]"]]
60
[["di",13471],["af.httprm",13471,"-46557566088670092",63]]
27
[["e",7,null,null,59845]]
