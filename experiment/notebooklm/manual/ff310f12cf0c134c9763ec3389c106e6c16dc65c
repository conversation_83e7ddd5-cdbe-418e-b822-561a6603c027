)]}'

397
[["wrb.fr",null,"[[\"In the paper, the metrics \\\"score\\\" and \\\"quality\\\" are defined as follows:\\n\\n*   **Score**: This refers to a **raw performance score** [\",null,[\"26934f33-c5ad-427c-837d-3062ba0bc716\",\"8daa5cd8-2e65-4bab-8f0d-0c3318348c0b\",3150807202],null,[[[[0,71,[[[0,71,[\"In the paper, the metrics \\\"score\\\" and \\\"quality\\\" are defined as follows:\"]]]]]]]]]]"]]
14968
[["wrb.fr",null,"[[\"In the paper, the metrics \\\"score\\\" and \\\"quality\\\" are defined as follows:\\n\\n*   **Score**: This refers to a **raw performance score** [1]. For scaffold-based optimization tasks, the score is a **task-specific real number ranging from 0 to 1**, where higher values indicate a better match of the generated molecules to a target molecular profile [2]. Molecules that do not contain the required scaffold receive a total score of 0 [3].\\n*   **Quality**: This metric is **defined as the absence of undesirable substructures** in the molecules [1]. It is measured using quality filters, which are evaluated post-hoc on the optimized molecules [1, 2].\",null,[\"26934f33-c5ad-427c-837d-3062ba0bc716\",\"8daa5cd8-2e65-4bab-8f0d-0c3318348c0b\",3150807202],null,[[[[0,71,[[[0,71,[\"In the paper, the metrics \\\"score\\\" and \\\"quality\\\" are defined as follows:\"]]]]],[71,400,[[[71,76,[\"Score\",[true]]],[76,95,[\": This refers to a \"]],[95,116,[\"raw performance score\",[true]]],[116,172,[\". For scaffold-based optimization tasks, the score is a \"]],[172,217,[\"task-specific real number ranging from 0 to 1\",[true]]],[217,400,[\", where higher values indicate a better match of the generated molecules to a target molecular profile. Molecules that do not contain the required scaffold receive a total score of 0.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]]],[[[\"8d927a16-4018-47b8-811b-fdd7d36b2861\"],[null,116,116]],[[\"387db09b-087c-4b9f-b108-608c0c7b63df\"],[null,217,319]],[[\"a1203398-4918-417a-a6c1-3a78bf9d3c8b\"],[null,319,399]],[[\"8d927a16-4018-47b8-811b-fdd7d36b2861\"],[null,476,492]],[[\"8d927a16-4018-47b8-811b-fdd7d36b2861\"],[null,492,587]],[[\"387db09b-087c-4b9f-b108-608c0c7b63df\"],[null,492,587]]]],null,null,[[[\"8d927a16-4018-47b8-811b-fdd7d36b2861\"],[null,null,0.715564812971718,[[null,23744,25304]],[[[23744,25304,[[[23744,25304,[\"Unconstrained Optimization We experiment on the GuacaMol optimization benchmarks (Brown et al., 2019), tracking two metrics: raw performance score, and quality, defined as absence of undesirable substructures. In Table 2 (left), we compare our results with those taken from the literature. We find that MoLeR maintains a good balance between raw score and quality. Note that the quality filters are not directly available to the models during optimization, and rather are evaluated post-hoc on the optimized molecules. This ensures that high quality scores can only be achieved if the model is biased towards reasonable molecules, and not by learning to exploit and “slip through” the quality filters, similarly to what has been shown for property predictors (Renz et al., 2020). Consequently, the best performing models often produce unreasonable molecules (Winter et al., 2019b; Xu et al., 2020). While the SMILES LSTM baseline of Brown et al. (2019) also gets good results on both score and quality, as we will see below, it struggles to complete arbitrary scaffolds. Note that, out of 20 tasks in this suite, only one tests optimization from a scaffold, and that task uses a small scaffold (Figure 3 (top)), making it relatively easy (even simple models get near-perfect results). In contrast, scaffolds typically used in drug discovery are much more complex (Schuffenhauer et al., 2007; Schuffenhauer, 2012). We conclude that while MoLeR shows good performance on GuacaMol tasks, they do not properly evaluate the ability to complete realistic scaffolds. \"]]]]]]],[[[\"aa74ce07-82a5-4d89-aff1-5a9acabe176a\"],\"34fa0568-c25d-497c-a211-88097d1d22ae\"]],[\"8d927a16-4018-47b8-811b-fdd7d36b2861\"]]],[[\"387db09b-087c-4b9f-b108-608c0c7b63df\"],[null,null,0.6850496323875861,[[null,63561,64252]],[[[63561,63567,[[[63561,63567,[\"N N S \"]]]]],[63567,63572,[[[63567,63572,[\"O N+ \"]]]]],[63572,63574,[[[63572,63574,[\"O \"]]]]],[63574,63583,[[[63574,63583,[\"O-O O Cl \"]]]]],[63583,63585,[[[63583,63585,[\"O \"]]]]],[63585,63587,[[[63585,63587,[\"O \"]]]]],[63587,63589,[[[63587,63589,[\"N \"]]]]],[63589,63594,[[[63589,63594,[\"Br N \"]]]]],[63594,63597,[[[63594,63597,[\"HO \"]]]]],[63597,63599,[[[63597,63599,[\"N \"]]]]],[63599,63601,[[[63599,63601,[\"N \"]]]]],[63601,63605,[[[63601,63605,[\"H2N \"]]]]],[63605,63607,[[[63605,63607,[\"N \"]]]]],[63607,63609,[[[63607,63609,[\"N \"]]]]],[63609,63611,[[[63609,63611,[\"N \"]]]]],[63611,63614,[[[63611,63614,[\"HN \"]]]]],[63614,63617,[[[63614,63617,[\"Cl \"]]]]],[63617,63619,[[[63617,63619,[\"O \"]]]]],[63619,63621,[[[63619,63621,[\"N \"]]]]],[63621,63623,[[[63621,63623,[\"O \"]]]]],[63623,63626,[[[63623,63626,[\"HN \"]]]]],[63626,63628,[[[63626,63628,[\"S \"]]]]],[63628,63630,[[[63628,63630,[\"O \"]]]]],[63630,63632,[[[63630,63632,[\"O \"]]]]],[63632,63634,[[[63632,63634,[\"F \"]]]]],[63634,63637,[[[63634,63637,[\"HN \"]]]]],[63637,63641,[[[63637,63641,[\"NH2 \"]]]]],[63641,63644,[[[63641,63644,[\"NH \"]]]]],[63644,63646,[[[63644,63646,[\"O \"]]]]],[63646,63648,[[[63646,63648,[\"F \"]]]]],[63648,63650,[[[63648,63650,[\"N \"]]]]],[63650,63652,[[[63650,63652,[\"O \"]]]]],[63652,63654,[[[63652,63654,[\"S \"]]]]],[63654,63656,[[[63654,63656,[\"N \"]]]]],[63656,63658,[[[63656,63658,[\"N \"]]]]],[63658,63660,[[[63658,63660,[\"O \"]]]]],[63660,63662,[[[63660,63662,[\"N \"]]]]],[63662,63664,[[[63662,63664,[\"O \"]]]]],[63664,63666,[[[63664,63666,[\"N \"]]]]],[63666,63725,[[[63666,63725,[\"Figure 6: Samples from the prior of a trained MoLeR model. \"]]]]],[63725,63725,[]],[63725,63726,[[[63725,63726,[\" \"]]]]],[63726,63767,[[[63726,63767,[\"E SCAFFOLD-BASED OPTIMIZATION BENCHMARKS \"]]]]],[63767,64252,[[[63767,64252,[\"Our new scaffold-based benchmarks were inspired by real-world clinical candidates or marketed drugs, and employ large challenging scaffolds. The format closely follows the one used for tasks in Guacamol (Brown et al., 2019); in all cases, the score is a task-specific real number in the [0, 1] range, with higher values being better, indicating how well the given molecules match a target molecular profile. To measure quality, we used the same quality filters as Brown et al. (2019). \"]]]]]]],[[[\"aa74ce07-82a5-4d89-aff1-5a9acabe176a\"],\"34fa0568-c25d-497c-a211-88097d1d22ae\"]],[\"387db09b-087c-4b9f-b108-608c0c7b63df\"]]],[[\"a1203398-4918-417a-a6c1-3a78bf9d3c8b\"],[null,null,0.6576232383654953,[[null,25825,26758]],[[[25825,25829,[[[25825,25829,[\"NH2 \"]]],[null,6]]],[25829,25831,[[[25829,25831,[\"N \"]]],[null,6]]],[25831,25833,[[[25831,25833,[\"N \"]]],[null,6]]],[25833,25836,[[[25833,25836,[\"OH \"]]],[null,6]]],[25836,25840,[[[25836,25840,[\"N N \"]]],[null,6]]],[25840,25844,[[[25840,25844,[\"N H \"]]],[null,6]]],[25844,25846,[[[25844,25846,[\"F \"]]],[null,6]]],[25846,25848,[[[25846,25848,[\"S \"]]],[null,6]]],[25848,25850,[[[25848,25850,[\"O \"]]],[null,6]]],[25850,25852,[[[25850,25852,[\"O \"]]],[null,6]]],[25852,25854,[[[25852,25854,[\"N \"]]],[null,6]]],[25854,25857,[[[25854,25857,[\"HN \"]]],[null,6]]],[25857,25859,[[[25857,25859,[\"N \"]]],[null,6]]],[25859,25861,[[[25859,25861,[\"O \"]]],[null,6]]],[25861,25967,[[[25861,25967,[\"Figure 3: Scaffold from a GuacaMol benchmark (top) and a scaffold from our additional benchmark (bottom). \"]]]]],[25967,26758,[[[25967,26758,[\"Scaffold-constrained Optimization To evaluate scaffold-constrained optimization, we extend the GuacaMol benchmarks with 4 new scaffold-based tasks, using larger scaffolds extracted from or inspired by clinical candidate molecules or marketed drugs, which are more representative of real-world drug discovery (e.g. Figure 3 (bottom)). The task is to perform scaffold-constrained exploration towards a target property profile; as the components of the scoring functions are aggregated via the geometric mean, and presence of the scaffold is binary, molecules that do not contain the scaffold receive a total score of 0 (see Appendix E for more details). We show the results in Table 2 (right). We see that MoLeR performs well, while most baseline approaches struggle to maintain the scaffold. \"]]]]]]],[[[\"aa74ce07-82a5-4d89-aff1-5a9acabe176a\"],\"34fa0568-c25d-497c-a211-88097d1d22ae\"]],[\"a1203398-4918-417a-a6c1-3a78bf9d3c8b\"]]]]]],[[null,null,0.715564812971718,[[null,23744,25304]],[[[23744,25304,[[[23744,25304,[\"Unconstrained Optimization We experiment on the GuacaMol optimization benchmarks (Brown et al., 2019), tracking two metrics: raw performance score, and quality, defined as absence of undesirable substructures. In Table 2 (left), we compare our results with those taken from the literature. We find that MoLeR maintains a good balance between raw score and quality. Note that the quality filters are not directly available to the models during optimization, and rather are evaluated post-hoc on the optimized molecules. This ensures that high quality scores can only be achieved if the model is biased towards reasonable molecules, and not by learning to exploit and “slip through” the quality filters, similarly to what has been shown for property predictors (Renz et al., 2020). Consequently, the best performing models often produce unreasonable molecules (Winter et al., 2019b; Xu et al., 2020). While the SMILES LSTM baseline of Brown et al. (2019) also gets good results on both score and quality, as we will see below, it struggles to complete arbitrary scaffolds. Note that, out of 20 tasks in this suite, only one tests optimization from a scaffold, and that task uses a small scaffold (Figure 3 (top)), making it relatively easy (even simple models get near-perfect results). In contrast, scaffolds typically used in drug discovery are much more complex (Schuffenhauer et al., 2007; Schuffenhauer, 2012). We conclude that while MoLeR shows good performance on GuacaMol tasks, they do not properly evaluate the ability to complete realistic scaffolds. \"]]]]]]],[[[\"aa74ce07-82a5-4d89-aff1-5a9acabe176a\"],\"34fa0568-c25d-497c-a211-88097d1d22ae\"]],[\"8d927a16-4018-47b8-811b-fdd7d36b2861\"]],[null,null,0.6850496323875861,[[null,63561,64252]],[[[63561,63567,[[[63561,63567,[\"N N S \"]]]]],[63567,63572,[[[63567,63572,[\"O N+ \"]]]]],[63572,63574,[[[63572,63574,[\"O \"]]]]],[63574,63583,[[[63574,63583,[\"O-O O Cl \"]]]]],[63583,63585,[[[63583,63585,[\"O \"]]]]],[63585,63587,[[[63585,63587,[\"O \"]]]]],[63587,63589,[[[63587,63589,[\"N \"]]]]],[63589,63594,[[[63589,63594,[\"Br N \"]]]]],[63594,63597,[[[63594,63597,[\"HO \"]]]]],[63597,63599,[[[63597,63599,[\"N \"]]]]],[63599,63601,[[[63599,63601,[\"N \"]]]]],[63601,63605,[[[63601,63605,[\"H2N \"]]]]],[63605,63607,[[[63605,63607,[\"N \"]]]]],[63607,63609,[[[63607,63609,[\"N \"]]]]],[63609,63611,[[[63609,63611,[\"N \"]]]]],[63611,63614,[[[63611,63614,[\"HN \"]]]]],[63614,63617,[[[63614,63617,[\"Cl \"]]]]],[63617,63619,[[[63617,63619,[\"O \"]]]]],[63619,63621,[[[63619,63621,[\"N \"]]]]],[63621,63623,[[[63621,63623,[\"O \"]]]]],[63623,63626,[[[63623,63626,[\"HN \"]]]]],[63626,63628,[[[63626,63628,[\"S \"]]]]],[63628,63630,[[[63628,63630,[\"O \"]]]]],[63630,63632,[[[63630,63632,[\"O \"]]]]],[63632,63634,[[[63632,63634,[\"F \"]]]]],[63634,63637,[[[63634,63637,[\"HN \"]]]]],[63637,63641,[[[63637,63641,[\"NH2 \"]]]]],[63641,63644,[[[63641,63644,[\"NH \"]]]]],[63644,63646,[[[63644,63646,[\"O \"]]]]],[63646,63648,[[[63646,63648,[\"F \"]]]]],[63648,63650,[[[63648,63650,[\"N \"]]]]],[63650,63652,[[[63650,63652,[\"O \"]]]]],[63652,63654,[[[63652,63654,[\"S \"]]]]],[63654,63656,[[[63654,63656,[\"N \"]]]]],[63656,63658,[[[63656,63658,[\"N \"]]]]],[63658,63660,[[[63658,63660,[\"O \"]]]]],[63660,63662,[[[63660,63662,[\"N \"]]]]],[63662,63664,[[[63662,63664,[\"O \"]]]]],[63664,63666,[[[63664,63666,[\"N \"]]]]],[63666,63725,[[[63666,63725,[\"Figure 6: Samples from the prior of a trained MoLeR model. \"]]]]],[63725,63725,[]],[63725,63726,[[[63725,63726,[\" \"]]]]],[63726,63767,[[[63726,63767,[\"E SCAFFOLD-BASED OPTIMIZATION BENCHMARKS \"]]]]],[63767,64252,[[[63767,64252,[\"Our new scaffold-based benchmarks were inspired by real-world clinical candidates or marketed drugs, and employ large challenging scaffolds. The format closely follows the one used for tasks in Guacamol (Brown et al., 2019); in all cases, the score is a task-specific real number in the [0, 1] range, with higher values being better, indicating how well the given molecules match a target molecular profile. To measure quality, we used the same quality filters as Brown et al. (2019). \"]]]]]]],[[[\"aa74ce07-82a5-4d89-aff1-5a9acabe176a\"],\"34fa0568-c25d-497c-a211-88097d1d22ae\"]],[\"387db09b-087c-4b9f-b108-608c0c7b63df\"]],[null,null,0.6576232383654953,[[null,25825,26758]],[[[25825,25829,[[[25825,25829,[\"NH2 \"]]],[null,6]]],[25829,25831,[[[25829,25831,[\"N \"]]],[null,6]]],[25831,25833,[[[25831,25833,[\"N \"]]],[null,6]]],[25833,25836,[[[25833,25836,[\"OH \"]]],[null,6]]],[25836,25840,[[[25836,25840,[\"N N \"]]],[null,6]]],[25840,25844,[[[25840,25844,[\"N H \"]]],[null,6]]],[25844,25846,[[[25844,25846,[\"F \"]]],[null,6]]],[25846,25848,[[[25846,25848,[\"S \"]]],[null,6]]],[25848,25850,[[[25848,25850,[\"O \"]]],[null,6]]],[25850,25852,[[[25850,25852,[\"O \"]]],[null,6]]],[25852,25854,[[[25852,25854,[\"N \"]]],[null,6]]],[25854,25857,[[[25854,25857,[\"HN \"]]],[null,6]]],[25857,25859,[[[25857,25859,[\"N \"]]],[null,6]]],[25859,25861,[[[25859,25861,[\"O \"]]],[null,6]]],[25861,25967,[[[25861,25967,[\"Figure 3: Scaffold from a GuacaMol benchmark (top) and a scaffold from our additional benchmark (bottom). \"]]]]],[25967,26758,[[[25967,26758,[\"Scaffold-constrained Optimization To evaluate scaffold-constrained optimization, we extend the GuacaMol benchmarks with 4 new scaffold-based tasks, using larger scaffolds extracted from or inspired by clinical candidate molecules or marketed drugs, which are more representative of real-world drug discovery (e.g. Figure 3 (bottom)). The task is to perform scaffold-constrained exploration towards a target property profile; as the components of the scoring functions are aggregated via the geometric mean, and presence of the scaffold is binary, molecules that do not contain the scaffold receive a total score of 0 (see Appendix E for more details). We show the results in Table 2 (right). We see that MoLeR performs well, while most baseline approaches struggle to maintain the scaffold. \"]]]]]]],[[[\"aa74ce07-82a5-4d89-aff1-5a9acabe176a\"],\"34fa0568-c25d-497c-a211-88097d1d22ae\"]],[\"a1203398-4918-417a-a6c1-3a78bf9d3c8b\"]]],[[[null,116,116],[0]],[[null,217,319],[1]],[[null,319,399],[2]],[[null,476,492],[0]],[[null,492,587],[0,1]]]]"]]
15777
[["wrb.fr",null,"[[\"In the paper, the metrics \\\"score\\\" and \\\"quality\\\" are defined as follows:\\n\\n*   **Score**: This refers to a **raw performance score** [1]. For scaffold-based optimization tasks, the score is a **task-specific real number ranging from 0 to 1**, where higher values indicate a better match of the generated molecules to a target molecular profile [2]. Molecules that do not contain the required scaffold receive a total score of 0 [3].\\n*   **Quality**: This metric is **defined as the absence of undesirable substructures** in the molecules [1]. It is measured using quality filters, which are evaluated post-hoc on the optimized molecules [1, 2].\",null,[\"26934f33-c5ad-427c-837d-3062ba0bc716\",\"8daa5cd8-2e65-4bab-8f0d-0c3318348c0b\",3150807202],null,[[[[0,71,[[[0,71,[\"In the paper, the metrics \\\"score\\\" and \\\"quality\\\" are defined as follows:\"]]]]],[71,400,[[[71,76,[\"Score\",[true]]],[76,95,[\": This refers to a \"]],[95,116,[\"raw performance score\",[true]]],[116,172,[\". For scaffold-based optimization tasks, the score is a \"]],[172,217,[\"task-specific real number ranging from 0 to 1\",[true]]],[217,400,[\", where higher values indicate a better match of the generated molecules to a target molecular profile. Molecules that do not contain the required scaffold receive a total score of 0.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[400,588,[[[400,407,[\"Quality\",[true]]],[407,424,[\": This metric is \"]],[424,475,[\"defined as the absence of undesirable substructures\",[true]]],[475,588,[\" in the molecules. It is measured using quality filters, which are evaluated post-hoc on the optimized molecules.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]]],[[[\"8d927a16-4018-47b8-811b-fdd7d36b2861\"],[null,116,116]],[[\"387db09b-087c-4b9f-b108-608c0c7b63df\"],[null,217,319]],[[\"a1203398-4918-417a-a6c1-3a78bf9d3c8b\"],[null,319,399]],[[\"8d927a16-4018-47b8-811b-fdd7d36b2861\"],[null,476,492]],[[\"8d927a16-4018-47b8-811b-fdd7d36b2861\"],[null,492,587]],[[\"387db09b-087c-4b9f-b108-608c0c7b63df\"],[null,492,587]]]],null,null,[[[\"8d927a16-4018-47b8-811b-fdd7d36b2861\"],[null,null,0.715564812971718,[[null,23744,25304]],[[[23744,25304,[[[23744,25304,[\"Unconstrained Optimization We experiment on the GuacaMol optimization benchmarks (Brown et al., 2019), tracking two metrics: raw performance score, and quality, defined as absence of undesirable substructures. In Table 2 (left), we compare our results with those taken from the literature. We find that MoLeR maintains a good balance between raw score and quality. Note that the quality filters are not directly available to the models during optimization, and rather are evaluated post-hoc on the optimized molecules. This ensures that high quality scores can only be achieved if the model is biased towards reasonable molecules, and not by learning to exploit and “slip through” the quality filters, similarly to what has been shown for property predictors (Renz et al., 2020). Consequently, the best performing models often produce unreasonable molecules (Winter et al., 2019b; Xu et al., 2020). While the SMILES LSTM baseline of Brown et al. (2019) also gets good results on both score and quality, as we will see below, it struggles to complete arbitrary scaffolds. Note that, out of 20 tasks in this suite, only one tests optimization from a scaffold, and that task uses a small scaffold (Figure 3 (top)), making it relatively easy (even simple models get near-perfect results). In contrast, scaffolds typically used in drug discovery are much more complex (Schuffenhauer et al., 2007; Schuffenhauer, 2012). We conclude that while MoLeR shows good performance on GuacaMol tasks, they do not properly evaluate the ability to complete realistic scaffolds. \"]]]]]]],[[[\"aa74ce07-82a5-4d89-aff1-5a9acabe176a\"],\"34fa0568-c25d-497c-a211-88097d1d22ae\"]],[\"8d927a16-4018-47b8-811b-fdd7d36b2861\"]]],[[\"387db09b-087c-4b9f-b108-608c0c7b63df\"],[null,null,0.6850496323875861,[[null,63561,64252]],[[[63561,63567,[[[63561,63567,[\"N N S \"]]]]],[63567,63572,[[[63567,63572,[\"O N+ \"]]]]],[63572,63574,[[[63572,63574,[\"O \"]]]]],[63574,63583,[[[63574,63583,[\"O-O O Cl \"]]]]],[63583,63585,[[[63583,63585,[\"O \"]]]]],[63585,63587,[[[63585,63587,[\"O \"]]]]],[63587,63589,[[[63587,63589,[\"N \"]]]]],[63589,63594,[[[63589,63594,[\"Br N \"]]]]],[63594,63597,[[[63594,63597,[\"HO \"]]]]],[63597,63599,[[[63597,63599,[\"N \"]]]]],[63599,63601,[[[63599,63601,[\"N \"]]]]],[63601,63605,[[[63601,63605,[\"H2N \"]]]]],[63605,63607,[[[63605,63607,[\"N \"]]]]],[63607,63609,[[[63607,63609,[\"N \"]]]]],[63609,63611,[[[63609,63611,[\"N \"]]]]],[63611,63614,[[[63611,63614,[\"HN \"]]]]],[63614,63617,[[[63614,63617,[\"Cl \"]]]]],[63617,63619,[[[63617,63619,[\"O \"]]]]],[63619,63621,[[[63619,63621,[\"N \"]]]]],[63621,63623,[[[63621,63623,[\"O \"]]]]],[63623,63626,[[[63623,63626,[\"HN \"]]]]],[63626,63628,[[[63626,63628,[\"S \"]]]]],[63628,63630,[[[63628,63630,[\"O \"]]]]],[63630,63632,[[[63630,63632,[\"O \"]]]]],[63632,63634,[[[63632,63634,[\"F \"]]]]],[63634,63637,[[[63634,63637,[\"HN \"]]]]],[63637,63641,[[[63637,63641,[\"NH2 \"]]]]],[63641,63644,[[[63641,63644,[\"NH \"]]]]],[63644,63646,[[[63644,63646,[\"O \"]]]]],[63646,63648,[[[63646,63648,[\"F \"]]]]],[63648,63650,[[[63648,63650,[\"N \"]]]]],[63650,63652,[[[63650,63652,[\"O \"]]]]],[63652,63654,[[[63652,63654,[\"S \"]]]]],[63654,63656,[[[63654,63656,[\"N \"]]]]],[63656,63658,[[[63656,63658,[\"N \"]]]]],[63658,63660,[[[63658,63660,[\"O \"]]]]],[63660,63662,[[[63660,63662,[\"N \"]]]]],[63662,63664,[[[63662,63664,[\"O \"]]]]],[63664,63666,[[[63664,63666,[\"N \"]]]]],[63666,63725,[[[63666,63725,[\"Figure 6: Samples from the prior of a trained MoLeR model. \"]]]]],[63725,63725,[]],[63725,63726,[[[63725,63726,[\" \"]]]]],[63726,63767,[[[63726,63767,[\"E SCAFFOLD-BASED OPTIMIZATION BENCHMARKS \"]]]]],[63767,64252,[[[63767,64252,[\"Our new scaffold-based benchmarks were inspired by real-world clinical candidates or marketed drugs, and employ large challenging scaffolds. The format closely follows the one used for tasks in Guacamol (Brown et al., 2019); in all cases, the score is a task-specific real number in the [0, 1] range, with higher values being better, indicating how well the given molecules match a target molecular profile. To measure quality, we used the same quality filters as Brown et al. (2019). \"]]]]]]],[[[\"aa74ce07-82a5-4d89-aff1-5a9acabe176a\"],\"34fa0568-c25d-497c-a211-88097d1d22ae\"]],[\"387db09b-087c-4b9f-b108-608c0c7b63df\"]]],[[\"a1203398-4918-417a-a6c1-3a78bf9d3c8b\"],[null,null,0.6576232383654953,[[null,25825,26758]],[[[25825,25829,[[[25825,25829,[\"NH2 \"]]],[null,6]]],[25829,25831,[[[25829,25831,[\"N \"]]],[null,6]]],[25831,25833,[[[25831,25833,[\"N \"]]],[null,6]]],[25833,25836,[[[25833,25836,[\"OH \"]]],[null,6]]],[25836,25840,[[[25836,25840,[\"N N \"]]],[null,6]]],[25840,25844,[[[25840,25844,[\"N H \"]]],[null,6]]],[25844,25846,[[[25844,25846,[\"F \"]]],[null,6]]],[25846,25848,[[[25846,25848,[\"S \"]]],[null,6]]],[25848,25850,[[[25848,25850,[\"O \"]]],[null,6]]],[25850,25852,[[[25850,25852,[\"O \"]]],[null,6]]],[25852,25854,[[[25852,25854,[\"N \"]]],[null,6]]],[25854,25857,[[[25854,25857,[\"HN \"]]],[null,6]]],[25857,25859,[[[25857,25859,[\"N \"]]],[null,6]]],[25859,25861,[[[25859,25861,[\"O \"]]],[null,6]]],[25861,25967,[[[25861,25967,[\"Figure 3: Scaffold from a GuacaMol benchmark (top) and a scaffold from our additional benchmark (bottom). \"]]]]],[25967,26758,[[[25967,26758,[\"Scaffold-constrained Optimization To evaluate scaffold-constrained optimization, we extend the GuacaMol benchmarks with 4 new scaffold-based tasks, using larger scaffolds extracted from or inspired by clinical candidate molecules or marketed drugs, which are more representative of real-world drug discovery (e.g. Figure 3 (bottom)). The task is to perform scaffold-constrained exploration towards a target property profile; as the components of the scoring functions are aggregated via the geometric mean, and presence of the scaffold is binary, molecules that do not contain the scaffold receive a total score of 0 (see Appendix E for more details). We show the results in Table 2 (right). We see that MoLeR performs well, while most baseline approaches struggle to maintain the scaffold. \"]]]]]]],[[[\"aa74ce07-82a5-4d89-aff1-5a9acabe176a\"],\"34fa0568-c25d-497c-a211-88097d1d22ae\"]],[\"a1203398-4918-417a-a6c1-3a78bf9d3c8b\"]]]]]],[[null,null,0.715564812971718,[[null,23744,25304]],[[[23744,25304,[[[23744,25304,[\"Unconstrained Optimization We experiment on the GuacaMol optimization benchmarks (Brown et al., 2019), tracking two metrics: raw performance score, and quality, defined as absence of undesirable substructures. In Table 2 (left), we compare our results with those taken from the literature. We find that MoLeR maintains a good balance between raw score and quality. Note that the quality filters are not directly available to the models during optimization, and rather are evaluated post-hoc on the optimized molecules. This ensures that high quality scores can only be achieved if the model is biased towards reasonable molecules, and not by learning to exploit and “slip through” the quality filters, similarly to what has been shown for property predictors (Renz et al., 2020). Consequently, the best performing models often produce unreasonable molecules (Winter et al., 2019b; Xu et al., 2020). While the SMILES LSTM baseline of Brown et al. (2019) also gets good results on both score and quality, as we will see below, it struggles to complete arbitrary scaffolds. Note that, out of 20 tasks in this suite, only one tests optimization from a scaffold, and that task uses a small scaffold (Figure 3 (top)), making it relatively easy (even simple models get near-perfect results). In contrast, scaffolds typically used in drug discovery are much more complex (Schuffenhauer et al., 2007; Schuffenhauer, 2012). We conclude that while MoLeR shows good performance on GuacaMol tasks, they do not properly evaluate the ability to complete realistic scaffolds. \"]]]]]]],[[[\"aa74ce07-82a5-4d89-aff1-5a9acabe176a\"],\"34fa0568-c25d-497c-a211-88097d1d22ae\"]],[\"8d927a16-4018-47b8-811b-fdd7d36b2861\"]],[null,null,0.6850496323875861,[[null,63561,64252]],[[[63561,63567,[[[63561,63567,[\"N N S \"]]]]],[63567,63572,[[[63567,63572,[\"O N+ \"]]]]],[63572,63574,[[[63572,63574,[\"O \"]]]]],[63574,63583,[[[63574,63583,[\"O-O O Cl \"]]]]],[63583,63585,[[[63583,63585,[\"O \"]]]]],[63585,63587,[[[63585,63587,[\"O \"]]]]],[63587,63589,[[[63587,63589,[\"N \"]]]]],[63589,63594,[[[63589,63594,[\"Br N \"]]]]],[63594,63597,[[[63594,63597,[\"HO \"]]]]],[63597,63599,[[[63597,63599,[\"N \"]]]]],[63599,63601,[[[63599,63601,[\"N \"]]]]],[63601,63605,[[[63601,63605,[\"H2N \"]]]]],[63605,63607,[[[63605,63607,[\"N \"]]]]],[63607,63609,[[[63607,63609,[\"N \"]]]]],[63609,63611,[[[63609,63611,[\"N \"]]]]],[63611,63614,[[[63611,63614,[\"HN \"]]]]],[63614,63617,[[[63614,63617,[\"Cl \"]]]]],[63617,63619,[[[63617,63619,[\"O \"]]]]],[63619,63621,[[[63619,63621,[\"N \"]]]]],[63621,63623,[[[63621,63623,[\"O \"]]]]],[63623,63626,[[[63623,63626,[\"HN \"]]]]],[63626,63628,[[[63626,63628,[\"S \"]]]]],[63628,63630,[[[63628,63630,[\"O \"]]]]],[63630,63632,[[[63630,63632,[\"O \"]]]]],[63632,63634,[[[63632,63634,[\"F \"]]]]],[63634,63637,[[[63634,63637,[\"HN \"]]]]],[63637,63641,[[[63637,63641,[\"NH2 \"]]]]],[63641,63644,[[[63641,63644,[\"NH \"]]]]],[63644,63646,[[[63644,63646,[\"O \"]]]]],[63646,63648,[[[63646,63648,[\"F \"]]]]],[63648,63650,[[[63648,63650,[\"N \"]]]]],[63650,63652,[[[63650,63652,[\"O \"]]]]],[63652,63654,[[[63652,63654,[\"S \"]]]]],[63654,63656,[[[63654,63656,[\"N \"]]]]],[63656,63658,[[[63656,63658,[\"N \"]]]]],[63658,63660,[[[63658,63660,[\"O \"]]]]],[63660,63662,[[[63660,63662,[\"N \"]]]]],[63662,63664,[[[63662,63664,[\"O \"]]]]],[63664,63666,[[[63664,63666,[\"N \"]]]]],[63666,63725,[[[63666,63725,[\"Figure 6: Samples from the prior of a trained MoLeR model. \"]]]]],[63725,63725,[]],[63725,63726,[[[63725,63726,[\" \"]]]]],[63726,63767,[[[63726,63767,[\"E SCAFFOLD-BASED OPTIMIZATION BENCHMARKS \"]]]]],[63767,64252,[[[63767,64252,[\"Our new scaffold-based benchmarks were inspired by real-world clinical candidates or marketed drugs, and employ large challenging scaffolds. The format closely follows the one used for tasks in Guacamol (Brown et al., 2019); in all cases, the score is a task-specific real number in the [0, 1] range, with higher values being better, indicating how well the given molecules match a target molecular profile. To measure quality, we used the same quality filters as Brown et al. (2019). \"]]]]]]],[[[\"aa74ce07-82a5-4d89-aff1-5a9acabe176a\"],\"34fa0568-c25d-497c-a211-88097d1d22ae\"]],[\"387db09b-087c-4b9f-b108-608c0c7b63df\"]],[null,null,0.6576232383654953,[[null,25825,26758]],[[[25825,25829,[[[25825,25829,[\"NH2 \"]]],[null,6]]],[25829,25831,[[[25829,25831,[\"N \"]]],[null,6]]],[25831,25833,[[[25831,25833,[\"N \"]]],[null,6]]],[25833,25836,[[[25833,25836,[\"OH \"]]],[null,6]]],[25836,25840,[[[25836,25840,[\"N N \"]]],[null,6]]],[25840,25844,[[[25840,25844,[\"N H \"]]],[null,6]]],[25844,25846,[[[25844,25846,[\"F \"]]],[null,6]]],[25846,25848,[[[25846,25848,[\"S \"]]],[null,6]]],[25848,25850,[[[25848,25850,[\"O \"]]],[null,6]]],[25850,25852,[[[25850,25852,[\"O \"]]],[null,6]]],[25852,25854,[[[25852,25854,[\"N \"]]],[null,6]]],[25854,25857,[[[25854,25857,[\"HN \"]]],[null,6]]],[25857,25859,[[[25857,25859,[\"N \"]]],[null,6]]],[25859,25861,[[[25859,25861,[\"O \"]]],[null,6]]],[25861,25967,[[[25861,25967,[\"Figure 3: Scaffold from a GuacaMol benchmark (top) and a scaffold from our additional benchmark (bottom). \"]]]]],[25967,26758,[[[25967,26758,[\"Scaffold-constrained Optimization To evaluate scaffold-constrained optimization, we extend the GuacaMol benchmarks with 4 new scaffold-based tasks, using larger scaffolds extracted from or inspired by clinical candidate molecules or marketed drugs, which are more representative of real-world drug discovery (e.g. Figure 3 (bottom)). The task is to perform scaffold-constrained exploration towards a target property profile; as the components of the scoring functions are aggregated via the geometric mean, and presence of the scaffold is binary, molecules that do not contain the scaffold receive a total score of 0 (see Appendix E for more details). We show the results in Table 2 (right). We see that MoLeR performs well, while most baseline approaches struggle to maintain the scaffold. \"]]]]]]],[[[\"aa74ce07-82a5-4d89-aff1-5a9acabe176a\"],\"34fa0568-c25d-497c-a211-88097d1d22ae\"]],[\"a1203398-4918-417a-a6c1-3a78bf9d3c8b\"]]],[[[null,116,116],[0]],[[null,217,319],[1]],[[null,319,399],[2]],[[null,476,492],[0]],[[null,492,587],[0,1]]]]"]]
59
[["di",4021],["af.httprm",4021,"5175004700935090576",48]]
27
[["e",6,null,null,31273]]
