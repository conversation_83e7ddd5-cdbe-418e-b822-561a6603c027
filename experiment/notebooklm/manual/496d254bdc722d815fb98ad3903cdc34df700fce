)]}'

4336
[["wrb.fr",null,"[[\"The provided complexity $t\\u003d\\\\frac{\\\\delta_f}{\\\\epsilon^{1.75}}\\\\log d$ does not match the total number of iterations complexity stated in Theorem 2 [1].\\n\\nThe iteration complexity in Theorem 2 is\",null,[\"f01df53a-a97b-437d-88a6-09275aae2350\",\"d5298089-bf9c-46f0-8475-ebf7daa63fa7\",2443083085],null,[[[[0,144,[[[0,144,[\"The provided complexity $t\\u003d\\\\frac{\\\\delta_f}{\\\\epsilon^{1.75}}\\\\log d$ does not match the total number of iterations complexity stated in Theorem 2.\"]]]]]],[[[\"47e838d2-cc5d-47b0-8075-9ac5c32917bf\"],[null,0,143]]]],null,null,[[[\"47e838d2-cc5d-47b0-8075-9ac5c32917bf\"],[null,null,0.7118869760866848,[[null,22253,23044]],[[[22253,22325,[[[22253,22325,[\"f∗ ≤ ∆f , if we set the hyperparameters as in Eq. (7) with δ0 \\u003d δ 384∆f \"]]]]],[22325,22330,[[[22325,22330,[\"√ ϵ3 \"]]]]],[22330,22347,[[[22330,22347,[\"ρ and choose µ \\u003d \"]]]]],[22347,22356,[[[22347,22356,[\"Õ( ϵ 1/2 \"]]]]],[22356,22395,[[[22356,22395,[\"d1/4 ) in Line 3 and 16, µ \\u003d Õ( ϵ 13/8 \"]]]]],[22395,22556,[[[22395,22556,[\"d1/2 ) in Line 11 of Algorithm 3. Then with probability at least 1 − δ, one of the iterates of xt in Algorithm 3 will be an ϵ-approximate SOSP. The total number \"]]]]],[22556,22590,[[[22556,22590,[\"of iterations is no more than O ( \"]]]]],[22590,22603,[[[22590,22603,[\"∆f ℓ 1/2ρ1/4 \"]]]]],[22603,22613,[[[22603,22613,[\"ϵ7/4 log( \"]]]]],[22613,22621,[[[22613,22621,[\"ℓ √ d∆f \"]]]]],[22621,22629,[[[22621,22629,[\"δϵ2 ) ) \"]]]]],[22629,22706,[[[22629,22706,[\"and the total number of function queries (oracle complexity) is no more than \"]]]]],[22706,22708,[[[22706,22708,[\"O \"]]]]],[22708,22716,[[[22708,22716,[\"( d∆f ℓ \"]]]]],[22716,22724,[[[22716,22724,[\"1/2ρ1/4 \"]]]]],[22724,22734,[[[22724,22734,[\"ϵ7/4 log( \"]]]]],[22734,22742,[[[22734,22742,[\"ℓ √ d∆f \"]]]]],[22742,22748,[[[22742,22748,[\"δϵ2 ) \"]]]]],[22748,22752,[[[22748,22752,[\") . \"]]]]],[22752,23044,[[[22752,23044,[\"Remark 4. Similar to Algorithm 1, we can also add an termination condition for Algorithm 3: Once the pre-condition of random perturbation step is reached, record the current iterate point xt0 and the current function value f(xt0) before adding the random perturbation. If the decrease of the \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"47e838d2-cc5d-47b0-8075-9ac5c32917bf\"]]]]]],[[null,null,0.7118869760866848,[[null,22253,23044]],[[[22253,22325,[[[22253,22325,[\"f∗ ≤ ∆f , if we set the hyperparameters as in Eq. (7) with δ0 \\u003d δ 384∆f \"]]]]],[22325,22330,[[[22325,22330,[\"√ ϵ3 \"]]]]],[22330,22347,[[[22330,22347,[\"ρ and choose µ \\u003d \"]]]]],[22347,22356,[[[22347,22356,[\"Õ( ϵ 1/2 \"]]]]],[22356,22395,[[[22356,22395,[\"d1/4 ) in Line 3 and 16, µ \\u003d Õ( ϵ 13/8 \"]]]]],[22395,22556,[[[22395,22556,[\"d1/2 ) in Line 11 of Algorithm 3. Then with probability at least 1 − δ, one of the iterates of xt in Algorithm 3 will be an ϵ-approximate SOSP. The total number \"]]]]],[22556,22590,[[[22556,22590,[\"of iterations is no more than O ( \"]]]]],[22590,22603,[[[22590,22603,[\"∆f ℓ 1/2ρ1/4 \"]]]]],[22603,22613,[[[22603,22613,[\"ϵ7/4 log( \"]]]]],[22613,22621,[[[22613,22621,[\"ℓ √ d∆f \"]]]]],[22621,22629,[[[22621,22629,[\"δϵ2 ) ) \"]]]]],[22629,22706,[[[22629,22706,[\"and the total number of function queries (oracle complexity) is no more than \"]]]]],[22706,22708,[[[22706,22708,[\"O \"]]]]],[22708,22716,[[[22708,22716,[\"( d∆f ℓ \"]]]]],[22716,22724,[[[22716,22724,[\"1/2ρ1/4 \"]]]]],[22724,22734,[[[22724,22734,[\"ϵ7/4 log( \"]]]]],[22734,22742,[[[22734,22742,[\"ℓ √ d∆f \"]]]]],[22742,22748,[[[22742,22748,[\"δϵ2 ) \"]]]]],[22748,22752,[[[22748,22752,[\") . \"]]]]],[22752,23044,[[[22752,23044,[\"Remark 4. Similar to Algorithm 1, we can also add an termination condition for Algorithm 3: Once the pre-condition of random perturbation step is reached, record the current iterate point xt0 and the current function value f(xt0) before adding the random perturbation. If the decrease of the \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"47e838d2-cc5d-47b0-8075-9ac5c32917bf\"]]],[[[null,0,143],[0]]],[[\"What is a SOSP?\",\"What is the iteration complexity?\",\"What is gradient-free optimization?\"]]]"]]
5950
[["wrb.fr",null,"[[\"The provided complexity $t\\u003d\\\\frac{\\\\delta_f}{\\\\epsilon^{1.75}}\\\\log d$ does not match the total number of iterations complexity stated in Theorem 2 [1].\\n\\nThe iteration complexity in Theorem 2 is $\\\\mathbf{O} ( \\\\frac{\\\\Delta f \\\\ell^{1/2}\\\\rho^{1/4}}{\\\\epsilon^{7/4}} \\\\log( \\\\frac{\\\\ell \\\\sqrt{d}\\\\Delta f}{\\\\delta\\\\epsilon^2} ) )$ [1].\\nThe differences include:\\n*   The given complexity lacks the $\\\\mathbf{\\\\ell^{1/2}}$ and $\\\\mathbf{\\\\rho^{1/4}}$ factors in the numerator that are present in Theorem 2 [1].\\n*   The logarithmic term in Theorem 2 is $\\\\mathbf{\\\\log( \\\\frac{\\\\ell \\\\sqrt{d}\\\\Delta\",null,[\"f01df53a-a97b-437d-88a6-09275aae2350\",\"d5298089-bf9c-46f0-8475-ebf7daa63fa7\",2443083085],null,[[[[0,144,[[[0,144,[\"The provided complexity $t\\u003d\\\\frac{\\\\delta_f}{\\\\epsilon^{1.75}}\\\\log d$ does not match the total number of iterations complexity stated in Theorem 2.\"]]]]],[144,335,[[[144,310,[\"The iteration complexity in Theorem 2 is $\\\\mathbf{O} ( \\\\frac{\\\\Delta f \\\\ell^{1/2}\\\\rho^{1/4}}{\\\\epsilon^{7/4}} \\\\log( \\\\frac{\\\\ell \\\\sqrt{d}\\\\Delta f}{\\\\delta\\\\epsilon^2} ) )$.\"]],[310,311,[\" \"]],[311,335,[\"The differences include:\"]]]]],[335,469,[[[335,469,[\"The given complexity lacks the $\\\\mathbf{\\\\ell^{1/2}}$ and $\\\\mathbf{\\\\rho^{1/4}}$ factors in the numerator that are present in Theorem 2.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]]],[[[\"241fb6da-2d45-4180-8c2c-0d08d6bb9d6c\"],[null,0,143]],[[\"241fb6da-2d45-4180-8c2c-0d08d6bb9d6c\"],[null,144,309]],[[\"241fb6da-2d45-4180-8c2c-0d08d6bb9d6c\"],[null,335,468]]]],null,null,[[[\"241fb6da-2d45-4180-8c2c-0d08d6bb9d6c\"],[null,null,0.7118869760866848,[[null,22253,23044]],[[[22253,22325,[[[22253,22325,[\"f∗ ≤ ∆f , if we set the hyperparameters as in Eq. (7) with δ0 \\u003d δ 384∆f \"]]]]],[22325,22330,[[[22325,22330,[\"√ ϵ3 \"]]]]],[22330,22347,[[[22330,22347,[\"ρ and choose µ \\u003d \"]]]]],[22347,22356,[[[22347,22356,[\"Õ( ϵ 1/2 \"]]]]],[22356,22395,[[[22356,22395,[\"d1/4 ) in Line 3 and 16, µ \\u003d Õ( ϵ 13/8 \"]]]]],[22395,22556,[[[22395,22556,[\"d1/2 ) in Line 11 of Algorithm 3. Then with probability at least 1 − δ, one of the iterates of xt in Algorithm 3 will be an ϵ-approximate SOSP. The total number \"]]]]],[22556,22590,[[[22556,22590,[\"of iterations is no more than O ( \"]]]]],[22590,22603,[[[22590,22603,[\"∆f ℓ 1/2ρ1/4 \"]]]]],[22603,22613,[[[22603,22613,[\"ϵ7/4 log( \"]]]]],[22613,22621,[[[22613,22621,[\"ℓ √ d∆f \"]]]]],[22621,22629,[[[22621,22629,[\"δϵ2 ) ) \"]]]]],[22629,22706,[[[22629,22706,[\"and the total number of function queries (oracle complexity) is no more than \"]]]]],[22706,22708,[[[22706,22708,[\"O \"]]]]],[22708,22716,[[[22708,22716,[\"( d∆f ℓ \"]]]]],[22716,22724,[[[22716,22724,[\"1/2ρ1/4 \"]]]]],[22724,22734,[[[22724,22734,[\"ϵ7/4 log( \"]]]]],[22734,22742,[[[22734,22742,[\"ℓ √ d∆f \"]]]]],[22742,22748,[[[22742,22748,[\"δϵ2 ) \"]]]]],[22748,22752,[[[22748,22752,[\") . \"]]]]],[22752,23044,[[[22752,23044,[\"Remark 4. Similar to Algorithm 1, we can also add an termination condition for Algorithm 3: Once the pre-condition of random perturbation step is reached, record the current iterate point xt0 and the current function value f(xt0) before adding the random perturbation. If the decrease of the \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"241fb6da-2d45-4180-8c2c-0d08d6bb9d6c\"]]]]]],[[null,null,0.7118869760866848,[[null,22253,23044]],[[[22253,22325,[[[22253,22325,[\"f∗ ≤ ∆f , if we set the hyperparameters as in Eq. (7) with δ0 \\u003d δ 384∆f \"]]]]],[22325,22330,[[[22325,22330,[\"√ ϵ3 \"]]]]],[22330,22347,[[[22330,22347,[\"ρ and choose µ \\u003d \"]]]]],[22347,22356,[[[22347,22356,[\"Õ( ϵ 1/2 \"]]]]],[22356,22395,[[[22356,22395,[\"d1/4 ) in Line 3 and 16, µ \\u003d Õ( ϵ 13/8 \"]]]]],[22395,22556,[[[22395,22556,[\"d1/2 ) in Line 11 of Algorithm 3. Then with probability at least 1 − δ, one of the iterates of xt in Algorithm 3 will be an ϵ-approximate SOSP. The total number \"]]]]],[22556,22590,[[[22556,22590,[\"of iterations is no more than O ( \"]]]]],[22590,22603,[[[22590,22603,[\"∆f ℓ 1/2ρ1/4 \"]]]]],[22603,22613,[[[22603,22613,[\"ϵ7/4 log( \"]]]]],[22613,22621,[[[22613,22621,[\"ℓ √ d∆f \"]]]]],[22621,22629,[[[22621,22629,[\"δϵ2 ) ) \"]]]]],[22629,22706,[[[22629,22706,[\"and the total number of function queries (oracle complexity) is no more than \"]]]]],[22706,22708,[[[22706,22708,[\"O \"]]]]],[22708,22716,[[[22708,22716,[\"( d∆f ℓ \"]]]]],[22716,22724,[[[22716,22724,[\"1/2ρ1/4 \"]]]]],[22724,22734,[[[22724,22734,[\"ϵ7/4 log( \"]]]]],[22734,22742,[[[22734,22742,[\"ℓ √ d∆f \"]]]]],[22742,22748,[[[22742,22748,[\"δϵ2 ) \"]]]]],[22748,22752,[[[22748,22752,[\") . \"]]]]],[22752,23044,[[[22752,23044,[\"Remark 4. Similar to Algorithm 1, we can also add an termination condition for Algorithm 3: Once the pre-condition of random perturbation step is reached, record the current iterate point xt0 and the current function value f(xt0) before adding the random perturbation. If the decrease of the \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"241fb6da-2d45-4180-8c2c-0d08d6bb9d6c\"]]],[[[null,0,143],[0]],[[null,144,309],[0]],[[null,335,468],[0]]],[[\"What is a SOSP?\",\"What is the iteration complexity?\",\"What is gradient-free optimization?\"]]]"]]
14878
[["wrb.fr",null,"[[\"The provided complexity $t\\u003d\\\\frac{\\\\delta_f}{\\\\epsilon^{1.75}}\\\\log d$ does not match the total number of iterations complexity stated in Theorem 2 [1].\\n\\nThe iteration complexity in Theorem 2 is $\\\\mathbf{O} ( \\\\frac{\\\\Delta f \\\\ell^{1/2}\\\\rho^{1/4}}{\\\\epsilon^{7/4}} \\\\log( \\\\frac{\\\\ell \\\\sqrt{d}\\\\Delta f}{\\\\delta\\\\epsilon^2} ) )$ [1].\\nThe differences include:\\n*   The given complexity lacks the $\\\\mathbf{\\\\ell^{1/2}}$ and $\\\\mathbf{\\\\rho^{1/4}}$ factors in the numerator that are present in Theorem 2 [1].\\n*   The logarithmic term in Theorem 2 is $\\\\mathbf{\\\\log( \\\\frac{\\\\ell \\\\sqrt{d}\\\\Delta f}{\\\\delta\\\\epsilon^2} )}$, which includes additional parameters such as $\\\\ell$, $\\\\sqrt{d}$, $\\\\Delta f$, $\\\\delta$, and $\\\\epsilon$, making it more complex than the simpler $\\\\mathbf{\\\\log d}$ term in your query [1].\\n*   While $\\\\delta_f$ from your query is notationally different, it refers to the same concept as $\\\\Delta f$ in the theorem [2].\\n*   The exponent for $\\\\epsilon$ is consistent, as $\\\\epsilon^{1.75}$ is equivalent to $\\\\epsilon^{7/4}$ [1, 3].\",null,[\"f01df53a-a97b-437d-88a6-09275aae2350\",\"d5298089-bf9c-46f0-8475-ebf7daa63fa7\",2443083085],null,[[[[0,144,[[[0,144,[\"The provided complexity $t\\u003d\\\\frac{\\\\delta_f}{\\\\epsilon^{1.75}}\\\\log d$ does not match the total number of iterations complexity stated in Theorem 2.\"]]]]],[144,335,[[[144,310,[\"The iteration complexity in Theorem 2 is $\\\\mathbf{O} ( \\\\frac{\\\\Delta f \\\\ell^{1/2}\\\\rho^{1/4}}{\\\\epsilon^{7/4}} \\\\log( \\\\frac{\\\\ell \\\\sqrt{d}\\\\Delta f}{\\\\delta\\\\epsilon^2} ) )$.\"]],[310,311,[\" \"]],[311,335,[\"The differences include:\"]]]]],[335,469,[[[335,469,[\"The given complexity lacks the $\\\\mathbf{\\\\ell^{1/2}}$ and $\\\\mathbf{\\\\rho^{1/4}}$ factors in the numerator that are present in Theorem 2.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[469,753,[[[469,753,[\"The logarithmic term in Theorem 2 is $\\\\mathbf{\\\\log( \\\\frac{\\\\ell \\\\sqrt{d}\\\\Delta f}{\\\\delta\\\\epsilon^2} )}$, which includes additional parameters such as $\\\\ell$, $\\\\sqrt{d}$, $\\\\Delta f$, $\\\\delta$, and $\\\\epsilon$, making it more complex than the simpler $\\\\mathbf{\\\\log d}$ term in your query.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[753,872,[[[753,872,[\"While $\\\\delta_f$ from your query is notationally different, it refers to the same concept as $\\\\Delta f$ in the theorem.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]]],[[[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"],[null,0,143]],[[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"],[null,144,309]],[[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"],[null,335,468]],[[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"],[null,469,752]],[[\"e12abde9-efd1-4850-8478-e2fbb6751954\"],[null,753,871]],[[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"],[null,872,969]],[[\"b2c480d0-52bc-4deb-92dc-2b84a1c42751\"],[null,872,969]]]],null,null,[[[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"],[null,null,0.7118869760866848,[[null,22253,23044]],[[[22253,22325,[[[22253,22325,[\"f∗ ≤ ∆f , if we set the hyperparameters as in Eq. (7) with δ0 \\u003d δ 384∆f \"]]]]],[22325,22330,[[[22325,22330,[\"√ ϵ3 \"]]]]],[22330,22347,[[[22330,22347,[\"ρ and choose µ \\u003d \"]]]]],[22347,22356,[[[22347,22356,[\"Õ( ϵ 1/2 \"]]]]],[22356,22395,[[[22356,22395,[\"d1/4 ) in Line 3 and 16, µ \\u003d Õ( ϵ 13/8 \"]]]]],[22395,22556,[[[22395,22556,[\"d1/2 ) in Line 11 of Algorithm 3. Then with probability at least 1 − δ, one of the iterates of xt in Algorithm 3 will be an ϵ-approximate SOSP. The total number \"]]]]],[22556,22590,[[[22556,22590,[\"of iterations is no more than O ( \"]]]]],[22590,22603,[[[22590,22603,[\"∆f ℓ 1/2ρ1/4 \"]]]]],[22603,22613,[[[22603,22613,[\"ϵ7/4 log( \"]]]]],[22613,22621,[[[22613,22621,[\"ℓ √ d∆f \"]]]]],[22621,22629,[[[22621,22629,[\"δϵ2 ) ) \"]]]]],[22629,22706,[[[22629,22706,[\"and the total number of function queries (oracle complexity) is no more than \"]]]]],[22706,22708,[[[22706,22708,[\"O \"]]]]],[22708,22716,[[[22708,22716,[\"( d∆f ℓ \"]]]]],[22716,22724,[[[22716,22724,[\"1/2ρ1/4 \"]]]]],[22724,22734,[[[22724,22734,[\"ϵ7/4 log( \"]]]]],[22734,22742,[[[22734,22742,[\"ℓ √ d∆f \"]]]]],[22742,22748,[[[22742,22748,[\"δϵ2 ) \"]]]]],[22748,22752,[[[22748,22752,[\") . \"]]]]],[22752,23044,[[[22752,23044,[\"Remark 4. Similar to Algorithm 1, we can also add an termination condition for Algorithm 3: Once the pre-condition of random perturbation step is reached, record the current iterate point xt0 and the current function value f(xt0) before adding the random perturbation. If the decrease of the \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"]]],[[\"e12abde9-efd1-4850-8478-e2fbb6751954\"],[null,null,0.6849275271430355,[[null,13655,14161]],[[[13655,13661,[[[13655,13661,[\"η \\u003d 1 \"]]]]],[13661,13670,[[[13661,13670,[\"4ℓ , θ \\u003d \"]]]]],[13670,13672,[[[13670,13672,[\"1 \"]]]]],[13672,13684,[[[13672,13684,[\"4 √ κ , γ \\u003d \"]]]]],[13684,13687,[[[13684,13687,[\"θ2 \"]]]]],[13687,13691,[[[13687,13691,[\"η , \"]]]]],[13691,13697,[[[13691,13697,[\"s \\u003d γ \"]]]]],[13697,13706,[[[13697,13706,[\"4ρ , T \\u003d \"]]]]],[13706,13731,[[[13706,13731,[\"√ κχc, r \\u003d ηϵχ−5c−8, (5) \"]]]]],[13731,13775,[[[13731,13775,[\"where c is constant and χ \\u003d max{1, log dℓ∆f \"]]]]],[13775,13809,[[[13775,13809,[\"ρϵδ } with ∆f :\\u003d f(x0)− f(x∗) \\u003c∞. \"]]]]],[13809,13997,[[[13809,13997,[\"Since we only have access to the zeroth-order information, we can verify if a point x is an ϵ-approximate FOSP by using the coordinate-wise gradient estimator based on the following fact: \"]]]]],[13997,13997,[]],[13997,13998,[[[13997,13998,[\" \"]]]]],[13998,14062,[[[13998,14062,[\"Algorithm 1 Zeroth-Order Perturbed Accelerated Gradient Descent \"]]]]],[14062,14132,[[[14062,14132,[\"1: v0 ← 0, tperturb ← 0 2: for t \\u003d 0, 1, . . . do 3: if ∥∇̂f(xt)∥ ≤ 3 \"]]]]],[14132,14161,[[[14132,14161,[\"4ϵ and t − tperturb \\u003e T then \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"e12abde9-efd1-4850-8478-e2fbb6751954\"]]],[[\"b2c480d0-52bc-4deb-92dc-2b84a1c42751\"],[null,null,0.6474951167009989,[[null,0,1471]],[[[0,1,[[[0,1,[\" \"]]]]],[1,57,[[[1,57,[\"FASTER GRADIENT-FREE METHODS FOR ESCAPING SADDLE POINTS \"]]],[null,6]]],[57,84,[[[57,84,[\"Hualin Zhang 1, Bin Gu 1,2 \"]]]]],[84,183,[[[84,183,[\"1Nanjing University of Information Science \\u0026 Technology 2MBZUAI {zhanghualin98, jsgubin}@gmail.com \"]]]]],[183,192,[[[183,192,[\"ABSTRACT \"]]]]],[192,1471,[[[192,1471,[\"Escaping from saddle points has become an important research topic in non-convex optimization. In this paper, we study the case when calculations of explicit gradients are expensive or even infeasible, and only function values are accessi-ble. Currently, there have two types of gradient-free (zeroth-order) methods based on random perturbation and negative curvature finding proposed to escape sad-dle points efficiently and converge to an ϵ-approximate second-order stationary point. Nesterov’s accelerated gradient descent (AGD) method can escape saddle points faster than gradient descent (GD) which have been verified in first-order algorithms. However, whether AGD could accelerate the gradient-free methods is still unstudied. To unfold this mystery, in this paper, we propose two accelerated variants for the two types of gradient-free methods of escaping saddle points. We show that our algorithms can find an ϵ-approximate second-order stationary point with Õ(1/ϵ1.75) iteration complexity and Õ(d/ϵ1.75) oracle complexity, where d is the problem dimension. Thus, our methods achieve a comparable convergence rate to their first-order counterparts and have smaller oracle complexity compared to prior derivative-free methods for finding second-order stationary points. \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"b2c480d0-52bc-4deb-92dc-2b84a1c42751\"]]]]]],[[null,null,0.7118869760866848,[[null,22253,23044]],[[[22253,22325,[[[22253,22325,[\"f∗ ≤ ∆f , if we set the hyperparameters as in Eq. (7) with δ0 \\u003d δ 384∆f \"]]]]],[22325,22330,[[[22325,22330,[\"√ ϵ3 \"]]]]],[22330,22347,[[[22330,22347,[\"ρ and choose µ \\u003d \"]]]]],[22347,22356,[[[22347,22356,[\"Õ( ϵ 1/2 \"]]]]],[22356,22395,[[[22356,22395,[\"d1/4 ) in Line 3 and 16, µ \\u003d Õ( ϵ 13/8 \"]]]]],[22395,22556,[[[22395,22556,[\"d1/2 ) in Line 11 of Algorithm 3. Then with probability at least 1 − δ, one of the iterates of xt in Algorithm 3 will be an ϵ-approximate SOSP. The total number \"]]]]],[22556,22590,[[[22556,22590,[\"of iterations is no more than O ( \"]]]]],[22590,22603,[[[22590,22603,[\"∆f ℓ 1/2ρ1/4 \"]]]]],[22603,22613,[[[22603,22613,[\"ϵ7/4 log( \"]]]]],[22613,22621,[[[22613,22621,[\"ℓ √ d∆f \"]]]]],[22621,22629,[[[22621,22629,[\"δϵ2 ) ) \"]]]]],[22629,22706,[[[22629,22706,[\"and the total number of function queries (oracle complexity) is no more than \"]]]]],[22706,22708,[[[22706,22708,[\"O \"]]]]],[22708,22716,[[[22708,22716,[\"( d∆f ℓ \"]]]]],[22716,22724,[[[22716,22724,[\"1/2ρ1/4 \"]]]]],[22724,22734,[[[22724,22734,[\"ϵ7/4 log( \"]]]]],[22734,22742,[[[22734,22742,[\"ℓ √ d∆f \"]]]]],[22742,22748,[[[22742,22748,[\"δϵ2 ) \"]]]]],[22748,22752,[[[22748,22752,[\") . \"]]]]],[22752,23044,[[[22752,23044,[\"Remark 4. Similar to Algorithm 1, we can also add an termination condition for Algorithm 3: Once the pre-condition of random perturbation step is reached, record the current iterate point xt0 and the current function value f(xt0) before adding the random perturbation. If the decrease of the \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"]],[null,null,0.6849275271430355,[[null,13655,14161]],[[[13655,13661,[[[13655,13661,[\"η \\u003d 1 \"]]]]],[13661,13670,[[[13661,13670,[\"4ℓ , θ \\u003d \"]]]]],[13670,13672,[[[13670,13672,[\"1 \"]]]]],[13672,13684,[[[13672,13684,[\"4 √ κ , γ \\u003d \"]]]]],[13684,13687,[[[13684,13687,[\"θ2 \"]]]]],[13687,13691,[[[13687,13691,[\"η , \"]]]]],[13691,13697,[[[13691,13697,[\"s \\u003d γ \"]]]]],[13697,13706,[[[13697,13706,[\"4ρ , T \\u003d \"]]]]],[13706,13731,[[[13706,13731,[\"√ κχc, r \\u003d ηϵχ−5c−8, (5) \"]]]]],[13731,13775,[[[13731,13775,[\"where c is constant and χ \\u003d max{1, log dℓ∆f \"]]]]],[13775,13809,[[[13775,13809,[\"ρϵδ } with ∆f :\\u003d f(x0)− f(x∗) \\u003c∞. \"]]]]],[13809,13997,[[[13809,13997,[\"Since we only have access to the zeroth-order information, we can verify if a point x is an ϵ-approximate FOSP by using the coordinate-wise gradient estimator based on the following fact: \"]]]]],[13997,13997,[]],[13997,13998,[[[13997,13998,[\" \"]]]]],[13998,14062,[[[13998,14062,[\"Algorithm 1 Zeroth-Order Perturbed Accelerated Gradient Descent \"]]]]],[14062,14132,[[[14062,14132,[\"1: v0 ← 0, tperturb ← 0 2: for t \\u003d 0, 1, . . . do 3: if ∥∇̂f(xt)∥ ≤ 3 \"]]]]],[14132,14161,[[[14132,14161,[\"4ϵ and t − tperturb \\u003e T then \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"e12abde9-efd1-4850-8478-e2fbb6751954\"]],[null,null,0.6474951167009989,[[null,0,1471]],[[[0,1,[[[0,1,[\" \"]]]]],[1,57,[[[1,57,[\"FASTER GRADIENT-FREE METHODS FOR ESCAPING SADDLE POINTS \"]]],[null,6]]],[57,84,[[[57,84,[\"Hualin Zhang 1, Bin Gu 1,2 \"]]]]],[84,183,[[[84,183,[\"1Nanjing University of Information Science \\u0026 Technology 2MBZUAI {zhanghualin98, jsgubin}@gmail.com \"]]]]],[183,192,[[[183,192,[\"ABSTRACT \"]]]]],[192,1471,[[[192,1471,[\"Escaping from saddle points has become an important research topic in non-convex optimization. In this paper, we study the case when calculations of explicit gradients are expensive or even infeasible, and only function values are accessi-ble. Currently, there have two types of gradient-free (zeroth-order) methods based on random perturbation and negative curvature finding proposed to escape sad-dle points efficiently and converge to an ϵ-approximate second-order stationary point. Nesterov’s accelerated gradient descent (AGD) method can escape saddle points faster than gradient descent (GD) which have been verified in first-order algorithms. However, whether AGD could accelerate the gradient-free methods is still unstudied. To unfold this mystery, in this paper, we propose two accelerated variants for the two types of gradient-free methods of escaping saddle points. We show that our algorithms can find an ϵ-approximate second-order stationary point with Õ(1/ϵ1.75) iteration complexity and Õ(d/ϵ1.75) oracle complexity, where d is the problem dimension. Thus, our methods achieve a comparable convergence rate to their first-order counterparts and have smaller oracle complexity compared to prior derivative-free methods for finding second-order stationary points. \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"b2c480d0-52bc-4deb-92dc-2b84a1c42751\"]]],[[[null,0,143],[0]],[[null,144,309],[0]],[[null,335,468],[0]],[[null,469,752],[0]],[[null,753,871],[1]],[[null,872,969],[0,2]]],[[\"What is a SOSP?\",\"What is the iteration complexity?\",\"What is gradient-free optimization?\"]]]"]]
15541
[["wrb.fr",null,"[[\"The provided complexity $t\\u003d\\\\frac{\\\\delta_f}{\\\\epsilon^{1.75}}\\\\log d$ does not match the total number of iterations complexity stated in Theorem 2 [1].\\n\\nThe iteration complexity in Theorem 2 is $\\\\mathbf{O} ( \\\\frac{\\\\Delta f \\\\ell^{1/2}\\\\rho^{1/4}}{\\\\epsilon^{7/4}} \\\\log( \\\\frac{\\\\ell \\\\sqrt{d}\\\\Delta f}{\\\\delta\\\\epsilon^2} ) )$ [1].\\nThe differences include:\\n*   The given complexity lacks the $\\\\mathbf{\\\\ell^{1/2}}$ and $\\\\mathbf{\\\\rho^{1/4}}$ factors in the numerator that are present in Theorem 2 [1].\\n*   The logarithmic term in Theorem 2 is $\\\\mathbf{\\\\log( \\\\frac{\\\\ell \\\\sqrt{d}\\\\Delta f}{\\\\delta\\\\epsilon^2} )}$, which includes additional parameters such as $\\\\ell$, $\\\\sqrt{d}$, $\\\\Delta f$, $\\\\delta$, and $\\\\epsilon$, making it more complex than the simpler $\\\\mathbf{\\\\log d}$ term in your query [1].\\n*   While $\\\\delta_f$ from your query is notationally different, it refers to the same concept as $\\\\Delta f$ in the theorem [2].\\n*   The exponent for $\\\\epsilon$ is consistent, as $\\\\epsilon^{1.75}$ is equivalent to $\\\\epsilon^{7/4}$ [1, 3].\",null,[\"f01df53a-a97b-437d-88a6-09275aae2350\",\"d5298089-bf9c-46f0-8475-ebf7daa63fa7\",2443083085],null,[[[[0,144,[[[0,144,[\"The provided complexity $t\\u003d\\\\frac{\\\\delta_f}{\\\\epsilon^{1.75}}\\\\log d$ does not match the total number of iterations complexity stated in Theorem 2.\"]]]]],[144,335,[[[144,310,[\"The iteration complexity in Theorem 2 is $\\\\mathbf{O} ( \\\\frac{\\\\Delta f \\\\ell^{1/2}\\\\rho^{1/4}}{\\\\epsilon^{7/4}} \\\\log( \\\\frac{\\\\ell \\\\sqrt{d}\\\\Delta f}{\\\\delta\\\\epsilon^2} ) )$.\"]],[310,311,[\" \"]],[311,335,[\"The differences include:\"]]]]],[335,469,[[[335,469,[\"The given complexity lacks the $\\\\mathbf{\\\\ell^{1/2}}$ and $\\\\mathbf{\\\\rho^{1/4}}$ factors in the numerator that are present in Theorem 2.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[469,753,[[[469,753,[\"The logarithmic term in Theorem 2 is $\\\\mathbf{\\\\log( \\\\frac{\\\\ell \\\\sqrt{d}\\\\Delta f}{\\\\delta\\\\epsilon^2} )}$, which includes additional parameters such as $\\\\ell$, $\\\\sqrt{d}$, $\\\\Delta f$, $\\\\delta$, and $\\\\epsilon$, making it more complex than the simpler $\\\\mathbf{\\\\log d}$ term in your query.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[753,872,[[[753,872,[\"While $\\\\delta_f$ from your query is notationally different, it refers to the same concept as $\\\\Delta f$ in the theorem.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]],[872,970,[[[872,970,[\"The exponent for $\\\\epsilon$ is consistent, as $\\\\epsilon^{1.75}$ is equivalent to $\\\\epsilon^{7/4}$.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,3]]]],[[[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"],[null,0,143]],[[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"],[null,144,309]],[[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"],[null,335,468]],[[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"],[null,469,752]],[[\"e12abde9-efd1-4850-8478-e2fbb6751954\"],[null,753,871]],[[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"],[null,872,969]],[[\"b2c480d0-52bc-4deb-92dc-2b84a1c42751\"],[null,872,969]]]],null,null,[[[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"],[null,null,0.7118869760866848,[[null,22253,23044]],[[[22253,22325,[[[22253,22325,[\"f∗ ≤ ∆f , if we set the hyperparameters as in Eq. (7) with δ0 \\u003d δ 384∆f \"]]]]],[22325,22330,[[[22325,22330,[\"√ ϵ3 \"]]]]],[22330,22347,[[[22330,22347,[\"ρ and choose µ \\u003d \"]]]]],[22347,22356,[[[22347,22356,[\"Õ( ϵ 1/2 \"]]]]],[22356,22395,[[[22356,22395,[\"d1/4 ) in Line 3 and 16, µ \\u003d Õ( ϵ 13/8 \"]]]]],[22395,22556,[[[22395,22556,[\"d1/2 ) in Line 11 of Algorithm 3. Then with probability at least 1 − δ, one of the iterates of xt in Algorithm 3 will be an ϵ-approximate SOSP. The total number \"]]]]],[22556,22590,[[[22556,22590,[\"of iterations is no more than O ( \"]]]]],[22590,22603,[[[22590,22603,[\"∆f ℓ 1/2ρ1/4 \"]]]]],[22603,22613,[[[22603,22613,[\"ϵ7/4 log( \"]]]]],[22613,22621,[[[22613,22621,[\"ℓ √ d∆f \"]]]]],[22621,22629,[[[22621,22629,[\"δϵ2 ) ) \"]]]]],[22629,22706,[[[22629,22706,[\"and the total number of function queries (oracle complexity) is no more than \"]]]]],[22706,22708,[[[22706,22708,[\"O \"]]]]],[22708,22716,[[[22708,22716,[\"( d∆f ℓ \"]]]]],[22716,22724,[[[22716,22724,[\"1/2ρ1/4 \"]]]]],[22724,22734,[[[22724,22734,[\"ϵ7/4 log( \"]]]]],[22734,22742,[[[22734,22742,[\"ℓ √ d∆f \"]]]]],[22742,22748,[[[22742,22748,[\"δϵ2 ) \"]]]]],[22748,22752,[[[22748,22752,[\") . \"]]]]],[22752,23044,[[[22752,23044,[\"Remark 4. Similar to Algorithm 1, we can also add an termination condition for Algorithm 3: Once the pre-condition of random perturbation step is reached, record the current iterate point xt0 and the current function value f(xt0) before adding the random perturbation. If the decrease of the \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"]]],[[\"e12abde9-efd1-4850-8478-e2fbb6751954\"],[null,null,0.6849275271430355,[[null,13655,14161]],[[[13655,13661,[[[13655,13661,[\"η \\u003d 1 \"]]]]],[13661,13670,[[[13661,13670,[\"4ℓ , θ \\u003d \"]]]]],[13670,13672,[[[13670,13672,[\"1 \"]]]]],[13672,13684,[[[13672,13684,[\"4 √ κ , γ \\u003d \"]]]]],[13684,13687,[[[13684,13687,[\"θ2 \"]]]]],[13687,13691,[[[13687,13691,[\"η , \"]]]]],[13691,13697,[[[13691,13697,[\"s \\u003d γ \"]]]]],[13697,13706,[[[13697,13706,[\"4ρ , T \\u003d \"]]]]],[13706,13731,[[[13706,13731,[\"√ κχc, r \\u003d ηϵχ−5c−8, (5) \"]]]]],[13731,13775,[[[13731,13775,[\"where c is constant and χ \\u003d max{1, log dℓ∆f \"]]]]],[13775,13809,[[[13775,13809,[\"ρϵδ } with ∆f :\\u003d f(x0)− f(x∗) \\u003c∞. \"]]]]],[13809,13997,[[[13809,13997,[\"Since we only have access to the zeroth-order information, we can verify if a point x is an ϵ-approximate FOSP by using the coordinate-wise gradient estimator based on the following fact: \"]]]]],[13997,13997,[]],[13997,13998,[[[13997,13998,[\" \"]]]]],[13998,14062,[[[13998,14062,[\"Algorithm 1 Zeroth-Order Perturbed Accelerated Gradient Descent \"]]]]],[14062,14132,[[[14062,14132,[\"1: v0 ← 0, tperturb ← 0 2: for t \\u003d 0, 1, . . . do 3: if ∥∇̂f(xt)∥ ≤ 3 \"]]]]],[14132,14161,[[[14132,14161,[\"4ϵ and t − tperturb \\u003e T then \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"e12abde9-efd1-4850-8478-e2fbb6751954\"]]],[[\"b2c480d0-52bc-4deb-92dc-2b84a1c42751\"],[null,null,0.6474951167009989,[[null,0,1471]],[[[0,1,[[[0,1,[\" \"]]]]],[1,57,[[[1,57,[\"FASTER GRADIENT-FREE METHODS FOR ESCAPING SADDLE POINTS \"]]],[null,6]]],[57,84,[[[57,84,[\"Hualin Zhang 1, Bin Gu 1,2 \"]]]]],[84,183,[[[84,183,[\"1Nanjing University of Information Science \\u0026 Technology 2MBZUAI {zhanghualin98, jsgubin}@gmail.com \"]]]]],[183,192,[[[183,192,[\"ABSTRACT \"]]]]],[192,1471,[[[192,1471,[\"Escaping from saddle points has become an important research topic in non-convex optimization. In this paper, we study the case when calculations of explicit gradients are expensive or even infeasible, and only function values are accessi-ble. Currently, there have two types of gradient-free (zeroth-order) methods based on random perturbation and negative curvature finding proposed to escape sad-dle points efficiently and converge to an ϵ-approximate second-order stationary point. Nesterov’s accelerated gradient descent (AGD) method can escape saddle points faster than gradient descent (GD) which have been verified in first-order algorithms. However, whether AGD could accelerate the gradient-free methods is still unstudied. To unfold this mystery, in this paper, we propose two accelerated variants for the two types of gradient-free methods of escaping saddle points. We show that our algorithms can find an ϵ-approximate second-order stationary point with Õ(1/ϵ1.75) iteration complexity and Õ(d/ϵ1.75) oracle complexity, where d is the problem dimension. Thus, our methods achieve a comparable convergence rate to their first-order counterparts and have smaller oracle complexity compared to prior derivative-free methods for finding second-order stationary points. \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"b2c480d0-52bc-4deb-92dc-2b84a1c42751\"]]]]]],[[null,null,0.7118869760866848,[[null,22253,23044]],[[[22253,22325,[[[22253,22325,[\"f∗ ≤ ∆f , if we set the hyperparameters as in Eq. (7) with δ0 \\u003d δ 384∆f \"]]]]],[22325,22330,[[[22325,22330,[\"√ ϵ3 \"]]]]],[22330,22347,[[[22330,22347,[\"ρ and choose µ \\u003d \"]]]]],[22347,22356,[[[22347,22356,[\"Õ( ϵ 1/2 \"]]]]],[22356,22395,[[[22356,22395,[\"d1/4 ) in Line 3 and 16, µ \\u003d Õ( ϵ 13/8 \"]]]]],[22395,22556,[[[22395,22556,[\"d1/2 ) in Line 11 of Algorithm 3. Then with probability at least 1 − δ, one of the iterates of xt in Algorithm 3 will be an ϵ-approximate SOSP. The total number \"]]]]],[22556,22590,[[[22556,22590,[\"of iterations is no more than O ( \"]]]]],[22590,22603,[[[22590,22603,[\"∆f ℓ 1/2ρ1/4 \"]]]]],[22603,22613,[[[22603,22613,[\"ϵ7/4 log( \"]]]]],[22613,22621,[[[22613,22621,[\"ℓ √ d∆f \"]]]]],[22621,22629,[[[22621,22629,[\"δϵ2 ) ) \"]]]]],[22629,22706,[[[22629,22706,[\"and the total number of function queries (oracle complexity) is no more than \"]]]]],[22706,22708,[[[22706,22708,[\"O \"]]]]],[22708,22716,[[[22708,22716,[\"( d∆f ℓ \"]]]]],[22716,22724,[[[22716,22724,[\"1/2ρ1/4 \"]]]]],[22724,22734,[[[22724,22734,[\"ϵ7/4 log( \"]]]]],[22734,22742,[[[22734,22742,[\"ℓ √ d∆f \"]]]]],[22742,22748,[[[22742,22748,[\"δϵ2 ) \"]]]]],[22748,22752,[[[22748,22752,[\") . \"]]]]],[22752,23044,[[[22752,23044,[\"Remark 4. Similar to Algorithm 1, we can also add an termination condition for Algorithm 3: Once the pre-condition of random perturbation step is reached, record the current iterate point xt0 and the current function value f(xt0) before adding the random perturbation. If the decrease of the \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"15073cd4-ea31-4b07-a5cd-96f0fd01e510\"]],[null,null,0.6849275271430355,[[null,13655,14161]],[[[13655,13661,[[[13655,13661,[\"η \\u003d 1 \"]]]]],[13661,13670,[[[13661,13670,[\"4ℓ , θ \\u003d \"]]]]],[13670,13672,[[[13670,13672,[\"1 \"]]]]],[13672,13684,[[[13672,13684,[\"4 √ κ , γ \\u003d \"]]]]],[13684,13687,[[[13684,13687,[\"θ2 \"]]]]],[13687,13691,[[[13687,13691,[\"η , \"]]]]],[13691,13697,[[[13691,13697,[\"s \\u003d γ \"]]]]],[13697,13706,[[[13697,13706,[\"4ρ , T \\u003d \"]]]]],[13706,13731,[[[13706,13731,[\"√ κχc, r \\u003d ηϵχ−5c−8, (5) \"]]]]],[13731,13775,[[[13731,13775,[\"where c is constant and χ \\u003d max{1, log dℓ∆f \"]]]]],[13775,13809,[[[13775,13809,[\"ρϵδ } with ∆f :\\u003d f(x0)− f(x∗) \\u003c∞. \"]]]]],[13809,13997,[[[13809,13997,[\"Since we only have access to the zeroth-order information, we can verify if a point x is an ϵ-approximate FOSP by using the coordinate-wise gradient estimator based on the following fact: \"]]]]],[13997,13997,[]],[13997,13998,[[[13997,13998,[\" \"]]]]],[13998,14062,[[[13998,14062,[\"Algorithm 1 Zeroth-Order Perturbed Accelerated Gradient Descent \"]]]]],[14062,14132,[[[14062,14132,[\"1: v0 ← 0, tperturb ← 0 2: for t \\u003d 0, 1, . . . do 3: if ∥∇̂f(xt)∥ ≤ 3 \"]]]]],[14132,14161,[[[14132,14161,[\"4ϵ and t − tperturb \\u003e T then \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"e12abde9-efd1-4850-8478-e2fbb6751954\"]],[null,null,0.6474951167009989,[[null,0,1471]],[[[0,1,[[[0,1,[\" \"]]]]],[1,57,[[[1,57,[\"FASTER GRADIENT-FREE METHODS FOR ESCAPING SADDLE POINTS \"]]],[null,6]]],[57,84,[[[57,84,[\"Hualin Zhang 1, Bin Gu 1,2 \"]]]]],[84,183,[[[84,183,[\"1Nanjing University of Information Science \\u0026 Technology 2MBZUAI {zhanghualin98, jsgubin}@gmail.com \"]]]]],[183,192,[[[183,192,[\"ABSTRACT \"]]]]],[192,1471,[[[192,1471,[\"Escaping from saddle points has become an important research topic in non-convex optimization. In this paper, we study the case when calculations of explicit gradients are expensive or even infeasible, and only function values are accessi-ble. Currently, there have two types of gradient-free (zeroth-order) methods based on random perturbation and negative curvature finding proposed to escape sad-dle points efficiently and converge to an ϵ-approximate second-order stationary point. Nesterov’s accelerated gradient descent (AGD) method can escape saddle points faster than gradient descent (GD) which have been verified in first-order algorithms. However, whether AGD could accelerate the gradient-free methods is still unstudied. To unfold this mystery, in this paper, we propose two accelerated variants for the two types of gradient-free methods of escaping saddle points. We show that our algorithms can find an ϵ-approximate second-order stationary point with Õ(1/ϵ1.75) iteration complexity and Õ(d/ϵ1.75) oracle complexity, where d is the problem dimension. Thus, our methods achieve a comparable convergence rate to their first-order counterparts and have smaller oracle complexity compared to prior derivative-free methods for finding second-order stationary points. \"]]]]]]],[[[\"0004ad35-4736-4bde-9e0b-39a3b3d3b469\"],\"6dd236d5-81e3-4389-be57-9ef10febaeaa\"]],[\"b2c480d0-52bc-4deb-92dc-2b84a1c42751\"]]],[[[null,0,143],[0]],[[null,144,309],[0]],[[null,335,468],[0]],[[null,469,752],[0]],[[null,753,871],[1]],[[null,872,969],[0,2]]],[[\"What is a SOSP?\",\"What is the iteration complexity?\",\"What is gradient-free optimization?\"]]]"]]
59
[["di",8176],["af.httprm",8175,"3713397028753214264",50]]
27
[["e",7,null,null,41507]]
