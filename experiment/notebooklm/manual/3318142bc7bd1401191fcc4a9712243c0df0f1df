)]}'

315
[["wrb.fr",null,"[[\"When \\\"high inter-class view similarity\",null,[\"baa41d25-0a90-4b04-8ca7-9ca7ccb67745\",\"e664f2d7-3c4d-4f9b-b34b-2735b7937b64\",3067737007],null,[[]]],null,null,[[\"What is MVP-N?\",\"What are the limitations of existing datasets?\",\"What is human-perceived information quantity?\"]]]"]]
11011
[["wrb.fr",null,"[[\"When \\\"high inter-class view similarity causes uncertainty of class labels for uninformative views, which generates multi-view label noise,\\\" it means the following in the context of the MVP-N dataset:\\n\\n*   **High inter-class view similarity** refers to the characteristic of the MVP-N dataset where different retail products, especially those from the same brand, can look very similar or even identical from specific viewpoints [1, 2]. This is inherent to the fine-grained categorization of the objects [3].\\n*   **Uninformative views** are views of an object where its distinctive features are not sufficiently included, making it difficult for humans to classify the object correctly\",null,[\"baa41d25-0a90-4b04-8ca7-9ca7ccb67745\",\"e664f2d7-3c4d-4f9b-b34b-2735b7937b64\",3067737007],null,[[[[0,199,[[[0,199,[\"When \\\"high inter-class view similarity causes uncertainty of class labels for uninformative views, which generates multi-view label noise,\\\" it means the following in the context of the MVP-N dataset:\"]]]]],[199,486,[[[199,231,[\"High inter-class view similarity\",[true]]],[231,486,[\" refers to the characteristic of the MVP-N dataset where different retail products, especially those from the same brand, can look very similar or even identical from specific viewpoints. This is inherent to the fine-grained categorization of the objects.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]]],[[[\"3f9d9ec6-ddd1-47ed-93e6-e760c731716f\"],[null,232,417]],[[\"fe73f9d2-7167-48dc-9652-1ee2a1a0c1c1\"],[null,232,417]],[[\"1e198efe-bb5f-4c7b-97c6-b38cf64431b1\"],[null,417,485]]]],null,null,[[[\"3f9d9ec6-ddd1-47ed-93e6-e760c731716f\"],[null,null,0.7680594985161724,[[null,8136,9082]],[[[8136,9082,[[[8136,9082,[\"To resolve the above limitations, this study proposes MVP-N, a new dataset containing 9k multi-view sets constructed from 16k real captured views of 44 real-world fine-grained retail products. In MVP-N, different objects can appear similar or identical in specific views, referred to as high inter-class view similarity. In this case, humans cannot classify an object accurately from these views for fine-grained (instance-level) object categorization, making the classification task challenging. Here, views with human uncertainty of class labels are denoted as uninformative views. The inconsistency between the one-hot manner of class labels and human judgment causes multi-view label noise. Soft label methods [51, 52, 53, 54, 55, 56, 57, 58, 59, 60] can help alleviate the inconsistency and render view-level predictions more consistent with human judgments, allowing the study of learning from noisy labels [61, 62] in the multi-view case. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"3f9d9ec6-ddd1-47ed-93e6-e760c731716f\"]]],[[\"fe73f9d2-7167-48dc-9652-1ee2a1a0c1c1\"],[null,null,0.6968088315614678,[[null,14752,15302]],[[[14752,14793,[[[14752,14793,[\"3 MVP-N: Dataset Design and Construction \"]]]]],[14793,15096,[[[14793,15096,[\"Object selection. Retail products can be distinguished without semantic confusion3. Therefore, a fine-grained categorization [73] can be easily established. Furthermore, retail products of the same brand with different flavors provide high inter-class view similarity. In total, 44 retail products from \"]]]]],[15096,15191,[[[15096,15191,[\"3Ma et al. [11] and Chen et al. [8] point out the semantic confusion issue in ModelNet40 [43]. \"]]]]],[15191,15192,[[[15191,15192,[\"4\"]]]]],[15192,15247,[[[15192,15247,[\"!!!! !!!\\\" !!!# !!!$ !!!% !!!\\u0026 !!!' !!!( !!!) !!!* !!\\\"! \"]]],[null,6]]],[15247,15302,[[[15247,15302,[\"!!\\\"\\\" !!\\\"# !!\\\"$ !!\\\"% !!\\\"\\u0026 !!\\\"' !!\\\"( !!\\\") !!\\\"* !!#! !!#\\\" \"]]],[null,6]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"fe73f9d2-7167-48dc-9652-1ee2a1a0c1c1\"]]],[[\"1e198efe-bb5f-4c7b-97c6-b38cf64431b1\"],[null,null,0.7098116407670348,[[null,0,1524]],[[[0,79,[[[0,79,[\"MVP-N: A Dataset and Benchmark for Real-World Multi-View Object Classification \"]]],[null,6]]],[79,114,[[[79,114,[\"Ren Wang Seoul National University \"]]]]],[114,137,[[[114,137,[\"<EMAIL> \"]]]]],[137,175,[[[137,175,[\"Jiayue Wang Seoul National University \"]]]]],[175,201,[[[175,201,[\"<EMAIL> \"]]]]],[201,234,[[[201,234,[\"Tae Sung Kim Sun Moon University \"]]]]],[234,255,[[[234,255,[\"<EMAIL> \"]]]]],[255,269,[[[255,269,[\"Jin-Sung Kim∗ \"]]]]],[269,312,[[[269,312,[\"Sun <NAME_EMAIL> \"]]]]],[312,373,[[[312,373,[\"Hyuk-Jae Lee∗ Seoul <NAME_EMAIL> \"]]]]],[373,382,[[[373,382,[\"Abstract \"]]]]],[382,1524,[[[382,1524,[\"Combining information from multiple views is essential for discriminating similar objects. However, existing datasets for multi-view object classification have several limitations, such as synthetic and coarse-grained objects, no validation split for hyperparameter tuning, and a lack of view-level information quantity annotations for analyzing multi-view-based methods. To address this issue, this study proposes a new dataset, MVP-N2, which contains 44 retail products, 16k real captured views with human-perceived information quantity annotations, and 9k multi-view sets. The fine-grained categorization of objects naturally generates multi-view label noise owing to the inter-class view similarity, allowing the study of learning from noisy la-bels in the multi-view case. Moreover, this study benchmarks four multi-view-based feature aggregation methods and twelve soft label methods on MVP-N. Experi-mental results show that MVP-N will be a valuable resource for facilitating the de-velopment of real-world multi-view object classification methods. The dataset and code are publicly available at https://github.com/SMNUResearch/MVP-N. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"1e198efe-bb5f-4c7b-97c6-b38cf64431b1\"]]]]]],[[null,null,0.7680594985161724,[[null,8136,9082]],[[[8136,9082,[[[8136,9082,[\"To resolve the above limitations, this study proposes MVP-N, a new dataset containing 9k multi-view sets constructed from 16k real captured views of 44 real-world fine-grained retail products. In MVP-N, different objects can appear similar or identical in specific views, referred to as high inter-class view similarity. In this case, humans cannot classify an object accurately from these views for fine-grained (instance-level) object categorization, making the classification task challenging. Here, views with human uncertainty of class labels are denoted as uninformative views. The inconsistency between the one-hot manner of class labels and human judgment causes multi-view label noise. Soft label methods [51, 52, 53, 54, 55, 56, 57, 58, 59, 60] can help alleviate the inconsistency and render view-level predictions more consistent with human judgments, allowing the study of learning from noisy labels [61, 62] in the multi-view case. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"3f9d9ec6-ddd1-47ed-93e6-e760c731716f\"]],[null,null,0.6968088315614678,[[null,14752,15302]],[[[14752,14793,[[[14752,14793,[\"3 MVP-N: Dataset Design and Construction \"]]]]],[14793,15096,[[[14793,15096,[\"Object selection. Retail products can be distinguished without semantic confusion3. Therefore, a fine-grained categorization [73] can be easily established. Furthermore, retail products of the same brand with different flavors provide high inter-class view similarity. In total, 44 retail products from \"]]]]],[15096,15191,[[[15096,15191,[\"3Ma et al. [11] and Chen et al. [8] point out the semantic confusion issue in ModelNet40 [43]. \"]]]]],[15191,15192,[[[15191,15192,[\"4\"]]]]],[15192,15247,[[[15192,15247,[\"!!!! !!!\\\" !!!# !!!$ !!!% !!!\\u0026 !!!' !!!( !!!) !!!* !!\\\"! \"]]],[null,6]]],[15247,15302,[[[15247,15302,[\"!!\\\"\\\" !!\\\"# !!\\\"$ !!\\\"% !!\\\"\\u0026 !!\\\"' !!\\\"( !!\\\") !!\\\"* !!#! !!#\\\" \"]]],[null,6]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"fe73f9d2-7167-48dc-9652-1ee2a1a0c1c1\"]],[null,null,0.7098116407670348,[[null,0,1524]],[[[0,79,[[[0,79,[\"MVP-N: A Dataset and Benchmark for Real-World Multi-View Object Classification \"]]],[null,6]]],[79,114,[[[79,114,[\"Ren Wang Seoul National University \"]]]]],[114,137,[[[114,137,[\"<EMAIL> \"]]]]],[137,175,[[[137,175,[\"Jiayue Wang Seoul National University \"]]]]],[175,201,[[[175,201,[\"<EMAIL> \"]]]]],[201,234,[[[201,234,[\"Tae Sung Kim Sun Moon University \"]]]]],[234,255,[[[234,255,[\"<EMAIL> \"]]]]],[255,269,[[[255,269,[\"Jin-Sung Kim∗ \"]]]]],[269,312,[[[269,312,[\"Sun <NAME_EMAIL> \"]]]]],[312,373,[[[312,373,[\"Hyuk-Jae Lee∗ Seoul <NAME_EMAIL> \"]]]]],[373,382,[[[373,382,[\"Abstract \"]]]]],[382,1524,[[[382,1524,[\"Combining information from multiple views is essential for discriminating similar objects. However, existing datasets for multi-view object classification have several limitations, such as synthetic and coarse-grained objects, no validation split for hyperparameter tuning, and a lack of view-level information quantity annotations for analyzing multi-view-based methods. To address this issue, this study proposes a new dataset, MVP-N2, which contains 44 retail products, 16k real captured views with human-perceived information quantity annotations, and 9k multi-view sets. The fine-grained categorization of objects naturally generates multi-view label noise owing to the inter-class view similarity, allowing the study of learning from noisy la-bels in the multi-view case. Moreover, this study benchmarks four multi-view-based feature aggregation methods and twelve soft label methods on MVP-N. Experi-mental results show that MVP-N will be a valuable resource for facilitating the de-velopment of real-world multi-view object classification methods. The dataset and code are publicly available at https://github.com/SMNUResearch/MVP-N. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"1e198efe-bb5f-4c7b-97c6-b38cf64431b1\"]]],[[[null,232,417],[0,1]],[[null,417,485],[2]]],[[\"What is MVP-N?\",\"What are the limitations of existing datasets?\",\"What is human-perceived information quantity?\"]]]"]]
18679
[["wrb.fr",null,"[[\"When \\\"high inter-class view similarity causes uncertainty of class labels for uninformative views, which generates multi-view label noise,\\\" it means the following in the context of the MVP-N dataset:\\n\\n*   **High inter-class view similarity** refers to the characteristic of the MVP-N dataset where different retail products, especially those from the same brand, can look very similar or even identical from specific viewpoints [1, 2]. This is inherent to the fine-grained categorization of the objects [3].\\n*   **Uninformative views** are views of an object where its distinctive features are not sufficiently included, making it difficult for humans to classify the object correctly without additional information from other views [1, 4].\\n*   The combination of these two factors—similar-looking objects and views lacking distinctive features—leads to **uncertainty of class labels**. In such cases, human annotators cannot accurately classify an object based solely on these uninformative views [1].\\n*   This inconsistency between the standard \\\"one-hot\\\" (single, definitive) class labels and the human judgment of uncertainty for those specific uninformative views is what **generates multi-view label noise** [1].\",null,[\"baa41d25-0a90-4b04-8ca7-9ca7ccb67745\",\"e664f2d7-3c4d-4f9b-b34b-2735b7937b64\",3067737007],null,[[[[0,199,[[[0,199,[\"When \\\"high inter-class view similarity causes uncertainty of class labels for uninformative views, which generates multi-view label noise,\\\" it means the following in the context of the MVP-N dataset:\"]]]]],[199,486,[[[199,231,[\"High inter-class view similarity\",[true]]],[231,486,[\" refers to the characteristic of the MVP-N dataset where different retail products, especially those from the same brand, can look very similar or even identical from specific viewpoints. This is inherent to the fine-grained categorization of the objects.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[486,703,[[[486,505,[\"Uninformative views\",[true]]],[505,703,[\" are views of an object where its distinctive features are not sufficiently included, making it difficult for humans to classify the object correctly without additional information from other views.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[703,952,[[[703,812,[\"The combination of these two factors—similar-looking objects and views lacking distinctive features—leads to \"]],[812,839,[\"uncertainty of class labels\",[true]]],[839,952,[\". In such cases, human annotators cannot accurately classify an object based solely on these uninformative views.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]]],[[[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"],[null,232,417]],[[\"77f078f4-54cc-4423-ae36-495b5f803251\"],[null,232,417]],[[\"d30f3f91-1bb7-490d-a957-7c9cea1c5b99\"],[null,417,485]],[[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"],[null,506,702]],[[\"5f0458ab-225d-4bb7-b851-fc4f87bc986f\"],[null,506,702]],[[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"],[null,839,951]],[[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"],[null,1153,1153]]]],null,null,[[[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"],[null,null,0.7680594985161724,[[null,8136,9082]],[[[8136,9082,[[[8136,9082,[\"To resolve the above limitations, this study proposes MVP-N, a new dataset containing 9k multi-view sets constructed from 16k real captured views of 44 real-world fine-grained retail products. In MVP-N, different objects can appear similar or identical in specific views, referred to as high inter-class view similarity. In this case, humans cannot classify an object accurately from these views for fine-grained (instance-level) object categorization, making the classification task challenging. Here, views with human uncertainty of class labels are denoted as uninformative views. The inconsistency between the one-hot manner of class labels and human judgment causes multi-view label noise. Soft label methods [51, 52, 53, 54, 55, 56, 57, 58, 59, 60] can help alleviate the inconsistency and render view-level predictions more consistent with human judgments, allowing the study of learning from noisy labels [61, 62] in the multi-view case. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"]]],[[\"77f078f4-54cc-4423-ae36-495b5f803251\"],[null,null,0.6968088315614678,[[null,14752,15302]],[[[14752,14793,[[[14752,14793,[\"3 MVP-N: Dataset Design and Construction \"]]]]],[14793,15096,[[[14793,15096,[\"Object selection. Retail products can be distinguished without semantic confusion3. Therefore, a fine-grained categorization [73] can be easily established. Furthermore, retail products of the same brand with different flavors provide high inter-class view similarity. In total, 44 retail products from \"]]]]],[15096,15191,[[[15096,15191,[\"3Ma et al. [11] and Chen et al. [8] point out the semantic confusion issue in ModelNet40 [43]. \"]]]]],[15191,15192,[[[15191,15192,[\"4\"]]]]],[15192,15247,[[[15192,15247,[\"!!!! !!!\\\" !!!# !!!$ !!!% !!!\\u0026 !!!' !!!( !!!) !!!* !!\\\"! \"]]],[null,6]]],[15247,15302,[[[15247,15302,[\"!!\\\"\\\" !!\\\"# !!\\\"$ !!\\\"% !!\\\"\\u0026 !!\\\"' !!\\\"( !!\\\") !!\\\"* !!#! !!#\\\" \"]]],[null,6]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"77f078f4-54cc-4423-ae36-495b5f803251\"]]],[[\"d30f3f91-1bb7-490d-a957-7c9cea1c5b99\"],[null,null,0.7098116407670348,[[null,0,1524]],[[[0,79,[[[0,79,[\"MVP-N: A Dataset and Benchmark for Real-World Multi-View Object Classification \"]]],[null,6]]],[79,114,[[[79,114,[\"Ren Wang Seoul National University \"]]]]],[114,137,[[[114,137,[\"<EMAIL> \"]]]]],[137,175,[[[137,175,[\"Jiayue Wang Seoul National University \"]]]]],[175,201,[[[175,201,[\"<EMAIL> \"]]]]],[201,234,[[[201,234,[\"Tae Sung Kim Sun Moon University \"]]]]],[234,255,[[[234,255,[\"<EMAIL> \"]]]]],[255,269,[[[255,269,[\"Jin-Sung Kim∗ \"]]]]],[269,312,[[[269,312,[\"Sun <NAME_EMAIL> \"]]]]],[312,373,[[[312,373,[\"Hyuk-Jae Lee∗ Seoul <NAME_EMAIL> \"]]]]],[373,382,[[[373,382,[\"Abstract \"]]]]],[382,1524,[[[382,1524,[\"Combining information from multiple views is essential for discriminating similar objects. However, existing datasets for multi-view object classification have several limitations, such as synthetic and coarse-grained objects, no validation split for hyperparameter tuning, and a lack of view-level information quantity annotations for analyzing multi-view-based methods. To address this issue, this study proposes a new dataset, MVP-N2, which contains 44 retail products, 16k real captured views with human-perceived information quantity annotations, and 9k multi-view sets. The fine-grained categorization of objects naturally generates multi-view label noise owing to the inter-class view similarity, allowing the study of learning from noisy la-bels in the multi-view case. Moreover, this study benchmarks four multi-view-based feature aggregation methods and twelve soft label methods on MVP-N. Experi-mental results show that MVP-N will be a valuable resource for facilitating the de-velopment of real-world multi-view object classification methods. The dataset and code are publicly available at https://github.com/SMNUResearch/MVP-N. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"d30f3f91-1bb7-490d-a957-7c9cea1c5b99\"]]],[[\"5f0458ab-225d-4bb7-b851-fc4f87bc986f\"],[null,null,0.7298242237265475,[[null,17512,18119]],[[[17512,17512,[]],[17512,17691,[[[17512,17691,[\" Sufficiently informative: A distinctive appearance is sufficiently included in this view. The object can be classified correctly without additional information from other views. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[17691,17691,[]],[17691,17844,[[[17691,17844,[\" Less informative: A distinctive appearance is partially included in this view. High classifica-tion accuracy cannot be guaranteed using only this view. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[17844,17844,[]],[17844,18001,[[[17844,18001,[\" Uninformative: A distinctive appearance is not included in this view. Additional information from other views is required to classify the object correctly. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[18001,18119,[[[18001,18119,[\"Figure 4(b) shows examples of human judgments on information quantity. The average annotation time per image is 45 s. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"5f0458ab-225d-4bb7-b851-fc4f87bc986f\"]]]]]],[[null,null,0.7680594985161724,[[null,8136,9082]],[[[8136,9082,[[[8136,9082,[\"To resolve the above limitations, this study proposes MVP-N, a new dataset containing 9k multi-view sets constructed from 16k real captured views of 44 real-world fine-grained retail products. In MVP-N, different objects can appear similar or identical in specific views, referred to as high inter-class view similarity. In this case, humans cannot classify an object accurately from these views for fine-grained (instance-level) object categorization, making the classification task challenging. Here, views with human uncertainty of class labels are denoted as uninformative views. The inconsistency between the one-hot manner of class labels and human judgment causes multi-view label noise. Soft label methods [51, 52, 53, 54, 55, 56, 57, 58, 59, 60] can help alleviate the inconsistency and render view-level predictions more consistent with human judgments, allowing the study of learning from noisy labels [61, 62] in the multi-view case. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"]],[null,null,0.6968088315614678,[[null,14752,15302]],[[[14752,14793,[[[14752,14793,[\"3 MVP-N: Dataset Design and Construction \"]]]]],[14793,15096,[[[14793,15096,[\"Object selection. Retail products can be distinguished without semantic confusion3. Therefore, a fine-grained categorization [73] can be easily established. Furthermore, retail products of the same brand with different flavors provide high inter-class view similarity. In total, 44 retail products from \"]]]]],[15096,15191,[[[15096,15191,[\"3Ma et al. [11] and Chen et al. [8] point out the semantic confusion issue in ModelNet40 [43]. \"]]]]],[15191,15192,[[[15191,15192,[\"4\"]]]]],[15192,15247,[[[15192,15247,[\"!!!! !!!\\\" !!!# !!!$ !!!% !!!\\u0026 !!!' !!!( !!!) !!!* !!\\\"! \"]]],[null,6]]],[15247,15302,[[[15247,15302,[\"!!\\\"\\\" !!\\\"# !!\\\"$ !!\\\"% !!\\\"\\u0026 !!\\\"' !!\\\"( !!\\\") !!\\\"* !!#! !!#\\\" \"]]],[null,6]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"77f078f4-54cc-4423-ae36-495b5f803251\"]],[null,null,0.7098116407670348,[[null,0,1524]],[[[0,79,[[[0,79,[\"MVP-N: A Dataset and Benchmark for Real-World Multi-View Object Classification \"]]],[null,6]]],[79,114,[[[79,114,[\"Ren Wang Seoul National University \"]]]]],[114,137,[[[114,137,[\"<EMAIL> \"]]]]],[137,175,[[[137,175,[\"Jiayue Wang Seoul National University \"]]]]],[175,201,[[[175,201,[\"<EMAIL> \"]]]]],[201,234,[[[201,234,[\"Tae Sung Kim Sun Moon University \"]]]]],[234,255,[[[234,255,[\"<EMAIL> \"]]]]],[255,269,[[[255,269,[\"Jin-Sung Kim∗ \"]]]]],[269,312,[[[269,312,[\"Sun <NAME_EMAIL> \"]]]]],[312,373,[[[312,373,[\"Hyuk-Jae Lee∗ Seoul <NAME_EMAIL> \"]]]]],[373,382,[[[373,382,[\"Abstract \"]]]]],[382,1524,[[[382,1524,[\"Combining information from multiple views is essential for discriminating similar objects. However, existing datasets for multi-view object classification have several limitations, such as synthetic and coarse-grained objects, no validation split for hyperparameter tuning, and a lack of view-level information quantity annotations for analyzing multi-view-based methods. To address this issue, this study proposes a new dataset, MVP-N2, which contains 44 retail products, 16k real captured views with human-perceived information quantity annotations, and 9k multi-view sets. The fine-grained categorization of objects naturally generates multi-view label noise owing to the inter-class view similarity, allowing the study of learning from noisy la-bels in the multi-view case. Moreover, this study benchmarks four multi-view-based feature aggregation methods and twelve soft label methods on MVP-N. Experi-mental results show that MVP-N will be a valuable resource for facilitating the de-velopment of real-world multi-view object classification methods. The dataset and code are publicly available at https://github.com/SMNUResearch/MVP-N. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"d30f3f91-1bb7-490d-a957-7c9cea1c5b99\"]],[null,null,0.7298242237265475,[[null,17512,18119]],[[[17512,17512,[]],[17512,17691,[[[17512,17691,[\" Sufficiently informative: A distinctive appearance is sufficiently included in this view. The object can be classified correctly without additional information from other views. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[17691,17691,[]],[17691,17844,[[[17691,17844,[\" Less informative: A distinctive appearance is partially included in this view. High classifica-tion accuracy cannot be guaranteed using only this view. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[17844,17844,[]],[17844,18001,[[[17844,18001,[\" Uninformative: A distinctive appearance is not included in this view. Additional information from other views is required to classify the object correctly. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[18001,18119,[[[18001,18119,[\"Figure 4(b) shows examples of human judgments on information quantity. The average annotation time per image is 45 s. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"5f0458ab-225d-4bb7-b851-fc4f87bc986f\"]]],[[[null,232,417],[0,1]],[[null,417,485],[2]],[[null,506,702],[0,3]],[[null,839,951],[0]],[[null,1153,1153],[0]]],[[\"What is MVP-N?\",\"What are the limitations of existing datasets?\",\"What is human-perceived information quantity?\"]]]"]]
19490
[["wrb.fr",null,"[[\"When \\\"high inter-class view similarity causes uncertainty of class labels for uninformative views, which generates multi-view label noise,\\\" it means the following in the context of the MVP-N dataset:\\n\\n*   **High inter-class view similarity** refers to the characteristic of the MVP-N dataset where different retail products, especially those from the same brand, can look very similar or even identical from specific viewpoints [1, 2]. This is inherent to the fine-grained categorization of the objects [3].\\n*   **Uninformative views** are views of an object where its distinctive features are not sufficiently included, making it difficult for humans to classify the object correctly without additional information from other views [1, 4].\\n*   The combination of these two factors—similar-looking objects and views lacking distinctive features—leads to **uncertainty of class labels**. In such cases, human annotators cannot accurately classify an object based solely on these uninformative views [1].\\n*   This inconsistency between the standard \\\"one-hot\\\" (single, definitive) class labels and the human judgment of uncertainty for those specific uninformative views is what **generates multi-view label noise** [1].\",null,[\"baa41d25-0a90-4b04-8ca7-9ca7ccb67745\",\"e664f2d7-3c4d-4f9b-b34b-2735b7937b64\",3067737007],null,[[[[0,199,[[[0,199,[\"When \\\"high inter-class view similarity causes uncertainty of class labels for uninformative views, which generates multi-view label noise,\\\" it means the following in the context of the MVP-N dataset:\"]]]]],[199,486,[[[199,231,[\"High inter-class view similarity\",[true]]],[231,486,[\" refers to the characteristic of the MVP-N dataset where different retail products, especially those from the same brand, can look very similar or even identical from specific viewpoints. This is inherent to the fine-grained categorization of the objects.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[486,703,[[[486,505,[\"Uninformative views\",[true]]],[505,703,[\" are views of an object where its distinctive features are not sufficiently included, making it difficult for humans to classify the object correctly without additional information from other views.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[703,952,[[[703,812,[\"The combination of these two factors—similar-looking objects and views lacking distinctive features—leads to \"]],[812,839,[\"uncertainty of class labels\",[true]]],[839,952,[\". In such cases, human annotators cannot accurately classify an object based solely on these uninformative views.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]],[952,1154,[[[952,1121,[\"This inconsistency between the standard \\\"one-hot\\\" (single, definitive) class labels and the human judgment of uncertainty for those specific uninformative views is what \"]],[1121,1153,[\"generates multi-view label noise\",[true]]],[1153,1154,[\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,3]]]],[[[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"],[null,232,417]],[[\"77f078f4-54cc-4423-ae36-495b5f803251\"],[null,232,417]],[[\"d30f3f91-1bb7-490d-a957-7c9cea1c5b99\"],[null,417,485]],[[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"],[null,506,702]],[[\"5f0458ab-225d-4bb7-b851-fc4f87bc986f\"],[null,506,702]],[[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"],[null,839,951]],[[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"],[null,1153,1153]]]],null,null,[[[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"],[null,null,0.7680594985161724,[[null,8136,9082]],[[[8136,9082,[[[8136,9082,[\"To resolve the above limitations, this study proposes MVP-N, a new dataset containing 9k multi-view sets constructed from 16k real captured views of 44 real-world fine-grained retail products. In MVP-N, different objects can appear similar or identical in specific views, referred to as high inter-class view similarity. In this case, humans cannot classify an object accurately from these views for fine-grained (instance-level) object categorization, making the classification task challenging. Here, views with human uncertainty of class labels are denoted as uninformative views. The inconsistency between the one-hot manner of class labels and human judgment causes multi-view label noise. Soft label methods [51, 52, 53, 54, 55, 56, 57, 58, 59, 60] can help alleviate the inconsistency and render view-level predictions more consistent with human judgments, allowing the study of learning from noisy labels [61, 62] in the multi-view case. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"]]],[[\"77f078f4-54cc-4423-ae36-495b5f803251\"],[null,null,0.6968088315614678,[[null,14752,15302]],[[[14752,14793,[[[14752,14793,[\"3 MVP-N: Dataset Design and Construction \"]]]]],[14793,15096,[[[14793,15096,[\"Object selection. Retail products can be distinguished without semantic confusion3. Therefore, a fine-grained categorization [73] can be easily established. Furthermore, retail products of the same brand with different flavors provide high inter-class view similarity. In total, 44 retail products from \"]]]]],[15096,15191,[[[15096,15191,[\"3Ma et al. [11] and Chen et al. [8] point out the semantic confusion issue in ModelNet40 [43]. \"]]]]],[15191,15192,[[[15191,15192,[\"4\"]]]]],[15192,15247,[[[15192,15247,[\"!!!! !!!\\\" !!!# !!!$ !!!% !!!\\u0026 !!!' !!!( !!!) !!!* !!\\\"! \"]]],[null,6]]],[15247,15302,[[[15247,15302,[\"!!\\\"\\\" !!\\\"# !!\\\"$ !!\\\"% !!\\\"\\u0026 !!\\\"' !!\\\"( !!\\\") !!\\\"* !!#! !!#\\\" \"]]],[null,6]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"77f078f4-54cc-4423-ae36-495b5f803251\"]]],[[\"d30f3f91-1bb7-490d-a957-7c9cea1c5b99\"],[null,null,0.7098116407670348,[[null,0,1524]],[[[0,79,[[[0,79,[\"MVP-N: A Dataset and Benchmark for Real-World Multi-View Object Classification \"]]],[null,6]]],[79,114,[[[79,114,[\"Ren Wang Seoul National University \"]]]]],[114,137,[[[114,137,[\"<EMAIL> \"]]]]],[137,175,[[[137,175,[\"Jiayue Wang Seoul National University \"]]]]],[175,201,[[[175,201,[\"<EMAIL> \"]]]]],[201,234,[[[201,234,[\"Tae Sung Kim Sun Moon University \"]]]]],[234,255,[[[234,255,[\"<EMAIL> \"]]]]],[255,269,[[[255,269,[\"Jin-Sung Kim∗ \"]]]]],[269,312,[[[269,312,[\"Sun <NAME_EMAIL> \"]]]]],[312,373,[[[312,373,[\"Hyuk-Jae Lee∗ Seoul <NAME_EMAIL> \"]]]]],[373,382,[[[373,382,[\"Abstract \"]]]]],[382,1524,[[[382,1524,[\"Combining information from multiple views is essential for discriminating similar objects. However, existing datasets for multi-view object classification have several limitations, such as synthetic and coarse-grained objects, no validation split for hyperparameter tuning, and a lack of view-level information quantity annotations for analyzing multi-view-based methods. To address this issue, this study proposes a new dataset, MVP-N2, which contains 44 retail products, 16k real captured views with human-perceived information quantity annotations, and 9k multi-view sets. The fine-grained categorization of objects naturally generates multi-view label noise owing to the inter-class view similarity, allowing the study of learning from noisy la-bels in the multi-view case. Moreover, this study benchmarks four multi-view-based feature aggregation methods and twelve soft label methods on MVP-N. Experi-mental results show that MVP-N will be a valuable resource for facilitating the de-velopment of real-world multi-view object classification methods. The dataset and code are publicly available at https://github.com/SMNUResearch/MVP-N. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"d30f3f91-1bb7-490d-a957-7c9cea1c5b99\"]]],[[\"5f0458ab-225d-4bb7-b851-fc4f87bc986f\"],[null,null,0.7298242237265475,[[null,17512,18119]],[[[17512,17512,[]],[17512,17691,[[[17512,17691,[\" Sufficiently informative: A distinctive appearance is sufficiently included in this view. The object can be classified correctly without additional information from other views. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[17691,17691,[]],[17691,17844,[[[17691,17844,[\" Less informative: A distinctive appearance is partially included in this view. High classifica-tion accuracy cannot be guaranteed using only this view. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[17844,17844,[]],[17844,18001,[[[17844,18001,[\" Uninformative: A distinctive appearance is not included in this view. Additional information from other views is required to classify the object correctly. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[18001,18119,[[[18001,18119,[\"Figure 4(b) shows examples of human judgments on information quantity. The average annotation time per image is 45 s. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"5f0458ab-225d-4bb7-b851-fc4f87bc986f\"]]]]]],[[null,null,0.7680594985161724,[[null,8136,9082]],[[[8136,9082,[[[8136,9082,[\"To resolve the above limitations, this study proposes MVP-N, a new dataset containing 9k multi-view sets constructed from 16k real captured views of 44 real-world fine-grained retail products. In MVP-N, different objects can appear similar or identical in specific views, referred to as high inter-class view similarity. In this case, humans cannot classify an object accurately from these views for fine-grained (instance-level) object categorization, making the classification task challenging. Here, views with human uncertainty of class labels are denoted as uninformative views. The inconsistency between the one-hot manner of class labels and human judgment causes multi-view label noise. Soft label methods [51, 52, 53, 54, 55, 56, 57, 58, 59, 60] can help alleviate the inconsistency and render view-level predictions more consistent with human judgments, allowing the study of learning from noisy labels [61, 62] in the multi-view case. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"4d1bc301-4825-406f-bff8-a66a5d0a6744\"]],[null,null,0.6968088315614678,[[null,14752,15302]],[[[14752,14793,[[[14752,14793,[\"3 MVP-N: Dataset Design and Construction \"]]]]],[14793,15096,[[[14793,15096,[\"Object selection. Retail products can be distinguished without semantic confusion3. Therefore, a fine-grained categorization [73] can be easily established. Furthermore, retail products of the same brand with different flavors provide high inter-class view similarity. In total, 44 retail products from \"]]]]],[15096,15191,[[[15096,15191,[\"3Ma et al. [11] and Chen et al. [8] point out the semantic confusion issue in ModelNet40 [43]. \"]]]]],[15191,15192,[[[15191,15192,[\"4\"]]]]],[15192,15247,[[[15192,15247,[\"!!!! !!!\\\" !!!# !!!$ !!!% !!!\\u0026 !!!' !!!( !!!) !!!* !!\\\"! \"]]],[null,6]]],[15247,15302,[[[15247,15302,[\"!!\\\"\\\" !!\\\"# !!\\\"$ !!\\\"% !!\\\"\\u0026 !!\\\"' !!\\\"( !!\\\") !!\\\"* !!#! !!#\\\" \"]]],[null,6]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"77f078f4-54cc-4423-ae36-495b5f803251\"]],[null,null,0.7098116407670348,[[null,0,1524]],[[[0,79,[[[0,79,[\"MVP-N: A Dataset and Benchmark for Real-World Multi-View Object Classification \"]]],[null,6]]],[79,114,[[[79,114,[\"Ren Wang Seoul National University \"]]]]],[114,137,[[[114,137,[\"<EMAIL> \"]]]]],[137,175,[[[137,175,[\"Jiayue Wang Seoul National University \"]]]]],[175,201,[[[175,201,[\"<EMAIL> \"]]]]],[201,234,[[[201,234,[\"Tae Sung Kim Sun Moon University \"]]]]],[234,255,[[[234,255,[\"<EMAIL> \"]]]]],[255,269,[[[255,269,[\"Jin-Sung Kim∗ \"]]]]],[269,312,[[[269,312,[\"Sun <NAME_EMAIL> \"]]]]],[312,373,[[[312,373,[\"Hyuk-Jae Lee∗ Seoul <NAME_EMAIL> \"]]]]],[373,382,[[[373,382,[\"Abstract \"]]]]],[382,1524,[[[382,1524,[\"Combining information from multiple views is essential for discriminating similar objects. However, existing datasets for multi-view object classification have several limitations, such as synthetic and coarse-grained objects, no validation split for hyperparameter tuning, and a lack of view-level information quantity annotations for analyzing multi-view-based methods. To address this issue, this study proposes a new dataset, MVP-N2, which contains 44 retail products, 16k real captured views with human-perceived information quantity annotations, and 9k multi-view sets. The fine-grained categorization of objects naturally generates multi-view label noise owing to the inter-class view similarity, allowing the study of learning from noisy la-bels in the multi-view case. Moreover, this study benchmarks four multi-view-based feature aggregation methods and twelve soft label methods on MVP-N. Experi-mental results show that MVP-N will be a valuable resource for facilitating the de-velopment of real-world multi-view object classification methods. The dataset and code are publicly available at https://github.com/SMNUResearch/MVP-N. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"d30f3f91-1bb7-490d-a957-7c9cea1c5b99\"]],[null,null,0.7298242237265475,[[null,17512,18119]],[[[17512,17512,[]],[17512,17691,[[[17512,17691,[\" Sufficiently informative: A distinctive appearance is sufficiently included in this view. The object can be classified correctly without additional information from other views. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[17691,17691,[]],[17691,17844,[[[17691,17844,[\" Less informative: A distinctive appearance is partially included in this view. High classifica-tion accuracy cannot be guaranteed using only this view. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[17844,17844,[]],[17844,18001,[[[17844,18001,[\" Uninformative: A distinctive appearance is not included in this view. Additional information from other views is required to classify the object correctly. \"]]],null,null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,1]]],[18001,18119,[[[18001,18119,[\"Figure 4(b) shows examples of human judgments on information quantity. The average annotation time per image is 45 s. \"]]]]]]],[[[\"56a696d5-045d-4278-a9e1-f498f75aedb1\"],\"78a2e52a-6f0d-4b07-86a0-1d1386ae612a\"]],[\"5f0458ab-225d-4bb7-b851-fc4f87bc986f\"]]],[[[null,232,417],[0,1]],[[null,417,485],[2]],[[null,506,702],[0,3]],[[null,839,951],[0]],[[null,1153,1153],[0]]],[[\"What is MVP-N?\",\"What are the limitations of existing datasets?\",\"What is human-perceived information quantity?\"]]]"]]
59
[["di",5000],["af.httprm",4999,"5094718460987797368",61]]
27
[["e",7,null,null,49689]]
