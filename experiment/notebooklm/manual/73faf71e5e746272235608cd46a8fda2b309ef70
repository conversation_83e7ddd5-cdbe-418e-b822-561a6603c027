)]}'

2277
[["wrb.fr",null,"[[\"The authors mean that they tuned **new noise values** for pre-training and fine-tuning by sweeping over a **set of five specific values: {0.005, 0.01, 0.02, 0.05, 0.1}** [1].\",null,[\"f39dc892-4e3d-43e6-a1e4-9a8b58471edd\",\"b007ab6e-8657-4724-a45a-0a429027243c\",829350173],null,[[null,[[[\"22f93b4b-6c94-498d-9acb-c9a0f4b413a0\"],[null,161,161]]]],null,null,[[[\"22f93b4b-6c94-498d-9acb-c9a0f4b413a0\"],[null,null,0.6916342505512925,[[null,82255,82863]],[[[82255,82863,[[[82255,82863,[\"Hyperparameter optimization. We note that effective pre-training via denoising requires sweeping noise values, as well as loss co-efficients for denoising and atom type recovery. For GNS/GNS-TAT, we relied on the hyperparameters published by Godwin et al. (2022) but determined new noise values for pre-training and fine-tuning by tuning over the set of values {0.005, 0.01, 0.02, 0.05, 0.1} for each of PCQM4Mv2 and QM9 (on the HOMO energy target). We used the same values for DES15K without modification. We also ran a similar number of experiments to determine cosine cycle parameters for learning rates. \"]]]]]]],[[[\"08f8904d-3ff1-47c9-bb45-460de6a3a645\"],\"fb479cf2-e0ca-46f6-a7e3-d4d69b12653f\"]],[\"22f93b4b-6c94-498d-9acb-c9a0f4b413a0\"]]]]]],[[null,null,0.6916342505512925,[[null,82255,82863]],[[[82255,82863,[[[82255,82863,[\"Hyperparameter optimization. We note that effective pre-training via denoising requires sweeping noise values, as well as loss co-efficients for denoising and atom type recovery. For GNS/GNS-TAT, we relied on the hyperparameters published by Godwin et al. (2022) but determined new noise values for pre-training and fine-tuning by tuning over the set of values {0.005, 0.01, 0.02, 0.05, 0.1} for each of PCQM4Mv2 and QM9 (on the HOMO energy target). We used the same values for DES15K without modification. We also ran a similar number of experiments to determine cosine cycle parameters for learning rates. \"]]]]]]],[[[\"08f8904d-3ff1-47c9-bb45-460de6a3a645\"],\"fb479cf2-e0ca-46f6-a7e3-d4d69b12653f\"]],[\"22f93b4b-6c94-498d-9acb-c9a0f4b413a0\"]]],[[[null,161,161],[0]]],[[\"How is denoising linked to force fields?\",\"What is the purpose of Edge-Delta initialization?\",\"Which datasets are used for fine-tuning?\"]]]"]]
5964
[["wrb.fr",null,"[[\"The authors mean that they tuned **new noise values** for pre-training and fine-tuning by sweeping over a **set of five specific values: {0.005, 0.01, 0.02, 0.05, 0.1}** [1]. This tuning was conducted on the HOMO energy target of the QM9 dataset [1, 2].\",null,[\"f39dc892-4e3d-43e6-a1e4-9a8b58471edd\",\"b007ab6e-8657-4724-a45a-0a429027243c\",829350173],null,[[null,[[[\"01a9c17d-5a22-4f65-b31d-0a3b97c3e990\"],[null,161,161]],[[\"01a9c17d-5a22-4f65-b31d-0a3b97c3e990\"],[null,161,233]],[[\"6c44dcdc-d360-4a88-a8f5-a8c66e41a915\"],[null,161,233]]]],null,null,[[[\"01a9c17d-5a22-4f65-b31d-0a3b97c3e990\"],[null,null,0.6916342505512925,[[null,82255,82863]],[[[82255,82863,[[[82255,82863,[\"Hyperparameter optimization. We note that effective pre-training via denoising requires sweeping noise values, as well as loss co-efficients for denoising and atom type recovery. For GNS/GNS-TAT, we relied on the hyperparameters published by Godwin et al. (2022) but determined new noise values for pre-training and fine-tuning by tuning over the set of values {0.005, 0.01, 0.02, 0.05, 0.1} for each of PCQM4Mv2 and QM9 (on the HOMO energy target). We used the same values for DES15K without modification. We also ran a similar number of experiments to determine cosine cycle parameters for learning rates. \"]]]]]]],[[[\"08f8904d-3ff1-47c9-bb45-460de6a3a645\"],\"fb479cf2-e0ca-46f6-a7e3-d4d69b12653f\"]],[\"01a9c17d-5a22-4f65-b31d-0a3b97c3e990\"]]],[[\"6c44dcdc-d360-4a88-a8f5-a8c66e41a915\"],[null,null,0.7476617991120416,[[null,23199,24605]],[[[23199,23470,[[[23199,23470,[\"Training setup. GNS/GNS-TAT were implemented in JAX (Bradbury et al., 2018) using Haiku and Jraph (Hennigan et al., 2020; Godwin* et al., 2020). All experiments were averaged over 3 seeds. Detailed hyperparameter and hardware settings can be found in Appendices E and F. \"]]]]],[23470,23489,[[[23470,23489,[\"4.2 RESULTS ON QM9 \"]]]]],[23489,24605,[[[23489,24605,[\"We evaluate two variants of our model on QM9 in Table 1, GNS-TAT with Noisy Nodes trained from a random initialization versus pre-trained parameters. Pre-training is done on PCQM4Mv2 via denoising. For best performance on QM9, we found that using atom type masking and prediction during pre-training additionally helped (Hu et al., 2020a). We fine-tune a separate model for each of the 12 targets, as usually done on QM9, using a single pre-trained model. This is repeated for three seeds (including pre-training). Following customary practice, hyperparameters, including the noise scale for denoising during pre-training and fine-tuning, are tuned on the HOMO target and then kept fixed for all other targets. We first observe that GNS-TAT with Noisy Nodes performs competitively with other models and significantly improves upon GNS with Noisy Nodes, revealing the benefit of the TAT modifications. Utilizing pre-training then further improves performance across all targets, achieving a new state-of-the-art compared to prior work for 10 out of 12 targets. Interestingly, for the electronic spatial extent target \"]]]]]]],[[[\"08f8904d-3ff1-47c9-bb45-460de6a3a645\"],\"fb479cf2-e0ca-46f6-a7e3-d4d69b12653f\"]],[\"6c44dcdc-d360-4a88-a8f5-a8c66e41a915\"]]]]]],[[null,null,0.6916342505512925,[[null,82255,82863]],[[[82255,82863,[[[82255,82863,[\"Hyperparameter optimization. We note that effective pre-training via denoising requires sweeping noise values, as well as loss co-efficients for denoising and atom type recovery. For GNS/GNS-TAT, we relied on the hyperparameters published by Godwin et al. (2022) but determined new noise values for pre-training and fine-tuning by tuning over the set of values {0.005, 0.01, 0.02, 0.05, 0.1} for each of PCQM4Mv2 and QM9 (on the HOMO energy target). We used the same values for DES15K without modification. We also ran a similar number of experiments to determine cosine cycle parameters for learning rates. \"]]]]]]],[[[\"08f8904d-3ff1-47c9-bb45-460de6a3a645\"],\"fb479cf2-e0ca-46f6-a7e3-d4d69b12653f\"]],[\"01a9c17d-5a22-4f65-b31d-0a3b97c3e990\"]],[null,null,0.7476617991120416,[[null,23199,24605]],[[[23199,23470,[[[23199,23470,[\"Training setup. GNS/GNS-TAT were implemented in JAX (Bradbury et al., 2018) using Haiku and Jraph (Hennigan et al., 2020; Godwin* et al., 2020). All experiments were averaged over 3 seeds. Detailed hyperparameter and hardware settings can be found in Appendices E and F. \"]]]]],[23470,23489,[[[23470,23489,[\"4.2 RESULTS ON QM9 \"]]]]],[23489,24605,[[[23489,24605,[\"We evaluate two variants of our model on QM9 in Table 1, GNS-TAT with Noisy Nodes trained from a random initialization versus pre-trained parameters. Pre-training is done on PCQM4Mv2 via denoising. For best performance on QM9, we found that using atom type masking and prediction during pre-training additionally helped (Hu et al., 2020a). We fine-tune a separate model for each of the 12 targets, as usually done on QM9, using a single pre-trained model. This is repeated for three seeds (including pre-training). Following customary practice, hyperparameters, including the noise scale for denoising during pre-training and fine-tuning, are tuned on the HOMO target and then kept fixed for all other targets. We first observe that GNS-TAT with Noisy Nodes performs competitively with other models and significantly improves upon GNS with Noisy Nodes, revealing the benefit of the TAT modifications. Utilizing pre-training then further improves performance across all targets, achieving a new state-of-the-art compared to prior work for 10 out of 12 targets. Interestingly, for the electronic spatial extent target \"]]]]]]],[[[\"08f8904d-3ff1-47c9-bb45-460de6a3a645\"],\"fb479cf2-e0ca-46f6-a7e3-d4d69b12653f\"]],[\"6c44dcdc-d360-4a88-a8f5-a8c66e41a915\"]]],[[[null,161,161],[0]],[[null,161,233],[0,1]]],[[\"How is denoising linked to force fields?\",\"What is the purpose of Edge-Delta initialization?\",\"Which datasets are used for fine-tuning?\"]]]"]]
6300
[["wrb.fr",null,"[[\"The authors mean that they tuned **new noise values** for pre-training and fine-tuning by sweeping over a **set of five specific values: {0.005, 0.01, 0.02, 0.05, 0.1}** [1]. This tuning was conducted on the HOMO energy target of the QM9 dataset [1, 2].\",null,[\"f39dc892-4e3d-43e6-a1e4-9a8b58471edd\",\"b007ab6e-8657-4724-a45a-0a429027243c\",829350173],null,[[[[0,234,[[[0,33,[\"The authors mean that they tuned \"]],[33,49,[\"new noise values\",[true]]],[49,102,[\" for pre-training and fine-tuning by sweeping over a \"]],[102,161,[\"set of five specific values: {0.005, 0.01, 0.02, 0.05, 0.1}\",[true]]],[161,234,[\". This tuning was conducted on the HOMO energy target of the QM9 dataset.\"]]]]]],[[[\"01a9c17d-5a22-4f65-b31d-0a3b97c3e990\"],[null,161,161]],[[\"01a9c17d-5a22-4f65-b31d-0a3b97c3e990\"],[null,161,233]],[[\"6c44dcdc-d360-4a88-a8f5-a8c66e41a915\"],[null,161,233]]]],null,null,[[[\"01a9c17d-5a22-4f65-b31d-0a3b97c3e990\"],[null,null,0.6916342505512925,[[null,82255,82863]],[[[82255,82863,[[[82255,82863,[\"Hyperparameter optimization. We note that effective pre-training via denoising requires sweeping noise values, as well as loss co-efficients for denoising and atom type recovery. For GNS/GNS-TAT, we relied on the hyperparameters published by Godwin et al. (2022) but determined new noise values for pre-training and fine-tuning by tuning over the set of values {0.005, 0.01, 0.02, 0.05, 0.1} for each of PCQM4Mv2 and QM9 (on the HOMO energy target). We used the same values for DES15K without modification. We also ran a similar number of experiments to determine cosine cycle parameters for learning rates. \"]]]]]]],[[[\"08f8904d-3ff1-47c9-bb45-460de6a3a645\"],\"fb479cf2-e0ca-46f6-a7e3-d4d69b12653f\"]],[\"01a9c17d-5a22-4f65-b31d-0a3b97c3e990\"]]],[[\"6c44dcdc-d360-4a88-a8f5-a8c66e41a915\"],[null,null,0.7476617991120416,[[null,23199,24605]],[[[23199,23470,[[[23199,23470,[\"Training setup. GNS/GNS-TAT were implemented in JAX (Bradbury et al., 2018) using Haiku and Jraph (Hennigan et al., 2020; Godwin* et al., 2020). All experiments were averaged over 3 seeds. Detailed hyperparameter and hardware settings can be found in Appendices E and F. \"]]]]],[23470,23489,[[[23470,23489,[\"4.2 RESULTS ON QM9 \"]]]]],[23489,24605,[[[23489,24605,[\"We evaluate two variants of our model on QM9 in Table 1, GNS-TAT with Noisy Nodes trained from a random initialization versus pre-trained parameters. Pre-training is done on PCQM4Mv2 via denoising. For best performance on QM9, we found that using atom type masking and prediction during pre-training additionally helped (Hu et al., 2020a). We fine-tune a separate model for each of the 12 targets, as usually done on QM9, using a single pre-trained model. This is repeated for three seeds (including pre-training). Following customary practice, hyperparameters, including the noise scale for denoising during pre-training and fine-tuning, are tuned on the HOMO target and then kept fixed for all other targets. We first observe that GNS-TAT with Noisy Nodes performs competitively with other models and significantly improves upon GNS with Noisy Nodes, revealing the benefit of the TAT modifications. Utilizing pre-training then further improves performance across all targets, achieving a new state-of-the-art compared to prior work for 10 out of 12 targets. Interestingly, for the electronic spatial extent target \"]]]]]]],[[[\"08f8904d-3ff1-47c9-bb45-460de6a3a645\"],\"fb479cf2-e0ca-46f6-a7e3-d4d69b12653f\"]],[\"6c44dcdc-d360-4a88-a8f5-a8c66e41a915\"]]]]]],[[null,null,0.6916342505512925,[[null,82255,82863]],[[[82255,82863,[[[82255,82863,[\"Hyperparameter optimization. We note that effective pre-training via denoising requires sweeping noise values, as well as loss co-efficients for denoising and atom type recovery. For GNS/GNS-TAT, we relied on the hyperparameters published by Godwin et al. (2022) but determined new noise values for pre-training and fine-tuning by tuning over the set of values {0.005, 0.01, 0.02, 0.05, 0.1} for each of PCQM4Mv2 and QM9 (on the HOMO energy target). We used the same values for DES15K without modification. We also ran a similar number of experiments to determine cosine cycle parameters for learning rates. \"]]]]]]],[[[\"08f8904d-3ff1-47c9-bb45-460de6a3a645\"],\"fb479cf2-e0ca-46f6-a7e3-d4d69b12653f\"]],[\"01a9c17d-5a22-4f65-b31d-0a3b97c3e990\"]],[null,null,0.7476617991120416,[[null,23199,24605]],[[[23199,23470,[[[23199,23470,[\"Training setup. GNS/GNS-TAT were implemented in JAX (Bradbury et al., 2018) using Haiku and Jraph (Hennigan et al., 2020; Godwin* et al., 2020). All experiments were averaged over 3 seeds. Detailed hyperparameter and hardware settings can be found in Appendices E and F. \"]]]]],[23470,23489,[[[23470,23489,[\"4.2 RESULTS ON QM9 \"]]]]],[23489,24605,[[[23489,24605,[\"We evaluate two variants of our model on QM9 in Table 1, GNS-TAT with Noisy Nodes trained from a random initialization versus pre-trained parameters. Pre-training is done on PCQM4Mv2 via denoising. For best performance on QM9, we found that using atom type masking and prediction during pre-training additionally helped (Hu et al., 2020a). We fine-tune a separate model for each of the 12 targets, as usually done on QM9, using a single pre-trained model. This is repeated for three seeds (including pre-training). Following customary practice, hyperparameters, including the noise scale for denoising during pre-training and fine-tuning, are tuned on the HOMO target and then kept fixed for all other targets. We first observe that GNS-TAT with Noisy Nodes performs competitively with other models and significantly improves upon GNS with Noisy Nodes, revealing the benefit of the TAT modifications. Utilizing pre-training then further improves performance across all targets, achieving a new state-of-the-art compared to prior work for 10 out of 12 targets. Interestingly, for the electronic spatial extent target \"]]]]]]],[[[\"08f8904d-3ff1-47c9-bb45-460de6a3a645\"],\"fb479cf2-e0ca-46f6-a7e3-d4d69b12653f\"]],[\"6c44dcdc-d360-4a88-a8f5-a8c66e41a915\"]]],[[[null,161,161],[0]],[[null,161,233],[0,1]]],[[\"How is denoising linked to force fields?\",\"What is the purpose of Edge-Delta initialization?\",\"Which datasets are used for fine-tuning?\"]]]"]]
60
[["di",4728],["af.httprm",4727,"-8145117278737939228",60]]
27
[["e",6,null,null,14650]]
