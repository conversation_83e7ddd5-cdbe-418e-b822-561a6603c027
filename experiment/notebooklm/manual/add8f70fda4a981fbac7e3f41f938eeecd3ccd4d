)]}'

2611
[["wrb.fr",null,"[[\"The rationale behind calling the first kind of rule \\\"Markovian\\\" is that **with Markovian constraints, the calculation of the next state probability is only related to the current state probability** [1]. This means that **a random walk is performed without the consideration of previously visited edges** [1].\",null,[\"aee2040d-8583-4282-ba13-41eca7c8feb1\",\"43ba8704-47a4-47ba-9f31-eb8230af3200\",1201147937],null,[[null,[[[\"72cd00e0-d086-4051-b52e-c9d86338fd74\"],[null,194,194]],[[\"72cd00e0-d086-4051-b52e-c9d86338fd74\"],[null,292,292]]]],null,null,[[[\"72cd00e0-d086-4051-b52e-c9d86338fd74\"],[null,null,0.6461957904710884,[[null,12367,12906]],[[[12367,12393,[[[12367,12393,[\"4 CONSTRAINED RANDOM WALK \"]]]]],[12393,12580,[[[12393,12580,[\"Path constraint. Temporal logical rules can be considered as constraints for random walks on tKG. Generally speaking, these constraints can be divided into two classes: Markovian and non-\"]]]]],[12580,12580,[]],[12580,12581,[[[12580,12581,[\" \"]]]]],[12581,12906,[[[12581,12906,[\"Markovian. With Markovian constraints, the calculation of next state probability is only related to current state probability, i.e., a random walk is performed without the consideration of previous visited edges. Otherwise, we need to record the previous visited edges to ensure that non-Markovian constraints are satisfied. \"]]]]]]],[[[\"1eae0cf6-f3b3-4f54-93ac-d554d71272ee\"],\"47a013e3-db2c-4fd0-b319-611f2d3c93bc\"]],[\"72cd00e0-d086-4051-b52e-c9d86338fd74\"]]]]]],[[null,null,0.6461957904710884,[[null,12367,12906]],[[[12367,12393,[[[12367,12393,[\"4 CONSTRAINED RANDOM WALK \"]]]]],[12393,12580,[[[12393,12580,[\"Path constraint. Temporal logical rules can be considered as constraints for random walks on tKG. Generally speaking, these constraints can be divided into two classes: Markovian and non-\"]]]]],[12580,12580,[]],[12580,12581,[[[12580,12581,[\" \"]]]]],[12581,12906,[[[12581,12906,[\"Markovian. With Markovian constraints, the calculation of next state probability is only related to current state probability, i.e., a random walk is performed without the consideration of previous visited edges. Otherwise, we need to record the previous visited edges to ensure that non-Markovian constraints are satisfied. \"]]]]]]],[[[\"1eae0cf6-f3b3-4f54-93ac-d554d71272ee\"],\"47a013e3-db2c-4fd0-b319-611f2d3c93bc\"]],[\"72cd00e0-d086-4051-b52e-c9d86338fd74\"]]],[[[null,194,194],[0]],[[null,292,292],[0]]],[[\"What is a temporal knowledge graph?\",\"Name one temporal feature modeled by TILP.\",\"How does TILP handle biased data?\"]]]"]]
3014
[["wrb.fr",null,"[[\"The rationale behind calling the first kind of rule \\\"Markovian\\\" is that **with Markovian constraints, the calculation of the next state probability is only related to the current state probability** [1]. This means that **a random walk is performed without the consideration of previously visited edges** [1].\",null,[\"aee2040d-8583-4282-ba13-41eca7c8feb1\",\"43ba8704-47a4-47ba-9f31-eb8230af3200\",1201147937],null,[[[[0,293,[[[0,72,[\"The rationale behind calling the first kind of rule \\\"Markovian\\\" is that \"]],[72,194,[\"with Markovian constraints, the calculation of the next state probability is only related to the current state probability\",[true]]],[194,212,[\". This means that \"]],[212,292,[\"a random walk is performed without the consideration of previously visited edges\",[true]]],[292,293,[\".\"]]]]]],[[[\"72cd00e0-d086-4051-b52e-c9d86338fd74\"],[null,194,194]],[[\"72cd00e0-d086-4051-b52e-c9d86338fd74\"],[null,292,292]]]],null,null,[[[\"72cd00e0-d086-4051-b52e-c9d86338fd74\"],[null,null,0.6461957904710884,[[null,12367,12906]],[[[12367,12393,[[[12367,12393,[\"4 CONSTRAINED RANDOM WALK \"]]]]],[12393,12580,[[[12393,12580,[\"Path constraint. Temporal logical rules can be considered as constraints for random walks on tKG. Generally speaking, these constraints can be divided into two classes: Markovian and non-\"]]]]],[12580,12580,[]],[12580,12581,[[[12580,12581,[\" \"]]]]],[12581,12906,[[[12581,12906,[\"Markovian. With Markovian constraints, the calculation of next state probability is only related to current state probability, i.e., a random walk is performed without the consideration of previous visited edges. Otherwise, we need to record the previous visited edges to ensure that non-Markovian constraints are satisfied. \"]]]]]]],[[[\"1eae0cf6-f3b3-4f54-93ac-d554d71272ee\"],\"47a013e3-db2c-4fd0-b319-611f2d3c93bc\"]],[\"72cd00e0-d086-4051-b52e-c9d86338fd74\"]]]]]],[[null,null,0.6461957904710884,[[null,12367,12906]],[[[12367,12393,[[[12367,12393,[\"4 CONSTRAINED RANDOM WALK \"]]]]],[12393,12580,[[[12393,12580,[\"Path constraint. Temporal logical rules can be considered as constraints for random walks on tKG. Generally speaking, these constraints can be divided into two classes: Markovian and non-\"]]]]],[12580,12580,[]],[12580,12581,[[[12580,12581,[\" \"]]]]],[12581,12906,[[[12581,12906,[\"Markovian. With Markovian constraints, the calculation of next state probability is only related to current state probability, i.e., a random walk is performed without the consideration of previous visited edges. Otherwise, we need to record the previous visited edges to ensure that non-Markovian constraints are satisfied. \"]]]]]]],[[[\"1eae0cf6-f3b3-4f54-93ac-d554d71272ee\"],\"47a013e3-db2c-4fd0-b319-611f2d3c93bc\"]],[\"72cd00e0-d086-4051-b52e-c9d86338fd74\"]]],[[[null,194,194],[0]],[[null,292,292],[0]]],[[\"What is a temporal knowledge graph?\",\"Name one temporal feature modeled by TILP.\",\"How does TILP handle biased data?\"]]]"]]
60
[["di",3363],["af.httprm",3363,"-3192067384416690513",61]]
26
[["e",5,null,null,5729]]
