)]}'

439
[["wrb.fr",null,"[[\"No, Assumption B does not explicitly state that $\\\\phi$ must be a vector. However, it defines $\\\\Phi$ as a collection of representations from the feature space X to some latent space Z, where Z is a subset of an\",null,[\"93d1bd49-f02e-445c-baf6-9dbfeb9d45aa\",\"858fcd3d-2c27-4c57-82ce-c3391ef013db\",2681741471],null,[[]]],null,null,[[\"What is TAWT?\",\"Name two NLP tasks.\",\"What is one limitation?\"]]]"]]
9274
[["wrb.fr",null,"[[\"No, Assumption B does not explicitly state that $\\\\phi$ must be a vector. However, it defines $\\\\Phi$ as a collection of representations from the feature space X to some latent space Z, where Z is a subset of an r-dimensional real space ($Z \\\\subseteq Rr$) [1, 2]. Additionally, footnote 3 clarifies that for two functions $\\\\phi, \\\\psi \\\\in \\\\Phi$, they are described as \\\"vector-valued functions\\\" [3]. This implies that the output of $\\\\phi$ is a vector.\",null,[\"93d1bd49-f02e-445c-baf6-9dbfeb9d45aa\",\"858fcd3d-2c27-4c57-82ce-c3391ef013db\",2681741471],null,[[null,[[[\"eda7f0c0-5d80-48fc-88f7-b08dc0b51b98\"],[null,0,253]],[[\"5896a95c-32ac-4fc8-aba8-4049fd2ee34a\"],[null,0,253]],[[\"a7e064b3-5c5d-4bed-88c4-dfc3522f3f86\"],[null,253,383]]]],null,null,[[[\"eda7f0c0-5d80-48fc-88f7-b08dc0b51b98\"],[null,null,0.6307501161406368,[[null,7274,7790]],[[[7274,7650,[[[7274,7650,[\"Let Φ be a collection of representations from the feature space X to some latent space Z ⊆ Rr. We refer to Φ as the representation class. Let F be a collection of task-specific functions from the latent space Z to the label space Y . The complexity of the representation class Φ is usually much larger (i.e., more expressive) than that of the task-specific function class F . \"]]]]],[7650,7790,[[[7650,7790,[\"Given a bounded loss function ` : Y ×Y → [0, 1], the optimal pair of representation and task-specific function of the t-th task is given by \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"eda7f0c0-5d80-48fc-88f7-b08dc0b51b98\"]]],[[\"5896a95c-32ac-4fc8-aba8-4049fd2ee34a\"],[null,null,0.6525237270968838,[[null,19331,20304]],[[[19331,19376,[[[19331,19376,[\"N (Φ;L2(QX ); ε) ≤ (CΦ/ε) νΦ , ∀ε \\u003e 0, (3.3) \"]]]]],[19376,19615,[[[19376,19615,[\"whereN (Φ;L2(QX ); ε) is the L2(QX ) covering number of Φ (i.e., the minimum number of L2(QX ) balls3 with radius ε required to cover Φ). In parallel, there exist CF \\u003e 0, νF \\u003e 0, such that for any probability measure QZ on Z ⊆ Rr, we have \"]]]]],[19615,19660,[[[19615,19660,[\"N (F ;L2(QZ); ε) ≤ (CF/ε) νG , ∀ε \\u003e 0, (3.4) \"]]]]],[19660,19720,[[[19660,19720,[\"where N (F ;L2(QZ); ε) is the L2(QZ) covering number of F . \"]]]]],[19720,20304,[[[19720,20304,[\"Uniform entropy generalizes the notion of Vapnik-Chervonenkis dimension (Vapnik, 2013) and allows us to give a unified treatment of regression and classification problems. For this reason, function classes satisfying the above assumption are also referred to as “VC-type classes” in the literature (Koltchinskii, 2006). In particular, if each coordinate of Φ has VC-subgraph dimension c(Φ), then (3.3) is satisfied with νΦ \\u003d Θ(r · c(Φ)) (recall that r is the dimension of the latent space Z). Similarly, if F has VC-subgraph dimension c(F), then (3.4) is satisfied with νF \\u003d Θ(c(F)). \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"5896a95c-32ac-4fc8-aba8-4049fd2ee34a\"]]],[[\"a7e064b3-5c5d-4bed-88c4-dfc3522f3f86\"],[null,null,0.6808876892895416,[[null,22000,22901]],[[[22000,22019,[[[22000,22019,[\"L̂(1) 0 (φ, f0) :\\u003d \"]]]]],[22019,22021,[[[22019,22021,[\"1 \"]]]]],[22021,22033,[[[22021,22033,[\"|B1| ∑ i∈B1 \"]]]]],[22033,22073,[[[22033,22073,[\"`(f0 ◦ φ(x0i), y0i), L̂(2) 0 (φ, f0) :\\u003d \"]]]]],[22073,22075,[[[22073,22075,[\"1 \"]]]]],[22075,22087,[[[22075,22087,[\"|B2| ∑ i∈B2 \"]]]]],[22087,22108,[[[22087,22108,[\"`(f0 ◦ φ(x0i), y0i). \"]]]]],[22108,22172,[[[22108,22172,[\"We first solve (OPT1) restricted to the first part of the data: \"]]]]],[22172,22201,[[[22172,22201,[\"(φ̂, ω̂) ∈ argmin φ∈Φ,ω∈∆T−1 \"]]]]],[22201,22210,[[[22201,22210,[\"min f0∈F \"]]]]],[22210,22248,[[[22210,22248,[\"L̂(1) 0 (φ, f0) subject to φ ∈ argmin \"]]]]],[22248,22263,[[[22248,22263,[\"ψ∈Φ min {ft}⊂F \"]]]]],[22263,22270,[[[22263,22270,[\"T∑ t\\u003d1 \"]]]]],[22270,22290,[[[22270,22290,[\"ωtL̂t(ψ, ft). (3.6) \"]]]]],[22290,22383,[[[22290,22383,[\"3For two vector-valued functions φ, ψ ∈ Φ, their L2(QX ) distance is (∫ ‖φ(x)− ψ(x)‖2dQX (x) \"]]]]],[22383,22389,[[[22383,22389,[\")1/2. \"]]]]],[22389,22389,[]],[22389,22390,[[[22389,22390,[\" \"]]]]],[22390,22418,[[[22390,22418,[\"Then, we proceed by solving \"]]]]],[22418,22436,[[[22418,22436,[\"f̂0 ∈ argmin f0∈F \"]]]]],[22436,22460,[[[22436,22460,[\"L̂(2) 0 (φ̂, f0). (3.7) \"]]]]],[22460,22901,[[[22460,22901,[\"Such a sample splitting ensures the independence of φ̂ and the second part of target data B2, hence allowing for more transparent theoretical analyses. Such strategies are common in statistics and econometrics literature when the algorithm has delicate dependence structures (see, e.g., (Hansen, 2000; Chernozhukov et al., 2018)). We emphasize that sample splitting is conducted only for theoretical convenience and is not used in practice. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"a7e064b3-5c5d-4bed-88c4-dfc3522f3f86\"]]]]]],[[null,null,0.6307501161406368,[[null,7274,7790]],[[[7274,7650,[[[7274,7650,[\"Let Φ be a collection of representations from the feature space X to some latent space Z ⊆ Rr. We refer to Φ as the representation class. Let F be a collection of task-specific functions from the latent space Z to the label space Y . The complexity of the representation class Φ is usually much larger (i.e., more expressive) than that of the task-specific function class F . \"]]]]],[7650,7790,[[[7650,7790,[\"Given a bounded loss function ` : Y ×Y → [0, 1], the optimal pair of representation and task-specific function of the t-th task is given by \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"eda7f0c0-5d80-48fc-88f7-b08dc0b51b98\"]],[null,null,0.6525237270968838,[[null,19331,20304]],[[[19331,19376,[[[19331,19376,[\"N (Φ;L2(QX ); ε) ≤ (CΦ/ε) νΦ , ∀ε \\u003e 0, (3.3) \"]]]]],[19376,19615,[[[19376,19615,[\"whereN (Φ;L2(QX ); ε) is the L2(QX ) covering number of Φ (i.e., the minimum number of L2(QX ) balls3 with radius ε required to cover Φ). In parallel, there exist CF \\u003e 0, νF \\u003e 0, such that for any probability measure QZ on Z ⊆ Rr, we have \"]]]]],[19615,19660,[[[19615,19660,[\"N (F ;L2(QZ); ε) ≤ (CF/ε) νG , ∀ε \\u003e 0, (3.4) \"]]]]],[19660,19720,[[[19660,19720,[\"where N (F ;L2(QZ); ε) is the L2(QZ) covering number of F . \"]]]]],[19720,20304,[[[19720,20304,[\"Uniform entropy generalizes the notion of Vapnik-Chervonenkis dimension (Vapnik, 2013) and allows us to give a unified treatment of regression and classification problems. For this reason, function classes satisfying the above assumption are also referred to as “VC-type classes” in the literature (Koltchinskii, 2006). In particular, if each coordinate of Φ has VC-subgraph dimension c(Φ), then (3.3) is satisfied with νΦ \\u003d Θ(r · c(Φ)) (recall that r is the dimension of the latent space Z). Similarly, if F has VC-subgraph dimension c(F), then (3.4) is satisfied with νF \\u003d Θ(c(F)). \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"5896a95c-32ac-4fc8-aba8-4049fd2ee34a\"]],[null,null,0.6808876892895416,[[null,22000,22901]],[[[22000,22019,[[[22000,22019,[\"L̂(1) 0 (φ, f0) :\\u003d \"]]]]],[22019,22021,[[[22019,22021,[\"1 \"]]]]],[22021,22033,[[[22021,22033,[\"|B1| ∑ i∈B1 \"]]]]],[22033,22073,[[[22033,22073,[\"`(f0 ◦ φ(x0i), y0i), L̂(2) 0 (φ, f0) :\\u003d \"]]]]],[22073,22075,[[[22073,22075,[\"1 \"]]]]],[22075,22087,[[[22075,22087,[\"|B2| ∑ i∈B2 \"]]]]],[22087,22108,[[[22087,22108,[\"`(f0 ◦ φ(x0i), y0i). \"]]]]],[22108,22172,[[[22108,22172,[\"We first solve (OPT1) restricted to the first part of the data: \"]]]]],[22172,22201,[[[22172,22201,[\"(φ̂, ω̂) ∈ argmin φ∈Φ,ω∈∆T−1 \"]]]]],[22201,22210,[[[22201,22210,[\"min f0∈F \"]]]]],[22210,22248,[[[22210,22248,[\"L̂(1) 0 (φ, f0) subject to φ ∈ argmin \"]]]]],[22248,22263,[[[22248,22263,[\"ψ∈Φ min {ft}⊂F \"]]]]],[22263,22270,[[[22263,22270,[\"T∑ t\\u003d1 \"]]]]],[22270,22290,[[[22270,22290,[\"ωtL̂t(ψ, ft). (3.6) \"]]]]],[22290,22383,[[[22290,22383,[\"3For two vector-valued functions φ, ψ ∈ Φ, their L2(QX ) distance is (∫ ‖φ(x)− ψ(x)‖2dQX (x) \"]]]]],[22383,22389,[[[22383,22389,[\")1/2. \"]]]]],[22389,22389,[]],[22389,22390,[[[22389,22390,[\" \"]]]]],[22390,22418,[[[22390,22418,[\"Then, we proceed by solving \"]]]]],[22418,22436,[[[22418,22436,[\"f̂0 ∈ argmin f0∈F \"]]]]],[22436,22460,[[[22436,22460,[\"L̂(2) 0 (φ̂, f0). (3.7) \"]]]]],[22460,22901,[[[22460,22901,[\"Such a sample splitting ensures the independence of φ̂ and the second part of target data B2, hence allowing for more transparent theoretical analyses. Such strategies are common in statistics and econometrics literature when the algorithm has delicate dependence structures (see, e.g., (Hansen, 2000; Chernozhukov et al., 2018)). We emphasize that sample splitting is conducted only for theoretical convenience and is not used in practice. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"a7e064b3-5c5d-4bed-88c4-dfc3522f3f86\"]]],[[[null,0,253],[0,1]],[[null,253,383],[2]]],[[\"What is TAWT?\",\"Name two NLP tasks.\",\"What is one limitation?\"]]]"]]
9764
[["wrb.fr",null,"[[\"No, Assumption B does not explicitly state that $\\\\phi$ must be a vector. However, it defines $\\\\Phi$ as a collection of representations from the feature space X to some latent space Z, where Z is a subset of an r-dimensional real space ($Z \\\\subseteq Rr$) [1, 2]. Additionally, footnote 3 clarifies that for two functions $\\\\phi, \\\\psi \\\\in \\\\Phi$, they are described as \\\"vector-valued functions\\\" [3]. This implies that the output of $\\\\phi$ is a vector.\",null,[\"93d1bd49-f02e-445c-baf6-9dbfeb9d45aa\",\"858fcd3d-2c27-4c57-82ce-c3391ef013db\",2681741471],null,[[[[0,436,[[[0,436,[\"No, Assumption B does not explicitly state that $\\\\phi$ must be a vector. However, it defines $\\\\Phi$ as a collection of representations from the feature space X to some latent space Z, where Z is a subset of an r-dimensional real space ($Z \\\\subseteq Rr$). Additionally, footnote 3 clarifies that for two functions $\\\\phi, \\\\psi \\\\in \\\\Phi$, they are described as \\\"vector-valued functions\\\". This implies that the output of $\\\\phi$ is a vector.\"]]]]]],[[[\"eda7f0c0-5d80-48fc-88f7-b08dc0b51b98\"],[null,0,253]],[[\"5896a95c-32ac-4fc8-aba8-4049fd2ee34a\"],[null,0,253]],[[\"a7e064b3-5c5d-4bed-88c4-dfc3522f3f86\"],[null,253,383]]]],null,null,[[[\"eda7f0c0-5d80-48fc-88f7-b08dc0b51b98\"],[null,null,0.6307501161406368,[[null,7274,7790]],[[[7274,7650,[[[7274,7650,[\"Let Φ be a collection of representations from the feature space X to some latent space Z ⊆ Rr. We refer to Φ as the representation class. Let F be a collection of task-specific functions from the latent space Z to the label space Y . The complexity of the representation class Φ is usually much larger (i.e., more expressive) than that of the task-specific function class F . \"]]]]],[7650,7790,[[[7650,7790,[\"Given a bounded loss function ` : Y ×Y → [0, 1], the optimal pair of representation and task-specific function of the t-th task is given by \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"eda7f0c0-5d80-48fc-88f7-b08dc0b51b98\"]]],[[\"5896a95c-32ac-4fc8-aba8-4049fd2ee34a\"],[null,null,0.6525237270968838,[[null,19331,20304]],[[[19331,19376,[[[19331,19376,[\"N (Φ;L2(QX ); ε) ≤ (CΦ/ε) νΦ , ∀ε \\u003e 0, (3.3) \"]]]]],[19376,19615,[[[19376,19615,[\"whereN (Φ;L2(QX ); ε) is the L2(QX ) covering number of Φ (i.e., the minimum number of L2(QX ) balls3 with radius ε required to cover Φ). In parallel, there exist CF \\u003e 0, νF \\u003e 0, such that for any probability measure QZ on Z ⊆ Rr, we have \"]]]]],[19615,19660,[[[19615,19660,[\"N (F ;L2(QZ); ε) ≤ (CF/ε) νG , ∀ε \\u003e 0, (3.4) \"]]]]],[19660,19720,[[[19660,19720,[\"where N (F ;L2(QZ); ε) is the L2(QZ) covering number of F . \"]]]]],[19720,20304,[[[19720,20304,[\"Uniform entropy generalizes the notion of Vapnik-Chervonenkis dimension (Vapnik, 2013) and allows us to give a unified treatment of regression and classification problems. For this reason, function classes satisfying the above assumption are also referred to as “VC-type classes” in the literature (Koltchinskii, 2006). In particular, if each coordinate of Φ has VC-subgraph dimension c(Φ), then (3.3) is satisfied with νΦ \\u003d Θ(r · c(Φ)) (recall that r is the dimension of the latent space Z). Similarly, if F has VC-subgraph dimension c(F), then (3.4) is satisfied with νF \\u003d Θ(c(F)). \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"5896a95c-32ac-4fc8-aba8-4049fd2ee34a\"]]],[[\"a7e064b3-5c5d-4bed-88c4-dfc3522f3f86\"],[null,null,0.6808876892895416,[[null,22000,22901]],[[[22000,22019,[[[22000,22019,[\"L̂(1) 0 (φ, f0) :\\u003d \"]]]]],[22019,22021,[[[22019,22021,[\"1 \"]]]]],[22021,22033,[[[22021,22033,[\"|B1| ∑ i∈B1 \"]]]]],[22033,22073,[[[22033,22073,[\"`(f0 ◦ φ(x0i), y0i), L̂(2) 0 (φ, f0) :\\u003d \"]]]]],[22073,22075,[[[22073,22075,[\"1 \"]]]]],[22075,22087,[[[22075,22087,[\"|B2| ∑ i∈B2 \"]]]]],[22087,22108,[[[22087,22108,[\"`(f0 ◦ φ(x0i), y0i). \"]]]]],[22108,22172,[[[22108,22172,[\"We first solve (OPT1) restricted to the first part of the data: \"]]]]],[22172,22201,[[[22172,22201,[\"(φ̂, ω̂) ∈ argmin φ∈Φ,ω∈∆T−1 \"]]]]],[22201,22210,[[[22201,22210,[\"min f0∈F \"]]]]],[22210,22248,[[[22210,22248,[\"L̂(1) 0 (φ, f0) subject to φ ∈ argmin \"]]]]],[22248,22263,[[[22248,22263,[\"ψ∈Φ min {ft}⊂F \"]]]]],[22263,22270,[[[22263,22270,[\"T∑ t\\u003d1 \"]]]]],[22270,22290,[[[22270,22290,[\"ωtL̂t(ψ, ft). (3.6) \"]]]]],[22290,22383,[[[22290,22383,[\"3For two vector-valued functions φ, ψ ∈ Φ, their L2(QX ) distance is (∫ ‖φ(x)− ψ(x)‖2dQX (x) \"]]]]],[22383,22389,[[[22383,22389,[\")1/2. \"]]]]],[22389,22389,[]],[22389,22390,[[[22389,22390,[\" \"]]]]],[22390,22418,[[[22390,22418,[\"Then, we proceed by solving \"]]]]],[22418,22436,[[[22418,22436,[\"f̂0 ∈ argmin f0∈F \"]]]]],[22436,22460,[[[22436,22460,[\"L̂(2) 0 (φ̂, f0). (3.7) \"]]]]],[22460,22901,[[[22460,22901,[\"Such a sample splitting ensures the independence of φ̂ and the second part of target data B2, hence allowing for more transparent theoretical analyses. Such strategies are common in statistics and econometrics literature when the algorithm has delicate dependence structures (see, e.g., (Hansen, 2000; Chernozhukov et al., 2018)). We emphasize that sample splitting is conducted only for theoretical convenience and is not used in practice. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"a7e064b3-5c5d-4bed-88c4-dfc3522f3f86\"]]]]]],[[null,null,0.6307501161406368,[[null,7274,7790]],[[[7274,7650,[[[7274,7650,[\"Let Φ be a collection of representations from the feature space X to some latent space Z ⊆ Rr. We refer to Φ as the representation class. Let F be a collection of task-specific functions from the latent space Z to the label space Y . The complexity of the representation class Φ is usually much larger (i.e., more expressive) than that of the task-specific function class F . \"]]]]],[7650,7790,[[[7650,7790,[\"Given a bounded loss function ` : Y ×Y → [0, 1], the optimal pair of representation and task-specific function of the t-th task is given by \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"eda7f0c0-5d80-48fc-88f7-b08dc0b51b98\"]],[null,null,0.6525237270968838,[[null,19331,20304]],[[[19331,19376,[[[19331,19376,[\"N (Φ;L2(QX ); ε) ≤ (CΦ/ε) νΦ , ∀ε \\u003e 0, (3.3) \"]]]]],[19376,19615,[[[19376,19615,[\"whereN (Φ;L2(QX ); ε) is the L2(QX ) covering number of Φ (i.e., the minimum number of L2(QX ) balls3 with radius ε required to cover Φ). In parallel, there exist CF \\u003e 0, νF \\u003e 0, such that for any probability measure QZ on Z ⊆ Rr, we have \"]]]]],[19615,19660,[[[19615,19660,[\"N (F ;L2(QZ); ε) ≤ (CF/ε) νG , ∀ε \\u003e 0, (3.4) \"]]]]],[19660,19720,[[[19660,19720,[\"where N (F ;L2(QZ); ε) is the L2(QZ) covering number of F . \"]]]]],[19720,20304,[[[19720,20304,[\"Uniform entropy generalizes the notion of Vapnik-Chervonenkis dimension (Vapnik, 2013) and allows us to give a unified treatment of regression and classification problems. For this reason, function classes satisfying the above assumption are also referred to as “VC-type classes” in the literature (Koltchinskii, 2006). In particular, if each coordinate of Φ has VC-subgraph dimension c(Φ), then (3.3) is satisfied with νΦ \\u003d Θ(r · c(Φ)) (recall that r is the dimension of the latent space Z). Similarly, if F has VC-subgraph dimension c(F), then (3.4) is satisfied with νF \\u003d Θ(c(F)). \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"5896a95c-32ac-4fc8-aba8-4049fd2ee34a\"]],[null,null,0.6808876892895416,[[null,22000,22901]],[[[22000,22019,[[[22000,22019,[\"L̂(1) 0 (φ, f0) :\\u003d \"]]]]],[22019,22021,[[[22019,22021,[\"1 \"]]]]],[22021,22033,[[[22021,22033,[\"|B1| ∑ i∈B1 \"]]]]],[22033,22073,[[[22033,22073,[\"`(f0 ◦ φ(x0i), y0i), L̂(2) 0 (φ, f0) :\\u003d \"]]]]],[22073,22075,[[[22073,22075,[\"1 \"]]]]],[22075,22087,[[[22075,22087,[\"|B2| ∑ i∈B2 \"]]]]],[22087,22108,[[[22087,22108,[\"`(f0 ◦ φ(x0i), y0i). \"]]]]],[22108,22172,[[[22108,22172,[\"We first solve (OPT1) restricted to the first part of the data: \"]]]]],[22172,22201,[[[22172,22201,[\"(φ̂, ω̂) ∈ argmin φ∈Φ,ω∈∆T−1 \"]]]]],[22201,22210,[[[22201,22210,[\"min f0∈F \"]]]]],[22210,22248,[[[22210,22248,[\"L̂(1) 0 (φ, f0) subject to φ ∈ argmin \"]]]]],[22248,22263,[[[22248,22263,[\"ψ∈Φ min {ft}⊂F \"]]]]],[22263,22270,[[[22263,22270,[\"T∑ t\\u003d1 \"]]]]],[22270,22290,[[[22270,22290,[\"ωtL̂t(ψ, ft). (3.6) \"]]]]],[22290,22383,[[[22290,22383,[\"3For two vector-valued functions φ, ψ ∈ Φ, their L2(QX ) distance is (∫ ‖φ(x)− ψ(x)‖2dQX (x) \"]]]]],[22383,22389,[[[22383,22389,[\")1/2. \"]]]]],[22389,22389,[]],[22389,22390,[[[22389,22390,[\" \"]]]]],[22390,22418,[[[22390,22418,[\"Then, we proceed by solving \"]]]]],[22418,22436,[[[22418,22436,[\"f̂0 ∈ argmin f0∈F \"]]]]],[22436,22460,[[[22436,22460,[\"L̂(2) 0 (φ̂, f0). (3.7) \"]]]]],[22460,22901,[[[22460,22901,[\"Such a sample splitting ensures the independence of φ̂ and the second part of target data B2, hence allowing for more transparent theoretical analyses. Such strategies are common in statistics and econometrics literature when the algorithm has delicate dependence structures (see, e.g., (Hansen, 2000; Chernozhukov et al., 2018)). We emphasize that sample splitting is conducted only for theoretical convenience and is not used in practice. \"]]]]]]],[[[\"bfbc68f7-b5c1-437e-abee-adc72503c48d\"],\"f4a2a98c-bdad-497f-9c07-3b561ab8f59d\"]],[\"a7e064b3-5c5d-4bed-88c4-dfc3522f3f86\"]]],[[[null,0,253],[0,1]],[[null,253,383],[2]]],[[\"What is TAWT?\",\"Name two NLP tasks.\",\"What is one limitation?\"]]]"]]
59
[["di",4161],["af.httprm",4160,"5788183378148938575",55]]
27
[["e",6,null,null,20092]]
