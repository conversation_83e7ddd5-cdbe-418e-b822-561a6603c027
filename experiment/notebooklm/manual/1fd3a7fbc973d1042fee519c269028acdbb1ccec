)]}'

5282
[["wrb.fr",null,"[[\"The antecedent `verb-nsubj:noun-obj:noun` is included in the sum in formula #6 because the addition of properties in this framework calculates the value of a **composite property** [1]. This addition expresses the truth value for **any combination of several alternative constructions being present in a language** [2]. Specifically, for <PERSON>'s universals, the framework denotes \\\"p is an alternative word order of q\\\" as `v`p + v`q` [2]. Therefore, `(verb-nsubj:noun-obj:noun + nsubj:noun-verb-obj:noun)` represents\",null,[\"2e830676-0b40-442a-b6d0-899aa42f2dfc\",\"a5599ae1-b917-42e6-a64c-7468f3b844d3\",**********],null,[[null,[[[\"a3425f3d-3d47-4d82-b16f-6167e90c21f1\"],[null,174,174]],[[\"c6502ec6-0ec3-424d-95e3-96884b7cc132\"],[null,300,300]],[[\"c6502ec6-0ec3-424d-95e3-96884b7cc132\"],[null,414,414]]]],null,null,[[[\"a3425f3d-3d47-4d82-b16f-6167e90c21f1\"],[null,null,0.****************,[[null,19581,20542]],[[[19581,19971,[[[19581,19971,[\"7Addition is not a logical connective but used here to calculate the value of properties which cannot be directly extracted from the UD treebanks. For example, the degree to which a language has S-O order is the percentage occupied by V-S-O, S-V-O and S-O-V constructions. Thus, the value v`nsubj:noun-obj:noun can be calculated as v`verb-nsubj:noun-obj:noun + v`nsubj:noun-verb-obj:noun + \"]]]]],[19971,20542,[[[19971,20542,[\"v`nsubj:noun-obj:noun-verb. Addition is restricted to variables that are mutually exclusive, i.e. that are calculated with the same de-nominator (compare eq. (7)). That said, (out-of-logic) addition in our framework is comparable to addition of probabilities of disjoint events in probability theory; it is not as powerful and does not serve the same purpose as (in-logic) addition (“strong dis-junction”) in certain real-valued logics, e.g. Łukasiewicz logic (Łukasiewicz and Tarski (1930); cf. Bergmann (2008, p. 179)), where it is possible to add arbitrary variables. \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"a3425f3d-3d47-4d82-b16f-6167e90c21f1\"]]],[[\"c6502ec6-0ec3-424d-95e3-96884b7cc132\"],[null,null,0.6685101790222447,[[null,20542,21157]],[[[20542,21157,[[[20542,21157,[\"The result of an addition thus expresses the truth value for any (combination) of several alternative constructions being present in a language. Greenberg (1963), as others, uses the terms “dominant” and “alternative” to indicate two degrees of relative frequency of word orders. This binary distinction cannot be directly expressed with UD variables because they already express infinitely many degrees of relative frequency. We denote “p is a dominant word order in `” as v`p and “p is an alternative word order of q in `” as v`p + v`q , since we think that these are the most appropriate real-valued equivalents.\"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"c6502ec6-0ec3-424d-95e3-96884b7cc132\"]]]]]],[[null,null,0.****************,[[null,19581,20542]],[[[19581,19971,[[[19581,19971,[\"7Addition is not a logical connective but used here to calculate the value of properties which cannot be directly extracted from the UD treebanks. For example, the degree to which a language has S-O order is the percentage occupied by V-S-O, S-V-O and S-O-V constructions. Thus, the value v`nsubj:noun-obj:noun can be calculated as v`verb-nsubj:noun-obj:noun + v`nsubj:noun-verb-obj:noun + \"]]]]],[19971,20542,[[[19971,20542,[\"v`nsubj:noun-obj:noun-verb. Addition is restricted to variables that are mutually exclusive, i.e. that are calculated with the same de-nominator (compare eq. (7)). That said, (out-of-logic) addition in our framework is comparable to addition of probabilities of disjoint events in probability theory; it is not as powerful and does not serve the same purpose as (in-logic) addition (“strong dis-junction”) in certain real-valued logics, e.g. Łukasiewicz logic (Łukasiewicz and Tarski (1930); cf. Bergmann (2008, p. 179)), where it is possible to add arbitrary variables. \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"a3425f3d-3d47-4d82-b16f-6167e90c21f1\"]],[null,null,0.6685101790222447,[[null,20542,21157]],[[[20542,21157,[[[20542,21157,[\"The result of an addition thus expresses the truth value for any (combination) of several alternative constructions being present in a language. Greenberg (1963), as others, uses the terms “dominant” and “alternative” to indicate two degrees of relative frequency of word orders. This binary distinction cannot be directly expressed with UD variables because they already express infinitely many degrees of relative frequency. We denote “p is a dominant word order in `” as v`p and “p is an alternative word order of q in `” as v`p + v`q , since we think that these are the most appropriate real-valued equivalents.\"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"c6502ec6-0ec3-424d-95e3-96884b7cc132\"]]],[[[null,174,174],[0]],[[null,300,300],[1]],[[null,414,414],[1]]],[[\"How are typological statements represented?\",\"What is a single-link property?\",\"Why is phylogenetic weighting used?\"]]]"]]
5446
[["wrb.fr",null,"[[\"The antecedent `verb-nsubj:noun-obj:noun` is included in the sum in formula #6 because the addition of properties in this framework calculates the value of a **composite property** [1]. This addition expresses the truth value for **any combination of several alternative constructions being present in a language** [2]. Specifically, for Greenberg's universals, the framework denotes \\\"p is an alternative word order of q\\\" as `v`p + v`q` [2]. Therefore, `(verb-nsubj:noun-obj:noun + nsubj:noun-verb-obj:noun)` represents the combined presence of VSO and SVO word orders as alternative constructions [2].\",null,[\"2e830676-0b40-442a-b6d0-899aa42f2dfc\",\"a5599ae1-b917-42e6-a64c-7468f3b844d3\",**********],null,[[null,[[[\"741e052f-5905-4827-aaff-32c7c6cd6516\"],[null,174,174]],[[\"186f9c88-2cc3-4dab-b181-ac58beb819af\"],[null,300,300]],[[\"186f9c88-2cc3-4dab-b181-ac58beb819af\"],[null,414,414]],[[\"186f9c88-2cc3-4dab-b181-ac58beb819af\"],[null,481,569]]]],null,null,[[[\"741e052f-5905-4827-aaff-32c7c6cd6516\"],[null,null,0.****************,[[null,19581,20542]],[[[19581,19971,[[[19581,19971,[\"7Addition is not a logical connective but used here to calculate the value of properties which cannot be directly extracted from the UD treebanks. For example, the degree to which a language has S-O order is the percentage occupied by V-S-O, S-V-O and S-O-V constructions. Thus, the value v`nsubj:noun-obj:noun can be calculated as v`verb-nsubj:noun-obj:noun + v`nsubj:noun-verb-obj:noun + \"]]]]],[19971,20542,[[[19971,20542,[\"v`nsubj:noun-obj:noun-verb. Addition is restricted to variables that are mutually exclusive, i.e. that are calculated with the same de-nominator (compare eq. (7)). That said, (out-of-logic) addition in our framework is comparable to addition of probabilities of disjoint events in probability theory; it is not as powerful and does not serve the same purpose as (in-logic) addition (“strong dis-junction”) in certain real-valued logics, e.g. Łukasiewicz logic (Łukasiewicz and Tarski (1930); cf. Bergmann (2008, p. 179)), where it is possible to add arbitrary variables. \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"741e052f-5905-4827-aaff-32c7c6cd6516\"]]],[[\"186f9c88-2cc3-4dab-b181-ac58beb819af\"],[null,null,0.6685101790222447,[[null,20542,21157]],[[[20542,21157,[[[20542,21157,[\"The result of an addition thus expresses the truth value for any (combination) of several alternative constructions being present in a language. Greenberg (1963), as others, uses the terms “dominant” and “alternative” to indicate two degrees of relative frequency of word orders. This binary distinction cannot be directly expressed with UD variables because they already express infinitely many degrees of relative frequency. We denote “p is a dominant word order in `” as v`p and “p is an alternative word order of q in `” as v`p + v`q , since we think that these are the most appropriate real-valued equivalents.\"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"186f9c88-2cc3-4dab-b181-ac58beb819af\"]]]]]],[[null,null,0.****************,[[null,19581,20542]],[[[19581,19971,[[[19581,19971,[\"7Addition is not a logical connective but used here to calculate the value of properties which cannot be directly extracted from the UD treebanks. For example, the degree to which a language has S-O order is the percentage occupied by V-S-O, S-V-O and S-O-V constructions. Thus, the value v`nsubj:noun-obj:noun can be calculated as v`verb-nsubj:noun-obj:noun + v`nsubj:noun-verb-obj:noun + \"]]]]],[19971,20542,[[[19971,20542,[\"v`nsubj:noun-obj:noun-verb. Addition is restricted to variables that are mutually exclusive, i.e. that are calculated with the same de-nominator (compare eq. (7)). That said, (out-of-logic) addition in our framework is comparable to addition of probabilities of disjoint events in probability theory; it is not as powerful and does not serve the same purpose as (in-logic) addition (“strong dis-junction”) in certain real-valued logics, e.g. Łukasiewicz logic (Łukasiewicz and Tarski (1930); cf. Bergmann (2008, p. 179)), where it is possible to add arbitrary variables. \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"741e052f-5905-4827-aaff-32c7c6cd6516\"]],[null,null,0.6685101790222447,[[null,20542,21157]],[[[20542,21157,[[[20542,21157,[\"The result of an addition thus expresses the truth value for any (combination) of several alternative constructions being present in a language. Greenberg (1963), as others, uses the terms “dominant” and “alternative” to indicate two degrees of relative frequency of word orders. This binary distinction cannot be directly expressed with UD variables because they already express infinitely many degrees of relative frequency. We denote “p is a dominant word order in `” as v`p and “p is an alternative word order of q in `” as v`p + v`q , since we think that these are the most appropriate real-valued equivalents.\"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"186f9c88-2cc3-4dab-b181-ac58beb819af\"]]],[[[null,174,174],[0]],[[null,300,300],[1]],[[null,414,414],[1]],[[null,481,569],[1]]],[[\"How are typological statements represented?\",\"What is a single-link property?\",\"Why is phylogenetic weighting used?\"]]]"]]
6428
[["wrb.fr",null,"[[\"The antecedent `verb-nsubj:noun-obj:noun` is included in the sum in formula #6 because the addition of properties in this framework calculates the value of a **composite property** [1]. This addition expresses the truth value for **any combination of several alternative constructions being present in a language** [2]. Specifically, for Greenberg's universals, the framework denotes \\\"p is an alternative word order of q\\\" as `v`p + v`q` [2]. Therefore, `(verb-nsubj:noun-obj:noun + nsubj:noun-verb-obj:noun)` represents the combined presence of VSO and SVO word orders as alternative constructions [2].\",null,[\"2e830676-0b40-442a-b6d0-899aa42f2dfc\",\"a5599ae1-b917-42e6-a64c-7468f3b844d3\",**********],null,[[[[0,570,[[[0,15,[\"The antecedent \"]],[15,39,[\"verb-nsubj:noun-obj:noun\",[null,null,null,null,null,null,null,true]]],[39,156,[\" is included in the sum in formula #6 because the addition of properties in this framework calculates the value of a \"]],[156,174,[\"composite property\",[true]]],[174,220,[\". This addition expresses the truth value for \"]],[220,300,[\"any combination of several alternative constructions being present in a language\",[true]]],[300,407,[\". Specifically, for Greenberg's universals, the framework denotes \\\"p is an alternative word order of q\\\" as \"]],[407,408,[\"v\",[null,null,null,null,null,null,null,true]]],[408,413,[\"p + v\"]],[413,414,[\"q\",[null,null,null,null,null,null,null,true]]],[414,427,[\". Therefore, \"]],[427,480,[\"(verb-nsubj:noun-obj:noun + nsubj:noun-verb-obj:noun)\",[null,null,null,null,null,null,null,true]]],[480,570,[\" represents the combined presence of VSO and SVO word orders as alternative constructions.\"]]]]]],[[[\"741e052f-5905-4827-aaff-32c7c6cd6516\"],[null,174,174]],[[\"186f9c88-2cc3-4dab-b181-ac58beb819af\"],[null,300,300]],[[\"186f9c88-2cc3-4dab-b181-ac58beb819af\"],[null,414,414]],[[\"186f9c88-2cc3-4dab-b181-ac58beb819af\"],[null,481,569]]]],null,null,[[[\"741e052f-5905-4827-aaff-32c7c6cd6516\"],[null,null,0.****************,[[null,19581,20542]],[[[19581,19971,[[[19581,19971,[\"7Addition is not a logical connective but used here to calculate the value of properties which cannot be directly extracted from the UD treebanks. For example, the degree to which a language has S-O order is the percentage occupied by V-S-O, S-V-O and S-O-V constructions. Thus, the value v`nsubj:noun-obj:noun can be calculated as v`verb-nsubj:noun-obj:noun + v`nsubj:noun-verb-obj:noun + \"]]]]],[19971,20542,[[[19971,20542,[\"v`nsubj:noun-obj:noun-verb. Addition is restricted to variables that are mutually exclusive, i.e. that are calculated with the same de-nominator (compare eq. (7)). That said, (out-of-logic) addition in our framework is comparable to addition of probabilities of disjoint events in probability theory; it is not as powerful and does not serve the same purpose as (in-logic) addition (“strong dis-junction”) in certain real-valued logics, e.g. Łukasiewicz logic (Łukasiewicz and Tarski (1930); cf. Bergmann (2008, p. 179)), where it is possible to add arbitrary variables. \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"741e052f-5905-4827-aaff-32c7c6cd6516\"]]],[[\"186f9c88-2cc3-4dab-b181-ac58beb819af\"],[null,null,0.6685101790222447,[[null,20542,21157]],[[[20542,21157,[[[20542,21157,[\"The result of an addition thus expresses the truth value for any (combination) of several alternative constructions being present in a language. Greenberg (1963), as others, uses the terms “dominant” and “alternative” to indicate two degrees of relative frequency of word orders. This binary distinction cannot be directly expressed with UD variables because they already express infinitely many degrees of relative frequency. We denote “p is a dominant word order in `” as v`p and “p is an alternative word order of q in `” as v`p + v`q , since we think that these are the most appropriate real-valued equivalents.\"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"186f9c88-2cc3-4dab-b181-ac58beb819af\"]]]]]],[[null,null,0.****************,[[null,19581,20542]],[[[19581,19971,[[[19581,19971,[\"7Addition is not a logical connective but used here to calculate the value of properties which cannot be directly extracted from the UD treebanks. For example, the degree to which a language has S-O order is the percentage occupied by V-S-O, S-V-O and S-O-V constructions. Thus, the value v`nsubj:noun-obj:noun can be calculated as v`verb-nsubj:noun-obj:noun + v`nsubj:noun-verb-obj:noun + \"]]]]],[19971,20542,[[[19971,20542,[\"v`nsubj:noun-obj:noun-verb. Addition is restricted to variables that are mutually exclusive, i.e. that are calculated with the same de-nominator (compare eq. (7)). That said, (out-of-logic) addition in our framework is comparable to addition of probabilities of disjoint events in probability theory; it is not as powerful and does not serve the same purpose as (in-logic) addition (“strong dis-junction”) in certain real-valued logics, e.g. Łukasiewicz logic (Łukasiewicz and Tarski (1930); cf. Bergmann (2008, p. 179)), where it is possible to add arbitrary variables. \"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"741e052f-5905-4827-aaff-32c7c6cd6516\"]],[null,null,0.6685101790222447,[[null,20542,21157]],[[[20542,21157,[[[20542,21157,[\"The result of an addition thus expresses the truth value for any (combination) of several alternative constructions being present in a language. Greenberg (1963), as others, uses the terms “dominant” and “alternative” to indicate two degrees of relative frequency of word orders. This binary distinction cannot be directly expressed with UD variables because they already express infinitely many degrees of relative frequency. We denote “p is a dominant word order in `” as v`p and “p is an alternative word order of q in `” as v`p + v`q , since we think that these are the most appropriate real-valued equivalents.\"]]]]]]],[[[\"70a5a67a-7c91-49db-83b8-d0fa58883e87\"],\"efa6b430-6a36-400a-a2bf-6df73d66fb99\"]],[\"186f9c88-2cc3-4dab-b181-ac58beb819af\"]]],[[[null,174,174],[0]],[[null,300,300],[1]],[[null,414,414],[1]],[[null,481,569],[1]]],[[\"How are typological statements represented?\",\"What is a single-link property?\",\"Why is phylogenetic weighting used?\"]]]"]]
60
[["di",14869],["af.httprm",14869,"598660255977714464",58]]
27
[["e",6,null,null,17397]]
