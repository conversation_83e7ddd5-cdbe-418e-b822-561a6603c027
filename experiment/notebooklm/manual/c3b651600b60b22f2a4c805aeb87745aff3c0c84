)]}'

9349
[["wrb.fr",null,"[[\"The full name of the proposed method \\\"REDQ\\\" is **Randomized ensembled double Q-learning** [1-3].\",null,[\"ecad392e-635d-443c-bf8a-af759409832e\",\"a7ca2eda-7824-409d-a8f4-249a05ad3519\",629017990],null,[[null,[[[\"0f2fb2ee-4e4b-4825-8af6-60bc597d2f17\"],[null,85,85]],[[\"0a3716db-b8b3-41f3-ac75-364c3ecaea37\"],[null,85,85]],[[\"dd860d40-9a3a-4051-a613-e986f837ecbe\"],[null,85,85]]]],null,null,[[[\"0f2fb2ee-4e4b-4825-8af6-60bc597d2f17\"],[null,null,0.693120613394453,[[null,0,1415]],[[[0,1,[[[0,1,[\" \"]]]]],[1,21,[[[1,21,[\"DROPOUT Q-FUNCTIONS \"]]],[null,6]]],[21,65,[[[21,65,[\"FOR DOUBLY EFFICIENT REINFORCEMENT LEARNING \"]]],[null,6]]],[65,170,[[[65,170,[\"Takuya Hiraoka 1,2, Takahisa Imagawa 2, Taisei Hashimoto 2,3, Takashi Onishi 1,2, Yoshimasa Tsuruoka 2,3 \",[null,true]]]]]],[170,400,[[[170,400,[\"1 NEC Corporation 2 National Institute of Advanced Industrial Science and Technology 3 The University of Tokyo {takuya-h1, takashi.onishi}@nec.com, <EMAIL>, {<EMAIL>, tsuruoka@logos.t}.u-tokyo.ac.jp \"]]]]],[400,409,[[[400,409,[\"ABSTRACT \"]]]]],[409,1415,[[[409,1415,[\"Randomized ensembled double Q-learning (REDQ) (Chen et al., 2021b) has re-cently achieved state-of-the-art sample efficiency on continuous-action reinforce-ment learning benchmarks. This superior sample efficiency is made possible by using a large Q-function ensemble. However, REDQ is much less com-putationally efficient than non-ensemble counterparts such as Soft Actor-Critic (SAC) (Haarnoja et al., 2018a). To make REDQ more computationally efficient, we propose a method of improving computational efficiency called DroQ, which is a variant of REDQ that uses a small ensemble of dropout Q-functions. Our dropout Q-functions are simple Q-functions equipped with dropout connection and layer normalization. Despite its simplicity of implementation, our experimen-tal results indicate that DroQ is doubly (sample and computationally) efficient. It achieved comparable sample efficiency with REDQ, much better computational efficiency than REDQ, and comparable computational efficiency with that of SAC. \"]]]]]]],[[[\"cc3427ac-1b38-4c36-8131-7a43fc70ae9e\"],\"acc4b258-05aa-47b3-9093-9ffb68668838\"]],[\"0f2fb2ee-4e4b-4825-8af6-60bc597d2f17\"]]],[[\"0a3716db-b8b3-41f3-ac75-364c3ecaea37\"],[null,null,0.6507746774722999,[[null,3037,3604]],[[[3037,3604,[[[3037,3604,[\"With such methods, randomized ensembled double Q-learning (REDQ) proposed by Chen et al. (2021b) is currently the most sample-efficient method for the MuJoCo benchmark. REDQ uses a high UTD ratio and large ensemble of Q-functions. The use of a high UTD ratio increases an estima-tion bias in policy evaluation, which degrades sample-efficient learning. REDQ uses an ensemble of Q-functions to suppress the estimation bias and improve sample efficiency. Chen et al. (2021b) demonstrated that the sample efficiency of REDQ is equal to or even better than that of MBPO. \"]]]]]]],[[[\"cc3427ac-1b38-4c36-8131-7a43fc70ae9e\"],\"acc4b258-05aa-47b3-9093-9ffb68668838\"]],[\"0a3716db-b8b3-41f3-ac75-364c3ecaea37\"]]],[[\"dd860d40-9a3a-4051-a613-e986f837ecbe\"],[null,null,0.6753162888887578,[[null,8804,10084]],[[[8804,8811,[[[8804,8811,[\"N∑ i\\u003d1 \",[null,true]]]]]],[8811,8836,[[[8811,8836,[\"Qϕi(s, a)− α log πθ(a|s) \",[null,true]]]]]],[8836,8852,[[[8836,8852,[\") , a ∼ πθ(·|s) \",[null,true]]]]]],[8852,8902,[[[8852,8902,[\"2.2 RANDOMIZED ENSEMBLED DOUBLE Q-LEARNING (REDQ) \"]]]]],[8902,10084,[[[8902,10084,[\"REDQ (Chen et al., 2021b) is a sample-efficient model-free method for solving maximum-entropy RL problems (Algorithm 1). It has two primary components to achieve high sample efficiency. 1. High UTD ratio: It uses a high UTD ratio G, which is the number of updates (lines 4–10) taken by the agent compared to the number of actual interactions with the environment (line 3). The high UTD ratio promotes sufficient training of Q-functions within a few interactions. However, this also increases an overestimation bias in the Q-function training, which degrades sample-efficient learn-ing (Chen et al., 2021b). 2. Ensemble of Q-functions: To reduce the overestimation bias, it uses an ensemble of N Q-functions for the target to be minimized (lines 6–7). Specifically, a random subset M of the en-semble is selected (line 6) then used for calculating the target (line 7). The size of the subset M is kept fixed and is denoted as M . In addition, each Q-function in the ensemble is randomly and independently initialized but updated with the same target (lines 8–9). Chen et al. (2021b) showed that using a large ensemble (N \\u003d 10) and small subset (M \\u003d 2) successfully reduces the bias. \",[null,true]]]]]]]],[[[\"cc3427ac-1b38-4c36-8131-7a43fc70ae9e\"],\"acc4b258-05aa-47b3-9093-9ffb68668838\"]],[\"dd860d40-9a3a-4051-a613-e986f837ecbe\"]]]]]],[[null,null,0.693120613394453,[[null,0,1415]],[[[0,1,[[[0,1,[\" \"]]]]],[1,21,[[[1,21,[\"DROPOUT Q-FUNCTIONS \"]]],[null,6]]],[21,65,[[[21,65,[\"FOR DOUBLY EFFICIENT REINFORCEMENT LEARNING \"]]],[null,6]]],[65,170,[[[65,170,[\"Takuya Hiraoka 1,2, Takahisa Imagawa 2, Taisei Hashimoto 2,3, Takashi Onishi 1,2, Yoshimasa Tsuruoka 2,3 \",[null,true]]]]]],[170,400,[[[170,400,[\"1 NEC Corporation 2 National Institute of Advanced Industrial Science and Technology 3 The University of Tokyo {takuya-h1, takashi.onishi}@nec.com, <EMAIL>, {<EMAIL>, tsuruoka@logos.t}.u-tokyo.ac.jp \"]]]]],[400,409,[[[400,409,[\"ABSTRACT \"]]]]],[409,1415,[[[409,1415,[\"Randomized ensembled double Q-learning (REDQ) (Chen et al., 2021b) has re-cently achieved state-of-the-art sample efficiency on continuous-action reinforce-ment learning benchmarks. This superior sample efficiency is made possible by using a large Q-function ensemble. However, REDQ is much less com-putationally efficient than non-ensemble counterparts such as Soft Actor-Critic (SAC) (Haarnoja et al., 2018a). To make REDQ more computationally efficient, we propose a method of improving computational efficiency called DroQ, which is a variant of REDQ that uses a small ensemble of dropout Q-functions. Our dropout Q-functions are simple Q-functions equipped with dropout connection and layer normalization. Despite its simplicity of implementation, our experimen-tal results indicate that DroQ is doubly (sample and computationally) efficient. It achieved comparable sample efficiency with REDQ, much better computational efficiency than REDQ, and comparable computational efficiency with that of SAC. \"]]]]]]],[[[\"cc3427ac-1b38-4c36-8131-7a43fc70ae9e\"],\"acc4b258-05aa-47b3-9093-9ffb68668838\"]],[\"0f2fb2ee-4e4b-4825-8af6-60bc597d2f17\"]],[null,null,0.6507746774722999,[[null,3037,3604]],[[[3037,3604,[[[3037,3604,[\"With such methods, randomized ensembled double Q-learning (REDQ) proposed by Chen et al. (2021b) is currently the most sample-efficient method for the MuJoCo benchmark. REDQ uses a high UTD ratio and large ensemble of Q-functions. The use of a high UTD ratio increases an estima-tion bias in policy evaluation, which degrades sample-efficient learning. REDQ uses an ensemble of Q-functions to suppress the estimation bias and improve sample efficiency. Chen et al. (2021b) demonstrated that the sample efficiency of REDQ is equal to or even better than that of MBPO. \"]]]]]]],[[[\"cc3427ac-1b38-4c36-8131-7a43fc70ae9e\"],\"acc4b258-05aa-47b3-9093-9ffb68668838\"]],[\"0a3716db-b8b3-41f3-ac75-364c3ecaea37\"]],[null,null,0.6753162888887578,[[null,8804,10084]],[[[8804,8811,[[[8804,8811,[\"N∑ i\\u003d1 \",[null,true]]]]]],[8811,8836,[[[8811,8836,[\"Qϕi(s, a)− α log πθ(a|s) \",[null,true]]]]]],[8836,8852,[[[8836,8852,[\") , a ∼ πθ(·|s) \",[null,true]]]]]],[8852,8902,[[[8852,8902,[\"2.2 RANDOMIZED ENSEMBLED DOUBLE Q-LEARNING (REDQ) \"]]]]],[8902,10084,[[[8902,10084,[\"REDQ (Chen et al., 2021b) is a sample-efficient model-free method for solving maximum-entropy RL problems (Algorithm 1). It has two primary components to achieve high sample efficiency. 1. High UTD ratio: It uses a high UTD ratio G, which is the number of updates (lines 4–10) taken by the agent compared to the number of actual interactions with the environment (line 3). The high UTD ratio promotes sufficient training of Q-functions within a few interactions. However, this also increases an overestimation bias in the Q-function training, which degrades sample-efficient learn-ing (Chen et al., 2021b). 2. Ensemble of Q-functions: To reduce the overestimation bias, it uses an ensemble of N Q-functions for the target to be minimized (lines 6–7). Specifically, a random subset M of the en-semble is selected (line 6) then used for calculating the target (line 7). The size of the subset M is kept fixed and is denoted as M . In addition, each Q-function in the ensemble is randomly and independently initialized but updated with the same target (lines 8–9). Chen et al. (2021b) showed that using a large ensemble (N \\u003d 10) and small subset (M \\u003d 2) successfully reduces the bias. \",[null,true]]]]]]]],[[[\"cc3427ac-1b38-4c36-8131-7a43fc70ae9e\"],\"acc4b258-05aa-47b3-9093-9ffb68668838\"]],[\"dd860d40-9a3a-4051-a613-e986f837ecbe\"]]],[[[null,85,85],[0,1,2]]],[[\"How does DroQ improve efficiency?\",\"What is the UTD ratio?\",\"Which normalization method works best?\"]]]"]]
9500
[["wrb.fr",null,"[[\"The full name of the proposed method \\\"REDQ\\\" is **Randomized ensembled double Q-learning** [1-3].\",null,[\"ecad392e-635d-443c-bf8a-af759409832e\",\"a7ca2eda-7824-409d-a8f4-249a05ad3519\",629017990],null,[[[[0,86,[[[0,47,[\"The full name of the proposed method \\\"REDQ\\\" is \"]],[47,85,[\"Randomized ensembled double Q-learning\",[true]]],[85,86,[\".\"]]]]]],[[[\"0f2fb2ee-4e4b-4825-8af6-60bc597d2f17\"],[null,85,85]],[[\"0a3716db-b8b3-41f3-ac75-364c3ecaea37\"],[null,85,85]],[[\"dd860d40-9a3a-4051-a613-e986f837ecbe\"],[null,85,85]]]],null,null,[[[\"0f2fb2ee-4e4b-4825-8af6-60bc597d2f17\"],[null,null,0.693120613394453,[[null,0,1415]],[[[0,1,[[[0,1,[\" \"]]]]],[1,21,[[[1,21,[\"DROPOUT Q-FUNCTIONS \"]]],[null,6]]],[21,65,[[[21,65,[\"FOR DOUBLY EFFICIENT REINFORCEMENT LEARNING \"]]],[null,6]]],[65,170,[[[65,170,[\"Takuya Hiraoka 1,2, Takahisa Imagawa 2, Taisei Hashimoto 2,3, Takashi Onishi 1,2, Yoshimasa Tsuruoka 2,3 \",[null,true]]]]]],[170,400,[[[170,400,[\"1 NEC Corporation 2 National Institute of Advanced Industrial Science and Technology 3 The University of Tokyo {takuya-h1, takashi.onishi}@nec.com, <EMAIL>, {<EMAIL>, tsuruoka@logos.t}.u-tokyo.ac.jp \"]]]]],[400,409,[[[400,409,[\"ABSTRACT \"]]]]],[409,1415,[[[409,1415,[\"Randomized ensembled double Q-learning (REDQ) (Chen et al., 2021b) has re-cently achieved state-of-the-art sample efficiency on continuous-action reinforce-ment learning benchmarks. This superior sample efficiency is made possible by using a large Q-function ensemble. However, REDQ is much less com-putationally efficient than non-ensemble counterparts such as Soft Actor-Critic (SAC) (Haarnoja et al., 2018a). To make REDQ more computationally efficient, we propose a method of improving computational efficiency called DroQ, which is a variant of REDQ that uses a small ensemble of dropout Q-functions. Our dropout Q-functions are simple Q-functions equipped with dropout connection and layer normalization. Despite its simplicity of implementation, our experimen-tal results indicate that DroQ is doubly (sample and computationally) efficient. It achieved comparable sample efficiency with REDQ, much better computational efficiency than REDQ, and comparable computational efficiency with that of SAC. \"]]]]]]],[[[\"cc3427ac-1b38-4c36-8131-7a43fc70ae9e\"],\"acc4b258-05aa-47b3-9093-9ffb68668838\"]],[\"0f2fb2ee-4e4b-4825-8af6-60bc597d2f17\"]]],[[\"0a3716db-b8b3-41f3-ac75-364c3ecaea37\"],[null,null,0.6507746774722999,[[null,3037,3604]],[[[3037,3604,[[[3037,3604,[\"With such methods, randomized ensembled double Q-learning (REDQ) proposed by Chen et al. (2021b) is currently the most sample-efficient method for the MuJoCo benchmark. REDQ uses a high UTD ratio and large ensemble of Q-functions. The use of a high UTD ratio increases an estima-tion bias in policy evaluation, which degrades sample-efficient learning. REDQ uses an ensemble of Q-functions to suppress the estimation bias and improve sample efficiency. Chen et al. (2021b) demonstrated that the sample efficiency of REDQ is equal to or even better than that of MBPO. \"]]]]]]],[[[\"cc3427ac-1b38-4c36-8131-7a43fc70ae9e\"],\"acc4b258-05aa-47b3-9093-9ffb68668838\"]],[\"0a3716db-b8b3-41f3-ac75-364c3ecaea37\"]]],[[\"dd860d40-9a3a-4051-a613-e986f837ecbe\"],[null,null,0.6753162888887578,[[null,8804,10084]],[[[8804,8811,[[[8804,8811,[\"N∑ i\\u003d1 \",[null,true]]]]]],[8811,8836,[[[8811,8836,[\"Qϕi(s, a)− α log πθ(a|s) \",[null,true]]]]]],[8836,8852,[[[8836,8852,[\") , a ∼ πθ(·|s) \",[null,true]]]]]],[8852,8902,[[[8852,8902,[\"2.2 RANDOMIZED ENSEMBLED DOUBLE Q-LEARNING (REDQ) \"]]]]],[8902,10084,[[[8902,10084,[\"REDQ (Chen et al., 2021b) is a sample-efficient model-free method for solving maximum-entropy RL problems (Algorithm 1). It has two primary components to achieve high sample efficiency. 1. High UTD ratio: It uses a high UTD ratio G, which is the number of updates (lines 4–10) taken by the agent compared to the number of actual interactions with the environment (line 3). The high UTD ratio promotes sufficient training of Q-functions within a few interactions. However, this also increases an overestimation bias in the Q-function training, which degrades sample-efficient learn-ing (Chen et al., 2021b). 2. Ensemble of Q-functions: To reduce the overestimation bias, it uses an ensemble of N Q-functions for the target to be minimized (lines 6–7). Specifically, a random subset M of the en-semble is selected (line 6) then used for calculating the target (line 7). The size of the subset M is kept fixed and is denoted as M . In addition, each Q-function in the ensemble is randomly and independently initialized but updated with the same target (lines 8–9). Chen et al. (2021b) showed that using a large ensemble (N \\u003d 10) and small subset (M \\u003d 2) successfully reduces the bias. \",[null,true]]]]]]]],[[[\"cc3427ac-1b38-4c36-8131-7a43fc70ae9e\"],\"acc4b258-05aa-47b3-9093-9ffb68668838\"]],[\"dd860d40-9a3a-4051-a613-e986f837ecbe\"]]]]]],[[null,null,0.693120613394453,[[null,0,1415]],[[[0,1,[[[0,1,[\" \"]]]]],[1,21,[[[1,21,[\"DROPOUT Q-FUNCTIONS \"]]],[null,6]]],[21,65,[[[21,65,[\"FOR DOUBLY EFFICIENT REINFORCEMENT LEARNING \"]]],[null,6]]],[65,170,[[[65,170,[\"Takuya Hiraoka 1,2, Takahisa Imagawa 2, Taisei Hashimoto 2,3, Takashi Onishi 1,2, Yoshimasa Tsuruoka 2,3 \",[null,true]]]]]],[170,400,[[[170,400,[\"1 NEC Corporation 2 National Institute of Advanced Industrial Science and Technology 3 The University of Tokyo {takuya-h1, takashi.onishi}@nec.com, <EMAIL>, {<EMAIL>, tsuruoka@logos.t}.u-tokyo.ac.jp \"]]]]],[400,409,[[[400,409,[\"ABSTRACT \"]]]]],[409,1415,[[[409,1415,[\"Randomized ensembled double Q-learning (REDQ) (Chen et al., 2021b) has re-cently achieved state-of-the-art sample efficiency on continuous-action reinforce-ment learning benchmarks. This superior sample efficiency is made possible by using a large Q-function ensemble. However, REDQ is much less com-putationally efficient than non-ensemble counterparts such as Soft Actor-Critic (SAC) (Haarnoja et al., 2018a). To make REDQ more computationally efficient, we propose a method of improving computational efficiency called DroQ, which is a variant of REDQ that uses a small ensemble of dropout Q-functions. Our dropout Q-functions are simple Q-functions equipped with dropout connection and layer normalization. Despite its simplicity of implementation, our experimen-tal results indicate that DroQ is doubly (sample and computationally) efficient. It achieved comparable sample efficiency with REDQ, much better computational efficiency than REDQ, and comparable computational efficiency with that of SAC. \"]]]]]]],[[[\"cc3427ac-1b38-4c36-8131-7a43fc70ae9e\"],\"acc4b258-05aa-47b3-9093-9ffb68668838\"]],[\"0f2fb2ee-4e4b-4825-8af6-60bc597d2f17\"]],[null,null,0.6507746774722999,[[null,3037,3604]],[[[3037,3604,[[[3037,3604,[\"With such methods, randomized ensembled double Q-learning (REDQ) proposed by Chen et al. (2021b) is currently the most sample-efficient method for the MuJoCo benchmark. REDQ uses a high UTD ratio and large ensemble of Q-functions. The use of a high UTD ratio increases an estima-tion bias in policy evaluation, which degrades sample-efficient learning. REDQ uses an ensemble of Q-functions to suppress the estimation bias and improve sample efficiency. Chen et al. (2021b) demonstrated that the sample efficiency of REDQ is equal to or even better than that of MBPO. \"]]]]]]],[[[\"cc3427ac-1b38-4c36-8131-7a43fc70ae9e\"],\"acc4b258-05aa-47b3-9093-9ffb68668838\"]],[\"0a3716db-b8b3-41f3-ac75-364c3ecaea37\"]],[null,null,0.6753162888887578,[[null,8804,10084]],[[[8804,8811,[[[8804,8811,[\"N∑ i\\u003d1 \",[null,true]]]]]],[8811,8836,[[[8811,8836,[\"Qϕi(s, a)− α log πθ(a|s) \",[null,true]]]]]],[8836,8852,[[[8836,8852,[\") , a ∼ πθ(·|s) \",[null,true]]]]]],[8852,8902,[[[8852,8902,[\"2.2 RANDOMIZED ENSEMBLED DOUBLE Q-LEARNING (REDQ) \"]]]]],[8902,10084,[[[8902,10084,[\"REDQ (Chen et al., 2021b) is a sample-efficient model-free method for solving maximum-entropy RL problems (Algorithm 1). It has two primary components to achieve high sample efficiency. 1. High UTD ratio: It uses a high UTD ratio G, which is the number of updates (lines 4–10) taken by the agent compared to the number of actual interactions with the environment (line 3). The high UTD ratio promotes sufficient training of Q-functions within a few interactions. However, this also increases an overestimation bias in the Q-function training, which degrades sample-efficient learn-ing (Chen et al., 2021b). 2. Ensemble of Q-functions: To reduce the overestimation bias, it uses an ensemble of N Q-functions for the target to be minimized (lines 6–7). Specifically, a random subset M of the en-semble is selected (line 6) then used for calculating the target (line 7). The size of the subset M is kept fixed and is denoted as M . In addition, each Q-function in the ensemble is randomly and independently initialized but updated with the same target (lines 8–9). Chen et al. (2021b) showed that using a large ensemble (N \\u003d 10) and small subset (M \\u003d 2) successfully reduces the bias. \",[null,true]]]]]]]],[[[\"cc3427ac-1b38-4c36-8131-7a43fc70ae9e\"],\"acc4b258-05aa-47b3-9093-9ffb68668838\"]],[\"dd860d40-9a3a-4051-a613-e986f837ecbe\"]]],[[[null,85,85],[0,1,2]]],[[\"How does DroQ improve efficiency?\",\"What is the UTD ratio?\",\"Which normalization method works best?\"]]]"]]
59
[["di",2260],["af.httprm",2260,"3939558125810342288",52]]
27
[["e",5,null,null,19029]]
