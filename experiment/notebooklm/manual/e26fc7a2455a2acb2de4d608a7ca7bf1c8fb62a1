)]}'

7518
[["wrb.fr",null,"[[\"Each neural module in the architecture is **responsible for solving a single, distinct subproblem (Fi)**, establishing a direct, one-to-one correspondence between subproblems and modules [1]. These subproblems can involve **pure sensing, pure acting, or a combination of both** [2]. For example, in a robotic manipulation task, subproblems could include **recognizing objects (sensing), devising a plan to reach a target (combined), or actuating the robot to grasp an object (\",null,[\"cd5711cf-9e99-474b-ad11-99b1d7f84ff4\",\"bb175c98-76a8-4ec4-9460-8654b5077446\",4225826133],null,[[null,[[[\"3db3e212-e486-471f-9f29-ea7235c657e8\"],[null,100,182]],[[\"f1bff2f5-73e1-4aad-aef6-9971af587d1d\"],[null,265,265]]]],null,null,[[[\"3db3e212-e486-471f-9f29-ea7235c657e8\"],[null,null,0.7671978922632009,[[null,21780,23738]],[[[21780,22255,[[[21780,22255,[\"Following the assumptions of Section 3, each neural module mi in our architecture is in charge of solving one specific subproblem Fi (e.g., finding an object’s grasping point in the robot tasks), such that there is a one-to-one and onto mapping from subproblems to modules. All tasks that require solving Fi will share mi. To construct the network for a task, modules are chained in sequence, thereby replicating the graph structure depicted in Figure 1 with neural modules. \"]]]]],[22255,23738,[[[22255,23738,[\"A typical modular architecture considers a pure chaining structure, in which the complete input is passed through a sequence of modules. Each module is required to not only process the information needed to solve its subproblem (e.g., the obstacle in the robot examples), but also to pass through information required by subsequent modules. Additionally, the chaining structure induces brittle dependencies among the modules, such that changes to the first module have cascading effects. While in MTL it is viable to learn such complex modules, in the lifelong setting the modules must generalize to unseen combinations with other modules after training on just a few tasks in sequence. One solution is to let each module mi only receive information needed to solve its subproblem Fi, such that it need only output the solution to Fi. Our architecture assumes that the state can factor into module-specific components, such that each subproblem Fi requires only access to a subset of the state components and passes only the relevant subset to each module. For example, in the robotics domain, robot modules only receive as input the state components related to the robot state. Equivalently, each element of the state vector is treated as a variable and only the variables necessary for solving each subproblem Fi are fed into mi. This process requires only high-level information about the semantics of the state representation, similar to the architecture of Devin et al. (2017). \"]]]]]]],[[[\"57fc3265-d231-40b6-b9f7-b259ddef6e58\"],\"b938e289-b561-4b6a-9a56-d302a3a3b4f7\"]],[\"3db3e212-e486-471f-9f29-ea7235c657e8\"]]],[[\"f1bff2f5-73e1-4aad-aef6-9971af587d1d\"],[null,null,0.7545172953799133,[[null,12647,13353]],[[[12647,12656,[[[12647,12656,[\"∑∞ i\\u003d0 γ \"]]]]],[12656,12668,[[[12656,12668,[\"iR(si,ai)]. \"]]]]],[12668,13353,[[[12668,13353,[\"An RL problem Z is a composition of subproblems F1, F2, . . . if its optimal policy π∗ can be constructed by combining solutions to those subproblems: π∗(s) \\u003d m1 ◦ m2 ◦ · · · , where each mi ∈ M : Xi 7→ Yi is the solution to the corresponding Fi. We first consider these subproblems from an intuitive perspective, and later define them precisely. In RL, each subproblem could involve pure sensing, pure acting, or a combination of both. For instance, in our earlier robotic manipulation example, the task can be decomposed into recognizing objects (sensing), detecting the target object and devising a plan to reach it (combined), and actuating the robot to grasp the object (acting). \"]]]]]]],[[[\"57fc3265-d231-40b6-b9f7-b259ddef6e58\"],\"b938e289-b561-4b6a-9a56-d302a3a3b4f7\"]],[\"f1bff2f5-73e1-4aad-aef6-9971af587d1d\"]]]]]],[[null,null,0.7671978922632009,[[null,21780,23738]],[[[21780,22255,[[[21780,22255,[\"Following the assumptions of Section 3, each neural module mi in our architecture is in charge of solving one specific subproblem Fi (e.g., finding an object’s grasping point in the robot tasks), such that there is a one-to-one and onto mapping from subproblems to modules. All tasks that require solving Fi will share mi. To construct the network for a task, modules are chained in sequence, thereby replicating the graph structure depicted in Figure 1 with neural modules. \"]]]]],[22255,23738,[[[22255,23738,[\"A typical modular architecture considers a pure chaining structure, in which the complete input is passed through a sequence of modules. Each module is required to not only process the information needed to solve its subproblem (e.g., the obstacle in the robot examples), but also to pass through information required by subsequent modules. Additionally, the chaining structure induces brittle dependencies among the modules, such that changes to the first module have cascading effects. While in MTL it is viable to learn such complex modules, in the lifelong setting the modules must generalize to unseen combinations with other modules after training on just a few tasks in sequence. One solution is to let each module mi only receive information needed to solve its subproblem Fi, such that it need only output the solution to Fi. Our architecture assumes that the state can factor into module-specific components, such that each subproblem Fi requires only access to a subset of the state components and passes only the relevant subset to each module. For example, in the robotics domain, robot modules only receive as input the state components related to the robot state. Equivalently, each element of the state vector is treated as a variable and only the variables necessary for solving each subproblem Fi are fed into mi. This process requires only high-level information about the semantics of the state representation, similar to the architecture of Devin et al. (2017). \"]]]]]]],[[[\"57fc3265-d231-40b6-b9f7-b259ddef6e58\"],\"b938e289-b561-4b6a-9a56-d302a3a3b4f7\"]],[\"3db3e212-e486-471f-9f29-ea7235c657e8\"]],[null,null,0.7545172953799133,[[null,12647,13353]],[[[12647,12656,[[[12647,12656,[\"∑∞ i\\u003d0 γ \"]]]]],[12656,12668,[[[12656,12668,[\"iR(si,ai)]. \"]]]]],[12668,13353,[[[12668,13353,[\"An RL problem Z is a composition of subproblems F1, F2, . . . if its optimal policy π∗ can be constructed by combining solutions to those subproblems: π∗(s) \\u003d m1 ◦ m2 ◦ · · · , where each mi ∈ M : Xi 7→ Yi is the solution to the corresponding Fi. We first consider these subproblems from an intuitive perspective, and later define them precisely. In RL, each subproblem could involve pure sensing, pure acting, or a combination of both. For instance, in our earlier robotic manipulation example, the task can be decomposed into recognizing objects (sensing), detecting the target object and devising a plan to reach it (combined), and actuating the robot to grasp the object (acting). \"]]]]]]],[[[\"57fc3265-d231-40b6-b9f7-b259ddef6e58\"],\"b938e289-b561-4b6a-9a56-d302a3a3b4f7\"]],[\"f1bff2f5-73e1-4aad-aef6-9971af587d1d\"]]],[[[null,100,182],[0]],[[null,265,265],[1]]],[[\"How do humans solve complex problems?\",\"What is lifelong reinforcement learning?\",\"What are compositional evaluation domains?\"]]]"]]
7846
[["wrb.fr",null,"[[\"Each neural module in the architecture is **responsible for solving a single, distinct subproblem (Fi)**, establishing a direct, one-to-one correspondence between subproblems and modules [1]. These subproblems can involve **pure sensing, pure acting, or a combination of both** [2]. For example, in a robotic manipulation task, subproblems could include **recognizing objects (sensing), devising a plan to reach a target (combined), or actuating the robot to grasp an object (acting)** [2]. The module is designed to **only receive information pertinent to its subproblem** and to **output the solution specifically for that subproblem** [1].\",null,[\"cd5711cf-9e99-474b-ad11-99b1d7f84ff4\",\"bb175c98-76a8-4ec4-9460-8654b5077446\",4225826133],null,[[null,[[[\"2edf2159-93af-4221-b312-edef2032e59b\"],[null,100,182]],[[\"e415c4be-c36a-4256-8fc5-630b1d15bc66\"],[null,265,265]],[[\"e415c4be-c36a-4256-8fc5-630b1d15bc66\"],[null,465,465]],[[\"2edf2159-93af-4221-b312-edef2032e59b\"],[null,605,605]]]],null,null,[[[\"2edf2159-93af-4221-b312-edef2032e59b\"],[null,null,0.7671978922632009,[[null,21780,23738]],[[[21780,22255,[[[21780,22255,[\"Following the assumptions of Section 3, each neural module mi in our architecture is in charge of solving one specific subproblem Fi (e.g., finding an object’s grasping point in the robot tasks), such that there is a one-to-one and onto mapping from subproblems to modules. All tasks that require solving Fi will share mi. To construct the network for a task, modules are chained in sequence, thereby replicating the graph structure depicted in Figure 1 with neural modules. \"]]]]],[22255,23738,[[[22255,23738,[\"A typical modular architecture considers a pure chaining structure, in which the complete input is passed through a sequence of modules. Each module is required to not only process the information needed to solve its subproblem (e.g., the obstacle in the robot examples), but also to pass through information required by subsequent modules. Additionally, the chaining structure induces brittle dependencies among the modules, such that changes to the first module have cascading effects. While in MTL it is viable to learn such complex modules, in the lifelong setting the modules must generalize to unseen combinations with other modules after training on just a few tasks in sequence. One solution is to let each module mi only receive information needed to solve its subproblem Fi, such that it need only output the solution to Fi. Our architecture assumes that the state can factor into module-specific components, such that each subproblem Fi requires only access to a subset of the state components and passes only the relevant subset to each module. For example, in the robotics domain, robot modules only receive as input the state components related to the robot state. Equivalently, each element of the state vector is treated as a variable and only the variables necessary for solving each subproblem Fi are fed into mi. This process requires only high-level information about the semantics of the state representation, similar to the architecture of Devin et al. (2017). \"]]]]]]],[[[\"57fc3265-d231-40b6-b9f7-b259ddef6e58\"],\"b938e289-b561-4b6a-9a56-d302a3a3b4f7\"]],[\"2edf2159-93af-4221-b312-edef2032e59b\"]]],[[\"e415c4be-c36a-4256-8fc5-630b1d15bc66\"],[null,null,0.7545172953799133,[[null,12647,13353]],[[[12647,12656,[[[12647,12656,[\"∑∞ i\\u003d0 γ \"]]]]],[12656,12668,[[[12656,12668,[\"iR(si,ai)]. \"]]]]],[12668,13353,[[[12668,13353,[\"An RL problem Z is a composition of subproblems F1, F2, . . . if its optimal policy π∗ can be constructed by combining solutions to those subproblems: π∗(s) \\u003d m1 ◦ m2 ◦ · · · , where each mi ∈ M : Xi 7→ Yi is the solution to the corresponding Fi. We first consider these subproblems from an intuitive perspective, and later define them precisely. In RL, each subproblem could involve pure sensing, pure acting, or a combination of both. For instance, in our earlier robotic manipulation example, the task can be decomposed into recognizing objects (sensing), detecting the target object and devising a plan to reach it (combined), and actuating the robot to grasp the object (acting). \"]]]]]]],[[[\"57fc3265-d231-40b6-b9f7-b259ddef6e58\"],\"b938e289-b561-4b6a-9a56-d302a3a3b4f7\"]],[\"e415c4be-c36a-4256-8fc5-630b1d15bc66\"]]]]]],[[null,null,0.7671978922632009,[[null,21780,23738]],[[[21780,22255,[[[21780,22255,[\"Following the assumptions of Section 3, each neural module mi in our architecture is in charge of solving one specific subproblem Fi (e.g., finding an object’s grasping point in the robot tasks), such that there is a one-to-one and onto mapping from subproblems to modules. All tasks that require solving Fi will share mi. To construct the network for a task, modules are chained in sequence, thereby replicating the graph structure depicted in Figure 1 with neural modules. \"]]]]],[22255,23738,[[[22255,23738,[\"A typical modular architecture considers a pure chaining structure, in which the complete input is passed through a sequence of modules. Each module is required to not only process the information needed to solve its subproblem (e.g., the obstacle in the robot examples), but also to pass through information required by subsequent modules. Additionally, the chaining structure induces brittle dependencies among the modules, such that changes to the first module have cascading effects. While in MTL it is viable to learn such complex modules, in the lifelong setting the modules must generalize to unseen combinations with other modules after training on just a few tasks in sequence. One solution is to let each module mi only receive information needed to solve its subproblem Fi, such that it need only output the solution to Fi. Our architecture assumes that the state can factor into module-specific components, such that each subproblem Fi requires only access to a subset of the state components and passes only the relevant subset to each module. For example, in the robotics domain, robot modules only receive as input the state components related to the robot state. Equivalently, each element of the state vector is treated as a variable and only the variables necessary for solving each subproblem Fi are fed into mi. This process requires only high-level information about the semantics of the state representation, similar to the architecture of Devin et al. (2017). \"]]]]]]],[[[\"57fc3265-d231-40b6-b9f7-b259ddef6e58\"],\"b938e289-b561-4b6a-9a56-d302a3a3b4f7\"]],[\"2edf2159-93af-4221-b312-edef2032e59b\"]],[null,null,0.7545172953799133,[[null,12647,13353]],[[[12647,12656,[[[12647,12656,[\"∑∞ i\\u003d0 γ \"]]]]],[12656,12668,[[[12656,12668,[\"iR(si,ai)]. \"]]]]],[12668,13353,[[[12668,13353,[\"An RL problem Z is a composition of subproblems F1, F2, . . . if its optimal policy π∗ can be constructed by combining solutions to those subproblems: π∗(s) \\u003d m1 ◦ m2 ◦ · · · , where each mi ∈ M : Xi 7→ Yi is the solution to the corresponding Fi. We first consider these subproblems from an intuitive perspective, and later define them precisely. In RL, each subproblem could involve pure sensing, pure acting, or a combination of both. For instance, in our earlier robotic manipulation example, the task can be decomposed into recognizing objects (sensing), detecting the target object and devising a plan to reach it (combined), and actuating the robot to grasp the object (acting). \"]]]]]]],[[[\"57fc3265-d231-40b6-b9f7-b259ddef6e58\"],\"b938e289-b561-4b6a-9a56-d302a3a3b4f7\"]],[\"e415c4be-c36a-4256-8fc5-630b1d15bc66\"]]],[[[null,100,182],[0]],[[null,265,265],[1]],[[null,465,465],[1]],[[null,605,605],[0]]],[[\"How do humans solve complex problems?\",\"What is lifelong reinforcement learning?\",\"What are compositional evaluation domains?\"]]]"]]
8679
[["wrb.fr",null,"[[\"Each neural module in the architecture is **responsible for solving a single, distinct subproblem (Fi)**, establishing a direct, one-to-one correspondence between subproblems and modules [1]. These subproblems can involve **pure sensing, pure acting, or a combination of both** [2]. For example, in a robotic manipulation task, subproblems could include **recognizing objects (sensing), devising a plan to reach a target (combined), or actuating the robot to grasp an object (acting)** [2]. The module is designed to **only receive information pertinent to its subproblem** and to **output the solution specifically for that subproblem** [1].\",null,[\"cd5711cf-9e99-474b-ad11-99b1d7f84ff4\",\"bb175c98-76a8-4ec4-9460-8654b5077446\",4225826133],null,[[[[0,606,[[[0,42,[\"Each neural module in the architecture is \"]],[42,100,[\"responsible for solving a single, distinct subproblem (Fi)\",[true]]],[100,214,[\", establishing a direct, one-to-one correspondence between subproblems and modules. These subproblems can involve \"]],[214,265,[\"pure sensing, pure acting, or a combination of both\",[true]]],[265,338,[\". For example, in a robotic manipulation task, subproblems could include \"]],[338,465,[\"recognizing objects (sensing), devising a plan to reach a target (combined), or actuating the robot to grasp an object (acting)\",[true]]],[465,493,[\". The module is designed to \"]],[493,545,[\"only receive information pertinent to its subproblem\",[true]]],[545,553,[\" and to \"]],[553,605,[\"output the solution specifically for that subproblem\",[true]]],[605,606,[\".\"]]]]]],[[[\"2edf2159-93af-4221-b312-edef2032e59b\"],[null,100,182]],[[\"e415c4be-c36a-4256-8fc5-630b1d15bc66\"],[null,265,265]],[[\"e415c4be-c36a-4256-8fc5-630b1d15bc66\"],[null,465,465]],[[\"2edf2159-93af-4221-b312-edef2032e59b\"],[null,605,605]]]],null,null,[[[\"2edf2159-93af-4221-b312-edef2032e59b\"],[null,null,0.7671978922632009,[[null,21780,23738]],[[[21780,22255,[[[21780,22255,[\"Following the assumptions of Section 3, each neural module mi in our architecture is in charge of solving one specific subproblem Fi (e.g., finding an object’s grasping point in the robot tasks), such that there is a one-to-one and onto mapping from subproblems to modules. All tasks that require solving Fi will share mi. To construct the network for a task, modules are chained in sequence, thereby replicating the graph structure depicted in Figure 1 with neural modules. \"]]]]],[22255,23738,[[[22255,23738,[\"A typical modular architecture considers a pure chaining structure, in which the complete input is passed through a sequence of modules. Each module is required to not only process the information needed to solve its subproblem (e.g., the obstacle in the robot examples), but also to pass through information required by subsequent modules. Additionally, the chaining structure induces brittle dependencies among the modules, such that changes to the first module have cascading effects. While in MTL it is viable to learn such complex modules, in the lifelong setting the modules must generalize to unseen combinations with other modules after training on just a few tasks in sequence. One solution is to let each module mi only receive information needed to solve its subproblem Fi, such that it need only output the solution to Fi. Our architecture assumes that the state can factor into module-specific components, such that each subproblem Fi requires only access to a subset of the state components and passes only the relevant subset to each module. For example, in the robotics domain, robot modules only receive as input the state components related to the robot state. Equivalently, each element of the state vector is treated as a variable and only the variables necessary for solving each subproblem Fi are fed into mi. This process requires only high-level information about the semantics of the state representation, similar to the architecture of Devin et al. (2017). \"]]]]]]],[[[\"57fc3265-d231-40b6-b9f7-b259ddef6e58\"],\"b938e289-b561-4b6a-9a56-d302a3a3b4f7\"]],[\"2edf2159-93af-4221-b312-edef2032e59b\"]]],[[\"e415c4be-c36a-4256-8fc5-630b1d15bc66\"],[null,null,0.7545172953799133,[[null,12647,13353]],[[[12647,12656,[[[12647,12656,[\"∑∞ i\\u003d0 γ \"]]]]],[12656,12668,[[[12656,12668,[\"iR(si,ai)]. \"]]]]],[12668,13353,[[[12668,13353,[\"An RL problem Z is a composition of subproblems F1, F2, . . . if its optimal policy π∗ can be constructed by combining solutions to those subproblems: π∗(s) \\u003d m1 ◦ m2 ◦ · · · , where each mi ∈ M : Xi 7→ Yi is the solution to the corresponding Fi. We first consider these subproblems from an intuitive perspective, and later define them precisely. In RL, each subproblem could involve pure sensing, pure acting, or a combination of both. For instance, in our earlier robotic manipulation example, the task can be decomposed into recognizing objects (sensing), detecting the target object and devising a plan to reach it (combined), and actuating the robot to grasp the object (acting). \"]]]]]]],[[[\"57fc3265-d231-40b6-b9f7-b259ddef6e58\"],\"b938e289-b561-4b6a-9a56-d302a3a3b4f7\"]],[\"e415c4be-c36a-4256-8fc5-630b1d15bc66\"]]]]]],[[null,null,0.7671978922632009,[[null,21780,23738]],[[[21780,22255,[[[21780,22255,[\"Following the assumptions of Section 3, each neural module mi in our architecture is in charge of solving one specific subproblem Fi (e.g., finding an object’s grasping point in the robot tasks), such that there is a one-to-one and onto mapping from subproblems to modules. All tasks that require solving Fi will share mi. To construct the network for a task, modules are chained in sequence, thereby replicating the graph structure depicted in Figure 1 with neural modules. \"]]]]],[22255,23738,[[[22255,23738,[\"A typical modular architecture considers a pure chaining structure, in which the complete input is passed through a sequence of modules. Each module is required to not only process the information needed to solve its subproblem (e.g., the obstacle in the robot examples), but also to pass through information required by subsequent modules. Additionally, the chaining structure induces brittle dependencies among the modules, such that changes to the first module have cascading effects. While in MTL it is viable to learn such complex modules, in the lifelong setting the modules must generalize to unseen combinations with other modules after training on just a few tasks in sequence. One solution is to let each module mi only receive information needed to solve its subproblem Fi, such that it need only output the solution to Fi. Our architecture assumes that the state can factor into module-specific components, such that each subproblem Fi requires only access to a subset of the state components and passes only the relevant subset to each module. For example, in the robotics domain, robot modules only receive as input the state components related to the robot state. Equivalently, each element of the state vector is treated as a variable and only the variables necessary for solving each subproblem Fi are fed into mi. This process requires only high-level information about the semantics of the state representation, similar to the architecture of Devin et al. (2017). \"]]]]]]],[[[\"57fc3265-d231-40b6-b9f7-b259ddef6e58\"],\"b938e289-b561-4b6a-9a56-d302a3a3b4f7\"]],[\"2edf2159-93af-4221-b312-edef2032e59b\"]],[null,null,0.7545172953799133,[[null,12647,13353]],[[[12647,12656,[[[12647,12656,[\"∑∞ i\\u003d0 γ \"]]]]],[12656,12668,[[[12656,12668,[\"iR(si,ai)]. \"]]]]],[12668,13353,[[[12668,13353,[\"An RL problem Z is a composition of subproblems F1, F2, . . . if its optimal policy π∗ can be constructed by combining solutions to those subproblems: π∗(s) \\u003d m1 ◦ m2 ◦ · · · , where each mi ∈ M : Xi 7→ Yi is the solution to the corresponding Fi. We first consider these subproblems from an intuitive perspective, and later define them precisely. In RL, each subproblem could involve pure sensing, pure acting, or a combination of both. For instance, in our earlier robotic manipulation example, the task can be decomposed into recognizing objects (sensing), detecting the target object and devising a plan to reach it (combined), and actuating the robot to grasp the object (acting). \"]]]]]]],[[[\"57fc3265-d231-40b6-b9f7-b259ddef6e58\"],\"b938e289-b561-4b6a-9a56-d302a3a3b4f7\"]],[\"e415c4be-c36a-4256-8fc5-630b1d15bc66\"]]],[[[null,100,182],[0]],[[null,265,265],[1]],[[null,465,465],[1]],[[null,605,605],[0]]],[[\"How do humans solve complex problems?\",\"What is lifelong reinforcement learning?\",\"What are compositional evaluation domains?\"]]]"]]
57
[["di",3521],["af.httprm",3520,"54308973434395443",54]]
27
[["e",6,null,null,24293]]
