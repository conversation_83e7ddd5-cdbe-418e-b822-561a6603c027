)]}'

13021
[["wrb.fr",null,"[[\"Yes, the proposed algorithms can be applied to other classes of probability distributions beyond the Gaussian distribution and the \\\"banana\\\" source [1-4].\\n\\nSpecifically:\\n*   The paper makes the **first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source** requiring only i.i.d. data samples [1].\\n*   They introduce new algorithms for upper\",null,[\"724e4669-1d95-45ee-b496-0c464e7ee299\",\"fd97e06c-5b55-4957-accc-b0adff291127\",2330387623],null,[[[[0,147,[[[0,147,[\"Yes, the proposed algorithms can be applied to other classes of probability distributions beyond the Gaussian distribution and the \\\"banana\\\" source.\"]]]]],[147,160,[[[147,160,[\"Specifically:\"]]]]],[160,325,[[[160,180,[\"The paper makes the \"]],[180,289,[\"first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source\",[true]]],[289,325,[\" requiring only i.i.d. data samples.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]]],[[[\"fa336ec7-666a-41f3-b4b1-762439e7cdbc\"],[null,0,146]],[[\"fd612ebf-61c7-4510-b261-48e45fd2f224\"],[null,0,146]],[[\"f68531ee-2de9-45b5-956d-3015e184c3b6\"],[null,0,146]],[[\"d5eb709a-089c-423c-b8a0-1d28e5e4a0e7\"],[null,0,146]],[[\"fa336ec7-666a-41f3-b4b1-762439e7cdbc\"],[null,290,324]]]],null,null,[[[\"fa336ec7-666a-41f3-b4b1-762439e7cdbc\"],[null,null,0.693826975858025,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"fa336ec7-666a-41f3-b4b1-762439e7cdbc\"]]],[[\"fd612ebf-61c7-4510-b261-48e45fd2f224\"],[null,null,0.6696627165023853,[[null,3970,4623]],[[[3970,3970,[]],[3970,3971,[[[3970,3971,[\" \"]]]]],[3971,4265,[[[3971,4265,[\"In this work, we make progress on this long-standing problem in information theory using tools from machine learning, and introduce new algorithms for upper and lower bounding the R-D function of a general (i.e., discrete, continuous, or neither), unknown memoryless source. More specifically, \"]]]]],[4265,4623,[[[4265,4623,[\"1. Similarly to how a VAE with a discrete likelihood model minimizes an upper bound on the data entropy, we establish that any β-VAE with a likelihood model induced by a distortion metric minimizes an upper bound on the data rate-distortion function. We thus open the deep generative modeling toolbox to the estimation of an upper bound on the R-D function. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"fd612ebf-61c7-4510-b261-48e45fd2f224\"]]],[[\"f68531ee-2de9-45b5-956d-3015e184c3b6\"],[null,null,0.6587625257247187,[[null,4623,5362]],[[[4623,4905,[[[4623,4905,[\"2. We derive a lower bound estimator of the R-D function that can be made asymptotically exact and optimized by stochastic gradient ascent. Facing the difficulty of the problem involving global optimization, we restrict to a squared error distortion for a practical implementation. \"]]]]],[4905,5362,[[[4905,5362,[\"3. We perform extensive experiments and obtain non-trivial sandwich bounds on various data sources, including GAN-generated artificial sources and real-world data from speech and physics. Our results shed light on the effectiveness of neural compression approaches (Ballé et al., 2021; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), and identify the intrinsic (rather than nominal) dimension of data as a key factor affecting the tightness of our lower bound. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"f68531ee-2de9-45b5-956d-3015e184c3b6\"]]],[[\"d5eb709a-089c-423c-b8a0-1d28e5e4a0e7\"],[null,null,0.7162808213644777,[[null,24759,26239]],[[[24759,24773,[[[24759,24773,[\"6 EXPERIMENTS \"]]]]],[24773,26239,[[[24773,26239,[\"We estimate the R-D functions of a variety of artificial and real-world data sources, in settings where the BA algorithm is infeasible and no prior known method has been applied. On Gaussian sources, our upper bound algorithm is shown to converge to the exact R-D function, while our lower bounds become increasingly loose in higher dimensions, an issue we investigate subsequently. We obtain tighter sandwich bounds on particle physics and speech data than on similar dimensional Gaussians, and compare with the performance of neural compression. We further investigate looseness in the lower bound, experimentally establishing the intrinsic dimension of the data as a much more critical contributing factor than the nominal/ambient dimension. Indeed, we obtain tight sandwich bounds on high-dimension GAN-generated images with a low intrinsic dimension, and compare with popular neural image compression methods. Finally, we estimate bounds on the R-D function of natural images. The intrinsic dimension is likely too high for our lower bound to be useful, while our upper bounds on the Kodak and Tecnick datasets imply at least one dB (in PSNR) of theoretical room for improving state-of-the-art image compression methods, at various bitrates. We also validate our bounds against the BA algorithm over the various data sources, using a 2D marginal of the source to make BA feasible. We provide experimental details and additional results in Appendix A.5 and A.6. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"d5eb709a-089c-423c-b8a0-1d28e5e4a0e7\"]]]]]],[[null,null,0.693826975858025,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"fa336ec7-666a-41f3-b4b1-762439e7cdbc\"]],[null,null,0.6696627165023853,[[null,3970,4623]],[[[3970,3970,[]],[3970,3971,[[[3970,3971,[\" \"]]]]],[3971,4265,[[[3971,4265,[\"In this work, we make progress on this long-standing problem in information theory using tools from machine learning, and introduce new algorithms for upper and lower bounding the R-D function of a general (i.e., discrete, continuous, or neither), unknown memoryless source. More specifically, \"]]]]],[4265,4623,[[[4265,4623,[\"1. Similarly to how a VAE with a discrete likelihood model minimizes an upper bound on the data entropy, we establish that any β-VAE with a likelihood model induced by a distortion metric minimizes an upper bound on the data rate-distortion function. We thus open the deep generative modeling toolbox to the estimation of an upper bound on the R-D function. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"fd612ebf-61c7-4510-b261-48e45fd2f224\"]],[null,null,0.6587625257247187,[[null,4623,5362]],[[[4623,4905,[[[4623,4905,[\"2. We derive a lower bound estimator of the R-D function that can be made asymptotically exact and optimized by stochastic gradient ascent. Facing the difficulty of the problem involving global optimization, we restrict to a squared error distortion for a practical implementation. \"]]]]],[4905,5362,[[[4905,5362,[\"3. We perform extensive experiments and obtain non-trivial sandwich bounds on various data sources, including GAN-generated artificial sources and real-world data from speech and physics. Our results shed light on the effectiveness of neural compression approaches (Ballé et al., 2021; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), and identify the intrinsic (rather than nominal) dimension of data as a key factor affecting the tightness of our lower bound. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"f68531ee-2de9-45b5-956d-3015e184c3b6\"]],[null,null,0.7162808213644777,[[null,24759,26239]],[[[24759,24773,[[[24759,24773,[\"6 EXPERIMENTS \"]]]]],[24773,26239,[[[24773,26239,[\"We estimate the R-D functions of a variety of artificial and real-world data sources, in settings where the BA algorithm is infeasible and no prior known method has been applied. On Gaussian sources, our upper bound algorithm is shown to converge to the exact R-D function, while our lower bounds become increasingly loose in higher dimensions, an issue we investigate subsequently. We obtain tighter sandwich bounds on particle physics and speech data than on similar dimensional Gaussians, and compare with the performance of neural compression. We further investigate looseness in the lower bound, experimentally establishing the intrinsic dimension of the data as a much more critical contributing factor than the nominal/ambient dimension. Indeed, we obtain tight sandwich bounds on high-dimension GAN-generated images with a low intrinsic dimension, and compare with popular neural image compression methods. Finally, we estimate bounds on the R-D function of natural images. The intrinsic dimension is likely too high for our lower bound to be useful, while our upper bounds on the Kodak and Tecnick datasets imply at least one dB (in PSNR) of theoretical room for improving state-of-the-art image compression methods, at various bitrates. We also validate our bounds against the BA algorithm over the various data sources, using a 2D marginal of the source to make BA feasible. We provide experimental details and additional results in Appendix A.5 and A.6. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"d5eb709a-089c-423c-b8a0-1d28e5e4a0e7\"]]],[[[null,0,146],[0,1,2,3]],[[null,290,324],[0]]],[[\"What is the R-D function?\",\"What is the Blahut-Arimoto algorithm?\",\"How are R-D upper bounds estimated?\"]]]"]]
17829
[["wrb.fr",null,"[[\"Yes, the proposed algorithms can be applied to other classes of probability distributions beyond the Gaussian distribution and the \\\"banana\\\" source [1-4].\\n\\nSpecifically:\\n*   The paper makes the **first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source** requiring only i.i.d. data samples [1].\\n*   They introduce new algorithms for upper and lower bounding the R-D function of a **general (i.e., discrete, continuous, or neither), unknown memoryless source** [2].\\n*   Extensive experiments were performed, obtaining non-trivial sandwich bounds on various data sources, including **GAN-generated artificial sources and real-world data from speech and physics** [3, 4].\\n*   On particle physics and speech data, tighter sandwich bounds were obtained compared to similar dimensional Gaussians [4, 5].\\n*   The upper bound algorithm optimizes a β-VAE whose likelihood model is specified by the distortion metric, with a common case being a\",null,[\"724e4669-1d95-45ee-b496-0c464e7ee299\",\"fd97e06c-5b55-4957-accc-b0adff291127\",2330387623],null,[[[[0,147,[[[0,147,[\"Yes, the proposed algorithms can be applied to other classes of probability distributions beyond the Gaussian distribution and the \\\"banana\\\" source.\"]]]]],[147,160,[[[147,160,[\"Specifically:\"]]]]],[160,325,[[[160,180,[\"The paper makes the \"]],[180,289,[\"first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source\",[true]]],[289,325,[\" requiring only i.i.d. data samples.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[325,482,[[[325,406,[\"They introduce new algorithms for upper and lower bounding the R-D function of a \"]],[406,481,[\"general (i.e., discrete, continuous, or neither), unknown memoryless source\",[true]]],[481,482,[\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[482,670,[[[482,593,[\"Extensive experiments were performed, obtaining non-trivial sandwich bounds on various data sources, including \"]],[593,669,[\"GAN-generated artificial sources and real-world data from speech and physics\",[true]]],[669,670,[\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]],[670,787,[[[670,787,[\"On particle physics and speech data, tighter sandwich bounds were obtained compared to similar dimensional Gaussians.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,3]]]],[[[\"e742bcf5-8c87-4d80-8072-89d0c6a95c0e\"],[null,0,146]],[[\"2fe14447-769e-4484-b584-21c9886cd6bd\"],[null,0,146]],[[\"87a3a567-52ed-4113-baf0-e2eee2f6b473\"],[null,0,146]],[[\"6b5714ef-abd2-4d62-a97b-39b9c7d4543c\"],[null,0,146]],[[\"e742bcf5-8c87-4d80-8072-89d0c6a95c0e\"],[null,290,324]],[[\"2fe14447-769e-4484-b584-21c9886cd6bd\"],[null,481,481]],[[\"87a3a567-52ed-4113-baf0-e2eee2f6b473\"],[null,669,669]],[[\"6b5714ef-abd2-4d62-a97b-39b9c7d4543c\"],[null,669,669]],[[\"6b5714ef-abd2-4d62-a97b-39b9c7d4543c\"],[null,670,786]],[[\"053f54b5-444f-43fe-ab1f-2f51488445f1\"],[null,670,786]]]],null,null,[[[\"e742bcf5-8c87-4d80-8072-89d0c6a95c0e\"],[null,null,0.693826975858025,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"e742bcf5-8c87-4d80-8072-89d0c6a95c0e\"]]],[[\"2fe14447-769e-4484-b584-21c9886cd6bd\"],[null,null,0.6696627165023853,[[null,3970,4623]],[[[3970,3970,[]],[3970,3971,[[[3970,3971,[\" \"]]]]],[3971,4265,[[[3971,4265,[\"In this work, we make progress on this long-standing problem in information theory using tools from machine learning, and introduce new algorithms for upper and lower bounding the R-D function of a general (i.e., discrete, continuous, or neither), unknown memoryless source. More specifically, \"]]]]],[4265,4623,[[[4265,4623,[\"1. Similarly to how a VAE with a discrete likelihood model minimizes an upper bound on the data entropy, we establish that any β-VAE with a likelihood model induced by a distortion metric minimizes an upper bound on the data rate-distortion function. We thus open the deep generative modeling toolbox to the estimation of an upper bound on the R-D function. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"2fe14447-769e-4484-b584-21c9886cd6bd\"]]],[[\"87a3a567-52ed-4113-baf0-e2eee2f6b473\"],[null,null,0.6587625257247187,[[null,4623,5362]],[[[4623,4905,[[[4623,4905,[\"2. We derive a lower bound estimator of the R-D function that can be made asymptotically exact and optimized by stochastic gradient ascent. Facing the difficulty of the problem involving global optimization, we restrict to a squared error distortion for a practical implementation. \"]]]]],[4905,5362,[[[4905,5362,[\"3. We perform extensive experiments and obtain non-trivial sandwich bounds on various data sources, including GAN-generated artificial sources and real-world data from speech and physics. Our results shed light on the effectiveness of neural compression approaches (Ballé et al., 2021; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), and identify the intrinsic (rather than nominal) dimension of data as a key factor affecting the tightness of our lower bound. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"87a3a567-52ed-4113-baf0-e2eee2f6b473\"]]],[[\"6b5714ef-abd2-4d62-a97b-39b9c7d4543c\"],[null,null,0.7162808213644777,[[null,24759,26239]],[[[24759,24773,[[[24759,24773,[\"6 EXPERIMENTS \"]]]]],[24773,26239,[[[24773,26239,[\"We estimate the R-D functions of a variety of artificial and real-world data sources, in settings where the BA algorithm is infeasible and no prior known method has been applied. On Gaussian sources, our upper bound algorithm is shown to converge to the exact R-D function, while our lower bounds become increasingly loose in higher dimensions, an issue we investigate subsequently. We obtain tighter sandwich bounds on particle physics and speech data than on similar dimensional Gaussians, and compare with the performance of neural compression. We further investigate looseness in the lower bound, experimentally establishing the intrinsic dimension of the data as a much more critical contributing factor than the nominal/ambient dimension. Indeed, we obtain tight sandwich bounds on high-dimension GAN-generated images with a low intrinsic dimension, and compare with popular neural image compression methods. Finally, we estimate bounds on the R-D function of natural images. The intrinsic dimension is likely too high for our lower bound to be useful, while our upper bounds on the Kodak and Tecnick datasets imply at least one dB (in PSNR) of theoretical room for improving state-of-the-art image compression methods, at various bitrates. We also validate our bounds against the BA algorithm over the various data sources, using a 2D marginal of the source to make BA feasible. We provide experimental details and additional results in Appendix A.5 and A.6. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"6b5714ef-abd2-4d62-a97b-39b9c7d4543c\"]]],[[\"053f54b5-444f-43fe-ab1f-2f51488445f1\"],[null,null,0.6552840976010041,[[null,30278,30804]],[[[30278,30320,[[[30278,30320,[\"6.2 DATA FROM PARTICLE PHYSICS AND SPEECH \"]]]]],[30320,30804,[[[30320,30804,[\"The quickly deteriorating lower bound on higher (even 16) dimensional Gaussians may seem dis-heartening. However, the Gaussian is also the hardest continuous source to compress under squared error (Gibson, 2017), and real-world data often exhibits considerably more structure than Gaussian noise. In this subsection, we experiment on data from particle physics (Howard et al., 2021) and speech (Jackson et al., 2018), and indeed obtain improved sandwich bounds compared to Gaussians. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"053f54b5-444f-43fe-ab1f-2f51488445f1\"]]]]]],[[null,null,0.693826975858025,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"e742bcf5-8c87-4d80-8072-89d0c6a95c0e\"]],[null,null,0.6696627165023853,[[null,3970,4623]],[[[3970,3970,[]],[3970,3971,[[[3970,3971,[\" \"]]]]],[3971,4265,[[[3971,4265,[\"In this work, we make progress on this long-standing problem in information theory using tools from machine learning, and introduce new algorithms for upper and lower bounding the R-D function of a general (i.e., discrete, continuous, or neither), unknown memoryless source. More specifically, \"]]]]],[4265,4623,[[[4265,4623,[\"1. Similarly to how a VAE with a discrete likelihood model minimizes an upper bound on the data entropy, we establish that any β-VAE with a likelihood model induced by a distortion metric minimizes an upper bound on the data rate-distortion function. We thus open the deep generative modeling toolbox to the estimation of an upper bound on the R-D function. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"2fe14447-769e-4484-b584-21c9886cd6bd\"]],[null,null,0.6587625257247187,[[null,4623,5362]],[[[4623,4905,[[[4623,4905,[\"2. We derive a lower bound estimator of the R-D function that can be made asymptotically exact and optimized by stochastic gradient ascent. Facing the difficulty of the problem involving global optimization, we restrict to a squared error distortion for a practical implementation. \"]]]]],[4905,5362,[[[4905,5362,[\"3. We perform extensive experiments and obtain non-trivial sandwich bounds on various data sources, including GAN-generated artificial sources and real-world data from speech and physics. Our results shed light on the effectiveness of neural compression approaches (Ballé et al., 2021; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), and identify the intrinsic (rather than nominal) dimension of data as a key factor affecting the tightness of our lower bound. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"87a3a567-52ed-4113-baf0-e2eee2f6b473\"]],[null,null,0.7162808213644777,[[null,24759,26239]],[[[24759,24773,[[[24759,24773,[\"6 EXPERIMENTS \"]]]]],[24773,26239,[[[24773,26239,[\"We estimate the R-D functions of a variety of artificial and real-world data sources, in settings where the BA algorithm is infeasible and no prior known method has been applied. On Gaussian sources, our upper bound algorithm is shown to converge to the exact R-D function, while our lower bounds become increasingly loose in higher dimensions, an issue we investigate subsequently. We obtain tighter sandwich bounds on particle physics and speech data than on similar dimensional Gaussians, and compare with the performance of neural compression. We further investigate looseness in the lower bound, experimentally establishing the intrinsic dimension of the data as a much more critical contributing factor than the nominal/ambient dimension. Indeed, we obtain tight sandwich bounds on high-dimension GAN-generated images with a low intrinsic dimension, and compare with popular neural image compression methods. Finally, we estimate bounds on the R-D function of natural images. The intrinsic dimension is likely too high for our lower bound to be useful, while our upper bounds on the Kodak and Tecnick datasets imply at least one dB (in PSNR) of theoretical room for improving state-of-the-art image compression methods, at various bitrates. We also validate our bounds against the BA algorithm over the various data sources, using a 2D marginal of the source to make BA feasible. We provide experimental details and additional results in Appendix A.5 and A.6. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"6b5714ef-abd2-4d62-a97b-39b9c7d4543c\"]],[null,null,0.6552840976010041,[[null,30278,30804]],[[[30278,30320,[[[30278,30320,[\"6.2 DATA FROM PARTICLE PHYSICS AND SPEECH \"]]]]],[30320,30804,[[[30320,30804,[\"The quickly deteriorating lower bound on higher (even 16) dimensional Gaussians may seem dis-heartening. However, the Gaussian is also the hardest continuous source to compress under squared error (Gibson, 2017), and real-world data often exhibits considerably more structure than Gaussian noise. In this subsection, we experiment on data from particle physics (Howard et al., 2021) and speech (Jackson et al., 2018), and indeed obtain improved sandwich bounds compared to Gaussians. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"053f54b5-444f-43fe-ab1f-2f51488445f1\"]]],[[[null,0,146],[0,1,2,3]],[[null,290,324],[0]],[[null,481,481],[1]],[[null,669,669],[2,3]],[[null,670,786],[3,4]]],[[\"What is the R-D function?\",\"What is the Blahut-Arimoto algorithm?\",\"How are R-D upper bounds estimated?\"]]]"]]
24908
[["wrb.fr",null,"[[\"Yes, the proposed algorithms can be applied to other classes of probability distributions beyond the Gaussian distribution and the \\\"banana\\\" source [1-4].\\n\\nSpecifically:\\n*   The paper makes the **first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source** requiring only i.i.d. data samples [1].\\n*   They introduce new algorithms for upper and lower bounding the R-D function of a **general (i.e., discrete, continuous, or neither), unknown memoryless source** [2].\\n*   Extensive experiments were performed, obtaining non-trivial sandwich bounds on various data sources, including **GAN-generated artificial sources and real-world data from speech and physics** [3, 4].\\n*   On particle physics and speech data, tighter sandwich bounds were obtained compared to similar dimensional Gaussians [4, 5].\\n*   The upper bound algorithm optimizes a β-VAE whose likelihood model is specified by the distortion metric, with a common case being a Gaussian likelihood with a fixed variance [6].\\n*   For the lower bound, while a practical implementation restricts to a squared error distortion, the theoretical derivation is for a general continuous reproduction alphabet [3, 7].\",null,[\"724e4669-1d95-45ee-b496-0c464e7ee299\",\"fd97e06c-5b55-4957-accc-b0adff291127\",2330387623],null,[[[[0,147,[[[0,147,[\"Yes, the proposed algorithms can be applied to other classes of probability distributions beyond the Gaussian distribution and the \\\"banana\\\" source.\"]]]]],[147,160,[[[147,160,[\"Specifically:\"]]]]],[160,325,[[[160,180,[\"The paper makes the \"]],[180,289,[\"first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source\",[true]]],[289,325,[\" requiring only i.i.d. data samples.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[325,482,[[[325,406,[\"They introduce new algorithms for upper and lower bounding the R-D function of a \"]],[406,481,[\"general (i.e., discrete, continuous, or neither), unknown memoryless source\",[true]]],[481,482,[\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[482,670,[[[482,593,[\"Extensive experiments were performed, obtaining non-trivial sandwich bounds on various data sources, including \"]],[593,669,[\"GAN-generated artificial sources and real-world data from speech and physics\",[true]]],[669,670,[\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]],[670,787,[[[670,787,[\"On particle physics and speech data, tighter sandwich bounds were obtained compared to similar dimensional Gaussians.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,3]]],[787,962,[[[787,962,[\"The upper bound algorithm optimizes a β-VAE whose likelihood model is specified by the distortion metric, with a common case being a Gaussian likelihood with a fixed variance.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,5,4]]]],[[[\"f1cd86a3-5a00-4ac4-9eaa-736941453c81\"],[null,0,146]],[[\"7f4d56fc-b2d8-475e-a287-1e2be5113f65\"],[null,0,146]],[[\"2d86a1d3-0798-44be-89a1-ff72843cbb56\"],[null,0,146]],[[\"4ba75094-80e3-426d-a7ec-1b69010be52f\"],[null,0,146]],[[\"f1cd86a3-5a00-4ac4-9eaa-736941453c81\"],[null,290,324]],[[\"7f4d56fc-b2d8-475e-a287-1e2be5113f65\"],[null,481,481]],[[\"2d86a1d3-0798-44be-89a1-ff72843cbb56\"],[null,669,669]],[[\"4ba75094-80e3-426d-a7ec-1b69010be52f\"],[null,669,669]],[[\"4ba75094-80e3-426d-a7ec-1b69010be52f\"],[null,670,786]],[[\"0f5fcca7-6ceb-442b-a5c5-b26ad72f9f97\"],[null,670,786]],[[\"6a8f883f-5f95-4b6e-bcb5-910781e26588\"],[null,787,961]],[[\"2d86a1d3-0798-44be-89a1-ff72843cbb56\"],[null,962,1133]],[[\"7d374768-282c-4f9e-a249-15447af33b25\"],[null,962,1133]]]],null,null,[[[\"f1cd86a3-5a00-4ac4-9eaa-736941453c81\"],[null,null,0.693826975858025,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"f1cd86a3-5a00-4ac4-9eaa-736941453c81\"]]],[[\"7f4d56fc-b2d8-475e-a287-1e2be5113f65\"],[null,null,0.6696627165023853,[[null,3970,4623]],[[[3970,3970,[]],[3970,3971,[[[3970,3971,[\" \"]]]]],[3971,4265,[[[3971,4265,[\"In this work, we make progress on this long-standing problem in information theory using tools from machine learning, and introduce new algorithms for upper and lower bounding the R-D function of a general (i.e., discrete, continuous, or neither), unknown memoryless source. More specifically, \"]]]]],[4265,4623,[[[4265,4623,[\"1. Similarly to how a VAE with a discrete likelihood model minimizes an upper bound on the data entropy, we establish that any β-VAE with a likelihood model induced by a distortion metric minimizes an upper bound on the data rate-distortion function. We thus open the deep generative modeling toolbox to the estimation of an upper bound on the R-D function. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"7f4d56fc-b2d8-475e-a287-1e2be5113f65\"]]],[[\"2d86a1d3-0798-44be-89a1-ff72843cbb56\"],[null,null,0.6587625257247187,[[null,4623,5362]],[[[4623,4905,[[[4623,4905,[\"2. We derive a lower bound estimator of the R-D function that can be made asymptotically exact and optimized by stochastic gradient ascent. Facing the difficulty of the problem involving global optimization, we restrict to a squared error distortion for a practical implementation. \"]]]]],[4905,5362,[[[4905,5362,[\"3. We perform extensive experiments and obtain non-trivial sandwich bounds on various data sources, including GAN-generated artificial sources and real-world data from speech and physics. Our results shed light on the effectiveness of neural compression approaches (Ballé et al., 2021; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), and identify the intrinsic (rather than nominal) dimension of data as a key factor affecting the tightness of our lower bound. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"2d86a1d3-0798-44be-89a1-ff72843cbb56\"]]],[[\"4ba75094-80e3-426d-a7ec-1b69010be52f\"],[null,null,0.7162808213644777,[[null,24759,26239]],[[[24759,24773,[[[24759,24773,[\"6 EXPERIMENTS \"]]]]],[24773,26239,[[[24773,26239,[\"We estimate the R-D functions of a variety of artificial and real-world data sources, in settings where the BA algorithm is infeasible and no prior known method has been applied. On Gaussian sources, our upper bound algorithm is shown to converge to the exact R-D function, while our lower bounds become increasingly loose in higher dimensions, an issue we investigate subsequently. We obtain tighter sandwich bounds on particle physics and speech data than on similar dimensional Gaussians, and compare with the performance of neural compression. We further investigate looseness in the lower bound, experimentally establishing the intrinsic dimension of the data as a much more critical contributing factor than the nominal/ambient dimension. Indeed, we obtain tight sandwich bounds on high-dimension GAN-generated images with a low intrinsic dimension, and compare with popular neural image compression methods. Finally, we estimate bounds on the R-D function of natural images. The intrinsic dimension is likely too high for our lower bound to be useful, while our upper bounds on the Kodak and Tecnick datasets imply at least one dB (in PSNR) of theoretical room for improving state-of-the-art image compression methods, at various bitrates. We also validate our bounds against the BA algorithm over the various data sources, using a 2D marginal of the source to make BA feasible. We provide experimental details and additional results in Appendix A.5 and A.6. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"4ba75094-80e3-426d-a7ec-1b69010be52f\"]]],[[\"0f5fcca7-6ceb-442b-a5c5-b26ad72f9f97\"],[null,null,0.6552840976010041,[[null,30278,30804]],[[[30278,30320,[[[30278,30320,[\"6.2 DATA FROM PARTICLE PHYSICS AND SPEECH \"]]]]],[30320,30804,[[[30320,30804,[\"The quickly deteriorating lower bound on higher (even 16) dimensional Gaussians may seem dis-heartening. However, the Gaussian is also the hardest continuous source to compress under squared error (Gibson, 2017), and real-world data often exhibits considerably more structure than Gaussian noise. In this subsection, we experiment on data from particle physics (Howard et al., 2021) and speech (Jackson et al., 2018), and indeed obtain improved sandwich bounds compared to Gaussians. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"0f5fcca7-6ceb-442b-a5c5-b26ad72f9f97\"]]],[[\"6a8f883f-5f95-4b6e-bcb5-910781e26588\"],[null,null,0.7356237310326131,[[null,8407,9440]],[[[8407,8627,[[[8407,8627,[\"1Both the expected distortion and mutual information terms are defined w.r.t. the joint distribution PXQY |X . We formally describe the general setting of the paper, including the technical definitions, in Appendix A.1. \"]]]]],[8627,8627,[]],[8627,8628,[[[8627,8628,[\" \"]]]]],[8628,8652,[[[8628,8652,[\"3 UPPER BOUND ALGORITHM \"]]]]],[8652,9440,[[[8652,9440,[\"R-D theory (Cover \\u0026 Thomas, 2006) tells us that every (distortion, rate) pair lying above the R(D)-curve is in theory realizable by a (possibly expensive) compression algorithm. An upper bound on R(D) thus reveals what kind of compression performance is theoretically achievable. Towards this goal, we borrow the variational principle of the Blahut-Arimoto (BA) algorithm, but extend it to a general (e.g., non-discrete) source requiring only its samples. Our resulting algorithm optimizes a β-VAE whose likelihood model is specified by the distortion metric, a common case being a Gaussian likelihood with a fixed variance. For the first time, we establish this class of models as computing a model-agnostic upper bound on the source R-D function, as defined by a data compression task. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"6a8f883f-5f95-4b6e-bcb5-910781e26588\"]]],[[\"7d374768-282c-4f9e-a249-15447af33b25\"],[null,null,0.6776464091575531,[[null,13874,15039]],[[[13874,13888,[[[13874,13888,[\"∏L l\\u003d1QZl|Z\\u003cl \"]]]]],[13888,13936,[[[13888,13936,[\"(similarly for QZ|X ) as in a hierarchical VAE. \"]]]]],[13936,13960,[[[13936,13960,[\"4 LOWER BOUND ALGORITHM \"]]]]],[13960,15039,[[[13960,15039,[\"Without knowing the tightness of an R-D upper bound, we could be wasting time and resources trying to improve the R-D performance of a compression algorithm, when it is in fact already close to the theoretical limit. This would be avoided if we could find a matching lower bound on R(D). Unfortunately, the problem turns out to be much more difficult computationally. Indeed, every compression algorithm, or every pair of variational distributions (QY , QY |X) yields a point above R(D). Conversely, establishing a lower bound requires disproving the existence of any compression algorithm that can conceivably operate below the R(D) curve. In this section, we derive an algorithm that can in theory produce arbitrarily tight R-D lower bounds. However, as an indication of its difficulty, the problem requires globally maximizing a family of partition functions. By restricting to a continuous reproduction alphabet and a squared error distortion, we make some progress on this problem and obtain useful lower bounds especially on data with low intrinsic dimension (see Sec. 6). \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"7d374768-282c-4f9e-a249-15447af33b25\"]]]]]],[[null,null,0.693826975858025,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"f1cd86a3-5a00-4ac4-9eaa-736941453c81\"]],[null,null,0.6696627165023853,[[null,3970,4623]],[[[3970,3970,[]],[3970,3971,[[[3970,3971,[\" \"]]]]],[3971,4265,[[[3971,4265,[\"In this work, we make progress on this long-standing problem in information theory using tools from machine learning, and introduce new algorithms for upper and lower bounding the R-D function of a general (i.e., discrete, continuous, or neither), unknown memoryless source. More specifically, \"]]]]],[4265,4623,[[[4265,4623,[\"1. Similarly to how a VAE with a discrete likelihood model minimizes an upper bound on the data entropy, we establish that any β-VAE with a likelihood model induced by a distortion metric minimizes an upper bound on the data rate-distortion function. We thus open the deep generative modeling toolbox to the estimation of an upper bound on the R-D function. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"7f4d56fc-b2d8-475e-a287-1e2be5113f65\"]],[null,null,0.6587625257247187,[[null,4623,5362]],[[[4623,4905,[[[4623,4905,[\"2. We derive a lower bound estimator of the R-D function that can be made asymptotically exact and optimized by stochastic gradient ascent. Facing the difficulty of the problem involving global optimization, we restrict to a squared error distortion for a practical implementation. \"]]]]],[4905,5362,[[[4905,5362,[\"3. We perform extensive experiments and obtain non-trivial sandwich bounds on various data sources, including GAN-generated artificial sources and real-world data from speech and physics. Our results shed light on the effectiveness of neural compression approaches (Ballé et al., 2021; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), and identify the intrinsic (rather than nominal) dimension of data as a key factor affecting the tightness of our lower bound. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"2d86a1d3-0798-44be-89a1-ff72843cbb56\"]],[null,null,0.7162808213644777,[[null,24759,26239]],[[[24759,24773,[[[24759,24773,[\"6 EXPERIMENTS \"]]]]],[24773,26239,[[[24773,26239,[\"We estimate the R-D functions of a variety of artificial and real-world data sources, in settings where the BA algorithm is infeasible and no prior known method has been applied. On Gaussian sources, our upper bound algorithm is shown to converge to the exact R-D function, while our lower bounds become increasingly loose in higher dimensions, an issue we investigate subsequently. We obtain tighter sandwich bounds on particle physics and speech data than on similar dimensional Gaussians, and compare with the performance of neural compression. We further investigate looseness in the lower bound, experimentally establishing the intrinsic dimension of the data as a much more critical contributing factor than the nominal/ambient dimension. Indeed, we obtain tight sandwich bounds on high-dimension GAN-generated images with a low intrinsic dimension, and compare with popular neural image compression methods. Finally, we estimate bounds on the R-D function of natural images. The intrinsic dimension is likely too high for our lower bound to be useful, while our upper bounds on the Kodak and Tecnick datasets imply at least one dB (in PSNR) of theoretical room for improving state-of-the-art image compression methods, at various bitrates. We also validate our bounds against the BA algorithm over the various data sources, using a 2D marginal of the source to make BA feasible. We provide experimental details and additional results in Appendix A.5 and A.6. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"4ba75094-80e3-426d-a7ec-1b69010be52f\"]],[null,null,0.6552840976010041,[[null,30278,30804]],[[[30278,30320,[[[30278,30320,[\"6.2 DATA FROM PARTICLE PHYSICS AND SPEECH \"]]]]],[30320,30804,[[[30320,30804,[\"The quickly deteriorating lower bound on higher (even 16) dimensional Gaussians may seem dis-heartening. However, the Gaussian is also the hardest continuous source to compress under squared error (Gibson, 2017), and real-world data often exhibits considerably more structure than Gaussian noise. In this subsection, we experiment on data from particle physics (Howard et al., 2021) and speech (Jackson et al., 2018), and indeed obtain improved sandwich bounds compared to Gaussians. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"0f5fcca7-6ceb-442b-a5c5-b26ad72f9f97\"]],[null,null,0.7356237310326131,[[null,8407,9440]],[[[8407,8627,[[[8407,8627,[\"1Both the expected distortion and mutual information terms are defined w.r.t. the joint distribution PXQY |X . We formally describe the general setting of the paper, including the technical definitions, in Appendix A.1. \"]]]]],[8627,8627,[]],[8627,8628,[[[8627,8628,[\" \"]]]]],[8628,8652,[[[8628,8652,[\"3 UPPER BOUND ALGORITHM \"]]]]],[8652,9440,[[[8652,9440,[\"R-D theory (Cover \\u0026 Thomas, 2006) tells us that every (distortion, rate) pair lying above the R(D)-curve is in theory realizable by a (possibly expensive) compression algorithm. An upper bound on R(D) thus reveals what kind of compression performance is theoretically achievable. Towards this goal, we borrow the variational principle of the Blahut-Arimoto (BA) algorithm, but extend it to a general (e.g., non-discrete) source requiring only its samples. Our resulting algorithm optimizes a β-VAE whose likelihood model is specified by the distortion metric, a common case being a Gaussian likelihood with a fixed variance. For the first time, we establish this class of models as computing a model-agnostic upper bound on the source R-D function, as defined by a data compression task. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"6a8f883f-5f95-4b6e-bcb5-910781e26588\"]],[null,null,0.6776464091575531,[[null,13874,15039]],[[[13874,13888,[[[13874,13888,[\"∏L l\\u003d1QZl|Z\\u003cl \"]]]]],[13888,13936,[[[13888,13936,[\"(similarly for QZ|X ) as in a hierarchical VAE. \"]]]]],[13936,13960,[[[13936,13960,[\"4 LOWER BOUND ALGORITHM \"]]]]],[13960,15039,[[[13960,15039,[\"Without knowing the tightness of an R-D upper bound, we could be wasting time and resources trying to improve the R-D performance of a compression algorithm, when it is in fact already close to the theoretical limit. This would be avoided if we could find a matching lower bound on R(D). Unfortunately, the problem turns out to be much more difficult computationally. Indeed, every compression algorithm, or every pair of variational distributions (QY , QY |X) yields a point above R(D). Conversely, establishing a lower bound requires disproving the existence of any compression algorithm that can conceivably operate below the R(D) curve. In this section, we derive an algorithm that can in theory produce arbitrarily tight R-D lower bounds. However, as an indication of its difficulty, the problem requires globally maximizing a family of partition functions. By restricting to a continuous reproduction alphabet and a squared error distortion, we make some progress on this problem and obtain useful lower bounds especially on data with low intrinsic dimension (see Sec. 6). \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"7d374768-282c-4f9e-a249-15447af33b25\"]]],[[[null,0,146],[0,1,2,3]],[[null,290,324],[0]],[[null,481,481],[1]],[[null,669,669],[2,3]],[[null,670,786],[3,4]],[[null,787,961],[5]],[[null,962,1133],[2,6]]],[[\"What is the R-D function?\",\"What is the Blahut-Arimoto algorithm?\",\"How are R-D upper bounds estimated?\"]]]"]]
25638
[["wrb.fr",null,"[[\"Yes, the proposed algorithms can be applied to other classes of probability distributions beyond the Gaussian distribution and the \\\"banana\\\" source [1-4].\\n\\nSpecifically:\\n*   The paper makes the **first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source** requiring only i.i.d. data samples [1].\\n*   They introduce new algorithms for upper and lower bounding the R-D function of a **general (i.e., discrete, continuous, or neither), unknown memoryless source** [2].\\n*   Extensive experiments were performed, obtaining non-trivial sandwich bounds on various data sources, including **GAN-generated artificial sources and real-world data from speech and physics** [3, 4].\\n*   On particle physics and speech data, tighter sandwich bounds were obtained compared to similar dimensional Gaussians [4, 5].\\n*   The upper bound algorithm optimizes a β-VAE whose likelihood model is specified by the distortion metric, with a common case being a Gaussian likelihood with a fixed variance [6].\\n*   For the lower bound, while a practical implementation restricts to a squared error distortion, the theoretical derivation is for a general continuous reproduction alphabet [3, 7].\",null,[\"724e4669-1d95-45ee-b496-0c464e7ee299\",\"fd97e06c-5b55-4957-accc-b0adff291127\",2330387623],null,[[[[0,147,[[[0,147,[\"Yes, the proposed algorithms can be applied to other classes of probability distributions beyond the Gaussian distribution and the \\\"banana\\\" source.\"]]]]],[147,160,[[[147,160,[\"Specifically:\"]]]]],[160,325,[[[160,180,[\"The paper makes the \"]],[180,289,[\"first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source\",[true]]],[289,325,[\" requiring only i.i.d. data samples.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,1,0]]],[325,482,[[[325,406,[\"They introduce new algorithms for upper and lower bounding the R-D function of a \"]],[406,481,[\"general (i.e., discrete, continuous, or neither), unknown memoryless source\",[true]]],[481,482,[\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,2,1]]],[482,670,[[[482,593,[\"Extensive experiments were performed, obtaining non-trivial sandwich bounds on various data sources, including \"]],[593,669,[\"GAN-generated artificial sources and real-world data from speech and physics\",[true]]],[669,670,[\".\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,3,2]]],[670,787,[[[670,787,[\"On particle physics and speech data, tighter sandwich bounds were obtained compared to similar dimensional Gaussians.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,4,3]]],[787,962,[[[787,962,[\"The upper bound algorithm optimizes a β-VAE whose likelihood model is specified by the distortion metric, with a common case being a Gaussian likelihood with a fixed variance.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,5,4]]],[962,1134,[[[962,1134,[\"For the lower bound, while a practical implementation restricts to a squared error distortion, the theoretical derivation is for a general continuous reproduction alphabet.\"]]],[null,1],null,[null,null,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,\"•\",1,6,5]]]],[[[\"f1cd86a3-5a00-4ac4-9eaa-736941453c81\"],[null,0,146]],[[\"7f4d56fc-b2d8-475e-a287-1e2be5113f65\"],[null,0,146]],[[\"2d86a1d3-0798-44be-89a1-ff72843cbb56\"],[null,0,146]],[[\"4ba75094-80e3-426d-a7ec-1b69010be52f\"],[null,0,146]],[[\"f1cd86a3-5a00-4ac4-9eaa-736941453c81\"],[null,290,324]],[[\"7f4d56fc-b2d8-475e-a287-1e2be5113f65\"],[null,481,481]],[[\"2d86a1d3-0798-44be-89a1-ff72843cbb56\"],[null,669,669]],[[\"4ba75094-80e3-426d-a7ec-1b69010be52f\"],[null,669,669]],[[\"4ba75094-80e3-426d-a7ec-1b69010be52f\"],[null,670,786]],[[\"0f5fcca7-6ceb-442b-a5c5-b26ad72f9f97\"],[null,670,786]],[[\"6a8f883f-5f95-4b6e-bcb5-910781e26588\"],[null,787,961]],[[\"2d86a1d3-0798-44be-89a1-ff72843cbb56\"],[null,962,1133]],[[\"7d374768-282c-4f9e-a249-15447af33b25\"],[null,962,1133]]]],null,null,[[[\"f1cd86a3-5a00-4ac4-9eaa-736941453c81\"],[null,null,0.693826975858025,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"f1cd86a3-5a00-4ac4-9eaa-736941453c81\"]]],[[\"7f4d56fc-b2d8-475e-a287-1e2be5113f65\"],[null,null,0.6696627165023853,[[null,3970,4623]],[[[3970,3970,[]],[3970,3971,[[[3970,3971,[\" \"]]]]],[3971,4265,[[[3971,4265,[\"In this work, we make progress on this long-standing problem in information theory using tools from machine learning, and introduce new algorithms for upper and lower bounding the R-D function of a general (i.e., discrete, continuous, or neither), unknown memoryless source. More specifically, \"]]]]],[4265,4623,[[[4265,4623,[\"1. Similarly to how a VAE with a discrete likelihood model minimizes an upper bound on the data entropy, we establish that any β-VAE with a likelihood model induced by a distortion metric minimizes an upper bound on the data rate-distortion function. We thus open the deep generative modeling toolbox to the estimation of an upper bound on the R-D function. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"7f4d56fc-b2d8-475e-a287-1e2be5113f65\"]]],[[\"2d86a1d3-0798-44be-89a1-ff72843cbb56\"],[null,null,0.6587625257247187,[[null,4623,5362]],[[[4623,4905,[[[4623,4905,[\"2. We derive a lower bound estimator of the R-D function that can be made asymptotically exact and optimized by stochastic gradient ascent. Facing the difficulty of the problem involving global optimization, we restrict to a squared error distortion for a practical implementation. \"]]]]],[4905,5362,[[[4905,5362,[\"3. We perform extensive experiments and obtain non-trivial sandwich bounds on various data sources, including GAN-generated artificial sources and real-world data from speech and physics. Our results shed light on the effectiveness of neural compression approaches (Ballé et al., 2021; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), and identify the intrinsic (rather than nominal) dimension of data as a key factor affecting the tightness of our lower bound. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"2d86a1d3-0798-44be-89a1-ff72843cbb56\"]]],[[\"4ba75094-80e3-426d-a7ec-1b69010be52f\"],[null,null,0.7162808213644777,[[null,24759,26239]],[[[24759,24773,[[[24759,24773,[\"6 EXPERIMENTS \"]]]]],[24773,26239,[[[24773,26239,[\"We estimate the R-D functions of a variety of artificial and real-world data sources, in settings where the BA algorithm is infeasible and no prior known method has been applied. On Gaussian sources, our upper bound algorithm is shown to converge to the exact R-D function, while our lower bounds become increasingly loose in higher dimensions, an issue we investigate subsequently. We obtain tighter sandwich bounds on particle physics and speech data than on similar dimensional Gaussians, and compare with the performance of neural compression. We further investigate looseness in the lower bound, experimentally establishing the intrinsic dimension of the data as a much more critical contributing factor than the nominal/ambient dimension. Indeed, we obtain tight sandwich bounds on high-dimension GAN-generated images with a low intrinsic dimension, and compare with popular neural image compression methods. Finally, we estimate bounds on the R-D function of natural images. The intrinsic dimension is likely too high for our lower bound to be useful, while our upper bounds on the Kodak and Tecnick datasets imply at least one dB (in PSNR) of theoretical room for improving state-of-the-art image compression methods, at various bitrates. We also validate our bounds against the BA algorithm over the various data sources, using a 2D marginal of the source to make BA feasible. We provide experimental details and additional results in Appendix A.5 and A.6. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"4ba75094-80e3-426d-a7ec-1b69010be52f\"]]],[[\"0f5fcca7-6ceb-442b-a5c5-b26ad72f9f97\"],[null,null,0.6552840976010041,[[null,30278,30804]],[[[30278,30320,[[[30278,30320,[\"6.2 DATA FROM PARTICLE PHYSICS AND SPEECH \"]]]]],[30320,30804,[[[30320,30804,[\"The quickly deteriorating lower bound on higher (even 16) dimensional Gaussians may seem dis-heartening. However, the Gaussian is also the hardest continuous source to compress under squared error (Gibson, 2017), and real-world data often exhibits considerably more structure than Gaussian noise. In this subsection, we experiment on data from particle physics (Howard et al., 2021) and speech (Jackson et al., 2018), and indeed obtain improved sandwich bounds compared to Gaussians. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"0f5fcca7-6ceb-442b-a5c5-b26ad72f9f97\"]]],[[\"6a8f883f-5f95-4b6e-bcb5-910781e26588\"],[null,null,0.7356237310326131,[[null,8407,9440]],[[[8407,8627,[[[8407,8627,[\"1Both the expected distortion and mutual information terms are defined w.r.t. the joint distribution PXQY |X . We formally describe the general setting of the paper, including the technical definitions, in Appendix A.1. \"]]]]],[8627,8627,[]],[8627,8628,[[[8627,8628,[\" \"]]]]],[8628,8652,[[[8628,8652,[\"3 UPPER BOUND ALGORITHM \"]]]]],[8652,9440,[[[8652,9440,[\"R-D theory (Cover \\u0026 Thomas, 2006) tells us that every (distortion, rate) pair lying above the R(D)-curve is in theory realizable by a (possibly expensive) compression algorithm. An upper bound on R(D) thus reveals what kind of compression performance is theoretically achievable. Towards this goal, we borrow the variational principle of the Blahut-Arimoto (BA) algorithm, but extend it to a general (e.g., non-discrete) source requiring only its samples. Our resulting algorithm optimizes a β-VAE whose likelihood model is specified by the distortion metric, a common case being a Gaussian likelihood with a fixed variance. For the first time, we establish this class of models as computing a model-agnostic upper bound on the source R-D function, as defined by a data compression task. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"6a8f883f-5f95-4b6e-bcb5-910781e26588\"]]],[[\"7d374768-282c-4f9e-a249-15447af33b25\"],[null,null,0.6776464091575531,[[null,13874,15039]],[[[13874,13888,[[[13874,13888,[\"∏L l\\u003d1QZl|Z\\u003cl \"]]]]],[13888,13936,[[[13888,13936,[\"(similarly for QZ|X ) as in a hierarchical VAE. \"]]]]],[13936,13960,[[[13936,13960,[\"4 LOWER BOUND ALGORITHM \"]]]]],[13960,15039,[[[13960,15039,[\"Without knowing the tightness of an R-D upper bound, we could be wasting time and resources trying to improve the R-D performance of a compression algorithm, when it is in fact already close to the theoretical limit. This would be avoided if we could find a matching lower bound on R(D). Unfortunately, the problem turns out to be much more difficult computationally. Indeed, every compression algorithm, or every pair of variational distributions (QY , QY |X) yields a point above R(D). Conversely, establishing a lower bound requires disproving the existence of any compression algorithm that can conceivably operate below the R(D) curve. In this section, we derive an algorithm that can in theory produce arbitrarily tight R-D lower bounds. However, as an indication of its difficulty, the problem requires globally maximizing a family of partition functions. By restricting to a continuous reproduction alphabet and a squared error distortion, we make some progress on this problem and obtain useful lower bounds especially on data with low intrinsic dimension (see Sec. 6). \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"7d374768-282c-4f9e-a249-15447af33b25\"]]]]]],[[null,null,0.693826975858025,[[null,0,1385]],[[[0,1,[[[0,1,[\" \"]]]]],[1,67,[[[1,67,[\"TOWARDS EMPIRICAL SANDWICH BOUNDS ON THE RATE-DISTORTION FUNCTION \"]]],[null,6]]],[67,160,[[[67,160,[\"Yibo Yang, Stephan Mandt Department of Computer Science, UC Irvine {yibo.yang,mandt}@uci.edu \"]]]]],[160,169,[[[160,169,[\"ABSTRACT \"]]]]],[169,1385,[[[169,1385,[\"Rate-distortion (R-D) function, a key quantity in information theory, characterizes the fundamental limit of how much a data source can be compressed subject to a fidelity criterion, by any compression algorithm. As researchers push for ever-improving compression performance, establishing the R-D function of a given data source is not only of scientific interest, but also reveals the possible room for improvement in compression algorithms. Previous work on this problem relied on distributional assumptions on the data source (Gibson, 2017) or only applied to discrete data. By contrast, this paper makes the first attempt at an algorithm for sandwiching the R-D function of a general (not necessarily discrete) source requiring only i.i.d. data samples. We estimate R-D sandwich bounds for a variety of artificial and real-world data sources, in settings far beyond the feasibility of any known method, and shed light on the optimality of neural data compression (Ballé et al., 2021; Yang et al., 2022). Our R-D upper bound on natural images indicates theoretical room for improving state-of-the-art image compression methods by at least one dB in PSNR at various bitrates. Our data and code can be found here. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"f1cd86a3-5a00-4ac4-9eaa-736941453c81\"]],[null,null,0.6696627165023853,[[null,3970,4623]],[[[3970,3970,[]],[3970,3971,[[[3970,3971,[\" \"]]]]],[3971,4265,[[[3971,4265,[\"In this work, we make progress on this long-standing problem in information theory using tools from machine learning, and introduce new algorithms for upper and lower bounding the R-D function of a general (i.e., discrete, continuous, or neither), unknown memoryless source. More specifically, \"]]]]],[4265,4623,[[[4265,4623,[\"1. Similarly to how a VAE with a discrete likelihood model minimizes an upper bound on the data entropy, we establish that any β-VAE with a likelihood model induced by a distortion metric minimizes an upper bound on the data rate-distortion function. We thus open the deep generative modeling toolbox to the estimation of an upper bound on the R-D function. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"7f4d56fc-b2d8-475e-a287-1e2be5113f65\"]],[null,null,0.6587625257247187,[[null,4623,5362]],[[[4623,4905,[[[4623,4905,[\"2. We derive a lower bound estimator of the R-D function that can be made asymptotically exact and optimized by stochastic gradient ascent. Facing the difficulty of the problem involving global optimization, we restrict to a squared error distortion for a practical implementation. \"]]]]],[4905,5362,[[[4905,5362,[\"3. We perform extensive experiments and obtain non-trivial sandwich bounds on various data sources, including GAN-generated artificial sources and real-world data from speech and physics. Our results shed light on the effectiveness of neural compression approaches (Ballé et al., 2021; Minnen et al., 2018; Minnen \\u0026 Singh, 2020), and identify the intrinsic (rather than nominal) dimension of data as a key factor affecting the tightness of our lower bound. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"2d86a1d3-0798-44be-89a1-ff72843cbb56\"]],[null,null,0.7162808213644777,[[null,24759,26239]],[[[24759,24773,[[[24759,24773,[\"6 EXPERIMENTS \"]]]]],[24773,26239,[[[24773,26239,[\"We estimate the R-D functions of a variety of artificial and real-world data sources, in settings where the BA algorithm is infeasible and no prior known method has been applied. On Gaussian sources, our upper bound algorithm is shown to converge to the exact R-D function, while our lower bounds become increasingly loose in higher dimensions, an issue we investigate subsequently. We obtain tighter sandwich bounds on particle physics and speech data than on similar dimensional Gaussians, and compare with the performance of neural compression. We further investigate looseness in the lower bound, experimentally establishing the intrinsic dimension of the data as a much more critical contributing factor than the nominal/ambient dimension. Indeed, we obtain tight sandwich bounds on high-dimension GAN-generated images with a low intrinsic dimension, and compare with popular neural image compression methods. Finally, we estimate bounds on the R-D function of natural images. The intrinsic dimension is likely too high for our lower bound to be useful, while our upper bounds on the Kodak and Tecnick datasets imply at least one dB (in PSNR) of theoretical room for improving state-of-the-art image compression methods, at various bitrates. We also validate our bounds against the BA algorithm over the various data sources, using a 2D marginal of the source to make BA feasible. We provide experimental details and additional results in Appendix A.5 and A.6. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"4ba75094-80e3-426d-a7ec-1b69010be52f\"]],[null,null,0.6552840976010041,[[null,30278,30804]],[[[30278,30320,[[[30278,30320,[\"6.2 DATA FROM PARTICLE PHYSICS AND SPEECH \"]]]]],[30320,30804,[[[30320,30804,[\"The quickly deteriorating lower bound on higher (even 16) dimensional Gaussians may seem dis-heartening. However, the Gaussian is also the hardest continuous source to compress under squared error (Gibson, 2017), and real-world data often exhibits considerably more structure than Gaussian noise. In this subsection, we experiment on data from particle physics (Howard et al., 2021) and speech (Jackson et al., 2018), and indeed obtain improved sandwich bounds compared to Gaussians. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"0f5fcca7-6ceb-442b-a5c5-b26ad72f9f97\"]],[null,null,0.7356237310326131,[[null,8407,9440]],[[[8407,8627,[[[8407,8627,[\"1Both the expected distortion and mutual information terms are defined w.r.t. the joint distribution PXQY |X . We formally describe the general setting of the paper, including the technical definitions, in Appendix A.1. \"]]]]],[8627,8627,[]],[8627,8628,[[[8627,8628,[\" \"]]]]],[8628,8652,[[[8628,8652,[\"3 UPPER BOUND ALGORITHM \"]]]]],[8652,9440,[[[8652,9440,[\"R-D theory (Cover \\u0026 Thomas, 2006) tells us that every (distortion, rate) pair lying above the R(D)-curve is in theory realizable by a (possibly expensive) compression algorithm. An upper bound on R(D) thus reveals what kind of compression performance is theoretically achievable. Towards this goal, we borrow the variational principle of the Blahut-Arimoto (BA) algorithm, but extend it to a general (e.g., non-discrete) source requiring only its samples. Our resulting algorithm optimizes a β-VAE whose likelihood model is specified by the distortion metric, a common case being a Gaussian likelihood with a fixed variance. For the first time, we establish this class of models as computing a model-agnostic upper bound on the source R-D function, as defined by a data compression task. \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"6a8f883f-5f95-4b6e-bcb5-910781e26588\"]],[null,null,0.6776464091575531,[[null,13874,15039]],[[[13874,13888,[[[13874,13888,[\"∏L l\\u003d1QZl|Z\\u003cl \"]]]]],[13888,13936,[[[13888,13936,[\"(similarly for QZ|X ) as in a hierarchical VAE. \"]]]]],[13936,13960,[[[13936,13960,[\"4 LOWER BOUND ALGORITHM \"]]]]],[13960,15039,[[[13960,15039,[\"Without knowing the tightness of an R-D upper bound, we could be wasting time and resources trying to improve the R-D performance of a compression algorithm, when it is in fact already close to the theoretical limit. This would be avoided if we could find a matching lower bound on R(D). Unfortunately, the problem turns out to be much more difficult computationally. Indeed, every compression algorithm, or every pair of variational distributions (QY , QY |X) yields a point above R(D). Conversely, establishing a lower bound requires disproving the existence of any compression algorithm that can conceivably operate below the R(D) curve. In this section, we derive an algorithm that can in theory produce arbitrarily tight R-D lower bounds. However, as an indication of its difficulty, the problem requires globally maximizing a family of partition functions. By restricting to a continuous reproduction alphabet and a squared error distortion, we make some progress on this problem and obtain useful lower bounds especially on data with low intrinsic dimension (see Sec. 6). \"]]]]]]],[[[\"b24a2382-07dd-4e7c-97b4-46f6e68ae81f\"],\"1dbaa973-3c34-47f2-a573-067aad70f1a8\"]],[\"7d374768-282c-4f9e-a249-15447af33b25\"]]],[[[null,0,146],[0,1,2,3]],[[null,290,324],[0]],[[null,481,481],[1]],[[null,669,669],[2,3]],[[null,670,786],[3,4]],[[null,787,961],[5]],[[null,962,1133],[2,6]]],[[\"What is the R-D function?\",\"What is the Blahut-Arimoto algorithm?\",\"How are R-D upper bounds estimated?\"]]]"]]
58
[["di",3337],["af.httprm",3336,"556939933057988275",61]]
27
[["e",7,null,null,81584]]
