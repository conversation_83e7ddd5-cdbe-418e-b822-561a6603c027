)]}'

2415
[["wrb.fr",null,"[[\"The low performance of the \\\"DESCRIPTION\\\" category in Table 5 is because **annotators found it the most difficult class to work with** [1].\",null,[\"640a6188-a26f-4e83-948e-d4dfc6c8878c\",\"0cbb3e5e-9f4d-4c24-98b1-d0e8e5317e53\",378497916],null,[[null,[[[\"68eda81e-a6dc-4129-8b7d-0bac752ded12\"],[null,129,129]]]],null,null,[[[\"68eda81e-a6dc-4129-8b7d-0bac752ded12\"],[null,null,0.7099583687326536,[[null,31320,31963]],[[[31320,31466,[[[31320,31466,[\"ENTITY and LOCATION (largely following the nat-ural distribution of the respective classes in the dataset). There is quite a drop for the NUMERIC \"]]]]],[31466,31514,[[[31466,31514,[\"class, and a big drop again for the DESCRIPTION \"]]]]],[31514,31589,[[[31514,31589,[\"class, which was also the class our annotators found most difficulty with. \"]]]]],[31589,31963,[[[31589,31963,[\"From the perspective of the number of spans, the model performs best on questions with many (\\u003e 7) answers. We think this is because the answers are usually a list of spans with similar semantics, often structured as a simple coordination. The perfor-mance drops as the answer number decreases be-cause the syntactic pattern in which answer spans occurs is less predictable. \"]]]]]]],[[[\"71470497-6e90-4154-8860-152b57fe975e\"],\"3067e6a6-37a5-467f-bdaa-c3f090e888a2\"]],[\"68eda81e-a6dc-4129-8b7d-0bac752ded12\"]]]]]],[[null,null,0.7099583687326536,[[null,31320,31963]],[[[31320,31466,[[[31320,31466,[\"ENTITY and LOCATION (largely following the nat-ural distribution of the respective classes in the dataset). There is quite a drop for the NUMERIC \"]]]]],[31466,31514,[[[31466,31514,[\"class, and a big drop again for the DESCRIPTION \"]]]]],[31514,31589,[[[31514,31589,[\"class, which was also the class our annotators found most difficulty with. \"]]]]],[31589,31963,[[[31589,31963,[\"From the perspective of the number of spans, the model performs best on questions with many (\\u003e 7) answers. We think this is because the answers are usually a list of spans with similar semantics, often structured as a simple coordination. The perfor-mance drops as the answer number decreases be-cause the syntactic pattern in which answer spans occurs is less predictable. \"]]]]]]],[[[\"71470497-6e90-4154-8860-152b57fe975e\"],\"3067e6a6-37a5-467f-bdaa-c3f090e888a2\"]],[\"68eda81e-a6dc-4129-8b7d-0bac752ded12\"]]],[[[null,129,129],[0]]]]"]]
2614
[["wrb.fr",null,"[[\"The low performance of the \\\"DESCRIPTION\\\" category in Table 5 is because **annotators found it the most difficult class to work with** [1].\",null,[\"640a6188-a26f-4e83-948e-d4dfc6c8878c\",\"0cbb3e5e-9f4d-4c24-98b1-d0e8e5317e53\",378497916],null,[[[[0,130,[[[0,72,[\"The low performance of the \\\"DESCRIPTION\\\" category in Table 5 is because \"]],[72,129,[\"annotators found it the most difficult class to work with\",[true]]],[129,130,[\".\"]]]]]],[[[\"68eda81e-a6dc-4129-8b7d-0bac752ded12\"],[null,129,129]]]],null,null,[[[\"68eda81e-a6dc-4129-8b7d-0bac752ded12\"],[null,null,0.7099583687326536,[[null,31320,31963]],[[[31320,31466,[[[31320,31466,[\"ENTITY and LOCATION (largely following the nat-ural distribution of the respective classes in the dataset). There is quite a drop for the NUMERIC \"]]]]],[31466,31514,[[[31466,31514,[\"class, and a big drop again for the DESCRIPTION \"]]]]],[31514,31589,[[[31514,31589,[\"class, which was also the class our annotators found most difficulty with. \"]]]]],[31589,31963,[[[31589,31963,[\"From the perspective of the number of spans, the model performs best on questions with many (\\u003e 7) answers. We think this is because the answers are usually a list of spans with similar semantics, often structured as a simple coordination. The perfor-mance drops as the answer number decreases be-cause the syntactic pattern in which answer spans occurs is less predictable. \"]]]]]]],[[[\"71470497-6e90-4154-8860-152b57fe975e\"],\"3067e6a6-37a5-467f-bdaa-c3f090e888a2\"]],[\"68eda81e-a6dc-4129-8b7d-0bac752ded12\"]]]]]],[[null,null,0.7099583687326536,[[null,31320,31963]],[[[31320,31466,[[[31320,31466,[\"ENTITY and LOCATION (largely following the nat-ural distribution of the respective classes in the dataset). There is quite a drop for the NUMERIC \"]]]]],[31466,31514,[[[31466,31514,[\"class, and a big drop again for the DESCRIPTION \"]]]]],[31514,31589,[[[31514,31589,[\"class, which was also the class our annotators found most difficulty with. \"]]]]],[31589,31963,[[[31589,31963,[\"From the perspective of the number of spans, the model performs best on questions with many (\\u003e 7) answers. We think this is because the answers are usually a list of spans with similar semantics, often structured as a simple coordination. The perfor-mance drops as the answer number decreases be-cause the syntactic pattern in which answer spans occurs is less predictable. \"]]]]]]],[[[\"71470497-6e90-4154-8860-152b57fe975e\"],\"3067e6a6-37a5-467f-bdaa-c3f090e888a2\"]],[\"68eda81e-a6dc-4129-8b7d-0bac752ded12\"]]],[[[null,129,129],[0]]]]"]]
60
[["di",2433],["af.httprm",2432,"-3040097433984338035",57]]
26
[["e",5,null,null,5133]]
