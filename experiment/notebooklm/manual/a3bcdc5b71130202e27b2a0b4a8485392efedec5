)]}'

2578
[["wrb.fr",null,"[[\"For **binary (discrete) treatments**, such as those in the IHDP and ACIC datasets, the $\\\\phi_A$ function **is not estimated** through a learning process [1]. Instead, it\",null,[\"035e02ac-aabe-4822-8d50-94cc364ef788\",\"1a183403-1215-4003-8b2c-c069c57149a6\",1252492976],null,[[null,[[[\"9b5a0574-be18-4e58-b419-1ea19cdce436\"],[null,118,144]]]],null,null,[[[\"9b5a0574-be18-4e58-b419-1ea19cdce436\"],[null,null,0.6645295819827725,[[null,56867,57401]],[[[56867,56899,[[[56867,56899,[\"ϕA(1) \\u003d [1, 0]⊤,ϕA(0) \\u003d [0, 1]⊤ \"]]]]],[56899,56977,[[[56899,56977,[\"in both IHDP setting and ACIC setting. This is equivalent to learn two models \"]]]]],[56977,57033,[[[56977,57033,[\"E [Y |X \\u003d x,A \\u003d 0] \\u003d w⊤ 0 ϕX(X),E [Y |X \\u003d x,A \\u003d 1] \\u003d w⊤ \"]]]]],[57033,57041,[[[57033,57041,[\"1 ϕX(X) \"]]]]],[57041,57078,[[[57041,57078,[\"with shared nonlinear feature ϕX(X). \"]]]]],[57078,57401,[[[57078,57401,[\"IHDP Dataset We used the 1000 data used in (Chernozhukov et al., 2022b), which is publicly available at Github page of the paper. The network structure for back-door feature ϕX(X) is shown in Table 2. Note that is much smaller network than Dragonnet or Riesznet, but increasing network size did not affect the result much. \"]]]]]]],[[[\"79bd221c-ef5e-467a-a4f5-9f92a641393c\"],\"0ee00171-298f-44d8-a61c-13a26da2b50c\"]],[\"9b5a0574-be18-4e58-b419-1ea19cdce436\"]]]]]],[[null,null,0.6645295819827725,[[null,56867,57401]],[[[56867,56899,[[[56867,56899,[\"ϕA(1) \\u003d [1, 0]⊤,ϕA(0) \\u003d [0, 1]⊤ \"]]]]],[56899,56977,[[[56899,56977,[\"in both IHDP setting and ACIC setting. This is equivalent to learn two models \"]]]]],[56977,57033,[[[56977,57033,[\"E [Y |X \\u003d x,A \\u003d 0] \\u003d w⊤ 0 ϕX(X),E [Y |X \\u003d x,A \\u003d 1] \\u003d w⊤ \"]]]]],[57033,57041,[[[57033,57041,[\"1 ϕX(X) \"]]]]],[57041,57078,[[[57041,57078,[\"with shared nonlinear feature ϕX(X). \"]]]]],[57078,57401,[[[57078,57401,[\"IHDP Dataset We used the 1000 data used in (Chernozhukov et al., 2022b), which is publicly available at Github page of the paper. The network structure for back-door feature ϕX(X) is shown in Table 2. Note that is much smaller network than Dragonnet or Riesznet, but increasing network size did not affect the result much. \"]]]]]]],[[[\"79bd221c-ef5e-467a-a4f5-9f92a641393c\"],\"0ee00171-298f-44d8-a61c-13a26da2b50c\"]],[\"9b5a0574-be18-4e58-b419-1ea19cdce436\"]]],[[[null,118,144],[0]]],[[\"What is a front-door variable?\",\"How is the ATE calculated?\",\"What is the purpose of dSprites data?\"]]]"]]
7260
[["wrb.fr",null,"[[\"For **binary (discrete) treatments**, such as those in the IHDP and ACIC datasets, the $\\\\phi_A$ function **is not estimated** through a learning process [1]. Instead, it is **pre-specified as a fixed representation** [1]. For example, in the experiments, $\\\\phi_A(1)$ is set to $[2]^\\\\top$ and $\\\\phi_A(0)$ is set to $[2]^\\\\top$ [1].\\n\\nTherefore, estimating the $\\\\phi_A$ function itself does not pose any challenges when treatments are discrete because it is **not a learned component** in this context; it's a predefined feature map [1].\",null,[\"035e02ac-aabe-4822-8d50-94cc364ef788\",\"1a183403-1215-4003-8b2c-c069c57149a6\",1252492976],null,[[[[0,299,[[[0,4,[\"For \"]],[4,32,[\"binary (discrete) treatments\",[true]]],[32,101,[\", such as those in the IHDP and ACIC datasets, the $\\\\phi_A$ function \"]],[101,117,[\"is not estimated\",[true]]],[117,161,[\" through a learning process. Instead, it is \"]],[161,200,[\"pre-specified as a fixed representation\",[true]]],[200,299,[\". For example, in the experiments, $\\\\phi_A(1)$ is set to $^\\\\top$ and $\\\\phi_A(0)$ is set to $^\\\\top$.\"]]]]]],[[[\"db225136-8e97-4b73-8ff6-6472c045e002\"],[null,118,144]],[[\"db225136-8e97-4b73-8ff6-6472c045e002\"],[null,200,200]],[[\"60c39af6-1f63-4380-aa40-40a2dd27f22e\"],[null,200,258]],[[\"60c39af6-1f63-4380-aa40-40a2dd27f22e\"],[null,258,292]],[[\"db225136-8e97-4b73-8ff6-6472c045e002\"],[null,292,298]],[[\"db225136-8e97-4b73-8ff6-6472c045e002\"],[null,446,492]]]],null,null,[[[\"db225136-8e97-4b73-8ff6-6472c045e002\"],[null,null,0.6645295819827725,[[null,56867,57401]],[[[56867,56899,[[[56867,56899,[\"ϕA(1) \\u003d [1, 0]⊤,ϕA(0) \\u003d [0, 1]⊤ \"]]]]],[56899,56977,[[[56899,56977,[\"in both IHDP setting and ACIC setting. This is equivalent to learn two models \"]]]]],[56977,57033,[[[56977,57033,[\"E [Y |X \\u003d x,A \\u003d 0] \\u003d w⊤ 0 ϕX(X),E [Y |X \\u003d x,A \\u003d 1] \\u003d w⊤ \"]]]]],[57033,57041,[[[57033,57041,[\"1 ϕX(X) \"]]]]],[57041,57078,[[[57041,57078,[\"with shared nonlinear feature ϕX(X). \"]]]]],[57078,57401,[[[57078,57401,[\"IHDP Dataset We used the 1000 data used in (Chernozhukov et al., 2022b), which is publicly available at Github page of the paper. The network structure for back-door feature ϕX(X) is shown in Table 2. Note that is much smaller network than Dragonnet or Riesznet, but increasing network size did not affect the result much. \"]]]]]]],[[[\"79bd221c-ef5e-467a-a4f5-9f92a641393c\"],\"0ee00171-298f-44d8-a61c-13a26da2b50c\"]],[\"db225136-8e97-4b73-8ff6-6472c045e002\"]]],[[\"60c39af6-1f63-4380-aa40-40a2dd27f22e\"],[null,null,0.6980217756971577,[[null,0,1321]],[[[0,42,[[[0,42,[\"A NEURAL MEAN EMBEDDING APPROACH FOR BACK-\"]]],[null,6]]],[42,73,[[[42,73,[\"DOOR AND FRONT-DOOR ADJUSTMENT \"]]],[null,6]]],[73,118,[[[73,118,[\"Liyuan Xu <NAME_EMAIL> \"]]]]],[118,170,[[[118,170,[\"Arthur Gretton <NAME_EMAIL> \"]]]]],[170,179,[[[170,179,[\"ABSTRACT \"]]]]],[179,1321,[[[179,1321,[\"We consider the estimation of average and counterfactual treatment effects, under two settings: back-door adjustment and front-door adjustment. The goal in both cases is to recover the treatment effect without having an access to a hidden con-founder. This objective is attained by first estimating the conditional mean of the desired outcome variable given relevant covariates (the “first stage” regression), and then taking the (conditional) expectation of this function as a “second stage” procedure. We propose to compute these conditional expectations directly using a regression function to the learned input features of the first stage, thus avoid-ing the need for sampling or density estimation. All functions and features (and in particular, the output features in the second stage) are neural networks learned adaptively from data, with the sole requirement that the final layer of the first stage should be linear. The proposed method is shown to converge to the true causal pa-rameter, and outperforms the recent state-of-the-art methods on challenging causal benchmarks, including settings involving high-dimensional image data. \"]]]]]]],[[[\"79bd221c-ef5e-467a-a4f5-9f92a641393c\"],\"0ee00171-298f-44d8-a61c-13a26da2b50c\"]],[\"60c39af6-1f63-4380-aa40-40a2dd27f22e\"]]]]]],[[null,null,0.6645295819827725,[[null,56867,57401]],[[[56867,56899,[[[56867,56899,[\"ϕA(1) \\u003d [1, 0]⊤,ϕA(0) \\u003d [0, 1]⊤ \"]]]]],[56899,56977,[[[56899,56977,[\"in both IHDP setting and ACIC setting. This is equivalent to learn two models \"]]]]],[56977,57033,[[[56977,57033,[\"E [Y |X \\u003d x,A \\u003d 0] \\u003d w⊤ 0 ϕX(X),E [Y |X \\u003d x,A \\u003d 1] \\u003d w⊤ \"]]]]],[57033,57041,[[[57033,57041,[\"1 ϕX(X) \"]]]]],[57041,57078,[[[57041,57078,[\"with shared nonlinear feature ϕX(X). \"]]]]],[57078,57401,[[[57078,57401,[\"IHDP Dataset We used the 1000 data used in (Chernozhukov et al., 2022b), which is publicly available at Github page of the paper. The network structure for back-door feature ϕX(X) is shown in Table 2. Note that is much smaller network than Dragonnet or Riesznet, but increasing network size did not affect the result much. \"]]]]]]],[[[\"79bd221c-ef5e-467a-a4f5-9f92a641393c\"],\"0ee00171-298f-44d8-a61c-13a26da2b50c\"]],[\"db225136-8e97-4b73-8ff6-6472c045e002\"]],[null,null,0.6980217756971577,[[null,0,1321]],[[[0,42,[[[0,42,[\"A NEURAL MEAN EMBEDDING APPROACH FOR BACK-\"]]],[null,6]]],[42,73,[[[42,73,[\"DOOR AND FRONT-DOOR ADJUSTMENT \"]]],[null,6]]],[73,118,[[[73,118,[\"Liyuan Xu <NAME_EMAIL> \"]]]]],[118,170,[[[118,170,[\"Arthur Gretton <NAME_EMAIL> \"]]]]],[170,179,[[[170,179,[\"ABSTRACT \"]]]]],[179,1321,[[[179,1321,[\"We consider the estimation of average and counterfactual treatment effects, under two settings: back-door adjustment and front-door adjustment. The goal in both cases is to recover the treatment effect without having an access to a hidden con-founder. This objective is attained by first estimating the conditional mean of the desired outcome variable given relevant covariates (the “first stage” regression), and then taking the (conditional) expectation of this function as a “second stage” procedure. We propose to compute these conditional expectations directly using a regression function to the learned input features of the first stage, thus avoid-ing the need for sampling or density estimation. All functions and features (and in particular, the output features in the second stage) are neural networks learned adaptively from data, with the sole requirement that the final layer of the first stage should be linear. The proposed method is shown to converge to the true causal pa-rameter, and outperforms the recent state-of-the-art methods on challenging causal benchmarks, including settings involving high-dimensional image data. \"]]]]]]],[[[\"79bd221c-ef5e-467a-a4f5-9f92a641393c\"],\"0ee00171-298f-44d8-a61c-13a26da2b50c\"]],[\"60c39af6-1f63-4380-aa40-40a2dd27f22e\"]]],[[[null,118,144],[0]],[[null,200,200],[0]],[[null,200,258],[1]],[[null,258,292],[1]],[[null,292,298],[0]],[[null,446,492],[0]]],[[\"What is a front-door variable?\",\"How is the ATE calculated?\",\"What is the purpose of dSprites data?\"]]]"]]
7529
[["wrb.fr",null,"[[\"For **binary (discrete) treatments**, such as those in the IHDP and ACIC datasets, the $\\\\phi_A$ function **is not estimated** through a learning process [1]. Instead, it is **pre-specified as a fixed representation** [1]. For example, in the experiments, $\\\\phi_A(1)$ is set to $[2]^\\\\top$ and $\\\\phi_A(0)$ is set to $[2]^\\\\top$ [1].\\n\\nTherefore, estimating the $\\\\phi_A$ function itself does not pose any challenges when treatments are discrete because it is **not a learned component** in this context; it's a predefined feature map [1].\",null,[\"035e02ac-aabe-4822-8d50-94cc364ef788\",\"1a183403-1215-4003-8b2c-c069c57149a6\",1252492976],null,[[[[0,299,[[[0,4,[\"For \"]],[4,32,[\"binary (discrete) treatments\",[true]]],[32,101,[\", such as those in the IHDP and ACIC datasets, the $\\\\phi_A$ function \"]],[101,117,[\"is not estimated\",[true]]],[117,161,[\" through a learning process. Instead, it is \"]],[161,200,[\"pre-specified as a fixed representation\",[true]]],[200,299,[\". For example, in the experiments, $\\\\phi_A(1)$ is set to $^\\\\top$ and $\\\\phi_A(0)$ is set to $^\\\\top$.\"]]]]],[299,493,[[[299,422,[\"Therefore, estimating the $\\\\phi_A$ function itself does not pose any challenges when treatments are discrete because it is \"]],[422,445,[\"not a learned component\",[true]]],[445,493,[\" in this context; it's a predefined feature map.\"]]]]]],[[[\"db225136-8e97-4b73-8ff6-6472c045e002\"],[null,118,144]],[[\"db225136-8e97-4b73-8ff6-6472c045e002\"],[null,200,200]],[[\"60c39af6-1f63-4380-aa40-40a2dd27f22e\"],[null,200,258]],[[\"60c39af6-1f63-4380-aa40-40a2dd27f22e\"],[null,258,292]],[[\"db225136-8e97-4b73-8ff6-6472c045e002\"],[null,292,298]],[[\"db225136-8e97-4b73-8ff6-6472c045e002\"],[null,446,492]]]],null,null,[[[\"db225136-8e97-4b73-8ff6-6472c045e002\"],[null,null,0.6645295819827725,[[null,56867,57401]],[[[56867,56899,[[[56867,56899,[\"ϕA(1) \\u003d [1, 0]⊤,ϕA(0) \\u003d [0, 1]⊤ \"]]]]],[56899,56977,[[[56899,56977,[\"in both IHDP setting and ACIC setting. This is equivalent to learn two models \"]]]]],[56977,57033,[[[56977,57033,[\"E [Y |X \\u003d x,A \\u003d 0] \\u003d w⊤ 0 ϕX(X),E [Y |X \\u003d x,A \\u003d 1] \\u003d w⊤ \"]]]]],[57033,57041,[[[57033,57041,[\"1 ϕX(X) \"]]]]],[57041,57078,[[[57041,57078,[\"with shared nonlinear feature ϕX(X). \"]]]]],[57078,57401,[[[57078,57401,[\"IHDP Dataset We used the 1000 data used in (Chernozhukov et al., 2022b), which is publicly available at Github page of the paper. The network structure for back-door feature ϕX(X) is shown in Table 2. Note that is much smaller network than Dragonnet or Riesznet, but increasing network size did not affect the result much. \"]]]]]]],[[[\"79bd221c-ef5e-467a-a4f5-9f92a641393c\"],\"0ee00171-298f-44d8-a61c-13a26da2b50c\"]],[\"db225136-8e97-4b73-8ff6-6472c045e002\"]]],[[\"60c39af6-1f63-4380-aa40-40a2dd27f22e\"],[null,null,0.6980217756971577,[[null,0,1321]],[[[0,42,[[[0,42,[\"A NEURAL MEAN EMBEDDING APPROACH FOR BACK-\"]]],[null,6]]],[42,73,[[[42,73,[\"DOOR AND FRONT-DOOR ADJUSTMENT \"]]],[null,6]]],[73,118,[[[73,118,[\"Liyuan Xu <NAME_EMAIL> \"]]]]],[118,170,[[[118,170,[\"Arthur Gretton <NAME_EMAIL> \"]]]]],[170,179,[[[170,179,[\"ABSTRACT \"]]]]],[179,1321,[[[179,1321,[\"We consider the estimation of average and counterfactual treatment effects, under two settings: back-door adjustment and front-door adjustment. The goal in both cases is to recover the treatment effect without having an access to a hidden con-founder. This objective is attained by first estimating the conditional mean of the desired outcome variable given relevant covariates (the “first stage” regression), and then taking the (conditional) expectation of this function as a “second stage” procedure. We propose to compute these conditional expectations directly using a regression function to the learned input features of the first stage, thus avoid-ing the need for sampling or density estimation. All functions and features (and in particular, the output features in the second stage) are neural networks learned adaptively from data, with the sole requirement that the final layer of the first stage should be linear. The proposed method is shown to converge to the true causal pa-rameter, and outperforms the recent state-of-the-art methods on challenging causal benchmarks, including settings involving high-dimensional image data. \"]]]]]]],[[[\"79bd221c-ef5e-467a-a4f5-9f92a641393c\"],\"0ee00171-298f-44d8-a61c-13a26da2b50c\"]],[\"60c39af6-1f63-4380-aa40-40a2dd27f22e\"]]]]]],[[null,null,0.6645295819827725,[[null,56867,57401]],[[[56867,56899,[[[56867,56899,[\"ϕA(1) \\u003d [1, 0]⊤,ϕA(0) \\u003d [0, 1]⊤ \"]]]]],[56899,56977,[[[56899,56977,[\"in both IHDP setting and ACIC setting. This is equivalent to learn two models \"]]]]],[56977,57033,[[[56977,57033,[\"E [Y |X \\u003d x,A \\u003d 0] \\u003d w⊤ 0 ϕX(X),E [Y |X \\u003d x,A \\u003d 1] \\u003d w⊤ \"]]]]],[57033,57041,[[[57033,57041,[\"1 ϕX(X) \"]]]]],[57041,57078,[[[57041,57078,[\"with shared nonlinear feature ϕX(X). \"]]]]],[57078,57401,[[[57078,57401,[\"IHDP Dataset We used the 1000 data used in (Chernozhukov et al., 2022b), which is publicly available at Github page of the paper. The network structure for back-door feature ϕX(X) is shown in Table 2. Note that is much smaller network than Dragonnet or Riesznet, but increasing network size did not affect the result much. \"]]]]]]],[[[\"79bd221c-ef5e-467a-a4f5-9f92a641393c\"],\"0ee00171-298f-44d8-a61c-13a26da2b50c\"]],[\"db225136-8e97-4b73-8ff6-6472c045e002\"]],[null,null,0.6980217756971577,[[null,0,1321]],[[[0,42,[[[0,42,[\"A NEURAL MEAN EMBEDDING APPROACH FOR BACK-\"]]],[null,6]]],[42,73,[[[42,73,[\"DOOR AND FRONT-DOOR ADJUSTMENT \"]]],[null,6]]],[73,118,[[[73,118,[\"Liyuan Xu <NAME_EMAIL> \"]]]]],[118,170,[[[118,170,[\"Arthur Gretton <NAME_EMAIL> \"]]]]],[170,179,[[[170,179,[\"ABSTRACT \"]]]]],[179,1321,[[[179,1321,[\"We consider the estimation of average and counterfactual treatment effects, under two settings: back-door adjustment and front-door adjustment. The goal in both cases is to recover the treatment effect without having an access to a hidden con-founder. This objective is attained by first estimating the conditional mean of the desired outcome variable given relevant covariates (the “first stage” regression), and then taking the (conditional) expectation of this function as a “second stage” procedure. We propose to compute these conditional expectations directly using a regression function to the learned input features of the first stage, thus avoid-ing the need for sampling or density estimation. All functions and features (and in particular, the output features in the second stage) are neural networks learned adaptively from data, with the sole requirement that the final layer of the first stage should be linear. The proposed method is shown to converge to the true causal pa-rameter, and outperforms the recent state-of-the-art methods on challenging causal benchmarks, including settings involving high-dimensional image data. \"]]]]]]],[[[\"79bd221c-ef5e-467a-a4f5-9f92a641393c\"],\"0ee00171-298f-44d8-a61c-13a26da2b50c\"]],[\"60c39af6-1f63-4380-aa40-40a2dd27f22e\"]]],[[[null,118,144],[0]],[[null,200,200],[0]],[[null,200,258],[1]],[[null,258,292],[1]],[[null,292,298],[0]],[[null,446,492],[0]]],[[\"What is a front-door variable?\",\"How is the ATE calculated?\",\"What is the purpose of dSprites data?\"]]]"]]
59
[["di",6581],["af.httprm",6581,"7615509545958935828",57]]
27
[["e",6,null,null,17591]]
