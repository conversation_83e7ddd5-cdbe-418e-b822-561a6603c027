)]}'

2950
[["wrb.fr",null,"[[\"No, the model does not need to compute $\\\\psi(m_i, e)$ for all entities in the target knowledge base during inference [1].\\n\\nInstead, the learned mention and entity representations are used for\",null,[\"542b6022-a2ce-4e53-84b8-5b01c8ddd8d5\",\"d523589a-d20e-44a5-a227-c372002fec92\",2943824453],null,[[[[0,117,[[[0,117,[\"No, the model does not need to compute $\\\\psi(m_i, e)$ for all entities in the target knowledge base during inference.\"]]]]]],[[[\"38798276-d7c2-4068-8c1d-e611c6600c2c\"],[null,0,116]]]],null,null,[[[\"38798276-d7c2-4068-8c1d-e611c6600c2c\"],[null,null,0.7104040329679147,[[null,2092,2929]],[[[2092,2162,[[[2092,2162,[\"∗Now at Google. 1Code, datasets, and models are available at https:// \"]]]]],[2162,2190,[[[2162,2190,[\"github.com/dhdhagar/arboEL. \"]]]]],[2190,2442,[[[2190,2442,[\"entity has labeled training data (Lin et al., 2017; Logeswaran et al., 2019). In such settings, a com-mon approach is to make use of entity descriptions, types, and aliases to form entity representations, which can then be used for making predictions. \"]]]]],[2442,2929,[[[2442,2929,[\"Learned vector representations of entity men-tions are an integral part of modern linking systems (Gillick et al., 2019; Wu et al., 2020, inter alia). These representations are used for (a) retrieving a short-list of entity candidates for a mention to use with a re-ranker (Wu et al., 2020), (b) making linking predictions directly (Zhang et al., 2021; Liu et al., 2020; Sung et al., 2020), and (c) performing coreference by clustering mentions to form entities (Logan IV et al., 2020). \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"38798276-d7c2-4068-8c1d-e611c6600c2c\"]]]]]],[[null,null,0.7104040329679147,[[null,2092,2929]],[[[2092,2162,[[[2092,2162,[\"∗Now at Google. 1Code, datasets, and models are available at https:// \"]]]]],[2162,2190,[[[2162,2190,[\"github.com/dhdhagar/arboEL. \"]]]]],[2190,2442,[[[2190,2442,[\"entity has labeled training data (Lin et al., 2017; Logeswaran et al., 2019). In such settings, a com-mon approach is to make use of entity descriptions, types, and aliases to form entity representations, which can then be used for making predictions. \"]]]]],[2442,2929,[[[2442,2929,[\"Learned vector representations of entity men-tions are an integral part of modern linking systems (Gillick et al., 2019; Wu et al., 2020, inter alia). These representations are used for (a) retrieving a short-list of entity candidates for a mention to use with a re-ranker (Wu et al., 2020), (b) making linking predictions directly (Zhang et al., 2021; Liu et al., 2020; Sung et al., 2020), and (c) performing coreference by clustering mentions to form entities (Logan IV et al., 2020). \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"38798276-d7c2-4068-8c1d-e611c6600c2c\"]]],[[[null,0,116],[0]]]]"]]
12113
[["wrb.fr",null,"[[\"No, the model does not need to compute $\\\\psi(m_i, e)$ for all entities in the target knowledge base during inference [1].\\n\\nInstead, the learned mention and entity representations are used for **retrieving a shortlist of entity candidates** for a mention [1]. The system uses **fast nearest-neighbor search** during inference [2], and predictions are evaluated based on whether the gold entity is retrieved in the **top-64 candidates** for a mention [3]. The inference procedure for dual-encoders involves constructing a graph by adding an edge from a mention's nearest entity to the mention [4].\",null,[\"542b6022-a2ce-4e53-84b8-5b01c8ddd8d5\",\"d523589a-d20e-44a5-a227-c372002fec92\",2943824453],null,[[[[0,117,[[[0,117,[\"No, the model does not need to compute $\\\\psi(m_i, e)$ for all entities in the target knowledge base during inference.\"]]]]]],[[[\"2c47432d-d72f-4c51-809e-a267cdd3e9ca\"],[null,0,116]],[[\"2c47432d-d72f-4c51-809e-a267cdd3e9ca\"],[null,230,243]],[[\"f73b3250-d343-4495-9151-228ab19ee421\"],[null,290,306]],[[\"939b0f25-686a-4211-b809-29b9d3b3ebb1\"],[null,409,422]],[[\"67d9abfe-d43f-4752-b0c6-f55a5dd16695\"],[null,422,560]]]],null,null,[[[\"2c47432d-d72f-4c51-809e-a267cdd3e9ca\"],[null,null,0.7104040329679147,[[null,2092,2929]],[[[2092,2162,[[[2092,2162,[\"∗Now at Google. 1Code, datasets, and models are available at https:// \"]]]]],[2162,2190,[[[2162,2190,[\"github.com/dhdhagar/arboEL. \"]]]]],[2190,2442,[[[2190,2442,[\"entity has labeled training data (Lin et al., 2017; Logeswaran et al., 2019). In such settings, a com-mon approach is to make use of entity descriptions, types, and aliases to form entity representations, which can then be used for making predictions. \"]]]]],[2442,2929,[[[2442,2929,[\"Learned vector representations of entity men-tions are an integral part of modern linking systems (Gillick et al., 2019; Wu et al., 2020, inter alia). These representations are used for (a) retrieving a short-list of entity candidates for a mention to use with a re-ranker (Wu et al., 2020), (b) making linking predictions directly (Zhang et al., 2021; Liu et al., 2020; Sung et al., 2020), and (c) performing coreference by clustering mentions to form entities (Logan IV et al., 2020). \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"2c47432d-d72f-4c51-809e-a267cdd3e9ca\"]]],[[\"f73b3250-d343-4495-9151-228ab19ee421\"],[null,null,0.6910954373859082,[[null,54820,56549]],[[[54820,55039,[[[54820,55039,[\"Sheng Zhang, Hao Cheng, Shikhar Vashishth, Cliff Wong, Jinfeng Xiao, Xiaodong Liu, Tristan Nau-mann, Jianfeng Gao, and Hoifung Poon. 2021. Knowledge-rich self-supervised entity linking. arXiv preprint arXiv:2112.07887. \"]]]]],[55039,55174,[[[55039,55174,[\"Wenzheng Zhang and Karl Stratos. 2021. Understand-ing hard negatives in noise contrastive estimation. arXiv preprint arXiv:2104.06245. \"]]]]],[55174,55178,[[[55174,55178,[\"4655\"]]]]],[55178,55189,[[[55178,55189,[\"A Appendix \"]]]]],[55189,56549,[[[55189,56549,[\"A.1 Experiment Details Each training procedure is run on a single machine using 2 NVIDIA Quadro RTX 8000 GPUs. Our dual-encoder models for ZeShEL and MedMen-tions have 218M and 230M parameters, respec-tively. Each variant is optimized using mini-batch gradient descent using the Adam optimizer for 5 epochs using a mini-batch size of 128 to accumu-late the gradients. Experiments with batch sizes \\u003c 128 performed poorly, possibly due to increased fluctuation of gradients, and sizes \\u003e 128 were com-putationally infeasible to run with our available compute resources. For ZeShEL, the dual-encoder models are trained using 192 warm-up steps and learning rates of 1e-5, 3e-5, and 3e-5 for IN-BATCH, K-NN, and ARBORESCENCE-based models, re-spectively. For MedMentions, each model is trained using 464 warm-up steps and a learning rate of 3e-5. All cross-encoder models are trained with a mini-batch size of 2, learning rate of 2e-5, and an addi-tional linear layer. Our MedMentions and ZeShEL cross-encoder models have 108M and 109M pa-rameters, respectively. We use FAISS2 (Johnson et al., 2017) for fast nearest-neighbor search dur-ing graph construction at both training and infer-ence. For MedMentions, the execution time was 70 mins to embed and index 2M entities and 120K mentions, and 20 mins to perform exact nearest-neighbor search for the 120K mentions. \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"f73b3250-d343-4495-9151-228ab19ee421\"]]],[[\"939b0f25-686a-4211-b809-29b9d3b3ebb1\"],[null,null,0.6546648497902477,[[null,27797,28612]],[[[27797,28027,[[[27797,28027,[\"prediction is evaluated as a hit if the gold entity is retrieved in the top-64 candidates for a mention. On each dataset, we additionally include the per-formance of candidate generators used by previous works that we compare to. \"]]]]],[28027,28612,[[[28027,28612,[\"We find that models trained with explicit coref-erence relationships outperform those that incorpo-rate this relationship only indirectly. For recall@64, our proposed methods improve over the baselines by at least 7.94 percentage points on MedMentions and 0.93 points on ZeShEL. Even at linking, or recall@1, our proposed methods show similar im-provements with gains of 13.61 and 1.52 points over the next best baseline models. We perform a more comprehensive analysis of the dual-encoder linking performance and describe our inference approach and results in Appendix §A.2 and §A.3. \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"939b0f25-686a-4211-b809-29b9d3b3ebb1\"]]],[[\"67d9abfe-d43f-4752-b0c6-f55a5dd16695\"],[null,null,0.6558335410865963,[[null,56549,57063]],[[[56549,57002,[[[56549,57002,[\"A.2 Dual-Encoder Inference Procedure Building the Graph The structure of the graph G impacts the dissimilarity function by changing the paths between pairs of nodes in addition to changing which pairs of nodes are connected. We advocate for a simple, deterministic approach to construct this graph. For each mentionm, construct Em by (a) adding edges from m’s k-nearest neigh-bor mentions in M to m, and (b) adding an edge from m’s nearest entity to m: \"]]]]],[57002,57007,[[[57002,57007,[\"Em \\u003d \"]]]]],[57007,57015,[[[57007,57015,[\"{ (u,m) \"]]]]],[57015,57038,[[[57015,57038,[\"∣∣∣ u ∈ argmink m′ ∈ M \"]]]]],[57038,57044,[[[57038,57044,[\"wm′,m \"]]]]],[57044,57063,[[[57044,57063,[\"∨ u \\u003d argmin e ∈ E \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"67d9abfe-d43f-4752-b0c6-f55a5dd16695\"]]]]]],[[null,null,0.7104040329679147,[[null,2092,2929]],[[[2092,2162,[[[2092,2162,[\"∗Now at Google. 1Code, datasets, and models are available at https:// \"]]]]],[2162,2190,[[[2162,2190,[\"github.com/dhdhagar/arboEL. \"]]]]],[2190,2442,[[[2190,2442,[\"entity has labeled training data (Lin et al., 2017; Logeswaran et al., 2019). In such settings, a com-mon approach is to make use of entity descriptions, types, and aliases to form entity representations, which can then be used for making predictions. \"]]]]],[2442,2929,[[[2442,2929,[\"Learned vector representations of entity men-tions are an integral part of modern linking systems (Gillick et al., 2019; Wu et al., 2020, inter alia). These representations are used for (a) retrieving a short-list of entity candidates for a mention to use with a re-ranker (Wu et al., 2020), (b) making linking predictions directly (Zhang et al., 2021; Liu et al., 2020; Sung et al., 2020), and (c) performing coreference by clustering mentions to form entities (Logan IV et al., 2020). \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"2c47432d-d72f-4c51-809e-a267cdd3e9ca\"]],[null,null,0.6910954373859082,[[null,54820,56549]],[[[54820,55039,[[[54820,55039,[\"Sheng Zhang, Hao Cheng, Shikhar Vashishth, Cliff Wong, Jinfeng Xiao, Xiaodong Liu, Tristan Nau-mann, Jianfeng Gao, and Hoifung Poon. 2021. Knowledge-rich self-supervised entity linking. arXiv preprint arXiv:2112.07887. \"]]]]],[55039,55174,[[[55039,55174,[\"Wenzheng Zhang and Karl Stratos. 2021. Understand-ing hard negatives in noise contrastive estimation. arXiv preprint arXiv:2104.06245. \"]]]]],[55174,55178,[[[55174,55178,[\"4655\"]]]]],[55178,55189,[[[55178,55189,[\"A Appendix \"]]]]],[55189,56549,[[[55189,56549,[\"A.1 Experiment Details Each training procedure is run on a single machine using 2 NVIDIA Quadro RTX 8000 GPUs. Our dual-encoder models for ZeShEL and MedMen-tions have 218M and 230M parameters, respec-tively. Each variant is optimized using mini-batch gradient descent using the Adam optimizer for 5 epochs using a mini-batch size of 128 to accumu-late the gradients. Experiments with batch sizes \\u003c 128 performed poorly, possibly due to increased fluctuation of gradients, and sizes \\u003e 128 were com-putationally infeasible to run with our available compute resources. For ZeShEL, the dual-encoder models are trained using 192 warm-up steps and learning rates of 1e-5, 3e-5, and 3e-5 for IN-BATCH, K-NN, and ARBORESCENCE-based models, re-spectively. For MedMentions, each model is trained using 464 warm-up steps and a learning rate of 3e-5. All cross-encoder models are trained with a mini-batch size of 2, learning rate of 2e-5, and an addi-tional linear layer. Our MedMentions and ZeShEL cross-encoder models have 108M and 109M pa-rameters, respectively. We use FAISS2 (Johnson et al., 2017) for fast nearest-neighbor search dur-ing graph construction at both training and infer-ence. For MedMentions, the execution time was 70 mins to embed and index 2M entities and 120K mentions, and 20 mins to perform exact nearest-neighbor search for the 120K mentions. \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"f73b3250-d343-4495-9151-228ab19ee421\"]],[null,null,0.6546648497902477,[[null,27797,28612]],[[[27797,28027,[[[27797,28027,[\"prediction is evaluated as a hit if the gold entity is retrieved in the top-64 candidates for a mention. On each dataset, we additionally include the per-formance of candidate generators used by previous works that we compare to. \"]]]]],[28027,28612,[[[28027,28612,[\"We find that models trained with explicit coref-erence relationships outperform those that incorpo-rate this relationship only indirectly. For recall@64, our proposed methods improve over the baselines by at least 7.94 percentage points on MedMentions and 0.93 points on ZeShEL. Even at linking, or recall@1, our proposed methods show similar im-provements with gains of 13.61 and 1.52 points over the next best baseline models. We perform a more comprehensive analysis of the dual-encoder linking performance and describe our inference approach and results in Appendix §A.2 and §A.3. \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"939b0f25-686a-4211-b809-29b9d3b3ebb1\"]],[null,null,0.6558335410865963,[[null,56549,57063]],[[[56549,57002,[[[56549,57002,[\"A.2 Dual-Encoder Inference Procedure Building the Graph The structure of the graph G impacts the dissimilarity function by changing the paths between pairs of nodes in addition to changing which pairs of nodes are connected. We advocate for a simple, deterministic approach to construct this graph. For each mentionm, construct Em by (a) adding edges from m’s k-nearest neigh-bor mentions in M to m, and (b) adding an edge from m’s nearest entity to m: \"]]]]],[57002,57007,[[[57002,57007,[\"Em \\u003d \"]]]]],[57007,57015,[[[57007,57015,[\"{ (u,m) \"]]]]],[57015,57038,[[[57015,57038,[\"∣∣∣ u ∈ argmink m′ ∈ M \"]]]]],[57038,57044,[[[57038,57044,[\"wm′,m \"]]]]],[57044,57063,[[[57044,57063,[\"∨ u \\u003d argmin e ∈ E \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"67d9abfe-d43f-4752-b0c6-f55a5dd16695\"]]],[[[null,0,116],[0]],[[null,230,243],[0]],[[null,290,306],[1]],[[null,409,422],[2]],[[null,422,560],[3]]]]"]]
12711
[["wrb.fr",null,"[[\"No, the model does not need to compute $\\\\psi(m_i, e)$ for all entities in the target knowledge base during inference [1].\\n\\nInstead, the learned mention and entity representations are used for **retrieving a shortlist of entity candidates** for a mention [1]. The system uses **fast nearest-neighbor search** during inference [2], and predictions are evaluated based on whether the gold entity is retrieved in the **top-64 candidates** for a mention [3]. The inference procedure for dual-encoders involves constructing a graph by adding an edge from a mention's nearest entity to the mention [4].\",null,[\"542b6022-a2ce-4e53-84b8-5b01c8ddd8d5\",\"d523589a-d20e-44a5-a227-c372002fec92\",2943824453],null,[[[[0,117,[[[0,117,[\"No, the model does not need to compute $\\\\psi(m_i, e)$ for all entities in the target knowledge base during inference.\"]]]]],[117,561,[[[117,186,[\"Instead, the learned mention and entity representations are used for \"]],[186,229,[\"retrieving a shortlist of entity candidates\",[true]]],[229,261,[\" for a mention. The system uses \"]],[261,289,[\"fast nearest-neighbor search\",[true]]],[289,391,[\" during inference, and predictions are evaluated based on whether the gold entity is retrieved in the \"]],[391,408,[\"top-64 candidates\",[true]]],[408,561,[\" for a mention. The inference procedure for dual-encoders involves constructing a graph by adding an edge from a mention's nearest entity to the mention.\"]]]]]],[[[\"2c47432d-d72f-4c51-809e-a267cdd3e9ca\"],[null,0,116]],[[\"2c47432d-d72f-4c51-809e-a267cdd3e9ca\"],[null,230,243]],[[\"f73b3250-d343-4495-9151-228ab19ee421\"],[null,290,306]],[[\"939b0f25-686a-4211-b809-29b9d3b3ebb1\"],[null,409,422]],[[\"67d9abfe-d43f-4752-b0c6-f55a5dd16695\"],[null,422,560]]]],null,null,[[[\"2c47432d-d72f-4c51-809e-a267cdd3e9ca\"],[null,null,0.7104040329679147,[[null,2092,2929]],[[[2092,2162,[[[2092,2162,[\"∗Now at Google. 1Code, datasets, and models are available at https:// \"]]]]],[2162,2190,[[[2162,2190,[\"github.com/dhdhagar/arboEL. \"]]]]],[2190,2442,[[[2190,2442,[\"entity has labeled training data (Lin et al., 2017; Logeswaran et al., 2019). In such settings, a com-mon approach is to make use of entity descriptions, types, and aliases to form entity representations, which can then be used for making predictions. \"]]]]],[2442,2929,[[[2442,2929,[\"Learned vector representations of entity men-tions are an integral part of modern linking systems (Gillick et al., 2019; Wu et al., 2020, inter alia). These representations are used for (a) retrieving a short-list of entity candidates for a mention to use with a re-ranker (Wu et al., 2020), (b) making linking predictions directly (Zhang et al., 2021; Liu et al., 2020; Sung et al., 2020), and (c) performing coreference by clustering mentions to form entities (Logan IV et al., 2020). \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"2c47432d-d72f-4c51-809e-a267cdd3e9ca\"]]],[[\"f73b3250-d343-4495-9151-228ab19ee421\"],[null,null,0.6910954373859082,[[null,54820,56549]],[[[54820,55039,[[[54820,55039,[\"Sheng Zhang, Hao Cheng, Shikhar Vashishth, Cliff Wong, Jinfeng Xiao, Xiaodong Liu, Tristan Nau-mann, Jianfeng Gao, and Hoifung Poon. 2021. Knowledge-rich self-supervised entity linking. arXiv preprint arXiv:2112.07887. \"]]]]],[55039,55174,[[[55039,55174,[\"Wenzheng Zhang and Karl Stratos. 2021. Understand-ing hard negatives in noise contrastive estimation. arXiv preprint arXiv:2104.06245. \"]]]]],[55174,55178,[[[55174,55178,[\"4655\"]]]]],[55178,55189,[[[55178,55189,[\"A Appendix \"]]]]],[55189,56549,[[[55189,56549,[\"A.1 Experiment Details Each training procedure is run on a single machine using 2 NVIDIA Quadro RTX 8000 GPUs. Our dual-encoder models for ZeShEL and MedMen-tions have 218M and 230M parameters, respec-tively. Each variant is optimized using mini-batch gradient descent using the Adam optimizer for 5 epochs using a mini-batch size of 128 to accumu-late the gradients. Experiments with batch sizes \\u003c 128 performed poorly, possibly due to increased fluctuation of gradients, and sizes \\u003e 128 were com-putationally infeasible to run with our available compute resources. For ZeShEL, the dual-encoder models are trained using 192 warm-up steps and learning rates of 1e-5, 3e-5, and 3e-5 for IN-BATCH, K-NN, and ARBORESCENCE-based models, re-spectively. For MedMentions, each model is trained using 464 warm-up steps and a learning rate of 3e-5. All cross-encoder models are trained with a mini-batch size of 2, learning rate of 2e-5, and an addi-tional linear layer. Our MedMentions and ZeShEL cross-encoder models have 108M and 109M pa-rameters, respectively. We use FAISS2 (Johnson et al., 2017) for fast nearest-neighbor search dur-ing graph construction at both training and infer-ence. For MedMentions, the execution time was 70 mins to embed and index 2M entities and 120K mentions, and 20 mins to perform exact nearest-neighbor search for the 120K mentions. \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"f73b3250-d343-4495-9151-228ab19ee421\"]]],[[\"939b0f25-686a-4211-b809-29b9d3b3ebb1\"],[null,null,0.6546648497902477,[[null,27797,28612]],[[[27797,28027,[[[27797,28027,[\"prediction is evaluated as a hit if the gold entity is retrieved in the top-64 candidates for a mention. On each dataset, we additionally include the per-formance of candidate generators used by previous works that we compare to. \"]]]]],[28027,28612,[[[28027,28612,[\"We find that models trained with explicit coref-erence relationships outperform those that incorpo-rate this relationship only indirectly. For recall@64, our proposed methods improve over the baselines by at least 7.94 percentage points on MedMentions and 0.93 points on ZeShEL. Even at linking, or recall@1, our proposed methods show similar im-provements with gains of 13.61 and 1.52 points over the next best baseline models. We perform a more comprehensive analysis of the dual-encoder linking performance and describe our inference approach and results in Appendix §A.2 and §A.3. \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"939b0f25-686a-4211-b809-29b9d3b3ebb1\"]]],[[\"67d9abfe-d43f-4752-b0c6-f55a5dd16695\"],[null,null,0.6558335410865963,[[null,56549,57063]],[[[56549,57002,[[[56549,57002,[\"A.2 Dual-Encoder Inference Procedure Building the Graph The structure of the graph G impacts the dissimilarity function by changing the paths between pairs of nodes in addition to changing which pairs of nodes are connected. We advocate for a simple, deterministic approach to construct this graph. For each mentionm, construct Em by (a) adding edges from m’s k-nearest neigh-bor mentions in M to m, and (b) adding an edge from m’s nearest entity to m: \"]]]]],[57002,57007,[[[57002,57007,[\"Em \\u003d \"]]]]],[57007,57015,[[[57007,57015,[\"{ (u,m) \"]]]]],[57015,57038,[[[57015,57038,[\"∣∣∣ u ∈ argmink m′ ∈ M \"]]]]],[57038,57044,[[[57038,57044,[\"wm′,m \"]]]]],[57044,57063,[[[57044,57063,[\"∨ u \\u003d argmin e ∈ E \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"67d9abfe-d43f-4752-b0c6-f55a5dd16695\"]]]]]],[[null,null,0.7104040329679147,[[null,2092,2929]],[[[2092,2162,[[[2092,2162,[\"∗Now at Google. 1Code, datasets, and models are available at https:// \"]]]]],[2162,2190,[[[2162,2190,[\"github.com/dhdhagar/arboEL. \"]]]]],[2190,2442,[[[2190,2442,[\"entity has labeled training data (Lin et al., 2017; Logeswaran et al., 2019). In such settings, a com-mon approach is to make use of entity descriptions, types, and aliases to form entity representations, which can then be used for making predictions. \"]]]]],[2442,2929,[[[2442,2929,[\"Learned vector representations of entity men-tions are an integral part of modern linking systems (Gillick et al., 2019; Wu et al., 2020, inter alia). These representations are used for (a) retrieving a short-list of entity candidates for a mention to use with a re-ranker (Wu et al., 2020), (b) making linking predictions directly (Zhang et al., 2021; Liu et al., 2020; Sung et al., 2020), and (c) performing coreference by clustering mentions to form entities (Logan IV et al., 2020). \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"2c47432d-d72f-4c51-809e-a267cdd3e9ca\"]],[null,null,0.6910954373859082,[[null,54820,56549]],[[[54820,55039,[[[54820,55039,[\"Sheng Zhang, Hao Cheng, Shikhar Vashishth, Cliff Wong, Jinfeng Xiao, Xiaodong Liu, Tristan Nau-mann, Jianfeng Gao, and Hoifung Poon. 2021. Knowledge-rich self-supervised entity linking. arXiv preprint arXiv:2112.07887. \"]]]]],[55039,55174,[[[55039,55174,[\"Wenzheng Zhang and Karl Stratos. 2021. Understand-ing hard negatives in noise contrastive estimation. arXiv preprint arXiv:2104.06245. \"]]]]],[55174,55178,[[[55174,55178,[\"4655\"]]]]],[55178,55189,[[[55178,55189,[\"A Appendix \"]]]]],[55189,56549,[[[55189,56549,[\"A.1 Experiment Details Each training procedure is run on a single machine using 2 NVIDIA Quadro RTX 8000 GPUs. Our dual-encoder models for ZeShEL and MedMen-tions have 218M and 230M parameters, respec-tively. Each variant is optimized using mini-batch gradient descent using the Adam optimizer for 5 epochs using a mini-batch size of 128 to accumu-late the gradients. Experiments with batch sizes \\u003c 128 performed poorly, possibly due to increased fluctuation of gradients, and sizes \\u003e 128 were com-putationally infeasible to run with our available compute resources. For ZeShEL, the dual-encoder models are trained using 192 warm-up steps and learning rates of 1e-5, 3e-5, and 3e-5 for IN-BATCH, K-NN, and ARBORESCENCE-based models, re-spectively. For MedMentions, each model is trained using 464 warm-up steps and a learning rate of 3e-5. All cross-encoder models are trained with a mini-batch size of 2, learning rate of 2e-5, and an addi-tional linear layer. Our MedMentions and ZeShEL cross-encoder models have 108M and 109M pa-rameters, respectively. We use FAISS2 (Johnson et al., 2017) for fast nearest-neighbor search dur-ing graph construction at both training and infer-ence. For MedMentions, the execution time was 70 mins to embed and index 2M entities and 120K mentions, and 20 mins to perform exact nearest-neighbor search for the 120K mentions. \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"f73b3250-d343-4495-9151-228ab19ee421\"]],[null,null,0.6546648497902477,[[null,27797,28612]],[[[27797,28027,[[[27797,28027,[\"prediction is evaluated as a hit if the gold entity is retrieved in the top-64 candidates for a mention. On each dataset, we additionally include the per-formance of candidate generators used by previous works that we compare to. \"]]]]],[28027,28612,[[[28027,28612,[\"We find that models trained with explicit coref-erence relationships outperform those that incorpo-rate this relationship only indirectly. For recall@64, our proposed methods improve over the baselines by at least 7.94 percentage points on MedMentions and 0.93 points on ZeShEL. Even at linking, or recall@1, our proposed methods show similar im-provements with gains of 13.61 and 1.52 points over the next best baseline models. We perform a more comprehensive analysis of the dual-encoder linking performance and describe our inference approach and results in Appendix §A.2 and §A.3. \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"939b0f25-686a-4211-b809-29b9d3b3ebb1\"]],[null,null,0.6558335410865963,[[null,56549,57063]],[[[56549,57002,[[[56549,57002,[\"A.2 Dual-Encoder Inference Procedure Building the Graph The structure of the graph G impacts the dissimilarity function by changing the paths between pairs of nodes in addition to changing which pairs of nodes are connected. We advocate for a simple, deterministic approach to construct this graph. For each mentionm, construct Em by (a) adding edges from m’s k-nearest neigh-bor mentions in M to m, and (b) adding an edge from m’s nearest entity to m: \"]]]]],[57002,57007,[[[57002,57007,[\"Em \\u003d \"]]]]],[57007,57015,[[[57007,57015,[\"{ (u,m) \"]]]]],[57015,57038,[[[57015,57038,[\"∣∣∣ u ∈ argmink m′ ∈ M \"]]]]],[57038,57044,[[[57038,57044,[\"wm′,m \"]]]]],[57044,57063,[[[57044,57063,[\"∨ u \\u003d argmin e ∈ E \"]]]]]]],[[[\"93966bb9-cad0-4c30-abf7-4996b7e247d4\"],\"9d49881b-a67d-4a57-bdbe-7d1ffa799558\"]],[\"67d9abfe-d43f-4752-b0c6-f55a5dd16695\"]]],[[[null,0,116],[0]],[[null,230,243],[0]],[[null,290,306],[1]],[[null,409,422],[2]],[[null,422,560],[3]]]]"]]
59
[["di",3874],["af.httprm",3874,"-371955203980775045",58]]
27
[["e",6,null,null,27992]]
