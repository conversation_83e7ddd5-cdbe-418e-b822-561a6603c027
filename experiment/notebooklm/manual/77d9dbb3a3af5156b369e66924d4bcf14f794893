)]}'

5092
[["wrb.fr",null,"[[\"The criteria used to evaluate \\\"provability\\\" in the paper is based on **neural network certification methods** [1]. A region is considered provably robust if it can be **formally proven to only contain adversarial examples** [2]. Specifically, for a given input region `I` and an adversarial target class `yt`,\",null,[\"8b5a3781-0449-4fb5-b6e3-630347faea9c\",\"bfd7b689-e6d7-4f53-a670-8baf2b74dc46\",967081476],null,[[null,[[[\"36707eb5-6f3e-4ced-92d0-cafcf78e5341\"],[null,105,105]],[[\"2b7352e6-b5ba-4f85-8cbb-e354cec3c631\"],[null,211,211]]]],null,null,[[[\"36707eb5-6f3e-4ced-92d0-cafcf78e5341\"],[null,null,0.6836549996205504,[[null,4874,5582]],[[[4874,5582,[[[4874,5582,[\"In this work, we rely on existing state-of-the-art neural network certification methods based on convex relaxations to prove that the adversarial examples produced by our algorithm are robust. These certification methods take a convex input region I ⊂ Rn0 and prove that every point in I is classified as the target label yt by f . They propagate the set I through the layers of the network, producing a convex region that covers all possible values of the output neurons (Gehr et al., 2018). Robustness follows by proving that, for all combinations of output neuron values in this region, the output neuron corresponding to class yt has a larger value than the one corresponding to any other class y 6\\u003d yt. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"36707eb5-6f3e-4ced-92d0-cafcf78e5341\"]]],[[\"2b7352e6-b5ba-4f85-8cbb-e354cec3c631\"],[null,null,0.6860472397871276,[[null,9429,10314]],[[[9429,9815,[[[9429,9815,[\"Intuitively, this definition states that for points x̃ for which Radv is bigger, the smoothed classifier is less confident about predicting the correct class, since more adversarial examples are sampled in this region and therefore, the attack on g is stronger. We use this measure in Section 5 to compare the effectiveness of our adversarial examples to examples obtained by PGD on g. \"]]]]],[9815,9826,[[[9815,9826,[\"3 OVERVIEW \"]]]]],[9826,10314,[[[9826,10314,[\"Existing methods for generating robust adversarial examples focus on achieving empirical robustness (Qin et al., 2019; Athalye et al., 2018). In contrast, we consider provably robust adversarial examples, defined below: Definition 3 (Provably Robust Adversarial Example). We define a provably robust adversarial example to be any large connected neural network input region, defined by a set of perturbations of an input, that can be formally proven to only contain adversarial examples. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"2b7352e6-b5ba-4f85-8cbb-e354cec3c631\"]]]]]],[[null,null,0.6836549996205504,[[null,4874,5582]],[[[4874,5582,[[[4874,5582,[\"In this work, we rely on existing state-of-the-art neural network certification methods based on convex relaxations to prove that the adversarial examples produced by our algorithm are robust. These certification methods take a convex input region I ⊂ Rn0 and prove that every point in I is classified as the target label yt by f . They propagate the set I through the layers of the network, producing a convex region that covers all possible values of the output neurons (Gehr et al., 2018). Robustness follows by proving that, for all combinations of output neuron values in this region, the output neuron corresponding to class yt has a larger value than the one corresponding to any other class y 6\\u003d yt. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"36707eb5-6f3e-4ced-92d0-cafcf78e5341\"]],[null,null,0.6860472397871276,[[null,9429,10314]],[[[9429,9815,[[[9429,9815,[\"Intuitively, this definition states that for points x̃ for which Radv is bigger, the smoothed classifier is less confident about predicting the correct class, since more adversarial examples are sampled in this region and therefore, the attack on g is stronger. We use this measure in Section 5 to compare the effectiveness of our adversarial examples to examples obtained by PGD on g. \"]]]]],[9815,9826,[[[9815,9826,[\"3 OVERVIEW \"]]]]],[9826,10314,[[[9826,10314,[\"Existing methods for generating robust adversarial examples focus on achieving empirical robustness (Qin et al., 2019; Athalye et al., 2018). In contrast, we consider provably robust adversarial examples, defined below: Definition 3 (Provably Robust Adversarial Example). We define a provably robust adversarial example to be any large connected neural network input region, defined by a set of perturbations of an input, that can be formally proven to only contain adversarial examples. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"2b7352e6-b5ba-4f85-8cbb-e354cec3c631\"]]],[[[null,105,105],[0]],[[null,211,211],[1]]],[[\"What is a provably robust adversarial example?\",\"What is PARADE's primary purpose?\",\"What types of perturbations are discussed?\"]]]"]]
8009
[["wrb.fr",null,"[[\"The criteria used to evaluate \\\"provability\\\" in the paper is based on **neural network certification methods** [1]. A region is considered provably robust if it can be **formally proven to only contain adversarial examples** [2]. Specifically, for a given input region `I` and an adversarial target class `yt`, robustness is proven if, for every point `x` in `I`, the network `f` classifies `x` as `yt` [1]. This is achieved by computing a **certification objective `L(x)` for each non-target label `y` (`y ≠ yt`)** such that `L(x) ≤ [f(x)]yt − [f(x)]y` [3]. **If the minimum value of `L(x)` over the region `I` (`minx∈I Ly(x)`) is positive for all `y ≠ yt`, then robustness is proven** [3].\",null,[\"8b5a3781-0449-4fb5-b6e3-630347faea9c\",\"bfd7b689-e6d7-4f53-a670-8baf2b74dc46\",967081476],null,[[null,[[[\"f41827ab-b60a-4599-95da-d3d36b585242\"],[null,105,105]],[[\"f9c57cee-aec0-48e8-89d1-bd37fe0065da\"],[null,211,211]],[[\"f41827ab-b60a-4599-95da-d3d36b585242\"],[null,371,371]],[[\"e06acad2-cfad-475c-8de7-6b1a22e0625a\"],[null,506,506]],[[\"e06acad2-cfad-475c-8de7-6b1a22e0625a\"],[null,623,623]]]],null,null,[[[\"f41827ab-b60a-4599-95da-d3d36b585242\"],[null,null,0.6836549996205504,[[null,4874,5582]],[[[4874,5582,[[[4874,5582,[\"In this work, we rely on existing state-of-the-art neural network certification methods based on convex relaxations to prove that the adversarial examples produced by our algorithm are robust. These certification methods take a convex input region I ⊂ Rn0 and prove that every point in I is classified as the target label yt by f . They propagate the set I through the layers of the network, producing a convex region that covers all possible values of the output neurons (Gehr et al., 2018). Robustness follows by proving that, for all combinations of output neuron values in this region, the output neuron corresponding to class yt has a larger value than the one corresponding to any other class y 6\\u003d yt. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"f41827ab-b60a-4599-95da-d3d36b585242\"]]],[[\"f9c57cee-aec0-48e8-89d1-bd37fe0065da\"],[null,null,0.6860472397871276,[[null,9429,10314]],[[[9429,9815,[[[9429,9815,[\"Intuitively, this definition states that for points x̃ for which Radv is bigger, the smoothed classifier is less confident about predicting the correct class, since more adversarial examples are sampled in this region and therefore, the attack on g is stronger. We use this measure in Section 5 to compare the effectiveness of our adversarial examples to examples obtained by PGD on g. \"]]]]],[9815,9826,[[[9815,9826,[\"3 OVERVIEW \"]]]]],[9826,10314,[[[9826,10314,[\"Existing methods for generating robust adversarial examples focus on achieving empirical robustness (Qin et al., 2019; Athalye et al., 2018). In contrast, we consider provably robust adversarial examples, defined below: Definition 3 (Provably Robust Adversarial Example). We define a provably robust adversarial example to be any large connected neural network input region, defined by a set of perturbations of an input, that can be formally proven to only contain adversarial examples. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"f9c57cee-aec0-48e8-89d1-bd37fe0065da\"]]],[[\"e06acad2-cfad-475c-8de7-6b1a22e0625a\"],[null,null,0.6804010563576985,[[null,5582,6474]],[[[5582,6474,[[[5582,6474,[\"Commonly, one proves this property by computing a function Ly : Rn0 → R for each label y 6\\u003d yt, such that, for all x ∈ I, we have Ly(x) ≤ [f(x)]yt − [f(x)]y. For each Ly, one computes minx∈I Ly(x) to obtain a global lower bound that is true for all x ∈ I. If we obtain positive bounds for all y 6\\u003d yt, robustness is proven. To simplify notation, we will say that the certification objective L(x) is the function Ly(x) with the smallest minimum value on I. We will call its corresponding minimum value the certification error. We requireLy(x) to be a linear function of x. This requirement is consistent with many popular certification algorithms based on convex relaxation, such as CROWN (Zhang et al., 2018), DeepZ (Singh et al., 2018a), and DeepPoly (Singh et al., 2019). Without loss of generality, for the rest of this paper, we will treat DeepPoly as our preferred certification method. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"e06acad2-cfad-475c-8de7-6b1a22e0625a\"]]]]]],[[null,null,0.6836549996205504,[[null,4874,5582]],[[[4874,5582,[[[4874,5582,[\"In this work, we rely on existing state-of-the-art neural network certification methods based on convex relaxations to prove that the adversarial examples produced by our algorithm are robust. These certification methods take a convex input region I ⊂ Rn0 and prove that every point in I is classified as the target label yt by f . They propagate the set I through the layers of the network, producing a convex region that covers all possible values of the output neurons (Gehr et al., 2018). Robustness follows by proving that, for all combinations of output neuron values in this region, the output neuron corresponding to class yt has a larger value than the one corresponding to any other class y 6\\u003d yt. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"f41827ab-b60a-4599-95da-d3d36b585242\"]],[null,null,0.6860472397871276,[[null,9429,10314]],[[[9429,9815,[[[9429,9815,[\"Intuitively, this definition states that for points x̃ for which Radv is bigger, the smoothed classifier is less confident about predicting the correct class, since more adversarial examples are sampled in this region and therefore, the attack on g is stronger. We use this measure in Section 5 to compare the effectiveness of our adversarial examples to examples obtained by PGD on g. \"]]]]],[9815,9826,[[[9815,9826,[\"3 OVERVIEW \"]]]]],[9826,10314,[[[9826,10314,[\"Existing methods for generating robust adversarial examples focus on achieving empirical robustness (Qin et al., 2019; Athalye et al., 2018). In contrast, we consider provably robust adversarial examples, defined below: Definition 3 (Provably Robust Adversarial Example). We define a provably robust adversarial example to be any large connected neural network input region, defined by a set of perturbations of an input, that can be formally proven to only contain adversarial examples. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"f9c57cee-aec0-48e8-89d1-bd37fe0065da\"]],[null,null,0.6804010563576985,[[null,5582,6474]],[[[5582,6474,[[[5582,6474,[\"Commonly, one proves this property by computing a function Ly : Rn0 → R for each label y 6\\u003d yt, such that, for all x ∈ I, we have Ly(x) ≤ [f(x)]yt − [f(x)]y. For each Ly, one computes minx∈I Ly(x) to obtain a global lower bound that is true for all x ∈ I. If we obtain positive bounds for all y 6\\u003d yt, robustness is proven. To simplify notation, we will say that the certification objective L(x) is the function Ly(x) with the smallest minimum value on I. We will call its corresponding minimum value the certification error. We requireLy(x) to be a linear function of x. This requirement is consistent with many popular certification algorithms based on convex relaxation, such as CROWN (Zhang et al., 2018), DeepZ (Singh et al., 2018a), and DeepPoly (Singh et al., 2019). Without loss of generality, for the rest of this paper, we will treat DeepPoly as our preferred certification method. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"e06acad2-cfad-475c-8de7-6b1a22e0625a\"]]],[[[null,105,105],[0]],[[null,211,211],[1]],[[null,371,371],[0]],[[null,506,506],[2]],[[null,623,623],[2]]],[[\"What is a provably robust adversarial example?\",\"What is PARADE's primary purpose?\",\"What types of perturbations are discussed?\"]]]"]]
8009
[["wrb.fr",null,"[[\"The criteria used to evaluate \\\"provability\\\" in the paper is based on **neural network certification methods** [1]. A region is considered provably robust if it can be **formally proven to only contain adversarial examples** [2]. Specifically, for a given input region `I` and an adversarial target class `yt`, robustness is proven if, for every point `x` in `I`, the network `f` classifies `x` as `yt` [1]. This is achieved by computing a **certification objective `L(x)` for each non-target label `y` (`y ≠ yt`)** such that `L(x) ≤ [f(x)]yt − [f(x)]y` [3]. **If the minimum value of `L(x)` over the region `I` (`minx∈I Ly(x)`) is positive for all `y ≠ yt`, then robustness is proven** [3].\",null,[\"8b5a3781-0449-4fb5-b6e3-630347faea9c\",\"bfd7b689-e6d7-4f53-a670-8baf2b74dc46\",967081476],null,[[null,[[[\"f8a11211-3028-43ca-bea6-f35659c2708e\"],[null,105,105]],[[\"c4215a5d-275e-42b3-8738-16fdcb6be371\"],[null,211,211]],[[\"f8a11211-3028-43ca-bea6-f35659c2708e\"],[null,371,371]],[[\"06f6b8c4-e9af-48ee-8edf-c53c0586f235\"],[null,506,506]],[[\"06f6b8c4-e9af-48ee-8edf-c53c0586f235\"],[null,623,623]]]],null,null,[[[\"f8a11211-3028-43ca-bea6-f35659c2708e\"],[null,null,0.6836549996205504,[[null,4874,5582]],[[[4874,5582,[[[4874,5582,[\"In this work, we rely on existing state-of-the-art neural network certification methods based on convex relaxations to prove that the adversarial examples produced by our algorithm are robust. These certification methods take a convex input region I ⊂ Rn0 and prove that every point in I is classified as the target label yt by f . They propagate the set I through the layers of the network, producing a convex region that covers all possible values of the output neurons (Gehr et al., 2018). Robustness follows by proving that, for all combinations of output neuron values in this region, the output neuron corresponding to class yt has a larger value than the one corresponding to any other class y 6\\u003d yt. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"f8a11211-3028-43ca-bea6-f35659c2708e\"]]],[[\"c4215a5d-275e-42b3-8738-16fdcb6be371\"],[null,null,0.6860472397871276,[[null,9429,10314]],[[[9429,9815,[[[9429,9815,[\"Intuitively, this definition states that for points x̃ for which Radv is bigger, the smoothed classifier is less confident about predicting the correct class, since more adversarial examples are sampled in this region and therefore, the attack on g is stronger. We use this measure in Section 5 to compare the effectiveness of our adversarial examples to examples obtained by PGD on g. \"]]]]],[9815,9826,[[[9815,9826,[\"3 OVERVIEW \"]]]]],[9826,10314,[[[9826,10314,[\"Existing methods for generating robust adversarial examples focus on achieving empirical robustness (Qin et al., 2019; Athalye et al., 2018). In contrast, we consider provably robust adversarial examples, defined below: Definition 3 (Provably Robust Adversarial Example). We define a provably robust adversarial example to be any large connected neural network input region, defined by a set of perturbations of an input, that can be formally proven to only contain adversarial examples. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"c4215a5d-275e-42b3-8738-16fdcb6be371\"]]],[[\"06f6b8c4-e9af-48ee-8edf-c53c0586f235\"],[null,null,0.6804010563576985,[[null,5582,6474]],[[[5582,6474,[[[5582,6474,[\"Commonly, one proves this property by computing a function Ly : Rn0 → R for each label y 6\\u003d yt, such that, for all x ∈ I, we have Ly(x) ≤ [f(x)]yt − [f(x)]y. For each Ly, one computes minx∈I Ly(x) to obtain a global lower bound that is true for all x ∈ I. If we obtain positive bounds for all y 6\\u003d yt, robustness is proven. To simplify notation, we will say that the certification objective L(x) is the function Ly(x) with the smallest minimum value on I. We will call its corresponding minimum value the certification error. We requireLy(x) to be a linear function of x. This requirement is consistent with many popular certification algorithms based on convex relaxation, such as CROWN (Zhang et al., 2018), DeepZ (Singh et al., 2018a), and DeepPoly (Singh et al., 2019). Without loss of generality, for the rest of this paper, we will treat DeepPoly as our preferred certification method. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"06f6b8c4-e9af-48ee-8edf-c53c0586f235\"]]]]]],[[null,null,0.6836549996205504,[[null,4874,5582]],[[[4874,5582,[[[4874,5582,[\"In this work, we rely on existing state-of-the-art neural network certification methods based on convex relaxations to prove that the adversarial examples produced by our algorithm are robust. These certification methods take a convex input region I ⊂ Rn0 and prove that every point in I is classified as the target label yt by f . They propagate the set I through the layers of the network, producing a convex region that covers all possible values of the output neurons (Gehr et al., 2018). Robustness follows by proving that, for all combinations of output neuron values in this region, the output neuron corresponding to class yt has a larger value than the one corresponding to any other class y 6\\u003d yt. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"f8a11211-3028-43ca-bea6-f35659c2708e\"]],[null,null,0.6860472397871276,[[null,9429,10314]],[[[9429,9815,[[[9429,9815,[\"Intuitively, this definition states that for points x̃ for which Radv is bigger, the smoothed classifier is less confident about predicting the correct class, since more adversarial examples are sampled in this region and therefore, the attack on g is stronger. We use this measure in Section 5 to compare the effectiveness of our adversarial examples to examples obtained by PGD on g. \"]]]]],[9815,9826,[[[9815,9826,[\"3 OVERVIEW \"]]]]],[9826,10314,[[[9826,10314,[\"Existing methods for generating robust adversarial examples focus on achieving empirical robustness (Qin et al., 2019; Athalye et al., 2018). In contrast, we consider provably robust adversarial examples, defined below: Definition 3 (Provably Robust Adversarial Example). We define a provably robust adversarial example to be any large connected neural network input region, defined by a set of perturbations of an input, that can be formally proven to only contain adversarial examples. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"c4215a5d-275e-42b3-8738-16fdcb6be371\"]],[null,null,0.6804010563576985,[[null,5582,6474]],[[[5582,6474,[[[5582,6474,[\"Commonly, one proves this property by computing a function Ly : Rn0 → R for each label y 6\\u003d yt, such that, for all x ∈ I, we have Ly(x) ≤ [f(x)]yt − [f(x)]y. For each Ly, one computes minx∈I Ly(x) to obtain a global lower bound that is true for all x ∈ I. If we obtain positive bounds for all y 6\\u003d yt, robustness is proven. To simplify notation, we will say that the certification objective L(x) is the function Ly(x) with the smallest minimum value on I. We will call its corresponding minimum value the certification error. We requireLy(x) to be a linear function of x. This requirement is consistent with many popular certification algorithms based on convex relaxation, such as CROWN (Zhang et al., 2018), DeepZ (Singh et al., 2018a), and DeepPoly (Singh et al., 2019). Without loss of generality, for the rest of this paper, we will treat DeepPoly as our preferred certification method. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"06f6b8c4-e9af-48ee-8edf-c53c0586f235\"]]],[[[null,105,105],[0]],[[null,211,211],[1]],[[null,371,371],[0]],[[null,506,506],[2]],[[null,623,623],[2]]],[[\"What is a provably robust adversarial example?\",\"What is PARADE's primary purpose?\",\"What types of perturbations are discussed?\"]]]"]]
10014
[["wrb.fr",null,"[[\"The criteria used to evaluate \\\"provability\\\" in the paper is based on **neural network certification methods** [1]. A region is considered provably robust if it can be **formally proven to only contain adversarial examples** [2]. Specifically, for a given input region `I` and an adversarial target class `yt`, robustness is proven if, for every point `x` in `I`, the network `f` classifies `x` as `yt` [1]. This is achieved by computing a **certification objective `L(x)` for each non-target label `y` (`y ≠ yt`)** such that `L(x) ≤ [f(x)]yt − [f(x)]y` [3]. **If the minimum value of `L(x)` over the region `I` (`minx∈I Ly(x)`) is positive for all `y ≠ yt`, then robustness is proven** [3].\",null,[\"8b5a3781-0449-4fb5-b6e3-630347faea9c\",\"bfd7b689-e6d7-4f53-a670-8baf2b74dc46\",967081476],null,[[[[0,624,[[[0,69,[\"The criteria used to evaluate \\\"provability\\\" in the paper is based on \"]],[69,105,[\"neural network certification methods\",[true]]],[105,159,[\". A region is considered provably robust if it can be \"]],[159,211,[\"formally proven to only contain adversarial examples\",[true]]],[211,252,[\". Specifically, for a given input region \"]],[252,253,[\"I\",[null,null,null,null,null,null,null,true]]],[253,286,[\" and an adversarial target class \"]],[286,288,[\"yt\",[null,null,null,null,null,null,null,true]]],[288,331,[\", robustness is proven if, for every point \"]],[331,332,[\"x\",[null,null,null,null,null,null,null,true]]],[332,336,[\" in \"]],[336,337,[\"I\",[null,null,null,null,null,null,null,true]]],[337,351,[\", the network \"]],[351,352,[\"f\",[null,null,null,null,null,null,null,true]]],[352,364,[\" classifies \"]],[364,365,[\"x\",[null,null,null,null,null,null,null,true]]],[365,369,[\" as \"]],[369,371,[\"yt\",[null,null,null,null,null,null,null,true]]],[371,405,[\". This is achieved by computing a \"]],[405,429,[\"certification objective \",[true]]],[429,433,[\"L(x)\",[true,null,null,null,null,null,null,true]]],[433,460,[\" for each non-target label \",[true]]],[460,461,[\"y\",[true,null,null,null,null,null,null,true]]],[461,463,[\" (\",[true]]],[463,469,[\"y ≠ yt\",[true,null,null,null,null,null,null,true]]],[469,470,[\")\",[true]]],[470,481,[\" such that \"]],[481,506,[\"L(x) ≤ [f(x)]yt − [f(x)]y\",[null,null,null,null,null,null,null,true]]],[506,508,[\". \"]],[508,532,[\"If the minimum value of \",[true]]],[532,536,[\"L(x)\",[true,null,null,null,null,null,null,true]]],[536,553,[\" over the region \",[true]]],[553,554,[\"I\",[true,null,null,null,null,null,null,true]]],[554,556,[\" (\",[true]]],[556,568,[\"minx∈I Ly(x)\",[true,null,null,null,null,null,null,true]]],[568,590,[\") is positive for all \",[true]]],[590,596,[\"y ≠ yt\",[true,null,null,null,null,null,null,true]]],[596,623,[\", then robustness is proven\",[true]]],[623,624,[\".\"]]]]]],[[[\"f8a11211-3028-43ca-bea6-f35659c2708e\"],[null,105,105]],[[\"c4215a5d-275e-42b3-8738-16fdcb6be371\"],[null,211,211]],[[\"f8a11211-3028-43ca-bea6-f35659c2708e\"],[null,371,371]],[[\"06f6b8c4-e9af-48ee-8edf-c53c0586f235\"],[null,506,506]],[[\"06f6b8c4-e9af-48ee-8edf-c53c0586f235\"],[null,623,623]]]],null,null,[[[\"f8a11211-3028-43ca-bea6-f35659c2708e\"],[null,null,0.6836549996205504,[[null,4874,5582]],[[[4874,5582,[[[4874,5582,[\"In this work, we rely on existing state-of-the-art neural network certification methods based on convex relaxations to prove that the adversarial examples produced by our algorithm are robust. These certification methods take a convex input region I ⊂ Rn0 and prove that every point in I is classified as the target label yt by f . They propagate the set I through the layers of the network, producing a convex region that covers all possible values of the output neurons (Gehr et al., 2018). Robustness follows by proving that, for all combinations of output neuron values in this region, the output neuron corresponding to class yt has a larger value than the one corresponding to any other class y 6\\u003d yt. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"f8a11211-3028-43ca-bea6-f35659c2708e\"]]],[[\"c4215a5d-275e-42b3-8738-16fdcb6be371\"],[null,null,0.6860472397871276,[[null,9429,10314]],[[[9429,9815,[[[9429,9815,[\"Intuitively, this definition states that for points x̃ for which Radv is bigger, the smoothed classifier is less confident about predicting the correct class, since more adversarial examples are sampled in this region and therefore, the attack on g is stronger. We use this measure in Section 5 to compare the effectiveness of our adversarial examples to examples obtained by PGD on g. \"]]]]],[9815,9826,[[[9815,9826,[\"3 OVERVIEW \"]]]]],[9826,10314,[[[9826,10314,[\"Existing methods for generating robust adversarial examples focus on achieving empirical robustness (Qin et al., 2019; Athalye et al., 2018). In contrast, we consider provably robust adversarial examples, defined below: Definition 3 (Provably Robust Adversarial Example). We define a provably robust adversarial example to be any large connected neural network input region, defined by a set of perturbations of an input, that can be formally proven to only contain adversarial examples. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"c4215a5d-275e-42b3-8738-16fdcb6be371\"]]],[[\"06f6b8c4-e9af-48ee-8edf-c53c0586f235\"],[null,null,0.6804010563576985,[[null,5582,6474]],[[[5582,6474,[[[5582,6474,[\"Commonly, one proves this property by computing a function Ly : Rn0 → R for each label y 6\\u003d yt, such that, for all x ∈ I, we have Ly(x) ≤ [f(x)]yt − [f(x)]y. For each Ly, one computes minx∈I Ly(x) to obtain a global lower bound that is true for all x ∈ I. If we obtain positive bounds for all y 6\\u003d yt, robustness is proven. To simplify notation, we will say that the certification objective L(x) is the function Ly(x) with the smallest minimum value on I. We will call its corresponding minimum value the certification error. We requireLy(x) to be a linear function of x. This requirement is consistent with many popular certification algorithms based on convex relaxation, such as CROWN (Zhang et al., 2018), DeepZ (Singh et al., 2018a), and DeepPoly (Singh et al., 2019). Without loss of generality, for the rest of this paper, we will treat DeepPoly as our preferred certification method. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"06f6b8c4-e9af-48ee-8edf-c53c0586f235\"]]]]]],[[null,null,0.6836549996205504,[[null,4874,5582]],[[[4874,5582,[[[4874,5582,[\"In this work, we rely on existing state-of-the-art neural network certification methods based on convex relaxations to prove that the adversarial examples produced by our algorithm are robust. These certification methods take a convex input region I ⊂ Rn0 and prove that every point in I is classified as the target label yt by f . They propagate the set I through the layers of the network, producing a convex region that covers all possible values of the output neurons (Gehr et al., 2018). Robustness follows by proving that, for all combinations of output neuron values in this region, the output neuron corresponding to class yt has a larger value than the one corresponding to any other class y 6\\u003d yt. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"f8a11211-3028-43ca-bea6-f35659c2708e\"]],[null,null,0.6860472397871276,[[null,9429,10314]],[[[9429,9815,[[[9429,9815,[\"Intuitively, this definition states that for points x̃ for which Radv is bigger, the smoothed classifier is less confident about predicting the correct class, since more adversarial examples are sampled in this region and therefore, the attack on g is stronger. We use this measure in Section 5 to compare the effectiveness of our adversarial examples to examples obtained by PGD on g. \"]]]]],[9815,9826,[[[9815,9826,[\"3 OVERVIEW \"]]]]],[9826,10314,[[[9826,10314,[\"Existing methods for generating robust adversarial examples focus on achieving empirical robustness (Qin et al., 2019; Athalye et al., 2018). In contrast, we consider provably robust adversarial examples, defined below: Definition 3 (Provably Robust Adversarial Example). We define a provably robust adversarial example to be any large connected neural network input region, defined by a set of perturbations of an input, that can be formally proven to only contain adversarial examples. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"c4215a5d-275e-42b3-8738-16fdcb6be371\"]],[null,null,0.6804010563576985,[[null,5582,6474]],[[[5582,6474,[[[5582,6474,[\"Commonly, one proves this property by computing a function Ly : Rn0 → R for each label y 6\\u003d yt, such that, for all x ∈ I, we have Ly(x) ≤ [f(x)]yt − [f(x)]y. For each Ly, one computes minx∈I Ly(x) to obtain a global lower bound that is true for all x ∈ I. If we obtain positive bounds for all y 6\\u003d yt, robustness is proven. To simplify notation, we will say that the certification objective L(x) is the function Ly(x) with the smallest minimum value on I. We will call its corresponding minimum value the certification error. We requireLy(x) to be a linear function of x. This requirement is consistent with many popular certification algorithms based on convex relaxation, such as CROWN (Zhang et al., 2018), DeepZ (Singh et al., 2018a), and DeepPoly (Singh et al., 2019). Without loss of generality, for the rest of this paper, we will treat DeepPoly as our preferred certification method. \"]]]]]]],[[[\"6aa517cb-f4af-4a4d-8121-01adaff65c5f\"],\"73120ccd-3c70-4e90-9afc-50c57c77ce81\"]],[\"06f6b8c4-e9af-48ee-8edf-c53c0586f235\"]]],[[[null,105,105],[0]],[[null,211,211],[1]],[[null,371,371],[0]],[[null,506,506],[2]],[[null,623,623],[2]]],[[\"What is a provably robust adversarial example?\",\"What is PARADE's primary purpose?\",\"What types of perturbations are discussed?\"]]]"]]
60
[["di",5706],["af.httprm",5706,"-1162867718231579189",55]]
27
[["e",7,null,null,31374]]
