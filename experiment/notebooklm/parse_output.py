import json

from data import data_store

raw_output = json.load(open("experiment/notebooklm/output_raw.json", "r"))
qa_data = data_store.get_qa_data()
qa_augmented_data = data_store.get_qa_augmented_data()

def find_answer(question_id, response) -> str:
  parts = response.split("\n")
  if (len(parts[-6]) == 0):
    response = open(f"experiment/notebooklm/manual/{question_id}", "r").read()
    parts = response.split("\n")
  data = json.loads(parts[-6])
  data = json.loads(data[0][2])
  
  # evidences = []
  # print(data)
  # for d in data[1]:
  #   evidence = []
  #   for e in d[4][0]:
  #     try:
  #       evidence.append(e[2][0][0][2][0])
  #     except:
  #       pass
  #   evidences.append("\n".join(evidence))
  # print(evidences)
  answer = data[0][0]
  return answer

def main():
  result = []
  for d in raw_output:
    question_id = d[0]
    paper_id = d[1]
    question_text = d[2]
    response = d[3]
    answer = find_answer(question_id, response)
    qa_row = qa_data[qa_data['question_id'] == question_id].iloc[0]
    expected_answer = qa_row['answer_free_form']
    qa_augmented_row = qa_augmented_data[qa_augmented_data['question_id'] == question_id].iloc[0]
    expected_answer_augmented = qa_augmented_row['augmented_answer_free_form']
    result.append({
      "question_id": question_id,
      "paper_id": qa_augmented_row['paper_id'],
      "question": question_text,
      "generated_answer": answer,
      "expected_answer": expected_answer,
      "expected_answer_augmented": expected_answer_augmented
    })
  json.dump(result, open("experiment/notebooklm.json", "w"), indent=2)  

if __name__ == "__main__":
  main()