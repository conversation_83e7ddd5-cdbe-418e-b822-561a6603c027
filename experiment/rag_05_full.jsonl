{"question_id": "7ed6482c76ae428708b4675495718652adfed68d", "paper_id": "nlpeer/COLING2020/679", "generation": "Six of the nine annotators who participated in the evaluation were native English speakers."}
{"question_id": "0081780bb3b2f8ca303dd426ce4ddfd89c13e80d", "paper_id": "nlpeer/ARR-22/0f595b4bd968ef5daacc88c4edb581bf7f683af30b6874ba157c6d2282aabead577041409b949924e4bc5ef54c543d85a10e962be8e70304dea65e1b18441bdb", "generation": "The average duration reported in Table 1 represents the average time taken by workers to complete a Human Intelligence Task (HIT) and to assess dialogues. This metric is used to provide information on the time investment required from workers during the human evaluation process of dialogue systems."}
{"question_id": "465b94530bc99598bbefb742da70cf99236b86d9", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "Classification-based models were not used for the zero-shot experiments in Tables 6 and 7 because there is no way to resolve ties with classi\ufb01er predictions when the model predicts the same class for two documents of different reading levels. This limitation makes it infeasible to use classi\ufb01ers as rankers in cross-corpus and cross-lingual evaluation settings, which require distinguishing between such ties. Consequently, only regression and ranking approaches were employed in these zero-shot experiments."}
{"question_id": "6a2461eebef2649b0832b8d91cb8ed490edbf2d3", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "The provided information does not explicitly state the reason why Ordinary Least Squares (OLS) was chosen for the regression-based systems instead of Support Vector Regression (SVR). However, it can be inferred that OLS was used as a baseline regression model with non-contextual word-level embeddings due to its simplicity and established use in regression tasks, allowing for straightforward comparison with more complex models such as the neural regressor (regBERT). The use of OLS facilitates evaluation through standard regression metrics and comparison with other embedding types, as shown in the experimental setup and results sections."}
{"question_id": "e76615760f7625a5b984cb961f5d948ab2407178", "paper_id": "nlpeer/PeerRead-ACL2017/193", "generation": "In the UCCA scheme, a node (or unit) corresponds either to a terminal (a single token) or to several terminals (not necessarily contiguous) viewed as a single entity. Thus, a node can represent a single terminal or a non-terminal unit comprising multiple terminals, but it is not both simultaneously. Non-terminal nodes are used to represent units comprising more than one word, including constructions with no clear head such as coordination structures, multi-word expressions, and prepositional phrases."}
{"question_id": "cf26cc0cd1000ad63bfba19b5159a20efba34b18", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "The parameters of the BLOSUM matrix are not estimated by the model; instead, the BLOSUM vectors are pre-computed once from the multiple sequence alignment and then processed into BLOSUM embeddings by a neural network. The model uses these pre-computed weighted averaged BLOSUM vectors as input, and the neural network NN(1)\u03b8 maps these vectors to position-specific embeddings. Thus, the substitution scores in the BLOSUM matrix are fixed and serve as a basis for the embeddings rather than being learned or estimated during model training."}
{"question_id": "a3bcdc5b71130202e27b2a0b4a8485392efedec5", "paper_id": "openreview/ICLR-2023-conf/rLguqxYvYHB", "generation": "The provided information does not explicitly discuss challenges specifically related to estimating the feature map function \\(\\phi_A\\) when the treatments are discrete. However, it is noted that in the case where the treatment \\(A\\) is discrete and the back-door variables \\(X\\) are also discrete, Assumption 1 is satisfied with \\(\\beta = 1\\) and \\(c = \\min_{(a,x) \\in A \\times X} P(A=a, X=x)\\), implying a non-zero probability of observing all elements in \\(A \\times X\\). This suggests that the estimation framework, including the feature maps \\(\\phi_A\\) and \\(\\phi_X\\), can be well-defined and consistent under discrete treatments.\n\nNo specific difficulties or challenges in estimating \\(\\phi_A\\) for discrete treatments are mentioned. In contrast, challenges are highlighted for high-dimensional continuous treatments, where the estimation of the Riesz representer \\(\\alpha\\) becomes difficult due to exponential growth of the error bound with dimension, but this is not directly linked to \\(\\phi_A\\) or discrete treatments.\n\nTherefore, there is no evidence indicating that estimating \\(\\phi_A\\) poses particular challenges when treatments are discrete."}
{"question_id": "7088d27d671a415164b81660a60173ea7602d968", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "A bidirectional gated recurrent unit (GRU) decoder is used for sequences of the same length to model the sequential dependencies along the protein sequences while incorporating both sequence-specific and position-specific information. Specifically, at each position of the sequence, the GRU input is a concatenation of the latent vector representing the entire sequence and a BLOSUM embedding that encodes position-specific amino acid preferences derived from the multiple sequence alignment (MSA). This architecture allows the GRU to capture complex patterns and correlations across sequence positions despite the fixed sequence length, enabling the decoder to produce a logit vector specifying the probabilities of all possible characters at each position. Thus, the GRU decoder effectively models the conditional distribution of characters along the sequence, leveraging both global latent representations and local position-specific embeddings."}
{"question_id": "058da91fd7bfe9ecd3163d9d8877e5116cf5cdf6", "paper_id": "openreview/ICLR-2022-conf/C03Ajc-NS5W", "generation": "The evidence that the generative model G-SphereNet is successful in synthesizing new molecules includes the following points:\n\n1. G-SphereNet outperforms previous methods such as G-SchNet in both random molecular geometry generation and targeted molecule discovery tasks, as demonstrated by higher validity and better property optimization metrics (e.g., achieving scores lower than 4.5 eV for the HOMO-LUMO gap and higher than 91 Bohr\u00b3 for isotropic polarizability).\n\n2. The model generates more molecular geometries with desirable quantum properties compared to competing methods, indicating its stronger ability to search molecular geometries with targeted properties.\n\n3. Visual illustrations of sample molecular geometries generated by G-SphereNet show molecules with optimized properties, such as minimized HOMO-LUMO gap and maximized isotropic polarizability.\n\n4. Ablation studies demonstrate that the use of local and global features, 3D geometric information (distance, angle, torsion), and the Sigmoid method for focal atom selection contribute to higher validity rates (up to 88.18%), supporting the model's effectiveness in generating chemically valid molecular geometries.\n\n5. The model employs a flexible sequential generation scheme that places atoms step-by-step in 3D space by generating distances, angles, and torsion angles, ensuring invariance and equivariance, which contributes to accurate and valid molecular geometry generation.\n\nCollectively, these results provide strong evidence that G-SphereNet successfully synthesizes new molecules with valid structures and desirable quantum properties."}
{"question_id": "cdb59f3c15dd8d951c169a9f2fae03e000215bae", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "Yes, the cross-linguistic analysis includes both French and Spanish. The zero-shot cross-lingual evaluation was conducted using models trained on English data (Newsela-En) and tested on Spanish (Newsela-Es) and French (Vikidia-Fr) datasets. The experiments demonstrated that the neural pairwise ranking model performs well in these cross-lingual scenarios, achieving a zero-shot cross-lingual ranking accuracy of over 80% for both French and Spanish."}
{"question_id": "3f958288ac95aea397ff6c9a6854d14d691778a2", "paper_id": "openreview/ICLR-2023-conf/-CoNloheTs", "generation": "The proposed algorithm is designed for exact reconstruction of neural networks under certain assumptions and conditions. It can recover two-layer neural networks with any number of hidden neurons and three-layer neural networks under mild general position assumptions, with additional constraints such as the number of first-layer neurons being smaller than the input dimension and the second layer having non-zero partial derivatives. The algorithm operates in polynomial time and query complexity, and it reconstructs the network up to an affine transformation.\n\nThe method relies on identifying points where the network's linearity breaks and extracting neurons by recovering the affine transformations near these points. It also includes a novel approach to distinguish neurons belonging to the first or second layer, enabling the handling of three-layer networks. The algorithm can reconstruct networks with any finite width in polynomial time, bypassing challenges related to neuron sign ambiguity.\n\nHowever, the algorithm requires that the input weights be slightly perturbed (e.g., by adding a small uniform noise) to ensure polynomial-time performance. This implies that the algorithm is applicable to networks whose weights are in \"general position,\" which excludes a measure-zero set of pathological cases.\n\nIn summary, the algorithm can be used to recover real neural networks that satisfy the mild general position assumptions and the specified structural conditions, particularly when the network weights are slightly perturbed to avoid degenerate cases."}
{"question_id": "5f4c9dea82aa176c2e42cd2c59ff3da0fce4a367", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "Yes, the pairwise Euclidean distances between the latent representations of sequences can be quantitatively compared to the corresponding branch lengths in a phylogenetic tree. This comparison was performed for both a standard variational autoencoder (VAE) and the Draupnir model, which incorporates a tree-structured Ornstein-Uhlenbeck (TOU) process as a prior. The correlation between the latent space distances and the phylogenetic branch lengths was measured, showing that Draupnir achieves a higher correlation coefficient (0.91) and Spearman correlation coefficient (0.94) compared to the standard VAE (correlation coefficient 0.79, Spearman 0.85). This indicates that the latent representations learned by Draupnir more accurately reflect the evolutionary distances encoded in the phylogenetic tree."}
{"question_id": "e2ee4a1059cbb3c736b7b00cd902bbbd428423e8", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "No, the authors have not considered formulating the Darpa dataset as a dynamic graph for network intrusion detection. The discussion of graph-based modeling is limited to some deep learning approaches for intrusion detection in general, where data is transformed into images or modeled using Graph Neural Networks (GNNs) [29], but this is not specifically applied to the Darpa dataset. The authors focus on the Kyoto-2006+ dataset and its temporal distribution shifts rather than the Darpa dataset or dynamic graph formulations."}
{"question_id": "b8c9effb94fbab2db127d7262041fecdb031c1dd", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "Yes, the numerical to categorical conversion (feature binarization) is performed for all methods to ensure a fair comparison. Specifically, three numerical float features\u2014connection duration, number of source bytes, and number of destination bytes\u2014are converted into categorical bins. This preprocessing step is applied uniformly across all methods, including classical and deep learning baselines, as stated to be proper for fair comparison. However, experiments without this preprocessing were also conducted, showing similar trends in performance drop across splits, but the primary evaluation uses the binarized features for all methods."}
{"question_id": "264d5ee47c97b02b5a597533254b78c97cb41f8b", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "There is no information in the provided chunks regarding the DARPA dataset or any limitations related to it preventing its use as a graph dataset. The discussed datasets focus primarily on network intrusion detection datasets such as Kyoto-2006+, CIC-IDS2017, and others, with no mention of DARPA or specific limitations about graph representations."}
{"question_id": "6ceebb857d2674bd38452e8bc30a3d280f2ce647", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "The lower performance on Vikidia-Fr compared to Newsela-Es can be attributed to several factors: \n\n1. Language difference: Vikidia-Fr is in French, whereas Newsela-Es is in Spanish, which may affect model transferability.\n\n2. Domain difference: Newsela-Es shares the same domain as Newsela-En (the training dataset), while Vikidia-Fr contains more diverse content, making it harder for the model to generalize.\n\n3. Number of reading levels: Vikidia-Fr has only two reading levels, whereas Newsela-Es has ten. Ranking metrics such as SRC and KTCC are more forgiving with a larger number of reading levels. In Vikidia-Fr, incorrect ranking of levels can result in a completely reversed list, heavily penalizing these metrics and leading to lower scores.\n\nThus, the combination of language, domain diversity, and fewer reading levels contributes to the observed lower performance on Vikidia-Fr."}
{"question_id": "1d2837da9c9a557b18715b0482bea0532e3ac176", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The performance of the Transformer model (BERT for Anomalies) is lower in the IID split than in the NEAR split because, during the IID period, the anomalies are modeled quite poorly by the language model. This results in slightly lower performance on the IID split compared to the NEAR split. Specifically, the model struggles to recognize anomalies well in the IID data, which explains the observed performance difference. In contrast, in the NEAR split, the model effectively models the outliers, leading to better anomaly detection performance."}
{"question_id": "815b242c673ecb7aea2ccec4f6c83ab1191a9124", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "A Multi-Layer Perceptron (MLP) cannot be used to decode the aligned sequences instead of a Gated Recurrent Unit (GRU) decoder because the decoding process requires modeling sequential dependencies along the alignment length. The GRU, being a recurrent neural network architecture, is specifically designed to handle sequential data by maintaining and updating hidden states across positions in the sequence. This allows the GRU decoder to incorporate both the latent vector representing the entire sequence and position-specific information (such as BLOSUM embeddings) to produce position-wise probability distributions over characters. In contrast, an MLP lacks the recurrent structure necessary to capture the sequential context and dependencies inherent in aligned protein sequences, making it unsuitable for decoding sequences where positional order and context are critical."}
{"question_id": "de0f53c58cedd98fd958715bb1a2f5a3d24e829d", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The source and destination IP addresses and port numbers were discarded because the dataset preprocessing focused on conventional features suitable for language-modeling approaches, and these additional features were not included in the main feature set used for the benchmark. Although these features might be useful for designing models that focus on connections between nodes in the system (e.g., graph-based models), they were excluded from the primary preprocessing to maintain consistency and suitability for the applied methods."}
{"question_id": "d256c384aa446ef6ba7d69269df08e3dbbdb2db9", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "Constructing the phylogenetic tree required as input for the algorithm involves inferring the tree topology, the labels of the tree\u2019s edges, and the composition of the ancestral sequences. This process is a central concern of the field of phylogenetics and typically relies on methods based on heuristics, such as maximum parsimony, or probabilistic evolutionary models. The tree is represented as a binary tree with leaf nodes corresponding to known extant sequences and internal nodes representing unknown ancestral sequences. Edges are labeled by positive real numbers indicating the time difference or amount of evolutionary change between nodes. Thus, the construction of the phylogenetic tree is a non-trivial task that requires specialized computational methods and evolutionary modeling."}
{"question_id": "a36f298f8941bf93ad6bdc2ef8db6471e6ca4156", "paper_id": "nlpeer/PeerRead-ACL2017/193", "generation": "The UCCA transition scheme offers capabilities that address limitations present in existing algorithms for Abstract Meaning Representation (AMR) and Semantic Dependency Parsing (SDP), but it is not explicitly stated that it would improve their performance directly.\n\nSpecifically, existing transition-based AMR parsers are limited in their ability to produce general directed acyclic graph (DAG) parses. They can only predict a subset of reentrancies and discontinuities because they may remove nodes before their parents have been predicted, restricting them to a sub-class of AMRs. In contrast, the UCCA transition set allows for general DAG parsing, supporting reentrancy, discontinuous nodes, and non-terminal units without requiring separate alignment of input tokens to graph nodes.\n\nRegarding SDP, while it supports discontinuous units and reentrancy, it relies on bilexical dependencies and requires selecting a head for every relation, even in constructions without a clear head. UCCA avoids this by using non-terminal nodes, which simplifies representation and better captures purely semantic, cross-linguistically applicable notions, as opposed to SDP's tighter coupling with syntactic considerations.\n\nTherefore, the UCCA transition scheme provides a more general and flexible framework for semantic graph parsing, particularly in handling reentrancies, discontinuities, and non-terminal nodes, which are challenging for current AMR and SDP parsers. This suggests that adopting or adapting the UCCA transition scheme could potentially enhance the expressiveness and coverage of AMR and SDP parsing algorithms. However, the text does not provide empirical evidence or direct claims of improved performance when applying the UCCA transition scheme to AMR or SDP parsing tasks."}
{"question_id": "ee956c6a1b9b9808fc77d7d1f8f82237123f5000", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "The phrase \"edit boundaries might be unusual\" refers to the challenge in determining the precise start and end points of edits when aligning original and corrected sentence pairs. This is fundamentally an alignment problem because edits may span multiple tokens or involve complex changes that do not correspond to simple one-to-one token substitutions. For example, a phrase in the original sentence might be replaced by a different phrase of varying length in the corrected sentence, making the identification of exact edit boundaries non-trivial. Consequently, edit boundaries can be irregular or atypical compared to straightforward token alignments, necessitating sophisticated alignment methods that incorporate linguistic information to accurately capture these multi-token or complex edits."}
{"question_id": "49887aceab5099bc8a45f1f01aa437f760c289a5", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "The 90% Normalized Information Gain (NInGa) achieved by the trained mixture model (zero-inflated Log-Normal likelihood, ZIL) on the real neural data example demonstrates strong performance for that specific dataset. Additional analyses were performed on multiple datasets to show how NInGa facilitates model comparison across different datasets (as mentioned in Section 3.2 and Appendix J). This suggests that the NInGa metric, and by extension the performance of the mixture model measured by it, can be meaningfully compared across datasets. However, the text does not explicitly state that the exact 90% NInGa performance level generalizes to other datasets, only that the metric enables such cross-dataset comparisons. Therefore, while the 90% NInGa indicates high performance on the evaluated dataset, it does not guarantee the same absolute performance on other datasets, but the metric provides a consistent framework for evaluation across datasets."}
{"question_id": "962be5303d07e7707c9750241730986cfeb7d754", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "The significance test was performed using precision, recall, and F0.5 scores obtained from evaluating each team in the CoNLL-2014 shared task with both gold references and automatic references, employing the M2 scorer and the authors' own scorer. The test was conducted by means of bootstrap significance testing with 1,000 iterations."}
{"question_id": "92394e14628bdc9941b0581b43b20ab42dbdd3fd", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "No, the expected label does not always match the most probable label given the noisy data. The analysis shows that during training with noisy or corrupted labels, the model's prediction initially moves towards the unknown true label distribution (p*), which may differ from the provided noisy label. This results in a zig-zag learning path where the model first approaches the true label before eventually memorizing the incorrect noisy label. Thus, the expected label (reflecting the true underlying distribution) can differ from the most probable label assigned by the noisy training data."}
{"question_id": "7869279cfc2dc07fcd82704dc07789afa6de5c82", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "The issue with the point estimate (PE) oracle itself is that it is sensitive to the sparse distribution of neural data combined with few responses per stimulus, which results in an overconfident and non-robust estimation of the GS parameters, particularly for higher moments beyond the mean. This sensitivity causes the PE approach to fail, as evidenced by the Null model outperforming the PE GS model for many neurons. The problem is inherent to the PE method's lack of robustness in estimating moments beyond the first, especially under conditions of low sample size, sparse responses, and low signal-to-noise ratios. The Bayesian approach, which treats parameters probabilistically, overcomes these limitations and consistently yields better likelihood performance. There is no indication that regularization alone improves the PE oracle; rather, the fundamental limitation lies in the PE estimation method itself."}
{"question_id": "f3d892c65c8fc7edb3624cad07d6ef39115402eb", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "A new set of error categories was designed to create a dataset-agnostic, rule-based framework that overcomes the limitations of previous approaches, such as maximum entropy classifiers trained on specific corpora, which are biased towards their particular training data and do not generalize well across different datasets annotated according to varying standards. The new framework aims to balance informativeness and practicality by providing a flexible and detailed error categorization system that can be prefixed to indicate the type of edit (Missing, Replacement, or Unnecessary) and allows evaluation at different levels of granularity. This approach facilitates standardized error type evaluation across datasets and reduces annotator workload, addressing the shortcomings of existing tagsets like those used in Nicholls (2003) or CoNLL-2014, which lack such flexibility and dataset independence."}
{"question_id": "a48eb6eab4e9448324227205ae04b8d47a5b181e", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "In addition to accuracy (ACC) and expected calibration error (ECE), no other specific criteria for defining generalization are explicitly mentioned in the provided content. The focus is on accuracy and expected calibration error as measures of generalization performance in the experiments described."}
{"question_id": "96a32bff80b5928198a99a4fc2c2e24cd1a982dd", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The magnitude of the noise in the data is represented by the parameter \u03c3, which is the noisy level for all the samples. Specifically, the input signal for each class k is sampled as x | y = k \u223c N(\u00b5_k, \u03c3\u00b2I), where \u03c3 controls the noise level in the Gaussian distribution from which the samples are drawn."}
{"question_id": "3b4dcf624027feff21ac63b6e451169e1ca6bf2a", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The proposed criterion for quantifying generalization performance is the average L2 distance between the target supervision distribution \\( p_{\\text{tar}} \\) and the ground truth distribution \\( p^* \\) over the samples, expressed as \\( \\mathbb{E}_x \\left[ \\| p_{\\text{tar}}(x) - p^*(x) \\|_2 \\right] \\). Smaller values of this average L2 distance indicate better generalization performance. This is supported by a theoretical bound showing that minimizing this distance leads to a better approximation of the true risk and is empirically validated by experiments demonstrating a strong correlation between smaller L2 distance and improved accuracy and calibration on test sets."}
{"question_id": "67b6a78d6cea6ff4cd6a6cdd262aaf4e4bfea275", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The relationship between the p*() label and the one-hot encoding for the hard sample in Figure 3 is that the one-hot label e_y is a less accurate approximation of the true conditional distribution p*(y|x) for hard samples. Hard samples have a larger distance gap \u2225p_tar \u2212 p*\u2225_2 when supervised with one-hot labels, indicating that the one-hot encoding deviates more from the true label distribution p*. This deviation contributes to overfitting and less effective supervision. In contrast, supervisory signals such as label smoothing (LS), knowledge distillation (KD), and early-stopped knowledge distillation (ESKD) produce p_tar distributions closer to p*, reducing the distance gap and improving generalization. Specifically, for hard samples, the one-hot label is a poor estimate of p*, which may be flat or multimodal, whereas alternative supervisory signals better approximate p*, leading to improved training dynamics and reduced overfitting."}
{"question_id": "4d8419e9aeb2f3d606bca8774d3618d08b70c41f", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "Improving the upper bound for the Information Gain evaluation metric provides several practical benefits. First, it enables a more accurate and robust normalization of likelihood-based model performance, placing the model's likelihood between meaningful lower and upper bounds. This normalization facilitates assessing whether a model has achieved its \"best possible\" performance on a given dataset. Second, a well-estimated upper bound allows for reliable comparison of models trained on different datasets, which may have varying levels of achievable performance due to differences in noise, sparsity, and signal-to-noise ratio. Third, optimizing the prior hyper-parameters of the upper bound oracle model on a per-neuron basis yields a more conservative and realistic estimate of model performance, improving the interpretability of the evaluation. Finally, a robust upper bound estimate, such as that obtained via a Bayesian posterior predictive approach, is data-efficient and resilient to challenges common in neural data, including sparse responses, low sample sizes, and low signal-to-noise ratios. This leads to more reliable evaluation metrics, as demonstrated by the ability to achieve high Normalized Information Gain (NInGa) values (e.g., 90%) when evaluating neural encoding models."}
{"question_id": "fffbbdd88b4cdc0b98de790921df08f7be1eed7d", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The zig-zag learning path behavior is representative and prevalent across the dataset, particularly for samples with higher base difficulty. Quantitative analysis using the defined zig-zag score shows a significant correlation between base difficulty and the extent of zig-zagging during training, as demonstrated in multiple toy datasets (Figure 14). In CIFAR10, samples with incorrect (flipped) labels, which are known to have high base difficulty, exhibit significantly higher zig-zag scores than average samples (Figure 13). Visualization of randomly selected samples from each class in CIFAR10 further confirms that easy samples with correct labels converge quickly with minimal zig-zagging, whereas samples with wrong labels or ambiguous cases display pronounced zig-zag learning paths (Figures 15 and 16). Thus, the zig-zag pattern is a common and characteristic feature of the learning dynamics, especially for difficult or mislabeled samples."}
{"question_id": "c67443bf273772ac2d4297564f839c0a0229e6eb", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The greyed out portion of Figure 3 represents the noise heads in the CLIPSep-NIT model. These noise heads are used to predict noisy target sounds, and their activation is regulated by the noise regularization level \u03b3 to control the extent to which signals are assigned to noise. The greyed out area visually distinguishes the noise heads from the query heads in the model architecture."}
{"question_id": "01163085d0c4776005e14d8621ce2bbdd3cc1c13", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The Filter-KD model outperforms both label smoothing (LS) and knowledge distillation (KD) with optimized temperature in terms of accuracy and calibration. Specifically, in self-distillation experiments on ResNet18, Filter-KD achieves higher accuracy and lower expected calibration error (ECE) compared to OHT (one-hot labels), KD, and ESKD (a variant of KD), as shown in Table 3. For example, on CIFAR100, Filter-KD attains an accuracy of 80.09% with an ECE of 0.029, which is superior to KD (78.83% accuracy, 0.067 ECE) and OHT (78.07% accuracy, 0.053 ECE).\n\nIn the teacher-to-student distillation setting on CIFAR100 (Table 2), Filter-KD also surpasses ESKD and the student trained without distillation across different architectures, indicating better generalization performance. The improvement is attributed to the refined supervisory signal provided by Filter-KD rather than differences in temperature, as the experiments optimize temperature for each baseline but fix it at \u03c4 = 1 for Filter-KD to isolate the effect of label refinement.\n\nOverall, Filter-KD provides a better supervisory signal that enhances prediction performance beyond what is achieved by standard label smoothing or knowledge distillation with optimized temperature."}
{"question_id": "fcf91acb3ff79184eb4af002b876fec65732620c", "paper_id": "egu/esd/14-185-2023", "generation": "The partial pressure of carbon dioxide (pCO2) for the measurement of dissolved inorganic carbon (MOS_DIC) was calculated by tracking the remineralized MOS-C as a tracer (MOS_DIC) within the UVic Earth System Climate Model (UVic ESCM). MOS_DIC participates in the inorganic ocean carbon cycle and, upon reaching the ocean surface, its outgassing follows the air\u2013sea gas exchange process as described in Weaver et al. (2001, Sect. 3e). The air\u2013sea exchange flux of MOS-C, which depends on the surface pCO2, is calculated within the model to analyze the location and quantity of outgassing. Thus, pCO2 is implicitly determined through the modeled air\u2013sea gas exchange dynamics of MOS_DIC in the UVic ESCM framework."}
{"question_id": "36e40e97993a08a2c5e50bfc69c991334be39e6e", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The noise heads are discarded at test time because the model is designed to use only the query heads for inference to specify the target sound by an input query. During training, the noise heads help handle noisy data by allowing the model to assign part of the target sounds to noise heads to minimize the loss through optimal permutation. However, at inference, only the query heads are used to separate the sounds corresponding to the query, and the noise heads are not needed. This approach avoids the need for any post-selection process and enables direct text-queried sound separation."}
{"question_id": "924a054e5ec561c4d58306dfd312782d7b4f70ca", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "Students are supervised using refined supervisory signals represented as target distributions \\( p_{\\text{tar}}(y|x) \\) for each training sample \\( x \\). These target distributions can differ from the traditional one-hot labels and include: \n\n1. One-hot labels \\( e_y \\), where the label vector has a 1 for the correct class and 0 for others, which is the standard approach.\n2. Distributions derived from multiple human annotators, as in the CIFAR10H dataset, which estimate a more accurate \\( p_{\\text{tar}} \\) by capturing label uncertainty.\n3. Label smoothing, which sets \\( p_{\\text{tar}} \\) to a convex combination of the one-hot label and a uniform distribution over classes, providing a smoothed supervisory signal.\n4. Knowledge distillation (KD), where a teacher network trained on the data produces soft output probabilities that serve as sample-specific target distributions \\( p_{\\text{tar}} \\) for the student. This includes self-distillation, where the teacher and student have identical architectures and training data.\n\nThus, students are supervised by target label distributions that can be one-hot vectors, smoothed labels, human-annotator-derived distributions, or teacher-generated soft labels, all of which aim to provide better supervisory signals than standard one-hot labels."}
{"question_id": "27a8c35fcd38d0a141fb5248ad93038196553dfb", "paper_id": "egu/esd/14-185-2023", "generation": "The authors do not explicitly discuss coastal seaweeds competing for iron against open ocean phytoplankton. However, in their macroalgae growth model, iron limitation is not considered. They note that although iron is utilized during macroalgae growth, iron limitation on macroalgae is not widely discussed, especially for the genus Saccharina. Furthermore, they state that the MOS (macroalgae open-ocean mariculture and sinking) platform could be designed with an iron supply for the macroalgae, effectively including a targeted variant of the ocean iron fertilization concept. Thus, the authors assume that iron can be sourced from the platform to support macroalgae growth, but they do not specifically address the competition for iron between coastal seaweeds and open ocean phytoplankton."}
{"question_id": "06dc05622c2bee5d4a078d2ef7542457179ac90b", "paper_id": "nlpeer/ARR-22/6d1b7e47f044e29d515db7e4762ace8e3f089fe88c2a9de074e8d71f0f78da6169be5756ec9320b5354d2f0ebef3352fedbf5dd82012a7eafde0b0dfbfc311df", "generation": "The baseline model achieves significantly better performance on ACE 2004 and ACE 2005 compared to Yu et al. (2020) likely because the baseline configuration is relatively simple and does not rely on additional auxiliary embeddings, yet it effectively handles nested NER tasks such as ACE 2004 and ACE 2005. The use of pretrained language models (RoBERTa for English and BERT with whole word masking for Chinese) combined with a BiLSTM layer and a biaffine decoder with span width embeddings provides strong contextualized representations that improve performance on these nested datasets. In contrast, for OntoNotes 5 and CoNLL 2003, which are flat NER tasks, the baseline and Yu et al. (2020) achieve similar performance, possibly because the simpler annotation scheme and entity types in these datasets reduce the advantage of the baseline's architectural choices. Thus, the baseline's design and hyperparameter tuning are particularly effective for the more complex nested NER tasks in ACE 2004 and ACE 2005, leading to better performance relative to Yu et al. (2020), while on flat NER datasets, the performance is comparable."}
{"question_id": "0c4afb8ced370f2f67477fe4617ff846513cfb6d", "paper_id": "openreview/NeurIPS-2022-dabt/8lQDn9zTQlW", "generation": "Table 2 presents the zero-shot evaluation results of various language models on multiple biomedical datasets from the BIGBIO benchmark. The values represented include the mean, standard error (SE), minimum (Min), and maximum (Max) Rouge-1 F-measure scores achieved by each model on each dataset. The table lists these metrics for different models such as SciFive-Base, SciFive-Large, GPT-Neo-1.3B, GPT-2, GPT-J-6B, T0_3B, T5 v1.1-xxl, T0, T0+, T0++, GPT-NeoX-20B, and GPT-3 across datasets including bigbio_blurb_bc5chem, bigbio_blurb_bc5disease, bigbio_blurb_ncbi_disease, bigbio_blurb_bc2gm, and bigbio_blurb_jnlpba."}
{"question_id": "3493acb3c91a1415959829136fe3e250966fc8f0", "paper_id": "openreview/NeurIPS-2022-dabt/8lQDn9zTQlW", "generation": "The prompt design for biomedical natural language processing (BioNLP) tasks in this study largely leveraged general domain templates already available in PromptSource, which were adapted to reflect the original classification tasks of the biomedical datasets. This approach contrasts with designing completely novel prompts specific to biomedical text. For named entity recognition (NER) tasks in BioNLP, prompts were formulated as text translation tasks, where the output is a list of all entities found in the input text, following a method similar to TANL. No iterative prompt tuning was performed to improve template performance. This differs from general NLP tasks where prompt templates may be more directly aligned with the original task format and may not necessarily use a text translation formulation for NER. Additionally, the biomedical prompts were considered more \"in-distribution\" for the T0 model because they were based on general domain templates used during T0's tuning, potentially giving T0 an advantage in biomedical zero-shot evaluation. In contrast, GPT-3 instruction tuning likely follows a different form, which may explain its generally lower performance on non-NER biomedical tasks."}
{"question_id": "8f2072e6213f44471d3294973c9cfdd790bc7259", "paper_id": "openreview/ICLR-2022-conf/_X90SIKbHa", "generation": "The proposed methods, ST-AM and MST-AM, have several advantages compared to Newton and standard quasi-Newton methods such as BFGS, L-BFGS, Chord, and Levenberg\u2013Marquardt:\n\n1. **Memory Efficiency**: Unlike full-memory quasi-Newton methods (e.g., BFGS) that require storing a large number of historical iterations to form secant equations, MST-AM only needs to store two corrected historical iterations. This significantly reduces memory overhead compared to full-memory methods and even limited-memory methods like L-BFGS, which still require storing multiple historical iterations and may lose local superlinear convergence properties as a result.\n\n2. **Short-Term Recurrence**: ST-AM is the first attempt to develop short-term recurrence quasi-Newton methods, enabling efficient iterative updates without the need for extensive historical data. This contrasts with existing limited-memory quasi-Newton methods that do not have short-term recurrence and whose performance can be sensitive to the choice of historical length.\n\n3. **Flexibility and Applicability**: ST-AM does not require direct access to the matrix or Hessian, only the residual is needed, making it suitable for problems where the Hessian is costly or difficult to obtain. This property allows ST-AM to be extended directly to unconstrained optimization problems where gradients are available but Hessians are not.\n\n4. **Performance**: Experimental results demonstrate that MST-AM can outperform BFGS in solving cubic-regularized problems and is comparable to full-memory methods in root-finding tasks such as those in MDEQ on CIFAR-10. ST-AM also significantly accelerates slow convergence of gradient descent and fixed-point iteration methods and can outperform Anderson Mixing (AM) and conjugate residual (CR) methods in certain cases.\n\n5. **No Line Search or Trust Region Needed**: Unlike Newton and many quasi-Newton methods that rely on line search or trust-region techniques, AM-based methods including MST-AM manipulate only residuals and do not require these additional procedures, enhancing efficiency.\n\n6. **Robustness to Parameter Choices**: MST-AM incorporates historical information through orthogonalization, avoiding the sensitivity to the number of stored historical iterations that affects limited-memory quasi-Newton methods.\n\nIn summary, the proposed ST-AM and MST-AM methods combine the theoretical strengths of quasi-Newton methods with reduced memory requirements and enhanced flexibility, making them competitive alternatives to Newton, BFGS, L-BFGS, Chord, and Levenberg\u2013Marquardt methods, especially in large-scale or high-dimensional nonlinear problems where Hessian computation or storage is prohibitive."}
{"question_id": "cd020940c9b12a598dae5fc4fde1d63c2d88d88d", "paper_id": "egu/esd/14-185-2023", "generation": "The provided information does not explicitly address the potential for longer permanence of CO2 storage through sediment carbonate dissolution caused by high respiratory dissolved inorganic carbon (DIC) at the ocean bottom. The study discusses the fate of sunken macroalgal biomass and its remineralization, which consumes oxygen and creates benthic oxygen minimum zones (OMZs), and notes that when macroalgal biomass does not undergo microbial remineralization, the captured CO2 can be permanently stored without leakage, thereby increasing the carbon dioxide removal (CDR) potential. However, there is no mention or analysis of sediment carbonate dissolution processes or their impact on the permanence of CO2 storage in the sediment. Therefore, the potential effect of sediment carbonate dissolution on longer-term CO2 storage permanence has not been considered or investigated in this study."}
{"question_id": "834016a31e50565175511dcdf3d75a1be44b532c", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The paper defines base difficulty as the Euclidean distance between the one-hot label vector \\( e_y \\) and the unknown true label distribution \\( p^*(x) \\), specifically \\( \\| e_y - p^*(x) \\|_2 \\). A sample has high base difficulty if it is ambiguous\u2014meaning \\( p^*(x) \\) has several large components and no one-hot label is close to \\( p^*(x) \\)\u2014or if the sample is not ambiguous but the assigned label \\( y \\) is from a low-probability class relative to \\( p^*(x) \\). This base difficulty serves as a proxy to characterize how challenging a sample is to learn and is used to analyze the evolution of the network\u2019s predicted distribution during training."}
{"question_id": "9c04f85fb5baad69d0ae21c1c2c07abc0422bd55", "paper_id": "nlpeer/ARR-22/013b9bf63a6f68fd0c3ecc36f8cbe2ad5bc92ea3bfe5a9f6c15eb056ecc4f858718410182c3765b2dc2695ae29ba08fb5dea5fc495faf2bbb77205bc3f765fcd", "generation": "To ensure a more diverse cross-cultural representation when building an NLP model, the following concrete steps can be taken:\n\n1. **Data Collection and Annotation:**\n   - Recruit a diverse pool of annotators from different geographic and cultural backgrounds to reduce cultural bias and capture multiple perspectives, especially for subjective tasks such as detecting affect, aggression, and hate speech.\n   - Release all annotations, including disagreeing ones, to allow training models that generalize better across cultural nuances.\n   - Carefully document the annotation process to provide transparency and facilitate understanding of cultural influences on the data.\n   - Engage participatory design approaches that consider the values, ideologies, and power relations among actors involved in data collection and annotation.\n\n2. **Data Selection and Curation:**\n   - Use data sources that are appropriate and representative of the target culture for downstream NLP applications.\n   - Curate large, general-purpose datasets to minimize cultural bias and ensure balanced representation of diverse sources.\n   - Avoid over-reliance on culturally biased corpora such as Wikipedia or web-based corpora skewed towards specific regions or groups.\n   - Strive to include data from underrepresented cultures and languages, leveraging communities such as Universal Dependencies and Masakhane that focus on typological and linguistic diversity.\n\n3. **Model Training:**\n   - Address representation disparity by considering the granularity at which cultural groups are defined and annotated, balancing between individual privacy and group-level representation.\n   - Implement training methodologies that maximize performance across different cultural groups rather than only on average performance, to avoid neglecting minority groups.\n   - Use techniques that account for model updates specific to different groups to reduce cultural biases.\n   - Employ fair modeling approaches that mitigate biases present in training data, potentially using protected demographic attributes as part of the training process.\n\n4. **Sampling Strategies:**\n   - Apply more uniform language sampling rates during pre-training to improve performance on low-resource languages and reduce skew towards high-resource languages.\n\nThese steps collectively contribute to building NLP models that better represent and respect cultural diversity, thereby improving fairness and generalizability across different cultural contexts."}
{"question_id": "e7e5b24e35bd512176a8587170677228842e2e24", "paper_id": "egu/esd/14-185-2023", "generation": "No, coastal seaweeds, which have a very low surface-to-volume ratio, are unlikely to be competitive in iron uptake against the mostly small and specialized open ocean phytoplankton that have a high surface-to-volume ratio, especially in iron-limited areas. The macroalgal growth model used does not consider iron limitation, and although iron is utilized during macroalgae growth, iron limitation on macroalgae is not widely discussed. Moreover, iron is a micronutrient needed in low quantities, and the MOS platform could be designed with an iron supply for macroalgae. This implies that natural coastal seaweeds with low surface-to-volume ratios would not be as efficient as small phytoplankton in iron uptake under iron-limited conditions."}
{"question_id": "c322ea8d24002e229a330243b37f745a157a764e", "paper_id": "egu/esd/14-185-2023", "generation": "The reference numbers for the delta to the DIC pool, as mentioned in Table 1, can be verified in Weaver et al. (2001, Sect. 3e), which describes the inorganic ocean carbon cycle and the air\u2013sea gas exchange process in the UVic Earth System Climate Model (UVic ESCM)."}
{"question_id": "c5b7931f3e58dd10d67e388fcd5680c37e267022", "paper_id": "nlpeer/ARR-22/0f8bc751a0472564732a976cd152643d92d5393c2e77c8234f386a801883d09ff9540378b3dab5834544a246c22f292d5b1c4d370219943c9a19a1626a8f8781", "generation": "The threshold values are selected to ensure that the distribution of class labels approximates a ratio of about 1:10, with the distituent class being the majority. This choice is motivated by the linguistic certainty that distituents must necessarily outnumber constituents, as well as by the need to account for the skewness of random spans, which are more likely to represent distituents. The thresholds, specifically \u03c4_min = 0.0005 and \u03c4_max = 0.995, are set to treat outside strings satisfying these bounds as gold-standard examples of constituents and distituents, respectively."}
{"question_id": "31314c6ad7630579c350af928493caac9c563dbb", "paper_id": "nlpeer/ARR-22/0f8bc751a0472564732a976cd152643d92d5393c2e77c8234f386a801883d09ff9540378b3dab5834544a246c22f292d5b1c4d370219943c9a19a1626a8f8781", "generation": "The minimum and maximum threshold values are \u03c4_min = 0.0005 and \u03c4_max = 0.995."}
{"question_id": "c83f53bbc1390bf3f6a15aa58e1c559cf391a507", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The task described in Section 4.1 of the paper is sound separation on the MUSIC dataset. This involves evaluating models that separate audio sources given queries in the form of text or images, with the goal of isolating target sounds from mixtures. The performance is measured using the signal-to-distortion ratio (SDR) metric, comparing various models including text-queried, image-queried, and nonqueried approaches."}
{"question_id": "44279226e8c3ea5497ac4c43754e3e890e0183fb", "paper_id": "openreview/ICLR-2023-conf/XrMWUuEevr", "generation": "The best way to compare the performance of different models or architectures is to evaluate them on the same benchmark tasks using consistent performance metrics, such as the area under the precision-recall curve (AUC-PR). This evaluation should include multiple sources of variation: variation across different tasks, variation across different support sets during test time, and variation across different training reruns. Performance metrics should be averaged across these variations, and error bars (e.g., standard errors or standard deviations) should be reported to reflect variability.\n\nStatistical significance tests, such as paired Wilcoxon tests or binomial tests, should be applied to determine whether observed differences in performance are significant. Task-wise model comparisons can be visualized using scatterplots where each point represents a task, and color coding indicates which model performs better on that task.\n\nAdditionally, multiple training reruns with different random seeds and multiple draws of support sets should be performed to ensure robustness and reliability of the results. This approach allows for a comprehensive and statistically sound comparison of models."}
{"question_id": "b9d3ed7981d9f1e47fea48aaf2b4037fe7b25658", "paper_id": "openreview/ICLR-2023-conf/_01dDd3f78", "generation": "The purpose of finetuning the same network for the concept model g is to ensure that g is similar to the target model f in terms of both architecture and weights, which leads to better alignment in the utilization of input representation. This similarity improves the accuracy of concept gradient attribution because the propagation of gradients through the shared input representation becomes more aligned between f and g. Finetuning g from the pretrained weights of f allows the concept model to converge to a solution closer to f, thereby enhancing the reliability of concept importance attribution via gradients. Additionally, finetuning can be controlled by freezing certain layers to maintain similarity while adapting g to predict concepts effectively."}
{"question_id": "9ee8e92c0faa3a3d17cff68d75d28be3dca8e8c4", "paper_id": "openreview/ICLR-2022-conf/hcQHRHKfN_", "generation": "The provided chunks do not contain any information regarding the impact of adjusting the parameter \\(\\delta\\) on the results of Table 1. There is no explicit discussion or analysis of \\(\\delta\\) in relation to Table 1 or its outcomes. Therefore, no conclusion can be drawn about the effect of changing \\(\\delta\\) on the results presented in Table 1 from the given information."}
{"question_id": "9824d5fa73a188c99c7d977a3dda3d2b24856f9d", "paper_id": "openreview/ICLR-2023-conf/_01dDd3f78", "generation": "The results of the experiments on the myocardial infarction complications dataset regarding mortality risk attribution have been compared with existing medical literature describing how each complication affects the risk of death. This comparison shows that the severity of complications as described in the medical literature is largely aligned with the Concept Gradient (CG) scores obtained from the model interpretations. However, there is no explicit mention that these results have been reviewed or validated by a medical expert beyond this literature comparison."}
{"question_id": "a81ef48de406906c5a847928da2bc47079136f55", "paper_id": "nlpeer/COLING2020/1681", "generation": "The exact number of distinct phrases used for phrase addition in the adversarial attack generation is not explicitly stated. The text mentions that a range of simple phrases were used for appending and prepending, such as \u201c\u2026 but he wasn\u2019t sure\u201d and \u201cit is true: ...\u201d, and that all phrases tried provided lower scores. However, only the phrase \u201cit is true\u201d is specifically presented in the results, with other phrases left out for simplicity and detailed results shown in the Appendix. Therefore, the number of distinct phrases used is unspecified but includes multiple simple phrases."}
{"question_id": "1aa55ba3f2f47947ad4df928f35cfc4c7d7035ec", "paper_id": "nlpeer/ARR-22/21dd1d5e232c7abf421302a95ef2c9ef44c7bad069da7ca4129a8cf1bfbd6f660afee123144ebcbd8da698aed212e2ca1151a13bca7aaef50ce59d27d23aa83a", "generation": "The focus of this paper is on intent classification and entity/slot recognition within the context of task-oriented cross-lingual natural language understanding (XNLU)."}
{"question_id": "0c64726cf3b593196fd8f350d0f2c2d4aba98d1e", "paper_id": "nlpeer/ARR-22/7e701552b7cfaad6239d3d5dfcd9da1852a03a66907c789fe65d368f1c2845e723adcecd7f7008849eef13c7ca467be73de53747c3376a6bb46d1911ed1b6409", "generation": "ELECTRA's binary classification head (RTE) uses a softmax layer to tag each token in the input sequence as either a \"good\" gap or not. In the training data, all tokens corresponding to gaps are replaced with the first intended target answer and labeled as positive (\"good\" gap), while all remaining tokens are labeled as negative (not a gap)."}
{"question_id": "5678b6bf40f5958402473fd66a08dc836eaa98a7", "paper_id": "nlpeer/COLING2020/1681", "generation": "The coreference resolution pipeline depicted in Table 1 is an idealized and hypothetical model rather than a universally accepted standard in the field. It sequentially involves markable detection, coreference resolution in the source language, and pronoun translation into the target language, resembling the rule-based approach implemented in Stanford CoreNLP\u2019s Coref-Annotator. However, neural machine translation (NMT) models currently do not decouple these individual steps, and the pipeline serves as a conceptual framework to isolate and evaluate specific steps through targeted examples. Thus, it is a proposed analytical framework rather than a universally adopted pipeline in coreference resolution research."}
{"question_id": "0258b0f39ec3f7316f9d299a25a7cd36274e9631", "paper_id": "openreview/ICLR-2023-conf/_01dDd3f78", "generation": "No, the concepts were not ranked according to the TCAV_Q score. Instead, the ranking shown is based on the Concept Gradients (CG) score, which is the recommended method in the paper for assessing the relative importance of multiple concepts. The CG method consistently outperforms TCAV in concept importance attribution, both locally and globally, as demonstrated in the quantitative analysis and experimental results."}
{"question_id": "ba25580bbc4ec4f20348cefaf968e1cdea408642", "paper_id": "openreview/ICLR-2023-conf/XrMWUuEevr", "generation": "In the domain shift experiment in Section 5.3, the support set for each task was constructed by randomly selecting 8 active (positive) and 8 inactive (negative) molecules from the training set. The difference between these examples lies in their activity status with respect to the specific toxic effect being studied: the 8 positive examples are molecules labeled as active (exhibiting the toxic effect), while the 8 negative examples are molecules labeled as inactive (not exhibiting the toxic effect)."}
{"question_id": "bf41e9f2b170cb8e1801812167b945e8f56aa8cb", "paper_id": "openreview/ICLR-2022-conf/hcQHRHKfN_", "generation": "The significance of the results in Table 1 lies in demonstrating that the Reward-Switching Policy Optimization (RSPO) algorithm successfully discovers a visually diverse collection of high-quality winning strategies on the hard map 2c_vs_64zg in the StarCraft Multi-Agent Challenge (SMAC). These strategies represent diverse local optima exhibiting intelligent emergent behaviors, which had not been previously reported in the literature. Although population diversity scores for RSPO and baseline methods all reach the maximum value of 1.000, these scores do not necessarily reflect true strategic diversity due to the complex dynamics of SMAC. Visual inspection remains the most reliable method to distinguish different learned strategies. Thus, Table 1 highlights RSPO's ability to continuously discover novel and diverse winning policies in a complex multi-agent environment, advancing the state-of-the-art in multi-agent reinforcement learning."}
{"question_id": "1a42a5af41f66bb6428c643d96fd05eba81ce635", "paper_id": "nlpeer/PeerRead-CONLL2016/166", "generation": "Yes, the proposed features and experiments account for named entities not explicitly listed on Wikipedia through the use of gazetteer features that generalize to unseen entities. Specifically, the model employs gazetteers gathered from Wikipedia titles for each language, grouped by topic, and uses partial matches with these gazetteers as features. The method iteratively extends a window over the input phrase and adds features based on matches found in the gazetteers. This approach allows the model to generalize to unseen entities by learning patterns from known entities. For example, the phrase structure \"B-PersonName and B-PersonName B-PersonName Foundation\" is learned as a strong signal for an organization, enabling the model to recognize a fictional organization such as \"Dave and Sue Harris Foundation\" even if it is not present in the gazetteers."}
{"question_id": "8fc13b01107e614b030a2c7dbc65aa19d0363778", "paper_id": "nlpeer/ARR-22/21dd1d5e232c7abf421302a95ef2c9ef44c7bad069da7ca4129a8cf1bfbd6f660afee123144ebcbd8da698aed212e2ca1151a13bca7aaef50ce59d27d23aa83a", "generation": "The authors did not explicitly state that they ran their experiments multiple times with different random initializations to confirm the results. The experimental setup mentions using fixed hyperparameters between experiments but does not indicate multiple runs or random seed variations. Statistical significance testing was conducted using a two-tailed z-test for the difference of proportions to robustly compare their method with previous state-of-the-art results, but no mention is made of repeated runs with different initializations."}
{"question_id": "10dbff5874380289cdab832a0eecab1cc3c34117", "paper_id": "openreview/ICLR-2023-conf/VA1YpcNr7ul", "generation": "The proposed algorithm DASHA is related to variance reduction methods but is not explicitly described as a variant of Stochastic Variance Reduced Gradient (SVRG). Instead, DASHA is inspired by MARINA and momentum variance reduction methods (MVR). It incorporates a variance reduction strategy borrowed from MVR rather than SVRG. Specifically, in the stochastic setting, DASHA uses an MVR-like strategy for gradient estimation. The algorithm reduces variance from the oracle and the compressor separately, which improves theoretical convergence rates.\n\nThus, DASHA belongs to the family of variance reduction methods, particularly those employing momentum variance reduction techniques, but it is distinct from SVRG and instead builds upon MVR and MARINA frameworks."}
{"question_id": "b879c4d1344942fd8fab9fbe8fc495f4ae67c0b0", "paper_id": "openreview/ICLR-2023-conf/VA1YpcNr7ul", "generation": "The provided chunks do not contain any explicit definition or statement regarding the nature or domain of the parameter \\(\\omega\\) in Definition 1.1. There is no information specifying whether \\(\\omega\\) is an element of \\(\\mathbb{R}\\) or any other set. Therefore, it cannot be confirmed from the given text that \\(\\omega \\in \\mathbb{R}\\) in Definition 1.1."}
{"question_id": "70418ac3cb9f40b039a74031b89324e2b891ccf5", "paper_id": "nlpeer/PeerRead-ACL2017/699", "generation": "The word embeddings are randomly initialized with a uniform distribution in the range [-0.1, 0.1]."}
{"question_id": "b97a690598219404cca037101ff05ec348b5e525", "paper_id": "openreview/NeurIPS-2022-dabt/heBKnuV42O", "generation": "There is no indication of a plan to open-source the proprietary medical knowledge base or the telemedicine software. The dataset released under a CC-BY license includes synthesized patient data generated using the proprietary medical knowledge base and the commercial rule-based AD system, but the knowledge base and the rule-based AD system themselves remain proprietary and are provided by Dialogue Health Technologies Inc."}
{"question_id": "89fb9729921ad950b90987550b32f9ede60c8a8c", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "Yes, the Deep Gaussian Process (DGP) with RBF kernels failed to capture the data trend during the interpolation experiment on the Mauna Loa CO2 dataset. This failure led to mean reversion and over-estimation of the prediction uncertainty. In contrast, the Deep Variational Implicit Process (DVIP) was able to capture the data trend effectively in the missing gaps, producing very smooth prior samples."}
{"question_id": "96525f380b4694ec387b707fa87e78c972a12b4c", "paper_id": "openreview/NeurIPS-2022-dabt/FPgCB_Z_0O", "generation": "Training on the DARTset dataset demonstrates significant effectiveness in improving 3D hand pose and mesh reconstruction performance, particularly when used in combination with other datasets. Cross-dataset evaluations using two mesh reconstruction methods, CMR and METRO, show that mixed training on both DARTset and FreiHAND datasets leads to improved accuracy. Specifically, for the CMR model, mixed training improves the PA-MPVPE on the FreiHAND testing set by 8.9%, indicating that DARTset complements challenging real-world datasets by providing diverse hand poses and rich textures. However, a domain gap exists between DARTset and FreiHAND due to differences in textures, accessories, and hand pose distributions. Despite this, mixed data training substantially boosts performance on FreiHAND, although the improvement is less pronounced on DARTset itself due to its wider pose distribution. Overall, DARTset enhances the generalizability and compatibility of hand reconstruction models and serves as a valuable complement to existing datasets."}
{"question_id": "d49df57b22ec381fed263033d6a02678f16a18c1", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "The width of the model does not affect its performance when the prior functions are the same for all units. This is because the prior functions are shared across units, so increasing the number of units (width) does not introduce additional variability or complexity in the prior. Consequently, the model's performance remains unchanged with respect to width under these conditions."}
{"question_id": "587b8f363bb9be4e82b38b70f74608f844559b6f", "paper_id": "nlpeer/COLING2020/1681", "generation": "The data augmentation strategy, termed Antecedent-free augmentation (afa), involves identifying sentences where a coreferential pronoun \"it\" refers to an antecedent not present in the current or previous sentence. For these sentences, two new training examples are created by modifying the gender of the German translation of \"it\" to masculine (\"er\") and feminine (\"sie\"), while keeping the source sentence unchanged. This augmentation is applied to fine-tune an already trained concatenation transformer model on a dataset consisting of both the original candidate sentences and the augmented samples. The approach uses a coreference resolution tool to identify antecedents and coreferential pronouns and employs Spacy\u2019s dependency parser to determine the case of the pronoun, which is necessary due to gender and case distinctions in German pronouns. The augmentation aims to break the model's strong prior bias towards the neuter pronoun (\"es\") and improve coreference resolution capabilities in translation."}
{"question_id": "65df6e41f1c8c77eec8b264ef0a3dcd104abb9dc", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The higher average out-degree of normal users compared to fraudsters is because fraudsters tend to fill fewer emergency contacts in general. This behavior aligns with their primary purpose of defrauding the platform, as providing more emergency contacts is not beneficial for committing fraud. Normal users, on the other hand, are more likely to fill out a greater number of emergency contacts, resulting in a higher average out-degree."}
{"question_id": "428b48f2d5cfea8890c3fe80599575b25565a976", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "Using only one mixture component (R = 1) during training simplifies the computational process, while using 100 mixture components (R = 100) during testing allows for a more flexible and accurate approximation of the predictive distribution. Specifically, the predictive distribution at the last layer is approximated by a Gaussian mixture model with R components. Setting R to 100 during testing results in a Gaussian mixture that is more flexible than the Gaussian predictive distribution used in training with a single component. This approach enables better modeling of uncertainty and improved predictive performance at test time, while maintaining computational efficiency during training."}
{"question_id": "5227809e5dbd6a7ef588b7a84fc243e6cd0eed8d", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "The provided evidence chunks do not contain any information regarding the compensation rate for the annotators hired for the Mechanical Turk crowdsourcing work."}
{"question_id": "e0385760ba4f37eeba3376cf4dd4ed0727cedb81", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "The rationale for choosing the prior for the model is to use an implicit process (IP) as a flexible and expressive prior over functions, which can be adapted to the observed data. An IP is defined by a generative process involving a noise source and a function parameterized by \u03b8, allowing samples of the function to be generated but generally making the prior and posterior intractable except in special cases like Gaussian processes (GPs). To address this, the IP prior is approximated by a GP, which enables tractable inference and adaptation of the prior parameters to the data. This approach, used in the Variational Implicit Process (VIP) framework, allows the model to leverage the flexibility of IPs while maintaining computational scalability through a linear approximation of the GP prior. Furthermore, in the Deep Variational Implicit Process (DVIP) model, stacking IPs as layers generalizes deep GPs and enhances flexibility and predictive performance. The ability to adapt the prior IPs to the observed data is critical for accurate predictions and improved performance, as fixed priors without adaptation (e.g., in fBNN or FVI) are less effective. Thus, the prior is chosen to balance expressiveness, adaptability, and computational tractability, enabling scalable variational inference and improved predictive distributions."}
{"question_id": "c3b651600b60b22f2a4c805aeb87745aff3c0c84", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The full name of the proposed method \"REDQ\" is Randomized Ensembled Double Q-learning."}
{"question_id": "4bb993f44c76628b67f41da43c78aa82b50cbc19", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The full name of the algorithm DUVN is not explicitly provided in the given chunks."}
{"question_id": "81b292ac9640d75024251269649ea71272b1710c", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "When both Deep Gaussian Processes (DGP) and Deep Variational Implicit Processes (DVIP) use the same Gaussian Process (GP) priors, their predictive performance on datasets such as Year is similar. However, the tradeoff lies in the posterior approximation and computational cost: DGP employs inducing points for scalability, while DVIP uses a linear model for posterior approximation. Sampling approximately from the GP prior in VIP (and by extension in DVIP with GP priors) is computationally expensive, making it impractical to add extra layers in DVIP. Consequently, although DVIP generalizes DGP and can achieve comparable results with GP priors, DVIP is faster than DGP when using its default Bayesian Neural Network (BNN) prior, but incurs higher computational cost when using GP priors due to the expensive sampling. Thus, the tradeoff is that DGP with inducing points offers scalable inference with good performance at a certain computational cost, whereas DVIP with GP priors can match performance but at a higher computational cost that limits model depth and runtime efficiency."}
{"question_id": "9073f9407118eaf0b34170000a57846d672b4a5d", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "The four conversational question answering (CQA) models were selected based on their differing model architectures and training strategies. Specifically, the chosen models include: (1) BERT, a simple baseline that concatenates previous question-answer pairs, the current question, and the passage to predict answer spans; (2) GraphFlow, which employs a recurrent graph neural network on top of BERT embeddings to model dependencies between the question, conversation history, and passage; (3) HAM, which uses a history attention mechanism to softly select the most relevant previous turns; and (4) ExCorD, which incorporates a question rewriting model trained to generate context-independent questions and uses both original and rewritten questions for training, achieving state-of-the-art performance on QuAC. These models were selected to represent a range of architectures and training approaches for human evaluation and analysis."}
{"question_id": "60c2bf23190bf1120b8652501ff951bae6f3e046", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The rationale for comparing 2-hop homophily (\u21e5B\u21e5 and \u21e5T\u21e5) with 1-hop homophily (\u21e5\u21e5) in Figure 3(d) is to investigate the role of background nodes as intermediate nodes in the graph connectivity and their impact on node similarity. The comparison reveals that 2-hop connections involving a background node as an intermediate node have a higher homophily ratio than direct 1-hop connections between nodes. This indicates that background nodes contribute to stronger class similarity in indirect relationships, which is significant because background nodes are difficult to separate based on node features but play a crucial role in maintaining the graph\u2019s connectivity and contain abundant semantic information. Therefore, analyzing 2-hop homophily helps to understand how background nodes enhance the structural and semantic coherence of the graph beyond direct connections."}
{"question_id": "abf4bcae7809ff5b01e8cf7fdb201caa7b8421ac", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The computation time referred to is the process time per Q-functions update (e.g., lines 4\u20139 in Algorithm 2). Specifically, REDQ requires process times in the range of approximately 2200\u20132300 milliseconds per update, whereas SAC requires process times in the 800\u2013900 milliseconds range. This indicates that REDQ runs more than two times slower than SAC in terms of the time needed to update Q-functions during training."}
{"question_id": "1cd2ab406e0d21ae6597092cde6d7a2fca652f82", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The provided information does not explicitly address whether dynamic nodes need to be included in the data for the dynamic graph approach to be effective. However, the dataset DGraph is described as a real-world dynamic graph in the finance domain, containing about 3 million nodes, 4 million dynamic edges, and 1 million ground-truth nodes. The dataset includes background nodes, which are nodes representing users who have not borrowed money yet and do not exhibit borrowing behaviors. These background nodes are essential for maintaining graph connectivity and contain abundant semantic information that aids in detecting fraudsters.\n\nExperiments show that removing background nodes decreases the performance of graph convolutional networks (GCNs) in anomaly detection, indicating the importance of including such nodes for effective graph learning. Moreover, background nodes are difficult to differentiate based on node features alone but play a crucial role in the graph's structure and semantic richness.\n\nWhile the text emphasizes the importance of background nodes and dynamic edges, it does not explicitly state that dynamic nodes themselves must be included for the dynamic graph approach to be effective. Instead, it highlights the significance of preserving nodes that maintain connectivity and semantic information, including background nodes, to enhance model performance.\n\nIn summary, the effectiveness of the dynamic graph approach relies on including nodes that maintain connectivity and provide semantic information, such as background nodes, rather than explicitly requiring dynamic nodes per se."}
{"question_id": "c3790b0e0925f080c8d91160ae348f35ac367993", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "Word overlap was used instead of an exact match when comparing F1(s\u2217_{j,1}, s_{j,1}) because the predicted entity mention may refer to the same entity as the gold answer but with different names. Using word-level F1 allows detection of the same entity even if the names differ slightly, thereby avoiding false positives in detecting invalid questions. This approach accounts for variations in entity naming that exact match would fail to capture."}
{"question_id": "92c772c75354552e709f16f3e3b15a31e395f1cf", "paper_id": "openreview/NeurIPS-2022-dabt/heBKnuV42O", "generation": "The rationale for altering the original disease incidence and prevalence rates from the knowledge base, despite it being compiled from medical papers, is to address limitations in the data generation process and to create a more balanced dataset. Specifically, some diseases have incidence rates exceeding 100%, which would cause the dataset to be dominated by a few pathologies, leading to a highly imbalanced dataset. To prevent this, incidence rates greater than 100% are capped at 100%. Conversely, some diseases have extremely low incidence rates, resulting in very few synthesized patients with those pathologies. To ensure adequate representation of these rare diseases, a minimum incidence rate of 10% is imposed. These adjustments help to avoid dataset imbalance and improve the diversity of pathologies represented in the synthetic patient data."}
{"question_id": "721388955b97db097e963c4b221fe9af9e2da4ae", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "When the absolute accuracy numbers are higher than the F1 scores, it indicates that human evaluation reports higher model performance compared to automatic evaluation. This discrepancy arises because automatic evaluations rely on gold answers that cannot capture all possible correct answers to open-ended or multi-answer questions, leading to lower F1 scores. In contrast, human evaluators can judge the correctness of a wider range of valid answers, resulting in higher accuracy measurements. Thus, the higher absolute accuracy reflects the broader and more flexible assessment capability of human evaluation compared to the more restrictive automatic evaluation metrics."}
{"question_id": "9dfb86a40b292918a304254d03b72b6fee37e740", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The target knowledge base (KB) size used for MedMentions in the experiments is 2.3 million entities."}
{"question_id": "672a51e77d6a34b48ad5a93aa1a37139d71e3c72", "paper_id": "nlpeer/ARR-22/1370310ca3e0e0d092a2e8b58f90b0d692331caa6e3ce5ca6dacbef926f2b01ff49c81282f32c554fa66c04979a125b7548c9692c2091bb2673642102d85412d", "generation": "The rationale for analyzing the self-attention functions in the last layer of the BERT model is that fine-tuning BERT does not significantly change the correlations of attention patterns to human attention, and attention functions in earlier layers remain largely stable during fine-tuning. Additionally, attention flow propagated from early, medium, and final layers does not considerably change correlations to human attention, indicating that the last layer's attention captures relevant information effectively. Moreover, selecting the highest correlating head in the last attention layer generally produces weaker correlations than using attention flow, emphasizing the importance of capturing the layered propagation structure rather than isolated raw attention weights. Thus, focusing on the last layer's self-attention functions provides a practical and meaningful representation of the model's attention relevant to human task-specific reading patterns."}
{"question_id": "8a7925cf9978728b68e7bc89204643a94468964a", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The benefit of using layer normalization in combination with dropout is that layer normalization suppresses the learning instability caused by dropout. Dropout alone destabilizes the Q-function learning, leading to significant oscillations in the Q-function loss and the variance of the gradient loss with respect to Q-function parameters. When combined, layer normalization effectively reduces these oscillations and stabilizes the learning process. This stabilization enables better Q-value propagation, which consequently improves overall performance, especially in complex environments such as Ant and Humanoid. The synergistic effect of using both dropout and layer normalization results in higher sample efficiency and reduced bias compared to using either technique alone or neither."}
{"question_id": "4a12daa058e224f39629de8997d5de7c8b0c2d3c", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "Using the complete graph for training is avoided because it introduces noise and undesirable connections that cross cluster boundaries. Specifically, constructing the graph by connecting all pairs of mentions (e.g., via a minimum spanning tree) ignores the directionality of nearest neighbor relationships, leading to noisy edges. This results in edges that connect mentions across different clusters, which violates the constraint that each cluster should contain at most one entity. The pruning procedure removes edges with weights above a threshold and those that violate cluster constraints, thereby preserving cluster connectivity while ensuring that each cluster contains exactly one entity. This selective pruning maintains the validity of the dissimilarity function and improves the quality of positive training examples by focusing on meaningful mention-mention and mention-entity relationships."}
{"question_id": "b509625d37c9da4c7585e00370dc166be8e9be88", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "No, the model does not need to compute \\(\\psi(m_i, e)\\) for all entities in the target knowledge base during inference. Instead, for each mention \\(m\\), the graph \\(G\\) is constructed by adding edges from the mention's \\(k\\)-nearest neighbor mentions and from the mention's nearest entity to \\(m\\), where the nearest entity is determined by minimizing the edge weight \\(w_{e,m} = -\\psi(e,m)\\). This implies that only the nearest entity (or a limited subset) is considered for each mention rather than computing \\(\\psi(m_i, e)\\) for all entities in the knowledge base."}
{"question_id": "720c06898bbd974bce657e8eefac71ea8641b762", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "Position embeddings are generated by table lookup from a matrix of trainable parameters, similar to word embeddings. Specifically, for relative positions of words with respect to the event mention, embeddings \\( v_i^p \\) of size \\( d_p \\) are obtained by looking up a matrix \\( W_p \\in \\mathbb{R}^{d_p \\times (s_{\\max} \\times 2 - 1)} \\), where \\( s_{\\max} \\) is the maximum sentence length in the dataset and \\( i \\) denotes the word position in the sentence. This process parallels the generation of word embeddings, which are also obtained by lookup from a trainable matrix \\( W_w \\in \\mathbb{R}^{d_w \\times |V|} \\), where \\( |V| \\) is the vocabulary size."}
{"question_id": "f567015ed8777554298ac8d5b511b255c317d3da", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "Dropout on its own does not work effectively for the proposed method because it destabilizes the learning of the Q-functions. Specifically, using dropout alone causes significant oscillations in the Q-function loss and increases the variance of the gradient of the loss with respect to the Q-function parameters. This instability in learning impairs the propagation of Q-values and degrades overall performance. The combination of dropout with layer normalization is important because layer normalization suppresses these learning instabilities caused by dropout, stabilizing the gradient oscillations and enabling better Q-value propagation, which consequently improves the overall performance of the method."}
{"question_id": "8b0ad0aeb866b9064a6bd71b1559fe85d81a612b", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The event-mention representation \\( v_e \\) serves as a fixed-size vector encoding of an individual event mention, capturing both sentential semantics and lexical-level features of the trigger word and its immediate context. It is generated by processing concatenated sentential features and trigger-local word embeddings through a hidden layer. This representation is subsequently used as input for the event-linking decision, enabling the model to assess coreference between event mentions by providing a comprehensive semantic and lexical characterization of each event mention."}
{"question_id": "2074c1cd08c7d4b134ac01c5ee57f13765a4cc47", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "Layer normalization offers distinct advantages over other normalization methods when combined with dropout to improve performance in deep reinforcement learning. Specifically, layer normalization effectively suppresses the learning instability caused by dropout, as evidenced by reduced oscillations in both the Q-function loss and the variance of its gradient during training. This stabilization facilitates better Q-value propagation, leading to improved overall performance, particularly in complex environments such as Ant and Humanoid.\n\nIn contrast, batch normalization does not significantly improve average return or estimation bias and results in very unstable Q-function learning. Group normalization, which shares a similar mechanism with layer normalization, can improve performance when combined with dropout but fails to do so when used alone. Additionally, a variant of layer normalization without variance re-scaling does not perform well and leads to unstable Q-function learning, indicating that variance re-scaling is a critical component of layer normalization's effectiveness.\n\nTherefore, the key advantages of layer normalization over other regularization schemes are its ability to stabilize the learning process through variance re-scaling and its synergistic effect with dropout, which together enhance sample efficiency and reduce bias in Q-function learning."}
{"question_id": "8d69a05246c31778897996bc35b60061f15554f3", "paper_id": "nlpeer/COLING2020/1550", "generation": "The results presented in the paper are primarily task- and dataset-specific. The study focuses on the Fake News Detection (FND) task framed as a multi-class classification problem using the FNC-1 and FNC-1 ARC datasets. The authors explicitly caution against generalizing their findings without further validation, stating that while their findings can serve as starting points for related experiments, they do not claim that these results are directly transferable to other Fake News related datasets employing different tasks or label sets. This specificity is underscored by the observation that model performance, such as that of RoBERTa and XLNet, is influenced by the nature of the task (segment-level rather than token-level) and the characteristics of the datasets (e.g., class distribution and heterogeneity)."}
{"question_id": "7b375e548c69cd6c0b0d75953da0021adb9e2a7e", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The process times per Q-function update for SAC are in the range of approximately 835 to 870 milliseconds across different environments (e.g., 870 msec for Hopper-v2, 835 msec for Walker2d-v2, 848 msec for Ant-v2, and 854 msec for Humanoid-v2). In contrast, REDQ requires significantly more time per update, approximately 2269 to 2340 milliseconds (e.g., 2269 msec for Hopper-v2, 2283 msec for Walker2d-v2, 2277 msec for Ant-v2, and 2340 msec for Humanoid-v2).\n\nThus, REDQ takes roughly 2.5 to 2.7 times longer than SAC per Q-function update."}
{"question_id": "52e2f447dc9bcf3dc208f99a89d4b59da4004923", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The embedding matrix for relative positions (W_p) is randomly initialized."}
{"question_id": "45a145511dd96e98d18e5ac09f454b95ceee5a38", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The left and right neighbors of the trigger word are used by concatenating their word embeddings with the sentential features of the event mention. Specifically, the sentential features vector \\( v_{sent} \\) is concatenated with the word embeddings of the trigger word and its immediate left and right neighbors, resulting in a combined vector \\( v_{sent+lex} \\in \\mathbb{R}^{2 \\cdot d_c + 3 \\cdot d_w} \\). This concatenation encourages the model to incorporate the lexical semantics of the trigger and its surrounding context, which can be strong indicators for coreference. The combined vector \\( v_{sent+lex} \\) is then processed by an additional hidden layer to generate the final event-mention representation \\( v_e \\)."}
{"question_id": "3356f072c76c12c8ebc215b5bd495d5ccbea0126", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The training batches are constructed by sequentially building batches of mentions \\( B \\subset M \\) over the training data, where each mention \\( m_i \\in B \\) has its gold entity \\( e_i^\\star \\) defined. The graph \\( G_B \\) built for each mini-batch includes nodes consisting of (a) each mention \\( m_i \\in B \\), (b) each mention coreferent to \\( m_i \\in B \\), and (c) the set of gold entities for each \\( m_i \\in B \\). This indicates that the batches include not only the mentions in the batch but also their coreferent mentions, which may come from the same or different documents, to explicitly model mention coreference relationships.\n\nThe clustering procedure partitions the graph into clusters such that each cluster contains at most one entity and all mentions coreferent to that entity, ensuring that coreferent mentions are grouped together during training. This approach leverages coreferent mentions by including them in the batch graph for positive sampling and training.\n\nHowever, the text does not explicitly state that the mentions in the training or inference batches are restricted to come from the same document. Instead, the batches are constructed to include mentions and their coreferent mentions, which can be cross-document. This is consistent with the goal of modeling cross-document coreference.\n\nTherefore, the training and inference batches contain mentions that are coreferent, potentially from multiple documents, rather than being limited to mentions from the same document. This design leverages coreferent mentions as much as possible by including them in the batch graph regardless of document boundaries."}
{"question_id": "5af1f02badf7c044e04f7544f4881486216b9f42", "paper_id": "openreview/ICLR-2023-conf/OTbRTIY4YS", "generation": "The new metric, Concept Purity, is introduced because the original metric proposed by Yuan et al. (2020), which relies on graph edit distance to evaluate concept representations, is computationally expensive. To address this, Concept Purity is adapted to exploit the annotation of local explanations, specifically using the typology of the motif represented by each local explanation. This adaptation allows for a more efficient and practical evaluation of how well the embedding clusters local explanations, by measuring the proportion of the most frequent label within each cluster, thereby providing a scalable and interpretable measure of concept quality."}
{"question_id": "5ab449ca87a0bb86111a5e4e594ac959ec7d0e86", "paper_id": "nlpeer/PeerRead-CONLL2016/142", "generation": "Post-editing rates were chosen over prediction HTER for intrinsic uncertainty evaluation because post-editing time is a more complete measure of post-editing effort, capturing not only technical effort like HTER but also cognitive effort. Additionally, post-editing time is more directly applicable in real translation environments, as it relates directly to productivity measures where uncertainty estimates could be useful."}
{"question_id": "dfa7d8d8808a8928555e5e665068db90d3261334", "paper_id": "openreview/ICLR-2022-conf/fQTlgI2qZqE", "generation": "Yes, it is valid to assume that finite evaluations of one arm are sufficient to obtain an accurate reward. This is explicitly stated as Assumption 1 in the proposed interaction detection method, which assumes that a finite number \\( m \\) of evaluations of one arm suffice to achieve an accurate estimate of its reward."}
{"question_id": "2e474adb8f0c5bd3285e43db4bfb774e7cd5b7a5", "paper_id": "nlpeer/ARR-22/78d7b31f3d19cf16bcc71aa109ca56b923d9082a2919fd8008060641daf4d5ae37d70daea5bb36f4aa83c9c49ad8b44e597aa41960348f3a0abe0abb529f9aea", "generation": "The proposed knowledge prompting method requires the knowledge generation model to be relatively large in order to generate useful and reliable knowledge. Smaller knowledge generation models, such as those with 1.3 billion or 0.4 billion parameters, do not provide significant improvement. However, moderately large models like the 6.7 billion parameter GPT-3 knowledge model yield noticeable gains (e.g., 5.0% improvement), though not as large as the 175 billion parameter model (which gives a 10.5% improvement). Therefore, while the method can still be effective with smaller models than the largest GPT-3, it is less effective with small language models as the knowledge source."}
{"question_id": "65ca807b7bfc58200ae0e5c46fcec1e31096cbf5", "paper_id": "openreview/ICLR-2022-conf/fQTlgI2qZqE", "generation": "Yes, the boost in performance of the ParaACE model observed in the selected datasets is expected to generalize to other datasets as well. The results show that ParaACE improves predictive performance and significantly reduces model size across multiple real-world datasets such as Elevators, Parkinsons, Skillcraft, Bike sharing, and Cal housing. Additionally, ParaACE demonstrates sample efficiency by surpassing over-parameterized neural networks trained with substantially more data. This consistent improvement across diverse datasets and tasks, including both regression and classification, indicates that the performance gains induced by interaction detection with ParaACE are likely to be seen in other datasets."}
{"question_id": "fced44b64830046f3b8766306510296277091b7a", "paper_id": "nlpeer/ARR-22/3282bdf968d7f87ae69c4ae7fdd35d4e9a46fa83f56d8bee0296c16dcf23b23cb1e4f20f510c7d01fb7be2309c08dd29cc240b54bdcbc3d66969041b5c84077a", "generation": "The provided evidence specifically illustrates the degree of formality and the use of Persian and Sanskrit lexical forms in Hindi. As sentences become more formal in Hindi, the English loanword \"job\" (\u091c\u0949\u092c) is replaced by Persian-derived (\u0928\u094c\u0915\u0930\u0940) and high Sanskrit-derived (\u01d3\u0928\u092f\u0941\u093f\u00c8\u0924) terms, accompanied by the use of honorifics (e.g., \u0906\u092a\u0915\u0940, \u092c\u0924\u093e\u090f\u0902). However, there is no explicit information or examples regarding the degree of formality or the use of Persian/Sanskrit forms for the other languages studied (such as Bengali, Kannada, Telugu, Swahili, Spanish, or Gujarati) in the provided chunks. The analysis and examples of lexical formality shifts are limited to Hindi only."}
{"question_id": "986801b0e009ad637f5bc4d62c8af27a2580f7b9", "paper_id": "nlpeer/ARR-22/3282bdf968d7f87ae69c4ae7fdd35d4e9a46fa83f56d8bee0296c16dcf23b23cb1e4f20f510c7d01fb7be2309c08dd29cc240b54bdcbc3d66969041b5c84077a", "generation": "The degree of formality in Hindi formality transfer is controlled by the parameter \u03bb, with higher values of \u03bb corresponding to more formal language. As formality increases, the English loanword \"job\" (\u091c\u0949\u092c) is replaced by more formal equivalents: first by the Persian-derived term \"\u0928\u094c\u0915\u0930\u0940\" and then by a high Sanskrit-derived term \"\u01d3\u0928\u092f\u0941\u093f\u00c8\u0924.\" Additionally, honorific forms such as \"\u0906\u092a\u0915\u0940\" and verb forms like \"\u092c\u0924\u093e\u090f\u0902\" are used to convey higher formality. This lexical and morphological shift demonstrates that the transfer process effectively maintains and modulates the use of Persian and Sanskrit forms in Hindi according to the desired formality level. The DIFFUR models, particularly DIFFUR-INDIC and DIFFUR-MLT, show strong calibration of style change with \u03bb, achieving higher style score increases and better control over formality transfer without loss of semantic content."}
{"question_id": "b248a530072224a71459f4ce7aa708f9990067c2", "paper_id": "openreview/ICLR-2023-conf/KDhFkA6MQsW", "generation": "The parameter \\(\\mu\\) is set to \\(0.001\\) in the experiments for the ZO-Perturbed-AGD algorithm and to either \\(0.001\\) or \\(0.01\\) for the ZO-Perturbed-AGD-ANCF algorithm, depending on the dimension \\(d\\). Specifically, for dimensions \\(d = 20, 100, 200\\), \\(\\mu = 0.001\\) or \\(\\mu = 0.01\\) is used as follows:\n\n- For ZO-Perturbed-AGD: \\(\\mu = 0.001\\)\n- For ZO-Perturbed-AGD-ANCF: \\(\\mu = 0.001\\) or \\(\\mu = 0.01\\) (e.g., \\(\\mu = 0.01\\) for \\(d=20\\) and \\(d=100\\), \\(\\mu = 0.01\\) for \\(d=200\\))\n\nIn the cubic regularization problem experiment, \\(\\mu\\) is consistently set to \\(0.001\\) for ZO-Perturbed-AGD and either \\(0.001\\) or \\(0.01\\) for ZO-Perturbed-AGD-ANCF."}
{"question_id": "a74c71ff53a5ff84cacb938350996a66ceb0ae12", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "In Phase-I training, the proposal network is trained on geometric cues such as depth or normal inputs using the base class bounding box annotations. The training employs the loss function defined in Equation (2) of the paper. The network is optimized using stochastic gradient descent (SGD) with an initial learning rate of 0.01 and a batch size of 16 for 8 epochs. The architecture used for the proposal network is OLN, which is built on top of Faster RCNN, with classification heads replaced by objectness score prediction heads. The objectness score is computed as the square root of the product of centerness and IoU for each bounding box proposal."}
{"question_id": "3f32444ad6371e1401f9047615faeed1a6572e73", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The difference between the results in Table 1 and Table 2(a) lies in the models and experimental settings reported:\n\n- Table 1 is not explicitly provided in the extracted chunks, but from the context, it can be inferred that Table 1 likely presents baseline or initial results of EViT on DeiT-S without detailed variations in image size or keep rate.\n\n- Table 2(a) specifically reports results of EViT on DeiT-S with varying keep rates and image sizes, showing the impact of token reorganization on Top-1 and Top-5 accuracy, throughput (images/s), and MACs (G). It includes detailed comparisons at different keep rates (e.g., 0.5, 0.6, 0.7) and image resolutions (e.g., 256, 288, 304), demonstrating how EViT achieves speedup and maintains or improves accuracy relative to the baseline DeiT-S.\n\nIn summary, Table 2(a) provides a more detailed and comprehensive evaluation of EViT on DeiT-S across multiple keep rates and image sizes, whereas Table 1 likely presents more general or baseline results without these detailed variations."}
{"question_id": "dc3fd256c5702edb18e7a21a01836945f7bc0b17", "paper_id": "openreview/ICLR-2023-conf/WlbG820mRH-", "generation": "The paper restricts its analysis to Message Passing Neural Networks (MPNNs) where the aggregation function computes a simple sum of its inputs, and the combination and readout functions are represented by neural networks with ReLU activation only. These common choices define the expressiveness level of the considered MPNN models.\n\nThe applicability of the results depends critically on these limitations:\n\n1. **Extension to More Expressive Models:**  \n   The negative results on the impossibility of formal verification of output reachability and adversarial robustness for graph-classifier MPNNs extend only to GNN models that are at least as expressive as the considered MPNNs. For models that are more expressive, such as DropGNN, which surpass MPNNs in expressiveness, the results do not directly apply and require separate investigation.\n\n2. **Extension to Less Expressive Models:**  \n   The positive results on the formal verifiability of node-classifier MPNNs with bounded degree input graphs extend only to GNN models that are at most as expressive as the considered MPNNs. Models less expressive than the considered MPNNs may also satisfy these verifiability results.\n\n3. **Activation Functions and Model Variants:**  \n   Minor changes, such as using other piecewise-linear activation functions instead of ReLU, do not affect the validity of the results. However, leaving the MPNN or spatial-based model framework opens the question of formal verifiability anew, as the results rely on the specific structural and functional assumptions of MPNNs.\n\nIn summary, the limitations on aggregation (simple summation), combination, and readout (NNs with ReLU) components confine the applicability of the results to GNN models with similar or comparable expressiveness and architectural characteristics. Models that deviate significantly in these aspects require separate analysis to determine the applicability of the formal verification results."}
{"question_id": "a4439f559ec40c32bb7edf1ee7fa3a854ed2b883", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Combining RGB, depth, and normals (referred to as GOOD-All) does not improve the performance of the model for smaller classes. While RGB-based object proposal networks tend to favor smaller detection boxes, which can correspond to small objects or parts of larger objects, adding RGB pseudo boxes to those from geometric cues (depth and normals) leads to either no performance gains or even worsened overall performance. Specifically, the gains in detecting small objects (AR_s) from RGB are too small to compensate for the losses in detecting medium and large objects (AR_m and AR_l), resulting in inferior overall performance compared to combining only geometric cues (GOOD-Both)."}
{"question_id": "1b3c40fd196db55e9ffea18c2b7d9ffe988c5ad2", "paper_id": "openreview/ICLR-2022-conf/IfNu7Dr-3fQ", "generation": "The kernel \\( k_{\\text{split}} \\) serves as an auxiliary kernel in the generalized kernel thinning algorithm. Its primary role is in the KT-SPLIT step, where it is used to divide the input point sequence into \\( 2^m \\) candidate coresets of size approximately \\( \\frac{n}{2^m} \\) using non-uniform randomness. The kernel \\( k_{\\text{split}} \\) is employed to compute swapping thresholds and parameters that guide the probabilistic swapping of points between candidate coresets, thereby controlling the selection and refinement of points in the thinning process.\n\nFurthermore, \\( k_{\\text{split}} \\) generates a reproducing kernel Hilbert space (RKHS) \\( \\mathcal{H}_{\\text{split}} \\), and the integration error bounds for the KT-SPLIT coresets are expressed in terms of the norm in this RKHS. The choice of \\( k_{\\text{split}} \\) affects the theoretical guarantees on the integration error of the coresets for functions in \\( \\mathcal{H}_{\\text{split}} \\). When \\( k_{\\text{split}} \\) is chosen as the square-root kernel \\( k_{\\text{rt}} \\) of the target kernel \\( k \\), the generalized kernel thinning recovers the original ROOT KT algorithm. Alternatively, choosing \\( k_{\\text{split}} = k \\) is a practical option that yields dimension-free error bounds and better theoretical guarantees compared to the square-root kernel choice.\n\nIn summary, \\( k_{\\text{split}} \\) is crucial for the initial partitioning and probabilistic swapping in KT-SPLIT, influences the integration error bounds of the resulting coresets, and its choice determines the relationship between generalized kernel thinning and prior algorithms."}
{"question_id": "c8e25c77b2ec42c4f94bc044959aa372dd3f9638", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The design of the proposed method is not arbitrary for all layers of a given ViT model; certain layers are fixed for token reorganization. Specifically, the token reorganization locations are chosen to evenly cut the ViT into blocks with the same number of layers. For a ViT with L layers and t token reorganization layers, the reorganization layers are placed at layer indices calculated as [s + 1, 2s + 1, ..., ts + 1], where s = L / (t + 1). In the experiments, the token identification modules are incorporated into fixed layers such as the 4th, 7th, and 10th layers for DeiT-S and DeiT-B (both with 12 layers), and the 5th, 9th, and 13th layers for LV-ViT-S (with 16 layers). Furthermore, placing token reorganization modules in shallow layers (e.g., before the third layer) significantly deteriorates accuracy, while placing them in deeper layers has only marginal influence on accuracy. Therefore, the method fixes the reorganization layers in deeper parts of the network rather than arbitrarily applying it to all layers."}
{"question_id": "888ba5daeae0d5b3d5120c824c8f61abd5b77ee3", "paper_id": "openreview/ICLR-2022-conf/IfNu7Dr-3fQ", "generation": "The difference in error between the KT+ and standard thinning (ST) methods for the Hinch posterior using the IMQ kernel (\u03bd = 0.5) across varying coreset sizes is explained by the improved MMD and integration error decay rates achieved by KT+. KT+ uniformly improves upon the MMD error of ST, even when ST exhibits better-than-i.i.d. accuracy. Specifically, KT+ provides significantly smaller integration errors for functions both inside the RKHS (such as k(X\u2032, \u00b7)) and outside the RKHS (including the first and second moments and the benchmark CIF function) in nearly every setting. This improvement occurs despite the IMQ kernel not having a square-root kernel with fast-decaying tails, which typically complicates error reduction.\n\nQuantitatively, for the Hinch posterior with IMQ kernel, KT+ achieves higher MMD values at given coreset sizes compared to ST (e.g., KT+ MMD values around 0.50\u20130.70 versus ST values around 0.44\u20130.46), indicating better approximation quality. The theoretical and empirical results show that KT+ attains better-than-i.i.d. MMD guarantees and improved error decay rates, which explains the observed superior performance of KT+ over ST as the coreset size increases."}
{"question_id": "888f26be8b81ccb011c79b043940eec47d9b414e", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The same network architecture, specifically the OLN (Open-World Localization Network) built on top of Faster R-CNN, is used separately for both depth and normal maps by training distinct object proposal networks on each type of geometric cue. In Phase-I training, one proposal network is trained on predicted depth maps, and another is trained on predicted normal maps, both extracted from RGB images using pretrained Omnidata models. The pseudo boxes generated by these two separately trained proposal networks are then merged by filtering overlapping boxes based on their objectness scores to form a combined set of pseudo boxes for subsequent training phases. Thus, the same network architecture is applied independently to depth and normal inputs, enabling the exploitation of both geometric cues for novel object detection."}
{"question_id": "5739894b5714e42337f53319a265bb28e2f6e18d", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Yes, incorporating RGB images in addition to depth and normal images in the first step (Phase-I) leads to worse performance. When RGB is stacked with geometric cues to train the proposal network, the model tends to rely more heavily on RGB inputs, which are stronger signals in closed-world setups but cause the model to underutilize geometric cues. This reliance prevents effective discovery of novel objects, which is crucial for open-world object detection, resulting in inferior performance across many benchmarks. Furthermore, merging pseudo boxes from RGB-trained models with those from geometric cues (the \"GOOD-All\" method) either yields no performance gains or degrades performance, as RGB-based models favor smaller detection boxes that often correspond to textures or parts of larger objects, potentially harming detection quality."}
{"question_id": "87b3ef59cb4832acb7306d8368503784307b1adc", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The provided information does not explicitly discuss the advantages of proposal-based object detectors compared to DETR or other similar object detectors. The text primarily focuses on the GOOD method, which is compatible with both proposal-based and proposal-free detectors, and compares architectures such as OLN (a two-stage proposal-based detector) and FCOS (a single-stage proposal-free detector). It highlights that OLN is a stronger architecture than FCOS within the GOOD framework for open-world object detection, but it does not provide a direct comparison or advantages of proposal-based detectors relative to DETR or similar models. Therefore, no specific advantages of proposal-based object detectors over DETR or similar detectors can be extracted from the given content."}
{"question_id": "4191cd3e5bd482f3d097b689c9857bf3d727f98b", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "In Phase-I training, there are two possible ways to combine the depth and normal maps for training the object proposal network: (1) stacking the two geometric cues together as input and training a single object proposal network on these stacked inputs; or (2) training two separate object proposal networks independently on the depth and normal inputs, respectively, and then merging their generated pseudo boxes into a single pseudo box pool for Phase-II training. Empirically, the second method\u2014ensembling pseudo labels by merging the pseudo boxes from separately trained networks\u2014is slightly better and is used throughout the paper for the combined approach (GOOD-Both). The merging process involves filtering out overlapping pseudo boxes with an IoU threshold of 0.5, retaining the box with the higher objectness score."}
{"question_id": "496d254bdc722d815fb98ad3903cdc34df700fce", "paper_id": "openreview/ICLR-2023-conf/KDhFkA6MQsW", "generation": "The complexity stated as \\( t = \\frac{\\delta_f}{\\epsilon^{1.75}} \\log d \\) does not match the complexity in Theorem 2 because Theorem 2 derives its iteration complexity based on a more detailed parameter setting and analysis involving the Hamiltonian decrease, perturbation steps, and negative curvature escape conditions, which yield a different dependence on \\(\\epsilon\\) and other problem parameters.\n\nSpecifically, Theorem 2's proof (Appendix D.1) sets parameters such as\n\\[\n\\delta_0 = \\frac{\\delta}{384 \\Delta_f} \\sqrt{\\frac{\\epsilon^3}{\\rho}}, \\quad \\eta = \\frac{1}{4\\ell}, \\quad \\theta = \\frac{1}{4\\sqrt{\\kappa}}, \\quad s = \\frac{\\gamma}{4\\rho}, \\quad T' = 32 \\sqrt{\\kappa} \\log\\left(\\frac{\\ell \\sqrt{d} \\delta_0 \\sqrt{\\rho \\epsilon}}{}\\right),\n\\]\nand defines a new parameter \\(\\tilde{T} = \\sqrt{\\kappa} c_A\\), where \\(c_A\\) is a large constant. The total number of perturbation steps is bounded by\n\\[\n384 \\frac{(f(x_0) - f^*) \\sqrt{\\rho}}{\\epsilon^3},\n\\]\nand the probability of failure is controlled by \\(\\delta\\).\n\nThis analysis shows that the iteration complexity depends on \\(\\epsilon^{-3}\\) (through the perturbation count and Hamiltonian decrease) and includes logarithmic factors in \\(d\\) and other problem constants, rather than the \\(\\epsilon^{-1.75}\\) scaling suggested by \\(t = \\frac{\\delta_f}{\\epsilon^{1.75}} \\log d\\).\n\nIn contrast, the complexity \\(t = \\frac{\\delta_f}{\\epsilon^{1.75}} \\log d\\) does not arise from the detailed parameter dependencies and probabilistic bounds established in Theorem 2. Theorem 2's complexity reflects the interplay of gradient norms, Hessian negative curvature, perturbation radius, and step sizes, which collectively yield a different exponent on \\(\\epsilon\\) and a more intricate logarithmic dependence on dimension \\(d\\).\n\nTherefore, the mismatch occurs because the simplified form \\(t = \\frac{\\delta_f}{\\epsilon^{1.75}} \\log d\\) does not capture the full parameter dependencies and probabilistic guarantees that determine the iteration complexity in Theorem 2."}
{"question_id": "ecbcef296be692e081bf8e60e3d63dcbf2090dbf", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "The paper does not explicitly state that a language expert was consulted to verify the accuracy of the translations of English time expressions into other languages. It mentions that the automatic translation of time expressions and templates from English to other languages may introduce some errors, indicating that the translations were likely done automatically rather than verified by experts. Additionally, some language-specific issues and errors were observed, such as the broader meaning of \"afternoon\" in Korean and word sense disambiguation errors in Hebrew, which further suggests that expert verification was not conducted."}
{"question_id": "68cdad6fbee4b667c3a6b10e5761a4ccf660e32b", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "The data and analysis presented do not directly show how the location of a country within its time zone affects the results. However, the discussion acknowledges that speakers of the same language living in different countries or regions within different time zones may perceive time expressions differently. For example, Portuguese speakers in Brazil and Portugal are expected to have different perceptions of time expressions due to their different time zones. The study treats language as a proxy for culture, which may obscure such intra-language regional differences. Additionally, the data collection did not control for exact worker locations within countries, and the average night start times were compared to average sunset times in capital cities, but no detailed analysis of intra-time zone location effects was reported."}
{"question_id": "e7ac4283196f09643f681e08d6093e9313983cf1", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "Yes, the translation of the template has been adapted to account for languages with grammatical gender. For example, in Italian, the time expression for morning (mattina) is feminine, whereas afternoon (pomeriggio) is masculine, which results in variation in the determiner used\u2014\u201cla <time_exp>\u201d for feminine and \u201cil <time_exp>\u201d for masculine. This adaptation ensures that the translated templates reflect the correct grammatical gender in the target language."}
{"question_id": "c12d6fb12967631fd985c4b437ee77cd1d2a8e2e", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Yes, when combining multiple modalities for the model, filtering is applied to merge pseudo boxes generated by object proposal networks trained separately on different geometric cues such as depth and normal maps. Specifically, if the Intersection over Union (IoU) of two pseudo boxes is larger than 0.5, they are considered overlapping, and the box with the lower objectness score is filtered out. This filtering process ensures that overlapping pseudo boxes from different modalities are merged effectively to form a single pseudo box pool for subsequent training phases."}
{"question_id": "d753561800b2ad04d3d8262519328d014142d717", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The rationale for choosing to remove tokens at the 4th, 7th, and 10th layers in Vision Transformer (ViT) models is based on a strategy that evenly divides the ViT into blocks with the same number of layers for token reorganization. Specifically, for a ViT with L layers and t token reorganization layers, the separating length s is calculated as s = L / (t + 1). The token reorganization layers are then placed at the indices [s + 1, 2s + 1, ..., ts + 1], which evenly cuts the ViT into segments. For example, in a ViT with 12 layers and 3 token reorganization layers, the reorganization layers would be placed at layers 4, 7, and 10. This approach ensures a uniform distribution of token removal points throughout the network depth, facilitating gradual reduction of tokens as the network goes deeper to decrease computational cost while maintaining model performance."}
{"question_id": "f21290e4ef8f0ead710c484502ad8e103217da11", "paper_id": "openreview/NeurIPS-2022-dabt/76w7bsdViZf", "generation": "The authors evaluated additional pretrained models on ImageNet, including DenseNet161 and VGG16, without finetuning. These models were assessed on Hard ImageNet and RIVAL20 datasets to analyze their reliance on spurious features. The evaluation showed that all models, including DenseNet161 and VGG16, exhibited higher accuracy under ablation, lower Relative Foreground Sensitivity (RFS) scores, and lower saliency alignment on Hard ImageNet compared to RIVAL20. However, there is no indication that the authors trained these models from scratch or performed finetuning on the full ImageNet dataset; the evaluations were conducted using pretrained weights without further training."}
{"question_id": "a7d741be648d514c67c1a0468a78782b19c6d11c", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Label smoothing does not always improve performance; its effectiveness depends on the scenario. It is particularly helpful when the pretrained features are well learned and the standard head probing (HP) training accuracy converges very quickly to above 90%, as it reserves energy for subsequent feature adaptation, leading to better generalization. However, when the HP training accuracy is low, indicating that the pretrained features are not suitable, the assumption that the model's prediction converges to the labels no longer holds, and label smoothing may fail to bring improvement or even degrade performance. Additionally, using label smoothing with opposite energy settings can cause features to adapt in opposite directions, resulting in worse generalization. Therefore, label smoothing should be applied with caution, and it is recommended to set the smoothing parameter during HP (\u03b7_HP) less than or equal to that during finetuning (\u03b7_FT) to ensure correct adaptation direction."}
{"question_id": "cf66689ffef1b230b7bab23901a8bf528a8e97f8", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "The paper incorporates section titles into the BOS token representation by generating section title embeddings (STE) or classified section title embeddings (classified STE) and adding them to the initial sentence representation at the BOS token. The section title embedding is obtained by feeding the tokenized section title into the pre-trained Transformer language model (TLM) and summing the last hidden states of all tokens in the title to form a single embedding. For classified STE, typical section title classes are manually predefined, and all intra-class section titles are replaced with the embedding of their corresponding class. This STE or classified STE is then added to the BOS token embedding of the sentence, enriching the sentence representation with section title information before it is fed into the hierarchical contextual learning layers."}
{"question_id": "8e2cb1c95dffd133cc91ab3123074a0853c829fb", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "The authors included CNN/DailyMail as an exemplary dataset with less conspicuous hierarchical structure compared to PubMed and arXiv. CNN/DailyMail has a smaller average hierarchical width (hi-width = 1.33) and a shallower hierarchical depth, representing short news articles without further grouping of paragraphs into sections. This contrast in hierarchical characteristics motivated the authors to experiment on CNN/DailyMail to evaluate the performance of their proposed method on datasets with less pronounced hierarchical structure, thereby testing their hypothesis that the method works better on datasets with more conspicuous hierarchical structures."}
{"question_id": "f29ff7d6be64035f374fe6b3fc470453591154e9", "paper_id": "nlpeer/COLING2020/1570", "generation": "Yes, the annotated mistakes will be released upon the release of ManyNames v2. The authors state that they will publicly release the raw verification annotations in addition to the consistent response sets, which include the different inadequacy types, to facilitate various uses and analyses."}
{"question_id": "e0bf6addede2ca2d1e76eac67027e72e3ff385f5", "paper_id": "nlpeer/ARR-22/3cfcbbad78a71ca6cf4a5b2bbaee2a7ad8a1de295cf9f4103408dfeaf38a0de01b2b8d23cb94e0ef0b5f76a1947815f0335a7077bdea03ea4a29f6a2bff432cc", "generation": "Hanja is considered an extinct language rather than merely a script because it represents a distinct linguistic system with its own lexical, semantic, and syntactic characteristics that differ significantly from modern Korean and modern Chinese. Although Hanja is based on ancient Chinese characters, it evolved during the Joseon dynasty to incorporate the basic syntax of classical Chinese mixed with features of colloquial Korean. This unique linguistic system was the main writing system in Korea before the 20th century but is no longer in active use as a living language. Thus, Hanja is treated as an extinct language due to its distinct linguistic identity and cessation of use, rather than simply as a writing system or script."}
{"question_id": "953feae01ae0b8d2066fd035c079f0a5dd581aaf", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Label smoothing does not always improve the performance of the hyperparameter-fine tuning (HP) procedure. It is particularly effective when the pretrained features are already well-optimized and the standard HP training accuracy converges very quickly to above 90%, as it reserves energy for further feature adaptation without reducing HP-train-accuracy. However, when the HP-train-accuracy is low, indicating that the pretrained features are not suitable or the model has not converged to the labels, the assumption underlying label smoothing no longer holds, and label smoothing may fail to bring improvement. Additionally, if label smoothing is applied in both the HP and fine-tuning (FT) phases with equal smoothing parameters, the reserved energy disappears, resulting in performance similar to the baseline without smoothing. Moreover, using label smoothing with opposite energy directions can lead to larger adaptation but worse generalization performance. Therefore, label smoothing is beneficial only under certain conditions and does not universally improve HP performance."}
{"question_id": "691280cb66aae7b9fee2d8ac0937e5f970437b43", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "Yes, human evaluation results for the extractive summarization task are presented through case analyses of extracted summaries from the HiStruct+ model and a baseline model, compared to the gold summary (abstract of the paper). Two samples are showcased: one from the arXiv dataset and one from PubMed. In both cases, the baseline model tends to select the first sentences, which often focus on background information and lack an overall perspective or key conclusions. In contrast, the HiStruct+ model selects sentences from later positions that better cover the main content, conclusions, and future work, aligning more closely with the gold summary. For example, in the arXiv sample, the HiStruct+ model\u2019s summary includes an overview of the proposed work and future outlook, while in the PubMed sample, it captures key statistics, mentions novel strains, and highlights conclusions more effectively than the baseline. This human evaluation demonstrates the superiority of the HiStruct+ model in producing more informative and representative extractive summaries."}
{"question_id": "ca87a914265cffe46bfb63e2e24a3568efbc7888", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Yes, there are cases where the 'end' histogram does not match the e_y when using different head types for hyperparameter tuning. Specifically, when using a linear head, the HP-train-accuracy plateaus at lower values (e.g., 92% or 78%), whereas a 2-layer MLP head can achieve higher HP-train-accuracy (e.g., 99% and 95%). This indicates that the feature adaptation and resulting representations differ between head types. Moreover, increasing the head capacity (e.g., using an MLP head) can slow down convergence and prolong an unpredictable \"chaos phase\" in feature adaptation, especially when using a small \u03c4 (large energy). This suggests that the final feature distributions (reflected in histograms) and their alignment with the target labels e_y can differ depending on the head type and hyperparameter settings."}
{"question_id": "789b3799040a63d59e93a029bab4459c7ff3aa2c", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The phrase \"information exchanges between the two parts of the network\" in Fig. 1 refers to the interaction between the backbone (feature extractor) and the task head during finetuning. This interaction is characterized by how the feature representations \\( z = f(x; B) \\) produced by the backbone change in response to updates in the task head parameters \\( v \\). Specifically, the update of the features \\( z \\) depends on the empirical neural tangent kernel (NTK) of the backbone, the gradient of the task head prediction with respect to the features, and the difference between the predicted probabilities and the true labels. This dynamic exchange governs how the backbone features adapt under the influence of the task head during finetuning, reflecting a bidirectional flow of information that shapes the overall network adaptation."}
{"question_id": "e111e75817d67b6fbeec06d5ba117b3419bf2f0f", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The initial value being discussed in Section 3.2 is the initialized task head parameter denoted as \\( v_0 \\). This parameter \\( v_0 \\) is considered as a good initialization for the task head that can lead to better downstream performance after fine-tuning. The analysis focuses on how \\( v_0 \\) influences certain distance metrics and the adaptation of features learned from the pretraining stage to the downstream task. The goal is to find an optimal \\( v_0 \\) that facilitates better generalization by properly adapting the features to the new environment."}
{"question_id": "28241e220ebb3458ef6d5eaf6829f8563161a7c1", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Mild adaptation results in the features being stretched (increased norm \u2225z_T\u2225_2) in a similar direction to the original features (cosine similarity changes little). This transformation makes originally overlapped features more separable without significantly altering the manifold of the features. Such behavior is desirable in most transfer learning scenarios, as it improves feature separability while preserving the overall structure of the feature space."}
{"question_id": "36fdc759d8b028d2f3c0c5cb9e8c26b5744962d0", "paper_id": "openreview/ICLR-2023-conf/OM7doLjQbOQ", "generation": "ILA-DA consistently outperforms other transferable attack methods, including LinBP and the CTM family, across different choices of intermediate layers. Specifically, ILA-DA achieves higher attack success rates than ILA without augmentation and shows superior transferability on multiple intermediate layers of models such as ResNet50, VGG19, and Inception V3. Unlike the CTM family, which requires stacking multiple attack methods to achieve high transferability, ILA-DA attains strong attack success rates using a single reference attack (e.g., I-FGSM + ILA-DA yields an average success rate of 75.9% on defended models, outperforming the strongest CTM method VNI-CT-FGSM by 4.4%). Additionally, ILA-DA combined with state-of-the-art attacks further improves transferability, reaching up to 81.9% success rate. This demonstrates that ILA-DA is more effective and less sensitive to the choice of intermediate layer compared to LinBP and CTM-based methods."}
{"question_id": "99d5ca18b41cac7092cd7ca7cf0888b8a29a3018", "paper_id": "openreview/ICLR-2023-conf/OM7doLjQbOQ", "generation": "The new automated image transformation strategy introduced in ILA-DA is specifically designed to create more diverse augmented references within the ILA framework by applying learnable augmentations such as translation, cropping, and rotation identically to all three ILA references (clean image, reference adversarial image, and updated image). This approach requires perfect pixel alignment among these references to avoid misalignment in feature maps, which is critical for optimizing the intermediate feature discrepancies targeted by ILA.\n\nWhile other transfer-based methods like MI-FGSM and DIM incorporate data augmentation techniques (e.g., DIM applies random resizing and zero-padding), the automated augmentation strategy in ILA-DA is tailored to the intermediate-level attack mechanism and its reliance on multiple aligned reference images. The text does not explicitly state that this automated augmentation strategy can be directly applied to other transfer-based attacks such as MI-FGSM and DIM. Instead, it highlights that ILA-DA advances the ILA attack by promoting reference diversity through these augmentations.\n\nTherefore, the automated image transformation strategy in ILA-DA is specifically developed for enhancing the ILA attack and is not described as being applicable to other transfer-based methods like MI-FGSM and DIM."}
{"question_id": "726e56359927a7fc14be9019ea69e50929fa9e67", "paper_id": "nlpeer/ARR-22/dd0a83852cb35c8d0ac0c40da6b7ebeb385f9c7e4816ef51f43abd6910a9adacc0daf460dfda4e56f37fa542a749961a78ddbbe74af0326f41e5cd38e867b121", "generation": "The provided evidence does not explicitly discuss or compare the effects of using predicted cues versus gold cue information for scope resolution. The experiments and analyses focus on improvements in negation detection through negation-focused pre-training strategies, such as explicit cue masking and adaptive pre-training on negation-focused data, and their impact on cue detection and scope resolution performance. However, there is no direct mention or evaluation of the difference in scope resolution performance when using predicted cues instead of gold cues."}
{"question_id": "ffe260fb92f4c53395118a567f59d32fd365c351", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The degree of separability when adapting a model to a task is influenced primarily by the structure and capacity of the task head, the quality of the pretrained features, and the extent of feature adaptation required. Specifically:\n\n1. **Task Head Capacity**:  \n   - A low-capacity task head (e.g., a linear head) may struggle to achieve high training accuracy during head probing, limiting the ability to preserve pretrained features and reducing separability.  \n   - Conversely, a task head with capacity much larger than the backbone can distort pretrained information, as the features may be rewritten during the random changing phase before gradients stabilize.\n\n2. **Pretrained Feature Quality and Backbone Depth**:  \n   - When pretrained features are of high quality (e.g., trained on large datasets like ImageNet), preserving earlier layers of the backbone tends to maintain useful information and requires less adaptation, supporting better separability.  \n   - If pretrained features are poor, discarding later layers and increasing the task head size can be beneficial, allowing more substantial adaptation.\n\n3. **Training Accuracy and Initial Energy**:  \n   - The initial training accuracy and loss at the beginning of finetuning determine the \"energy\" available for feature adaptation. Higher energy leads to greater changes in features, affecting their separability.  \n   - The degree of feature adaptation (strong, mild, or tiny) depends on how far the pretrained features are from the downstream task optimum.\n\n4. **Interaction Between Backbone and Task Head**:  \n   - The backbone and task head are tied during finetuning, so biases and noise in the pretrained backbone influence the learning dynamics of the task head, affecting the separability of adapted features.\n\nIn summary, the degree of separability is controlled by the balance between task head capacity and backbone preservation, the quality of pretrained features, and the initial conditions of finetuning that govern feature adaptation dynamics."}
{"question_id": "08c2ff08d58f88bfead47fc3783d34333d02f023", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The rationale behind choosing a label-smoothing coefficient of 0.9 (\u03b7_HP = 0.9) is to reserve a certain amount of \"energy\" during the head probing (HP) stage, which facilitates subsequent feature adaptation in finetuning. Specifically, by setting the labels during HP as a mixture \u03b7_HP * e_y + (1 - \u03b7_HP) * u, where u is a uniform K-class categorical distribution, the HP stage retains at least (1 - \u03b7_HP) * \u2225e_y - u\u2225\u00b2 energy for feature adaptation even as the HP training accuracy converges. This reserved energy prevents the model from fully converging to the hard labels too quickly, allowing for a milder and more effective adaptation of features during finetuning.\n\nChoosing \u03b7_HP = 0.9 means that 10% of the label distribution is smoothed uniformly, which is sufficient to maintain this reserved energy without significantly reducing the HP training accuracy, which often converges to over 90% rapidly. This balance is particularly useful when pretrained features are already strong and standard HP converges quickly, enabling better downstream adaptation and generalization.\n\nFurthermore, it is important that the label-smoothing coefficient during HP (\u03b7_HP) is less than or equal to that during finetuning (\u03b7_FT) to ensure that the feature adaptation proceeds in the correct direction. If both phases use the same smoothing coefficient (e.g., \u03b7_HP = \u03b7_FT = 0.9), the reserved energy disappears, and the adaptation behaves similarly to the baseline without smoothing. Conversely, mismatched smoothing can lead to larger but misdirected adaptation and worse generalization.\n\nIn summary, \u03b7_HP = 0.9 is chosen to reserve a moderate amount of energy for feature adaptation, enabling improved finetuning performance when pretrained features are already strong and HP training accuracy is high."}
{"question_id": "14f24eacc79985de8d643389b87e35ceb5209775", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "The search cost of the proposed \u039b-DARTS method is 0.8 GPU days on a GTX 1080 Ti GPU when performed on the DARTS search space and the CIFAR-10 dataset. This cost is approximately 20% less than the second-order DARTS (1.0 GPU days) and about 40% less than SDARTS-ADV (1.3 GPU days). By using forward or backward finite difference approximations in an interleaved fashion, the search cost can be further reduced by 25% to around 0.6 GPU days, which is only 50% more than the original first-order DARTS (0.4 GPU days) and comparable to DrNAS with progressive search."}
{"question_id": "9b256b585691520864c3cf7d1b8cfb8f863d6663", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The study reveals a non-trivial trend in feature adaptation during finetuning, characterized by the interplay of \"energy\" and \"direction\" terms in the learning dynamics of pretrained features. This trend is rigorously proven in an overparameterized linear model and experimentally validated across various practical settings. The findings indicate that mild adaptation of pretrained features is generally beneficial, especially when the pretrained features are not optimal for downstream tasks. Furthermore, the study demonstrates that controlling feature adaptation\u2014through early stopping in head probing, label smoothing during head probing, or modifying the task head's capacity\u2014can improve downstream performance. These insights go beyond the general empirical impression by providing a theoretical framework and practical guidelines for feature adaptation, highlighting that neither fully fixed nor fully adapted features are optimal in many cases. Additionally, the study shows that the complexity of the task head and selective reinitialization of backbone layers influence feature adaptation and downstream results, suggesting that preserving lower-level pretrained features while adapting higher layers can be advantageous depending on the quality of pretrained features and the nature of the downstream task."}
{"question_id": "9ff146fb1145a7e6cd038252a41b96f5c6ac0494", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "Yes, there are examples where synthetic data and ground-truth (real) data are used to compare model performance. Specifically, in the few-shot image recognition setting, synthetic data generated per class (e.g., 800 images per class using the Real Guidance (RG) method) are compared with few-shot real samples to evaluate classification results on 8 datasets such as CIFAR-100. Additionally, in the zero-shot setting, synthetic data generated for each class (e.g., 2000 synthetic images per class) are used to improve classification results on 17 diverse datasets, including CIFAR-10, CIFAR-100, Caltech101, ImageNet, and others. These setups allow for direct comparison of model performance when trained or tuned with synthetic data versus real data."}
{"question_id": "6ed4842f06973b3b3b83a068d590e3a5421678f8", "paper_id": "openreview/ICLR-2023-conf/T2Ncx_PN2K", "generation": "Using a transformer-based acoustic model, specifically a Conformer encoder, instead of an LSTM-based one can improve the model architecture in terms of adaptation effectiveness. The FixedGram-based TOLSTOI adaptation method works well with both bidirectional-LSTM and Conformer encoders; however, experiments with a Conformer-based RNN-T model trained on the SWB 300H dataset show significant reductions in word error rate (WER) for target domains with minimal catastrophic forgetting compared to shallow fusion. The Conformer encoder, which distributes acoustic features in the neighborhood through its convolution and attention mechanisms, supports effective in-situ text-only adaptation without introducing new layers or external language models during deployment. Thus, employing a transformer-based Conformer encoder can enhance adaptation performance while maintaining model efficiency."}
{"question_id": "e57f2a5a860c3aa8c1e0f8ca5a3375dd735d463c", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "The performance drop after 100 epochs in Figure 4 (c) is caused by the performance collapse phenomenon in DARTS, which is linked to low layer alignment and the resulting convergence to softmax saturation points dominated by skip-connection operations. Due to the weight-sharing structure and low correlation between convolution operation outputs across layers, the architecture parameters increasingly favor skip-connections because their gradients have larger magnitudes, especially in deeper layers where vanishing gradients are more severe. This leads to architectures overwhelmingly selecting skip-connections, which degrades performance as the search progresses beyond 100 epochs. Additionally, increasing the number of search iterations exacerbates this issue by intensifying vanishing gradients as the loss approaches its optimum, further causing the performance collapse."}
{"question_id": "ba8b96d10b44463b1ec163db65a29b6145f8729a", "paper_id": "openreview/ICLR-2023-conf/ySCL-NG_I3", "generation": "The learning curves for the Harmonic Molecular Representation (HMR) model during the rigid protein docking task demonstrate progressive improvement in key performance metrics over the course of training. Specifically, the total loss decreases steadily across 80 epochs, indicating effective optimization of the model parameters. Concurrently, the F-score, Area Under the Curve (AUC), and Average Precision (AP) metrics all show consistent increases, reflecting enhanced predictive accuracy and robustness in binding site prediction.\n\nThe model selection is based on the best validation AP score, which suggests that the training process successfully balances fitting the training data while maintaining generalization to unseen data. The use of a cosine annealing learning rate scheduler and the Adam optimizer with a learning rate of 5 \u00d7 10\u207b\u2074 likely contribute to the smooth convergence observed in the learning curves.\n\nOverall, the dynamics of the network during training reveal stable and effective learning, with improvements in both loss minimization and predictive performance metrics, culminating in a model well-suited for testing on the DB5.5 dataset."}
{"question_id": "9246fb2439ec9512f9298b927660f030736765c0", "paper_id": "openreview/ICLR-2023-conf/T2Ncx_PN2K", "generation": "The training process for the proposed model is not truly end-to-end. Although the RNN-Transducer (RNN-T) architecture itself is an end-to-end ASR model, the adaptation approach described involves a modular training strategy. Specifically, the speech module parameters (\u03b8_S) are kept fixed, while only the language module parameters (\u03b8_L) and the joint network parameters (\u03b8_J) are fine-tuned using imputed speech representations generated from text-only data. The imputation model, which generates proxy speech module output vectors (h) from text, is trained separately and independently from the main ASR model. This separation and the use of an imputation model to generate intermediate representations rather than raw audio frames indicate that the overall adaptation process is not a single, unified end-to-end training procedure."}
{"question_id": "f8b91940d2ce9e4e0df966f18d724e12b5aac0e5", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "In a fully-supervised setting where models are trained from scratch, synthetic data generated by text-to-image models deliver inferior performance compared to real data and are significantly less data-efficient. For example, training a ResNet-50 model from random initialization on a synthetic CIFAR-100 dataset of 50,000 images achieves only 28.74% top-1 accuracy, which is substantially lower than models pre-trained on real data. To match this performance using real data, approximately five times fewer real images (around 9,500 images) are required, indicating that synthetic data need a much larger volume to achieve comparable results. Furthermore, increasing the amount of synthetic data beyond a certain point does not yield further performance improvements. This suggests that the quality and diversity of synthetic data are currently insufficient for fully-supervised training from scratch, making synthetic data less effective and efficient than real data for image classification tasks in this setting."}
{"question_id": "94d3d227741b9bf8258649cb3567dc790b9dca07", "paper_id": "nlpeer/ARR-22/91d9e763eb1470028002e5c97689ada4b74f30be17291a14458f7542c387a9f2a7516e60f4022d887bdbd2165aa8cce6d566db380e5a82af677917c48efc2446", "generation": "The authors use regularization specifically for label embeddings to address the anisotropy problem and prevent model degeneration by promoting uniformity and dispersion of label representations on the unit hypersphere. The label embedding regularizer (LER) encourages the label representations to be uniformly dispersed, keeping the inter-label similarity as low as possible so that the feature space of each class is more dispersed. This prevents representation collapse or degeneration of the label embeddings, ensuring that the label space preserves maximal information and maintains uniformity.\n\nIn contrast, instance embeddings are aligned to their corresponding label embeddings through the instance-centered contrastive loss (ICL), which encourages each text representation to be close to its label representation while pushing away mismatched pairs. This alignment inherently guides the instance embeddings to be semantically meaningful and well-structured in the shared space without requiring an additional uniformity regularization. Thus, regularization is applied to label embeddings to maintain their uniform distribution and prevent degeneration, while instance embeddings are regulated indirectly through their alignment with these well-regularized label embeddings."}
{"question_id": "9f2536e9ca279c79b121794af57c2ad02d6b13c8", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "The use of synthetic data can improve model performance even when a large amount of ground-truth data is available. Specifically, in downstream-aware settings where synthetic data is generated according to the label space of the downstream task (e.g., CIFAR-100), pre-training on synthetic data with an amount equal to or greater than the real data (1.2M, 2.4M, 3.6M images) significantly reduces the performance gap compared to training from scratch and can outperform ImageNet-1K pre-training when the synthetic data amount is increased to 2\u00d7 or 3\u00d7 the size of ImageNet-1K. Furthermore, initializing the model from ImageNet-1K pre-trained weights and then pre-training on synthetic data yields additional performance gains.\n\nIn downstream-agnostic settings, synthetic data pre-training achieves comparable or even superior transfer learning results relative to ImageNet-1K real data pre-training, especially when using self-supervised methods (e.g., Moco v2) and ViT-based backbones. Increasing the synthetic data amount and label space diversity further improves performance, sometimes surpassing that of real data pre-training.\n\nTherefore, synthetic data not only complements but can also enhance model performance beyond what is achievable with large-scale ground-truth data alone."}
{"question_id": "b9dc0dacfa9d5676e09c6d6308a65cb2885392cd", "paper_id": "nlpeer/ARR-22/24714d27941075cbad95c02db3ac730df71d355f85f5d247a62c9453ea29004b15cfb386663c82ce55ba17d652253064f4ff70f21dd5c08d8e39986ab22c45ce", "generation": "The results of the Montreal Forced Aligner, when applied to singing recordings for obtaining alignments to lyrics, have not been explicitly stated as manually checked for accuracy. The alignments produced by the tool are used to derive ground-truth pitch alignment based on shared lyrics in the PopBuTFy dataset, but there is no mention of manual verification of these alignments in the provided information."}
{"question_id": "6f797e6284c2b0ebd83dc98348c33626ac517dbb", "paper_id": "openreview/NeurIPS-2022-dabt/Zx5qJzNesn0", "generation": "Yes, there are differences between the locations in the benchmark besides the weather. Specifically, the crop varieties used differ by location to better match local growing conditions. For fertilization experiments, maize with a relative maturity (RM) of 90 is used in ContinuousCorn simulations. For crop planning experiments, an RM 100 maize and maturity group (MG) 3 soybean are used to better suit the longer local growing conditions in Rock Springs (central Pennsylvania) and New Holland (southeast Pennsylvania). These maturities are widely used by producers in these areas and are recommended for those locations."}
{"question_id": "ff310f12cf0c134c9763ec3389c106e6c16dc65c", "paper_id": "openreview/ICLR-2022-conf/ZTsoE8G3GG", "generation": "The metric \"score\" is a task-specific real number in the range [0, 1], with higher values indicating better performance, reflecting how well the generated molecules match a target molecular profile or property objective. The \"quality\" metric is defined as the absence of undesirable substructures in the generated molecules, serving as a post-hoc evaluation to ensure that high scores correspond to chemically reasonable molecules rather than artifacts that exploit the scoring function. Quality filters are applied after optimization and are not directly available to the models during training or generation."}
{"question_id": "030389c23b9697a71ca59dec3bcab088d7f20ced", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "No, equation (1) is a summation from j = 1 to k, not from j = 0 to k."}
{"question_id": "46357f5d8816d410e6100ea03a5fde2f576ae270", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "The extracted patterns are representative traffic speed patterns derived from historical traffic data, specifically average daily patterns consisting of 288 speed data points per vertex, corresponding to 24 hours with 5-minute intervals. These daily patterns are segmented into smaller slices using a given window size, resulting in a set of traffic patterns. The patterns exhibit high correlation between leading and trailing segments, even over short-term periods, and many patterns are highly similar, causing class imbalance. To address this, clustering-based undersampling with cosine similarity is applied to group similar patterns, and the cluster centers are used as balanced and representative patterns. These representative patterns serve as keys for memory access in the forecasting model, enabling effective pattern matching and improved prediction of traffic conditions, including abrupt changes such as congestion and speed drops."}
{"question_id": "05fe45ed14b202a953eb1a25a0c4552d1d2ff42d", "paper_id": "openreview/ICLR-2022-conf/k7efTb0un9z", "generation": "Yes, the proposed method can be applied to simpler models such as multilayer perceptrons (MLPs). An evaluation was conducted on training a 3-layer MLP with a hidden size of 128 on the Fashion MNIST dataset, where the method (GNS) achieved the best test accuracy of 89.8%, demonstrating its applicability to such simple scenarios."}
{"question_id": "c0701214e646badb3bbb44e4985982c8e9e65fc7", "paper_id": "nlpeer/ARR-22/83dc885bef279cbb7a3b5b173494428b6cd043d34527016519250477d274fd1793fa0bddd2bd4bbb3edfa9709ddc85e90825a0554bfa8eceb8cb34d813c06c53", "generation": "To ensure the accuracy of the confidence network, the following measures have been taken:\n\n1. The confidence network (ConNet) is trained jointly with the NMT model in an unsupervised manner, where confidence is defined as the number of hints the model needs to make a correct prediction. More hints indicate lower confidence.\n\n2. During training, the NMT model is allowed to ask for hints by interpolating the ground truth probability distribution into the original prediction probabilities, with the interpolation degree controlled by the confidence output of the network. This mechanism encourages the model to translate independently when confident and ask for hints when uncertain, thereby aligning confidence estimates with actual prediction difficulty.\n\n3. The confidence network takes multi-layer hidden states of the decoder as inputs and outputs a scalar confidence value between 0 and 1 via a sigmoid function, which is trained to reflect the model's certainty at each decoding step.\n\n4. The learned confidence estimate is validated as a well-calibrated metric through extensive sentence- and word-level quality estimation tasks, demonstrating higher accuracy in predicting translation quality compared to existing unsupervised metrics.\n\n5. Analytical results confirm that the confidence estimate can detect potential risks such as noisy samples and out-of-domain data by assigning low confidence to these challenging cases.\n\n6. The confidence branch is designed as a lightweight component that does not degrade translation accuracy, ensuring that the confidence estimation does not compromise the primary translation performance.\n\nThese measures collectively ensure that the confidence network provides an accurate and reliable estimate of the NMT model's predictive uncertainty."}
{"question_id": "f18d6c2ac81007f7b42cab31234075307c85ee0a", "paper_id": "nlpeer/ARR-22/6262c349a3980c6850e252b3a44391b1b1318c26fbf29be55c61c0a32b8b626e5581c98234d1ecb410674c00ebcf3ba2adfdce41c2b2d32211721372a8ed1bc0", "generation": "The training languages used for the non-baseline models in the cross-lingual experiments do not include German. The models were trained on speech in 8 languages with one speaker per language, specifically the Nancy dataset (English) and the CSS10 dataset, which includes Greek, Spanish, Finnish, Russian, Hungarian, Dutch, and French. German was only used as the target language for fine-tuning on a 30-minute subset, not as part of the initial training languages."}
{"question_id": "7a4e6842b9fed6c17b9fc508c5e7f7bdc1614d7c", "paper_id": "openreview/ICLR-2023-conf/ySCL-NG_I3", "generation": "The design of network architectures for molecular representation learning on molecular surfaces follows several specific strategies:\n\n1. **Integration of Geometric and Chemical Features:** The network must encode both the geometric shape and the underlying chemical properties of the molecule, as these jointly determine molecular functionality and interactions.\n\n2. **Use of Manifold Harmonic Message Passing:** To properly learn geometric and chemical features on the molecular surface, the architecture emulates the message passing framework common in graph neural networks (GNNs). This involves propagating information between distant surface regions to encode features at multiple scales.\n\n3. **Spectral Filtering in the Laplace-Beltrami Eigenbasis:** The network computes the first k Laplace-Beltrami eigenfunctions of the molecular surface mesh and applies neural network-learned spectral filters (e.g., Gaussian frequency filters) to propagate features over the surface. This spectral approach enables efficient multi-range communication regardless of molecular size.\n\n4. **Multi-Resolution Feature Extraction:** The architecture extracts initial surface features through multilayer perceptrons (MLPs) and applies multiple message passing blocks to capture features at different resolutions, accommodating tasks requiring both holistic and fine-grained molecular information.\n\n5. **Bypassing Equivariance Constraints:** Unlike traditional Euclidean space message passing networks that require equivariant architectures to handle rotations and translations, this approach encodes 3D molecular structure on the surface manifold, thus bypassing the equivariance requirement and potentially enhancing expressive power.\n\n6. **Robustness to Surface Discretization:** By operating on the continuous Riemannian manifold and its spectrum rather than discrete graphs, the representation remains stable across different surface discretizations.\n\nThese strategies collectively guide the design of network architectures that effectively learn harmonic molecular representations on Riemannian manifolds for tasks such as molecule-level prediction and protein docking."}
{"question_id": "67314f99bdc98da9611efbf0de1f4660e36f629c", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "The sampling method used to extract patterns with a time window involves slicing the average daily traffic speed patterns into segments of a given window size \\( T' \\). Specifically, from historical data, an average daily pattern consisting of 288 speed data points (corresponding to 24 hours with 5-minute intervals) is computed for each vertex. This daily pattern is then segmented by slicing it into consecutive windows of length \\( T' \\), resulting in multiple traffic patterns each representing a time window of size \\( T' \\)."}
{"question_id": "d59bc31fea9ec1c2594f0ed7813ed2d9348abc75", "paper_id": "nlpeer/COLING2020/939", "generation": "The task described in the paper focuses specifically on predicting the direct hypernym of a given synset within the WordNet taxonomy, formulated as finding the correct point to attach a new synset node to the existing taxonomy. This is framed as a sequence generation problem where the model generates taxonomy paths from the direct hypernym to the root node. The task is restricted to modeling the hypernym relation alone, emphasizing direct hypernym prediction rather than indirect or generic hypernyms.\n\nIn contrast, taxonomy induction refers more broadly to the construction or extension of an entire taxonomy, which involves identifying and organizing multiple hierarchical relations among terms to build or expand a structured taxonomy. Taxonomy induction is a more general task that may involve discovering various is-a relations and integrating them into a coherent taxonomy, rather than focusing on predicting a single direct hypernym link for a given node.\n\nThus, the key difference is that the paper\u2019s task is a focused hypernym prediction problem targeting the attachment of a single synset to an existing taxonomy via its direct hypernym, whereas taxonomy induction involves the broader process of constructing or extending the taxonomy as a whole."}
{"question_id": "be0cd13d8445fb87a73943d5acf2e5089a02876c", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "The optimization in equation (1) for active training selects locations \\( l_j \\) from the accumulated uncertainty estimates in the global map, specifically from the map region over which the model estimates the semantic map \\( \\hat{m}_t \\). The locations are chosen to maximize epistemic uncertainty as a proxy for information gain.\n\nFor goal selection at test time, equation (2) defines a policy that selects goals using the upper confidence bound of the target class probability. This selection is performed over any map region the agent has either observed or hallucinated, i.e., the predictions and uncertainty estimations accumulated over time.\n\nTherefore, the optimization in equations (1) and (2) is performed over the geo-centric map restricted to the set of map locations that have been observed or hallucinated so far, not over the entire map including unobserved areas."}
{"question_id": "23c1d98a22e68ab8a92b7b1cd2fee83fa79e9a86", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The optimal depth of a ranked list of entities returned by an entity search engine approach should correspond to the actual size of the concept's entity set, which can vary significantly and is often much larger than commonly used fixed cutoffs. Existing evaluation metrics typically consider only the top 10 to 50 entities, but this is insufficient for user-generated text domains where the median concept size can be substantially larger (e.g., median sizes of 121 for Tripadvisor and 205 for Wiki). Therefore, to enable a user to find all relevant entities, the ranked list should extend beyond a small fixed number and ideally cover the full concept size, reflecting the true number of correct entities associated with the concept."}
{"question_id": "3f6d76b052a19c42fdc0f3fa7a03e99d76e053d5", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "The navigation tasks require the semantic layout of unseen regions in order to be effectively solved. The proposed approach actively learns to predict semantic maps beyond the agent\u2019s current field of view, including unobserved areas, and leverages the uncertainty over these predictions to guide long-term goal selection and navigation. This ability to hallucinate semantics in unobserved regions enables the encoding of contextual semantic priors that improve navigation performance in unknown environments. Traditional methods relying solely on observed areas and explicit 3D geometric maps do not capture this uncertainty or semantic inference, limiting their effectiveness for object goal navigation tasks. Thus, incorporating semantic layout predictions of unseen regions is essential for successful navigation."}
{"question_id": "7f3ceaefa9722ecb3ce14f4c48d0191a6893f607", "paper_id": "nlpeer/F1000-22/11-222", "generation": "The lupeol and pristimerin used in this study were obtained from Cayman Chemicals (MI, USA)."}
{"question_id": "d539111f12ea7af828c1637c34c8e6fcb06f589a", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "The framework described uses an ensemble of segmentation models to estimate epistemic uncertainty by measuring the variance between the outputs of the models in the ensemble. Specifically, the epistemic uncertainty is approximated from the variance of the models' outputs, which are probability distributions over semantic classes (logits passed through a softmax). This variance reflects uncertainty over the model parameters and is used to guide active training and goal selection.\n\nThe method relies on the probabilistic interpretation of the model outputs as likelihood functions over semantic classes, enabling the use of information gain objectives based on uncertainty in the predicted semantic maps. The variance between ensemble outputs is computed in the space of predicted class probabilities (logits after softmax), which directly corresponds to the semantic segmentation predictions.\n\nApplying the same variance-based ensemble framework directly in the semantic feature space (i.e., intermediate feature representations before the final classification layer) would differ fundamentally because these features are not probability distributions and do not have a direct probabilistic interpretation. The uncertainty estimation and information gain objectives depend on the probabilistic outputs to quantify epistemic uncertainty meaningfully.\n\nTherefore, while variance in the semantic feature space could capture some form of model disagreement, it would not straightforwardly correspond to epistemic uncertainty over semantic class predictions as it does in the logit or probability space. The framework as presented is specifically designed to operate on the ensemble outputs in the semantic prediction (logit/probability) space, where variance directly relates to uncertainty in semantic class assignments.\n\nIn conclusion, the variance-based ensemble uncertainty estimation framework is most effective and appropriate in the space of semantic prediction logits or probabilities rather than in the semantic feature space. It would not work equally well in the semantic feature space without additional modifications to interpret and utilize the variance meaningfully for uncertainty estimation."}
{"question_id": "0eb6095e3dbae2dd6e1abc90265e56378f49fa1a", "paper_id": "openreview/ICLR-2022-conf/ZTsoE8G3GG", "generation": "No, when using Algorithm 1 (referred to in the context as the generative procedure of MoLeR) with larger vocabulary sizes, the model can add either an entire motif or a single atom to the partial graph in each step. The vocabulary size corresponds to the number of motifs and atom types available for addition. Increasing the vocabulary size improves performance, indicating that larger vocabularies include more motifs and atom types. However, the procedure explicitly allows adding individual atoms as well as entire motifs, so individual atoms can still be added to the partial graph regardless of vocabulary size."}
{"question_id": "3611098cfd2590d775531ef564d87617713fe8bf", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The rationale for using entity set expansion (ESE) methods on user-generated text is that user-generated text data is abundant and largely unlabeled, making it a valuable resource for mining entities necessary for various natural language processing applications such as semantic search and question answering. However, user-generated text exhibits distinctive characteristics\u2014such as a higher prevalence of multifaceted entities, non-named entities, and vague concepts\u2014that differ significantly from well-curated text like Wikipedia. These characteristics complicate entity extraction and necessitate the development and evaluation of ESE methods tailored to user-generated text. Additionally, existing benchmarks and evaluation metrics, which focus on well-formed text and well-defined concepts, do not adequately capture the challenges or performance of ESE methods in this domain. Therefore, applying and adapting ESE methods to user-generated text is essential to enable effective entity mining in low-resource, real-world settings where training data is scarce and text is less structured."}
{"question_id": "cbd5e6e55ec199de5569c76823febc8a19d28e5e", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "Yes, the \u039b-DARTS NAS method can be trained on a large-scale dataset such as ImageNet. The search was performed using a setting proposed by PC-DARTS on a model with 8 layers and 16 initial channels, sampling subsets of the ImageNet dataset for optimizing parameters. The evaluation was conducted on a larger model with 14 layers and 48 initial channels for 250 epochs. The method demonstrated improved top-1 and top-5 accuracy compared to baseline methods, including DARTS, \u03b2-DARTS, and DrNAS, indicating its effectiveness on ImageNet. Additionally, the use of forward/backward finite difference approximation reduces the search cost, making \u039b-DARTS comparable to other state-of-the-art methods in terms of computational efficiency for large-scale datasets."}
{"question_id": "d8ac040e919b01e19818a6416896dd66bd58e69d", "paper_id": "nlpeer/COLING2020/939", "generation": "The success of the path-based model, specifically the hypo2path model, can be attributed to several factors:\n\n1. **Formulation as a Sequence Generation Task:** The model treats hypernym prediction as generating the entire hypernym path in the WordNet taxonomy from the root node to the direct hypernym, rather than predicting only the direct hypernym. This more difficult objective encourages the model to learn richer representations and hierarchical relationships.\n\n2. **Use of an LSTM-based Sequence-to-Sequence Architecture with Attention:** The model employs a standard LSTM encoder-decoder with Luong-style attention, which helps maintain focus on the source hyponym during the generation of long hypernym paths, preventing the model from \"forgetting\" the input.\n\n3. **Reversing Target Sequences:** A variant of the model, hypo2path rev, reverses the order of the target hypernym path to start generation from the direct hypernym. This framing simplifies each generation step to direct hypernym prediction, which the decoder can learn more easily, improving performance.\n\n4. **Training on Entire Hypernym Paths as an Auxiliary Task:** Training to generate the full hypernym path, rather than only the direct hypernym, acts as an auxiliary task that enhances the model's ability to accurately predict direct hypernyms.\n\n5. **Model Implementation Details:** The use of a single-layer unidirectional LSTM with 256 hidden units, dropout regularization, teacher forcing during training, and the Adam optimizer with a learning rate of 0.001 contributed to effective training. Freezing the embedding layer and assigning random vectors to synsets without pretrained embeddings also supported stable learning.\n\nCollectively, these factors enabled the hypo2path model to achieve state-of-the-art performance, outperforming previous benchmarks in direct hypernym prediction."}
{"question_id": "9d285bc752521120d3b45a5b35069f1365c8f603", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "Set expansion approaches are used to construct a dictionary from customer reviews because training data in new domains is often scarce, making it challenging to manually label or curate comprehensive entity sets. By starting with a small seed set of example entities provided by an expert, set expansion methods aim to discover and rank additional entities that belong to the same semantic concept within the unlabeled review corpus. This process enhances coverage of relevant entities, enabling applications such as semantic search to effectively identify and highlight mentions of various concepts (e.g., amenities) in user-generated text. The approach leverages contextual features and patterns from the corpus or probes pre-trained language models to expand the seed set with minimal supervision, addressing the challenges posed by the informal, subjective, and non-named nature of entities commonly found in customer reviews."}
{"question_id": "06b380902968cd38bbb66c2a75d9372c2f039f2f", "paper_id": "openreview/NeurIPS-2022-dabt/in7XC5RcjEn", "generation": "A graph with an average diameter in the range of 10-50 nodes is considered sufficiently large to indicate the presence of long-range relationships because the number of nodes in a node\u2019s receptive field grows exponentially with the number of GNN layers, and a larger graph size implies a larger problem radius (r) for the learning task. When the graph size is small (e.g., average size 23-26 nodes as in ZINC or ogbg-mol* datasets), the problem radius r is effectively small, and local message passing GNNs (MP-GNNs) can learn the task well without being affected by information bottlenecks caused by oversquashing. In contrast, larger graphs with diameters around 10-50 nodes create conditions where local MP-GNNs are more likely to experience bottlenecks, thus requiring models capable of long-range interaction (LRI) to effectively propagate information across distant nodes. This separation in performance between local MP-GNNs and LRI-enabled models serves as evidence that such graph sizes and diameters are indicative of tasks requiring long-range relationships."}
{"question_id": "280960bc073f24e47cd5b63da7388c21eb12d9be", "paper_id": "openreview/ICLR-2023-conf/pWVASryOyFw", "generation": "The CNN classifier was selected for the sentiment analysis experiments because it was pre-trained for two epochs following the methodology of Liu et al. (2019), ensuring a fair comparison with prior work."}
{"question_id": "8952c8598f43e3e36131d56d62db44fded0352d3", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The improved performance of the LM-Base model compared to the CGExpan model on user-generated datasets is primarily due to the following factors:\n\n1. **Handling of Multifaceted and Vague Entities:** CGExpan penalizes entities that belong to multiple concepts (multifaceted entities) or are mentioned in diverse contexts (vague entities) because it scores each candidate entity by selecting one positive concept and multiple negative concepts. This design choice leads to sub-optimal ranking of such entities. In contrast, LM-Base, which leverages language model-based contextual representations, better accommodates entities with diverse or ambiguous contexts common in user-generated text.\n\n2. **Contextual Diversity:** CGExpan tends to retrieve entities based on frequent co-occurrence with seed concepts, which may not capture the varied and informal contexts present in user-generated text. LM-Base, by using language model embeddings, is more effective at capturing the nuanced and diverse contexts of entities in user-generated text.\n\n3. **Robustness to User-generated Text Characteristics:** User-generated text contains a higher proportion of multifaceted, vague, and non-named entities, which negatively impact the performance of state-of-the-art (SOTA) methods like CGExpan. LM-Base outperforms CGExpan on concepts with these challenging characteristics because it does not penalize ambiguous or multifaceted contexts as strongly.\n\n4. **Simpler Model Design:** While CGExpan employs complex techniques to avoid ambiguous contexts, these techniques inadvertently reduce its effectiveness on user-generated text. LM-Base\u2019s simpler, language model-based approach is more adaptable to the noisy and diverse nature of user-generated content.\n\nOverall, LM-Base\u2019s use of contextual embeddings and its tolerance for diverse entity contexts enable it to outperform CGExpan on user-generated datasets, where entities often exhibit multifacetedness, vagueness, and non-named characteristics."}
{"question_id": "c0f788c455af0bee35fb5b9cc36af6a5ec6aaf3f", "paper_id": "nlpeer/ARR-22/212dff6380418cd7c050ec757b31e6b5b65bbe922aa30ffbd5950fe7a04ca737b7c7b3d706f1cd1502d7932b61d2b7c079924793e45e437526230c1e9c0626ed", "generation": "Additional fine-tuning of CLIP was not included in the experiments because fine-tuning the last few layers of CLIP with a classification head consistently resulted in worse performance compared to using extracted features for classification with SVM. This outcome is attributed to the relatively small size of the labeled dataset, which was insufficient for effective fine-tuning of CLIP for the claim detection task."}
{"question_id": "0d68ad6ddb3ddfccd1c2d71ae7fc8a724843e891", "paper_id": "nlpeer/F1000-22/10-72", "generation": "The econometric models and economic theories used to write equations (2) through (10) are based on the Agosin & Machado (2005) theoretical model, which itself is adapted from a neoclassical investment model incorporating adjustment of capital stock and adaptive expectations of economic growth, with Foreign Direct Investment (FDI) included as a key variable. This model has been applied in studies examining the relationship between FDI and Domestic Investment (DI).\n\nEquation (1) represents the initial specification of the model, which is then re-specified in equation (2) to account for different levels of country development, recognizing that the effects of FDI on DI may differ across developing countries, economies in transition, and developed countries. This re-specification includes interaction terms between FDI, DI, and development level indicators (DVP), as well as other control variables such as growth rate (GR), trade openness (TRS), and time effects.\n\nThe short-run effects of FDI on DI for different country groups are expressed in equations (3), (4), and (5), where the coefficients \u03b2 represent the estimated parameters for developing countries, transition economies, and developed countries, respectively. Equation (6) formulates the null hypothesis for the short-run effects across all three country groups combined.\n\nThe estimation procedure employs the system Generalized Method of Moments (system-GMM) estimator to address issues of endogeneity, correlation between lagged dependent variables and error terms, and unobserved country-specific effects. This approach combines regressions in first differences and levels, using internal instruments based on lagged values of dependent and independent variables, and relies on assumptions of no second-order serial correlation and validity of overidentifying restrictions tested by the Sargan test.\n\nIn summary, the equations are derived from a neoclassical investment theory framework adapted by Agosin & Machado (2005), incorporating FDI and country development levels, and estimated using system-GMM to control for endogeneity and unobserved heterogeneity."}
{"question_id": "fd627c54f792bc91ab0db4a51e8311a23d8af60d", "paper_id": "nlpeer/ARR-22/82520f1d31572d488cfeae02712db642750fda4063e5eab8a771a000f6e17e1397ab18af73e5362e4d660b3dcc78b2260e121fdefc4c1664448e1c30066d5cb9", "generation": "For prompts 7 and 8, which did not have trait-specific scores in the original ASAP AEG dataset, the trait-specific scores were obtained from the scores provided by Mathias and Bhattacharyya (2018a)."}
{"question_id": "9d42f16a53845fd4e37c5676307657425faf1538", "paper_id": "nlpeer/ARR-22/841d64f4eb3d5e3d6ea89751ea19897bb8424acf856c9dd1574ef7d2803ff7d155df1b9cc201df8b4fc8c1984f6ca8bdfaf94036960348047e8da35b0003ae34", "generation": "Yes, it is feasible to generate a lattice graph for a large dataset. The presented algorithm restructures decoding as a best-first search combined with hypothesis recombination, enabling the compact encoding of a massive number of generation candidates into a lattice structure. This approach has been demonstrated to efficiently encode thousands of diverse, high-quality generation options in tasks such as document summarization and machine translation. The lattice representation allows for dense encoding of related outputs, reducing redundancy compared to beam search, and supports exploration of a large hypothesis space within a controlled search budget defined by the number of nodes expanded. Although current benchmarks focus on nodes expanded rather than wall clock time, the method shows promise for scaling to large datasets by efficiently managing computational resources and encoding extensive candidate sets in a single compact data structure."}
{"question_id": "fd9af00fe3f20196d71e9e364f55c157d4cd2cd3", "paper_id": "nlpeer/COLING2020/939", "generation": "To ensure that paths were not shared between the training and test sets, the validation and test sets were filtered to include only hyponym queries that were unseen anywhere in the full taxonomy paths of the training data. Additionally, hyponyms appearing as hyponyms in the training set were excluded from the test and validation sets to prevent models from merely copying. This filtering ensured that all evaluated queries were equally new to both path-based and non-path models."}
{"question_id": "f2564b011de1ce166a9e4410c3857b5a9c2496ce", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The significant performance difference between CGExpan and LM-Base on user-generated text benchmarks can be explained by the distinct ways these methods handle entities with diverse and ambiguous contexts. CGExpan scores each candidate entity by selecting one positive concept and multiple negative concepts, which tends to penalize multifaceted entities (those belonging to multiple concepts) and vague entities (those mentioned in different or ambiguous contexts). This design choice leads CGExpan to underperform on user-generated text, where such challenging characteristics are more prevalent. In contrast, LM-Base, a simpler language model-based method, better accommodates entities with diverse contexts typical of user-generated text, resulting in superior performance. Additionally, CGExpan often retrieves entities based on frequent co-occurrence with seed concepts, which can lead to incorrect entity selections in user-generated text scenarios, whereas LM-Base more effectively captures relevant contextual information. Consequently, LM-Base outperforms CGExpan on user-generated text benchmarks, despite CGExpan's superior performance on well-curated datasets."}
{"question_id": "a37fa0862f28bd4005c14092d19b86f84c30c983", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The criteria for selecting videos and classes in the study included ensuring that the activity classes in the taxonomy were gender-neutral, culturally inclusive, and friendly toward people from different socioeconomic backgrounds. The video selection process involved a diverse team of researchers from different ethnicities and genders who examined and discussed each video to ensure diversity and the absence of offensive content. Additionally, keywords from multiple languages, including English, Chinese, French, and Japanese, were used to search for videos to enhance diversification and reduce potential bias in the dataset."}
{"question_id": "c0ddf26bae180b57c24cd4b90e7a0da4a0676425", "paper_id": "openreview/ICLR-2022-conf/KTPuIsx4pmo", "generation": "A real-world pushing task involves using a UR5 robotic arm equipped with an RGB camera to push a specified target object into a designated target area, which is a pink rectangular region measuring 12cm by 12cm. The task requires the robot to identify and push the correct object within a limited number of steps (up to 50), with success defined as the target object being positioned within the center of the pink target area at the end of the trial. The input to the robot's policy consists solely of sequences of 128 \u00d7 128 RGB pixel images captured from a RealSense D455 camera, without any direct information about the robot's joint states or end-effector position. The robot outputs incremental movements of its end-effector in 3D space, constrained within specified ranges for the x and y directions and a fixed height above the table. The environment includes distractor objects placed randomly, and the task is considered more challenging in real-world conditions due to observation and action noise, as well as potential misalignment in paired data."}
{"question_id": "e26fc7a2455a2acb2de4d608a7ca7bf1c8fb62a1", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "For a neural module to be \"in charge of solving one specific subproblem\" means that there is a one-to-one and onto mapping between the subproblems and the neural modules, such that each module is responsible for addressing a distinct subproblem within the overall reinforcement learning task. Each module processes only the information necessary to solve its assigned subproblem and outputs the solution to that subproblem. This design allows all tasks requiring the same subproblem to share the corresponding module. The modules are composed sequentially to replicate the compositional structure of the problem, with each module receiving only the relevant subset of the state components needed for its subproblem, thereby isolating its function and enabling modular reuse and generalization across tasks."}
{"question_id": "5219ee2947eb66850d9df883d32c6549b914d086", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The system contains four modules, corresponding to the subproblems of processing the static object, the target object, and the agent dynamics, as described in the modular architecture for the discrete 2-D tasks. This is confirmed by the experimental results where the original (correct) number of modules is stated as four."}
{"question_id": "be6ee11df60dadea667438571e3ed15560c3cb04", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The MOMA-LRG dataset differs from the MOMA dataset in several key aspects. First, MOMA-LRG introduces a new abstraction of human activity through activity graphs that serve as a single universal representation encompassing all three hierarchical levels: activity, sub-activity, and atomic action. In contrast, MOMA only uses graphical representations at the atomic level. Second, MOMA-LRG contains an order of magnitude more annotations and longer videos from a greater variety of scenes. Third, MOMA-LRG is motivated by the limitations of video-language models (VLMs) and introduces a new annotation schema that can be easily converted from natural language to graphical annotations, enabling few-shot learning. Finally, MOMA-LRG provides a new framework (GraphVLM) to evaluate VLMs on complex activity parsing, addressing the lack of a single overarching task for evaluating VLMs on complex activity recognition present in MOMA."}
{"question_id": "e774f0bb72932f381463769b74f98a8f360db732", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "GraphVLM is a framework designed to evaluate video-language models (VLMs) on the task of activity parsing, which is defined as the generation of activity graphs. These activity graphs serve as a single universal representation of human activities encompassing multiple hierarchical levels: activity, sub-activity, and atomic action. GraphVLM incorporates an architecture that includes a video stream, a text stream, and shared video tokenizers across all three levels of the activity hierarchy. It uses two different tokenizers for entities and contexts and employs task-specific heads for each level of the MOMA hierarchy. The framework also introduces a transfer learning approach based on knowledge distillation, enabling flexible and lightweight adaptation of VLMs for activity parsing. Thus, GraphVLM operationalizes the activity graphs abstraction introduced in MOMA-LRG by providing a model-agnostic method to integrate structured knowledge from these graphs into VLMs, facilitating hierarchical video understanding and few-shot learning."}
{"question_id": "e5d8459c3ebc7cdeb1a56ddced28a7467921a917", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The videos in the MOMA-LRG dataset were collected from YouTube."}
{"question_id": "507cdeff564fe9a3d5fe258fe00eef340d135d9b", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The ethical considerations taken into account when selecting the data for the MOMA-LRG dataset included the following protocols: (1) Taxonomy selection was carefully conducted to ensure that each activity class is gender-neutral, culturally inclusive, and respectful toward people from different socioeconomic backgrounds. (2) Video selection was performed by a diverse team of researchers from different ethnicities and genders who examined and discussed each video to ensure diversity and the absence of offensive content. Additionally, keywords from multiple languages (including English, Chinese, French, and Japanese) were used to search for videos to enhance diversity and reduce potential geographic and cultural bias in the dataset."}
{"question_id": "1740b93cc1257022895a050e975d38feebe0f904", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "Yes, the activity graph is a new abstraction introduced in the MOMA-LRG dataset that serves as a single universal representation of human activities encompassing multiple levels of granularity. It captures hierarchical video understanding at three levels: activity, sub-activity, and atomic action. This abstraction enables the evaluation of models on different levels of granularity by providing (1) an activity label for the entire graph, (2) dynamic sub-activity labels with temporal boundaries and semantic classes, and (3) fine-grained atomic actions with spatial and temporal localization of entities and predicates. Thus, the activity graph facilitates a comprehensive and hierarchical approach to activity parsing beyond the atomic level focus of the original MOMA dataset."}
{"question_id": "b6cb81cf492f5369fa4051c1d2e90b05b0aa9247", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The modules were partitioned such that each neural module \\( m_i \\) is responsible for solving one specific subproblem \\( F_i \\), with a one-to-one and onto mapping from subproblems to modules. Each module receives only the subset of the state components necessary to solve its corresponding subproblem, ensuring that it outputs only the solution to that subproblem. This partitioning assumes that the overall state can be factored into module-specific components, and each module processes only its relevant input variables. For example, in the discrete 2-D tasks, separate modules were designed for static objects, target objects, and the agent, each consuming distinct input channels corresponding to their subproblems. The modules are then chained in sequence, with the output of one module combined with the input of the next, forming a modular architecture where the static object module processes static object channels first, followed by the target object module which incorporates the static object module's output, and finally the agent module which integrates the target object module's output to produce the final policy outputs."}
{"question_id": "bf76bbb77fabe1a9105b55efcd591d179958b2c6", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "Optimizing the reward with respect to the possible combinations of modules means selecting and combining a subset of pre-trained neural modules in such a way that the resulting policy maximizes the expected return for a given reinforcement learning task. This involves performing a discrete search over all possible module combinations, evaluating each combination by rolling out the corresponding policy for multiple episodes, and choosing the combination that yields the highest average reward. The process ensures minimal modification to module parameters during selection, preserving knowledge from earlier tasks while identifying the most effective functional composition of modules to solve the current task."}
{"question_id": "62968052606a9490b4add4170997a2c69f19d4ec", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "Yes, the dataset is licensed under a Creative Commons license, specifically the Attribution 4.0 International (CC BY 4.0) license."}
{"question_id": "1b206d1d36f66f3d336a33e34858567e8a593ab0", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The system employs a neural modular policy architecture designed for compositional reinforcement learning (RL) tasks. Each neural module \\( m_i \\) is responsible for solving a specific subproblem \\( F_i \\), with a one-to-one mapping between subproblems and modules. Tasks are constructed by chaining these modules in sequence, reflecting an underlying graph structure where each module processes only the state components relevant to its subproblem and passes its output to the subsequent module.\n\nFor discrete 2-D tasks, the architecture consists of three types of modules arranged sequentially: a static object module, a target object module, and an agent module. The static object module processes static object channels through convolutional blocks with ReLU activations and max pooling. The target object module preprocesses the target object channel similarly, concatenates its output with the static object module's output, and applies further convolutional processing. The agent module preprocesses the agent channel, concatenates it with the target object module's output, and passes this through separate multi-layer perceptrons (MLPs) for the actor and critic, each with a single hidden layer of 64 units and tanh activation.\n\nIn the robot manipulation domain, the architecture mirrors the 2-D task structure with three modules: obstacle, object, and robot modules. The obstacle module processes obstacle states through a single hidden tanh layer of 32 units. The object module preprocesses object states with a tanh layer of 32 units, concatenates this with the obstacle module output, and applies another tanh layer of 32 units. The robot module processes robot and goal states through two tanh layers of 64 units each, concatenates this with the object module output, and applies a linear output layer. Separate networks are used for the actor and critic, with the critic additionally receiving the action as input to the robot module.\n\nOverall, the architecture is modular and compositional, with each module receiving only the necessary subset of the state relevant to its subproblem, enabling generalization to unseen task combinations. The modular networks are constructed by chaining these modules according to the task-specific graph structure, ensuring that information flows from the initial subproblems to the final agent actuation."}
{"question_id": "a3566edd083568caf0264186c9b8e1658c31e561", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The rationale behind the design of the different modules is to assign each neural module \\( m_i \\) the responsibility of solving one specific subproblem \\( F_i \\), establishing a one-to-one and onto mapping from subproblems to modules. This modular decomposition allows all tasks requiring the solution of \\( F_i \\) to share the same module \\( m_i \\). Each module receives only the subset of the state components necessary to solve its corresponding subproblem, thereby isolating its function and output to the solution of \\( F_i \\). This design avoids brittle dependencies caused by chaining entire inputs through all modules and facilitates generalization to unseen combinations of modules in lifelong learning settings. By restricting each module's input to relevant state variables, the architecture ensures that modules focus solely on their designated subproblems, enabling modular reuse and compositionality across tasks."}
{"question_id": "6159c6e153be58a55c17f3cda104c7ebdd581acc", "paper_id": "openreview/ICLR-2023-conf/8efJYMBrNb", "generation": "The rationale for using the CS-error metric instead of SPS metrics lies in the precise definition of alignment accuracy it provides. The column score (CS) quantifies accuracy by counting the number of alignment columns in the inferred alignment that exactly match both the coordinates and the characters of the columns in the true alignment. This means that a match is only considered if the aligned positions and their characters correspond exactly, ensuring a stringent and unambiguous measure of alignment correctness. The CS-error is defined as 1.0 minus the CS-score, reflecting the level of disagreement between the inferred and true multiple sequence alignments (MSAs). This metric is normalized by the total number of columns in the true MSA, providing a score in the range [0, 1]. This approach contrasts with SPS metrics, which may not require exact coordinate matching, thus making CS-error a more rigorous and precise measure of alignment accuracy."}
{"question_id": "ae75b890d30e2879b6a6571bbc634ee4e6157e30", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "Measuring the performance of a method using mean average precision at gold-k (MAP@k_g), where k_g equals the concept size, serves to characterize the effectiveness of entity set expansion (ESE) methods in scenarios with large and variable concept sizes typical of user-generated text. This metric adapts to different concept sizes and provides an estimate of recall, which is crucial for real-world applications such as knowledge base population. Unlike fixed small k metrics (e.g., MAP@20), MAP@k_g includes more instances of multifaceted, vague, and non-named entities that are often present in user-generated text but ignored in smaller k evaluations. Therefore, MAP@k_g is particularly useful for stress testing ESE methods in settings where coverage and completeness of entity sets are important, helping practitioners assess the suitability of methods for domains involving user-generated content."}
{"question_id": "0dcfbcf7b77777639a682294aaf99c3fff25cd20", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The authors ensure that the natural language sentences produced from the \"ground truth\" activity graphs accurately describe the scene by converting all levels of the MOMA-LRG activity graph hierarchy into natural language using a graph-to-language module. At the activity level, class names are represented as nouns or prompted phrases (e.g., \"A video of [CLS_NAME]\"). At the sub-activity level, class names are expressed as descriptions in the present continuous tense, following a narration style. At the atomic action level, predicates are tagged with [src] and [trg] templates to form grammatically correct present continuous sentences that specify the interaction between entities. For example, the predicate \"touching\" is represented as \"[src] touching [trg]\", which, given entities [src]=person and [trg]=table, produces the sentence \"A person is touching the table.\" This structured templating and hierarchical conversion ensure that the generated natural language sentences accurately and explicitly describe the entities and their interactions as annotated in the activity graphs."}
{"question_id": "5c090b48e2d8b39f413f602a716b92676b7e7ba7", "paper_id": "openreview/ICLR-2023-conf/8efJYMBrNb", "generation": "The provided information does not explicitly mention whether the baseline multiple sequence alignment (MSA) methods such as T-Coffee, ClustalW, DIALIGN, MUSCLE, MAFFT, and PRANK have adjustable parameters that could be tuned to improve their performance. The discussion focuses primarily on the novel deep learning-based method BetaAlign, highlighting its ability to be trained on specific datasets and evolutionary models to capture particular evolutionary dynamics, which implies a form of parameter adjustment through training on simulated data. However, there is no direct reference to parameter tuning or adjustable settings in the baseline aligners within the given text."}
{"question_id": "86b7bff4eb8f5701bb87715221c22a2db29eaae1", "paper_id": "openreview/ICLR-2022-conf/sPIFuucA3F", "generation": "In Algorithm 1, line 4, the data tuple \\((x_t, a_t, r_t)\\) is retrieved directly from the offline dataset \\(D_n\\)."}
{"question_id": "42aec356c91c672d813f540951f0b79d9f57705f", "paper_id": "openreview/ICLR-2022-conf/oiZJwC_fyS", "generation": "DeepNN refers to a deep neural network architecture with layers of size 28 \u00d7 28, 512, 256, 128, and 10. The compression method discussed is applied to all hidden layers sequentially from the input layer to the output layer."}
{"question_id": "f5c277093cecafb8c4d4588dfbae6b5279b14a79", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "The re-annotation process described was performed on the original MultiSpanQA dataset, which contains only multiple-span answers. The expanded dataset, created by adding single-span question\u2013answer pairs and unanswerable instances sampled from NQ, was generated by applying the same preprocessing but there is no indication that it underwent the same re-annotation procedure. Therefore, re-annotation was performed only on the original multiple-span dataset, not explicitly on the expanded dataset."}
{"question_id": "61588ca196125738d21c2d191ecac13249af297d", "paper_id": "openreview/ICLR-2022-conf/oiZJwC_fyS", "generation": "Yes, the Neural Path K-means algorithm can be tested on larger networks. Experiments conducted on CIFAR datasets using CIFAR-VGG and an altered version of AlexNet adapted for CIFAR demonstrate that Neural Path K-means retains good performance on larger datasets and deeper architectures. In these experiments, it generally achieves slightly better accuracy and lower deviation than baseline pruning methods, although its performance degrades when retaining very few weights. This indicates the algorithm's applicability and effectiveness in compressing larger and deeper neural networks."}
{"question_id": "e1369f11b53bb858522bacf4bf2e9d8448dc1ef5", "paper_id": "openreview/ICLR-2022-conf/EnwCZixjSh", "generation": "It is important for the metric to be insensitive to the number of layers when evaluating rank correlations because the evaluation involves testing each metric and GIN architecture combination across multiple configurations, including variations in the number of layers (e.g., L = 4). Insensitivity to the number of layers ensures that the metric's performance and rank correlation scores are stable and consistent regardless of the specific GIN model depth used. This stability allows for reliable aggregation of rank correlation scores across different GIN configurations, random seeds, perturbations, experiments, and datasets, thereby providing an objective and robust assessment of the metric's ability to measure dissimilarity between generated and reference graph distributions."}
{"question_id": "19e742075a73b79f8d1593d22060ea47535a485a", "paper_id": "openreview/ICLR-2022-conf/8uz0EWPQIMu", "generation": "Figure 3a illustrates the Standard accuracy pattern observed across the 156 configurations experimented with, where all top-to-bottom rankings consistently yield higher accuracy than the random ranking, which in turn outperforms all bottom-to-top rankings. This pattern demonstrates the expected relationship between ranking quality and classification accuracy when using different combinations of classifiers and rankings."}
{"question_id": "b49d17a4084f479d1c1f3e920a863be370968f66", "paper_id": "nlpeer/ARR-22/3cbf91f8e2f7e62be51109688c84a2b424e6018ee3c976fbe55e5331bc9ab2f4c46491d006087aed3be35a3370c945c100bd718e2c1131de19eac3dbe93326bc", "generation": "The per-triple templates were developed manually by one of the authors using only the input data, without relying on human reference texts. The templates are single-sentence, mostly clear-cut verbalizations of the predicates. The approach involved creating a single template for each predicate, such as \"<s> plays <o>\" for the predicate \"instrument.\" The templates were hand-crafted to introduce domain knowledge efficiently while minimizing manual workload and ensuring control over the input quality for subsequent processing steps. Although the option of extracting templates from training data by delexicalizing single-triple examples was considered, it was not used due to noise in the data and the zero-shot setup constraints."}
{"question_id": "c68719740a590d79c10dfd14e5cb4e1f51938871", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "The low performance of the \"DESCRIPTION\" answer type in Table 5 is attributed to the inherent difficulty of predicting this class, as it was also the class that annotators found most challenging. This suggests that the complexity and variability of description-type answers make them harder for the model to accurately predict compared to other answer types."}
{"question_id": "5ac9e91c8d313af9a0771c5e3e46e95d2a7c2315", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "The new evaluation metric proposed in the paper is the partial match precision, recall, and F1 score for multi-span question answering. This metric measures the overlap between predicted answer spans and ground truth answer spans by treating each span as a string and computing the length of the longest common substring (LCS) between each predicted span and each ground truth span. The partial retrieved score for a prediction is defined as the LCS length divided by the length of the predicted span, and the partial relevant score for a ground truth span is the LCS length divided by the length of the ground truth span. Precision and recall are then calculated by taking the maximum partial retrieved and relevant scores across all span pairs, and the partial match F1 score is computed as the harmonic mean of these precision and recall values. This metric allows for a graded evaluation of answer overlap rather than requiring exact span matches."}
{"question_id": "7cd7d4d944b16a2603aaaf3ebb5628edd813a346", "paper_id": "nlpeer/ARR-22/3cbf91f8e2f7e62be51109688c84a2b424e6018ee3c976fbe55e5331bc9ab2f4c46491d006087aed3be35a3370c945c100bd718e2c1131de19eac3dbe93326bc", "generation": "The criteria for delimiter placement in the sentence aggregation model is based on deciding whether neighboring facts should be mentioned separately or aggregated into a single sentence. Formally, the model takes an ordered sequence of facts as input and produces a sequence of sentence delimiters \u03b4_i \u2208 {0, 1}, where \u03b4_i = 1 indicates that the neighboring facts should be separated into different sentences (delimiter placed), and \u03b4_i = 0 indicates that the facts should be aggregated and fused into a single sentence (no delimiter placed). The model classifies each separator token position between facts accordingly, using a token classification head on top of RoBERTa-large, trained on synthesized sentences where \u03b4_i = 0 corresponds to originally aggregated sentences and \u03b4_i = 1 otherwise."}
{"question_id": "8c6b063b9a5318af6557db02c0c7dbc93f8939be", "paper_id": "openreview/NeurIPS-2022-dabt/HYELrdRdJI", "generation": "The 16k real captured views in the MVP-N dataset are individual images of objects taken from multiple cameras and different poses, collected through two main collections: Collection A with 288 images per object and Collection B with 800 images per object, resulting in a total of approximately 16,000 views. These views are then sampled and combined to construct approximately 9,000 multi-view sets used for training, validation, and testing. Specifically, for the training set, 20 informative and 20 uninformative views per object are manually sampled from Collection A to create ten multi-view sets per object, each containing two to six views with at least one informative and one uninformative view. For the validation and test sets, 40 informative and 120 uninformative views per object are randomly sampled from both Collections A and B to construct 100 multi-view sets per object, each containing two to six views with only a single informative view. Thus, the multi-view sets are curated subsets of the total 16k views, designed to provide diverse and informative combinations of views for multi-view object classification tasks."}
{"question_id": "1eafcdeb90458c749f5b2e6dcdaaa06a4ba58abd", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "The phrase \"but train the detector head with \\( r(\\cdot) \\) online in a single stage\" refers to the training approach used in the F-VLM method, where the detector head is the only trainable component and is trained directly on frozen backbone features in a single training stage. Specifically, \\( r(\\cdot) \\) denotes the detector head function that processes the frozen vision and language model (VLM) features to produce detection outputs such as bounding boxes, masks, and detection scores. Training \"online in a single stage\" means that this detector head is trained end-to-end in one continuous process without requiring multiple separate training phases or additional pretraining steps. This approach contrasts with multi-stage pipelines and eliminates the need for knowledge distillation or detection-specific pretraining, simplifying the training procedure while leveraging the frozen pretrained VLM backbone."}
{"question_id": "3e6d53b8861714d6727e6f1a924eb2046baac6a7", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "When novel categories with large vocabularies are added to the set of candidate categories, the accuracy on novel categories (open-vocabulary detection) tends to be slightly compromised if the backbone is finetuned, as finetuning improves base category detection but slightly hurts novel category detection. However, increasing the feature pyramid capacity improves accuracy on base categories significantly without compromising novel category accuracy, and can even slightly improve novel category accuracy. Additionally, score fusion using geometric mean with appropriate weighting of detection and vision-language model (VLM) scores is crucial for optimal performance on novel categories. Overall, larger vocabularies and novel categories pose challenges, but architectural enhancements and score fusion strategies can mitigate accuracy degradation on novel categories."}
{"question_id": "601d6dade2b1d6724ae69aafc64a71bafd79062e", "paper_id": "openreview/NeurIPS-2022-dabt/7w-a8PYPlP", "generation": "Yes, the published code includes the algorithms used to generate the data. The data generation pipeline involves synthesizing velocity maps from three different priors (mathematical representations, natural images, and geological reservoirs) and generating seismic data via forward modeling. The forward modeling algorithm is based on finite difference methods with absorbing boundary conditions and a Ricker wavelet source function, originally implemented in MATLAB and rewritten in Python for computational efficiency and neural network compatibility. The codes and related information are available on the Github repository (https://github.com/lanl/openfwi), which ensures reproducibility of the benchmarks and includes the data generation procedures."}
{"question_id": "cd38abc68b46b12d953fddc8838eb77978963fca", "paper_id": "openreview/ICLR-2023-conf/kDEL91Dufpa", "generation": "SimCLR and DCL are defined as follows with embeddings normalized column-wise (l2 normalization):\n\n- DCL criterion:\n\\[\nL_{\\text{DCL}} = \\sum_{i=1}^N -\\log \\frac{e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau}}{\\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau}} = \\sum_{i=1}^N \\left(-\\frac{K_{\\cdot,i}^T K'_{\\cdot,i}}{\\tau} + \\log \\left(\\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau}\\right)\\right).\n\\]\n\n- SimCLR criterion:\n\\[\nL_{\\text{SimCLR}} = \\sum_{i=1}^N -\\log \\frac{e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau}}{e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau} + \\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau}} = \\sum_{i=1}^N \\left(-\\frac{K_{\\cdot,i}^T K'_{\\cdot,i}}{\\tau} + \\log \\left(e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau} + \\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau}\\right)\\right).\n\\]\n\nHere, \\(K\\) and \\(K'\\) are the normalized embedding matrices, \\(\\tau\\) is a temperature parameter, \\(N\\) is the batch size, and \\(K_{\\cdot,i}\\) denotes the \\(i\\)-th column of \\(K\\). These definitions correspond to the loss functions used in Proposition 3.1."}
{"question_id": "c908b12fc3ea26161680a836fc0ee29b02fd4e96", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "Region classification for novel categories with a large vocabulary of class names not present in the image is performed by leveraging the open-vocabulary recognition capability of F-VLM at test time. Specifically, the system uses the frozen backbone features to extract region embeddings by cropping and resizing region features from the backbone output using ROI-Align, followed by applying a VLM pooling layer. These region embeddings are then compared via cosine similarity to text embeddings of both base and novel categories, which include the large vocabulary of class names.\n\nThe classification scores for each region and category are computed by combining two sets of scores: the detection scores from the detector head (which is trained only on base categories) and the VLM scores derived from the vision-language model's region embeddings. The combination is done using a geometric mean weighted by parameters \u03b1 and \u03b2 that control the influence of VLM scores for base and novel categories, respectively. This fusion allows the system to generalize to novel categories by incorporating their text embeddings at inference time, enabling classification without prior training on those categories or their presence in the image.\n\nThus, region classification for novel categories is achieved by expanding the text embedding space to include novel class names and computing similarity-based scores on frozen region features, combined with detection scores, enabling open-vocabulary detection and recognition."}
{"question_id": "8397ceb9d91201c9f2eb30de3c87e1e8243a827d", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The conceptual model presented in Figure 2 illustrates the framework of recycling intention initiated by gamified learning. It posits that youths' recycling intention is influenced by their motivation, which includes both intrinsic motivation (such as satisfaction, happiness, or enjoyment) and extrinsic motivation (such as monetary rewards, promotion, or punishment) evoked through a gameful experience. This gameful experience also directly influences the youths' family and friends via competitive and challenging game elements. Consequently, the youths are motivated intrinsically, extrinsically, and socially, which in turn influences their peers and family members to engage in recycling. The model integrates these motivational factors and social influence as key drivers leading to recycling intention."}
{"question_id": "fcd1a1d599ae695d923bfabe4f62e5e457ca2de1", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "Yes, the time requirements for training the surrogate model significantly outweigh the benefits in terms of speeding up the evaluation of the dynamic system during inference. The highest computational demand originates from the ML training time, which is orders of magnitude larger than the inference time. However, once trained, the ML surrogate models, such as the Fourier Neural Operator (FNO), can predict solutions much more efficiently\u2014multiple orders of magnitude faster than classical numerical PDE solvers. This efficiency gain in prediction is particularly notable because the ML models eliminate stability restrictions like the Courant-Friedrichs-Lewy condition, allowing efficient inference even in challenging regimes. Thus, while training is computationally expensive, the surrogate models provide substantial speed-up during evaluation after training is complete."}
{"question_id": "f117dce3beae4a1fc909bbadebcc235634d017c0", "paper_id": "openreview/NeurIPS-2022-dabt/7w-a8PYPlP", "generation": "The seismic forward modeling applied absorbing boundary conditions."}
{"question_id": "dd594c4d0897a3c1259ae3d2d2d23859fbb96f04", "paper_id": "nlpeer/F1000-22/10-637", "generation": "The purpose of the saliva sample is to validate the smoking status of participants."}
{"question_id": "a8466adf7868015b87e7447c11f576b29d121012", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The phrase \"environment's health\" refers to the overall well-being and condition of the natural environment, which is closely related to human health aspects. It implies maintaining the environment in a state that supports ecological balance and reduces environmental hazards, thereby promoting positive environmental outcomes and safeguarding resources. Recycling is adopted as a measure to preserve this health by mitigating environmental destruction and promoting sustainable waste management."}
{"question_id": "5420a636705116e4e99d17572011f028d54a72b2", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "Prior work related to this paper has primarily used classical statistical error measures such as the root-mean-squared-error (RMSE) over the whole domain and, in some cases, PDE-motivated variants like the RMSE of the gradient. These measures focus on statistical error without fully capturing the physical properties of the underlying systems. Measures based on the physics of the system, which are proposed in this paper, have been lacking in previous benchmarks."}
{"question_id": "41338acb67bcf333b2817c15b8b74290fb3d9327", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "The provided information does not mention any downstream tasks such as natural language inference, question answering, or semantic role labeling being tested using an encoder transferred from language 1 to language 2 without any parameter updates. The focus of the study is on transfer learning from artificial languages to natural language tasks, specifically evaluating language modeling and dependency parsing tasks. There is no reference to experiments involving natural language inference, question answering, or semantic role labeling in the context of zero-shot cross-lingual transfer without parameter updates."}
{"question_id": "4b00407f8b0e58aafdaee12f078de8b313872f04", "paper_id": "openreview/ICLR-2022-conf/8uz0EWPQIMu", "generation": "Yes, the proposed alternate way to use the linear method differs from the original linear model proposed by Dalvi et al. (2019). In the original method, after training the linear probe, its weights are fed into a neuron ranking algorithm that distributes neurons equally among labels, regardless of the actual importance of neurons for each label. This can result in non-important neurons being ranked higher than important ones. The alternate method modifies this by computing, for each neuron, the mean absolute value of the weights associated with it across all labels and then sorting neurons by this value from highest to lowest. This approach does not distribute neurons equally among labels but ranks them based on the overall magnitude of their weights, which empirically provides better results and is more suitable for large label sets."}
{"question_id": "73faf71e5e746272235608cd46a8fda2b309ef70", "paper_id": "openreview/ICLR-2023-conf/tYIMtogyee", "generation": "The authors refer to tuning \"approximately\" 5 values on the QM9 dataset in the context of selecting optimal noise scales for denoising during pre-training and fine-tuning. Specifically, they tuned over a discrete set of noise values {0.005, 0.01, 0.02, 0.05, 0.1} to determine the best noise level for the HOMO energy target. This tuning process involved evaluating these roughly five candidate noise values to identify the most effective one, which was then fixed for all other targets on QM9."}
{"question_id": "3318142bc7bd1401191fcc4a9712243c0df0f1df", "paper_id": "openreview/NeurIPS-2022-dabt/HYELrdRdJI", "generation": "When high inter-class view similarity occurs, it means that views of different object classes appear very similar to each other. This similarity leads to uncertainty in assigning correct class labels to uninformative views, as these views do not contain distinctive features that clearly differentiate one class from another. Consequently, this uncertainty results in multi-view label noise, where the labels associated with uninformative views may be ambiguous or inconsistent, thereby complicating accurate classification across multiple views."}
{"question_id": "d3ce17368ff699857360c15972ad48cb235350b8", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "Setting the dimension of the word vectors and discourse vectors to 10 causes the conditional token distribution to become close to the Zipfian distribution because this dimensionality empirically balances the representation capacity of the vectors such that the resulting unigram distribution over the vocabulary, induced by the log-linear model \\( p(w|s) \\propto \\exp(\\vec{c}_s \\cdot \\vec{v}_w) \\), approximates the statistical properties of natural language token frequencies. Specifically, the interaction between the 10-dimensional discourse vector \\(\\vec{c}_s\\) (representing the sentence topic) and the 10-dimensional word vectors \\(\\vec{v}_w\\) generates non-trivial cooccurrence patterns and a token frequency distribution that aligns with Zipf\u2019s law, where word frequency is inversely proportional to rank. This dimensionality was found empirically to produce a token distribution close to the Zipfian distribution."}
{"question_id": "d7f32782ecbb103a3971b1e3a918576549b44723", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The motivation for building provably robust adversarial examples arises from the limitations of existing adversarial attack methods, which typically produce only single concrete adversarial examples whose effects can often be mitigated by current defenses. Existing approaches to robust adversarial examples are empirical and lack formal guarantees, resulting in non-uniform robustness within the adversarial regions. This non-uniformity can lead to misleading impressions of attack strength, as significant subregions may be much less robust, especially near the original input image. Provably robust adversarial examples address these issues by constructing connected input regions that are guaranteed to be adversarial for a large set of points under real-world perturbations such as pixel intensity changes and geometric transformations. This guarantee ensures uniform robustness throughout the region, providing a more reliable and effective method for generating adversarial examples that are invariant to such perturbations and more resilient against state-of-the-art defenses."}
{"question_id": "94974352b0e42eb1b459e4b85aea1ca6ddb9b713", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "Yes, the robust region proposed in this paper explicitly accounts for physical distortions in the form of geometric perturbations, such as rotations and translations, which are common real-world image transformations. The robust region is defined in the low-dimensional geometric parameter space that models these transformations, ensuring that all images within this region are classified consistently by the neural network despite such geometric changes. The paper demonstrates that these regions can be large, containing a substantial number of concrete images (e.g., underapproximations of size on the order of 10^13 for certain MNIST examples), indicating that the robust region encompasses a wide variety of physically plausible distortions that may be encountered in practice."}
{"question_id": "174eff40340ae0616a2328f75efd8cf8431b3150", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The conceptual model in Figure 2 was developed through a six-phase thematic analysis of focus group discussions (FGDs) with 29 students during the COVID-19 pandemic. Initially, the transcriptions were familiarised and annotated, followed by coding to ensure reliability. Thirty-seven codes were identified and then sorted into 13 categories, which were further grouped into four main themes based on repeated patterns and similarities. These themes\u2014engagement with the gamified recycling activity, intrinsic motivation, extrinsic motivation, and social influence\u2014were reviewed and refined to ensure alignment with the research questions. Insights and verbatim responses from participants were used to progressively construct and verify the conceptual framework, capturing the factors influencing recycling intention. This iterative process culminated in the final refinement and reporting of the model, which illustrates the flow and relationships among the identified factors leading to recycling intention."}
{"question_id": "273390ef5c449b8574f4d7b6e46f332e7152a15d", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "The intuition and motivation behind the head-to-tail method in section 3.2.3 is to model latent dependency structures in sentences by representing dependency relations between tokens. In natural language, words often have dependency relations where the presence of one word (head) is predictive of another word (tail), such as the verb \"am\" cooccurring with the pronoun \"I.\" By pairing half of the tokens as heads and the other half as tails, and ensuring these pairs appear together in sentences, the method aims to endow the sampled tokens with a dependency-based latent structure. This approach is hypothesized to induce an inductive bias in pretrained language models toward identifying relations between tokens, which is important for processing natural language. The head-to-tail pairs are arranged either in a flat dependency structure allowing crossing arcs or in a nesting dependency structure with hierarchical, non-crossing arcs, reflecting different dependency patterns observed in natural language."}
{"question_id": "ce87b952cfde98f1de69d4c860537a4d3989c67a", "paper_id": "nlpeer/F1000-22/11-9", "generation": "The study analyzed a total of 30,229 high-quality SARS-CoV-2 genome sequences with collection dates ranging from January 1, 2020, to March 21, 2021. These sequences were downloaded from the GISAID database and filtered to include only complete genomes with high coverage (<1% Ns and <0.05% unique amino acid mutations) and no unverified insertions or deletions. The geographical distribution of these sequences, along with the date range, was summarized in a table and deposited as extended data on Figshare (DOI: 10.6084/m9.figshare.19721716.v1).\n\nFor genetic nomenclature, the sequences were aligned to the reference strain NC_045512.2 obtained from the NCBI database. Eleven different coding sequences were extracted based on their genomic positions in this reference. The frequency percentage of the top 10 nonsynonymous mutations was determined in the primary lineages associated with past and present variants of concern (VOC) using data from COVID CG.\n\nAdditionally, the study included multiple sequence alignment files divided into four time periods, also available on Figshare, and used GTF and VCF files for SIFT4G analysis to annotate the SARS-CoV-2 genome sequences and single nucleotide polymorphisms (SNPs). The frequency data of the top nonsynonymous mutations in VOC lineages are presented in Table 3 of the study."}
{"question_id": "356d231752c38eb7874ebc87c5847c943c9bdf95", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "The authors did not explicitly report observing jittering in the processed videos. Instead, they noted that their PV3D model produces temporally consistent and motion plausible videos with high-quality geometry, contrasting it with baseline models that resulted in implausible motions due to non-end-to-end trainable designs. The video discriminator in PV3D also improves temporal coherence by encoding auxiliary temporal information, further supporting motion plausibility and temporal consistency."}
{"question_id": "b0767779541047ab4deb8c71f900288615ddd5a7", "paper_id": "openreview/ICLR-2022-conf/5hLP5JY9S2d", "generation": "The Vision Transformer (ViT) model demonstrates better generalization to the open-set scenario on ImageNet compared to other methods primarily due to its architectural characteristics and training behavior. Despite having a large number of parameters (86 million) and lacking convolutional inductive biases, ViT does not overfit its representation to the training classes. This property allows it to better identify semantic novelty in open-set categories. The superior open-set recognition (OSR) performance of ViT supports findings that purely attention-based vision models benefit from improved generalization capabilities. Additionally, ViT's strong closed-set performance correlates positively with its open-set performance, further contributing to its effectiveness in OSR tasks."}
{"question_id": "d88d8f911add3ead1f649741f7c22700199f9094", "paper_id": "nlpeer/ARR-22/ed81d66dc55496061fd4c97530be7b966d2e734e8435092b2c0c6764f54ea145e62f67555e480d996aebb93772e0de2877ccaad504e0f2228a1395c68d157eda", "generation": "The provided chunks do not contain any information regarding the availability of the code or dataset associated with the paper to the public."}
{"question_id": "127423c37403d9d9d34a21c17bfb33234b5f944a", "paper_id": "openreview/ICLR-2023-conf/tYIMtogyee", "generation": "Yes, the authors used a validation set to tune the hyperparameters on the QM9 dataset. They tuned hyperparameters, including the noise scale for denoising during pre-training and fine-tuning, on the HOMO target and then kept these hyperparameters fixed for all other targets. The QM9 dataset was split into training, validation, and test sets, with 10,000 examples allocated for validation."}
{"question_id": "77d9dbb3a3af5156b369e66924d4bcf14f794893", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The criteria used to evaluate \"provability\" in the paper is based on the output of a verifier \\( V \\) applied to a candidate region \\( U \\). Specifically, the verifier \\( V \\) returns a non-negative value \\( e \\) if and only if the region \\( U \\) is provably robust. Algorithm 1 relies on this verifier: it only returns a region \\( U \\) as provably robust if the verifier \\( V \\) certifies it by producing a non-negative value \\( e \\). Thus, the provability criterion is that the verifier \\( V \\) must certify the region \\( U \\) by returning a non-negative robustness measure, ensuring that the entire region is guaranteed to contain only adversarial examples."}
{"question_id": "2a76dc6fa246d4afcfe9aea7050a47aa3949d755", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "No, the camera pose is not concatenated with the latent code in the same way as the EG3D method. Instead, the PV3D model conditions the generator on pre-estimated camera pose sequences at each time instant to alleviate motion ambiguity and facilitate convergence. Specifically, the appearance code is projected together with the camera pose into an intermediate appearance code for content synthesis, and the motion code is separately encoded with timesteps into intermediate motion codes. The generator uses a camera conditioning strategy that differs from EG3D by incorporating camera poses dynamically per frame rather than sharing a single camera pose for the entire video. This approach enables modeling head rotation as camera rotation rather than deforming the 3D scene, improving multi-view consistency and temporal coherence."}
{"question_id": "a8d6ed01ff1866040e47a7082ff97ea95a6edd03", "paper_id": "egu/esurf/11-917-2023", "generation": "Yes, there is a scientific rationale for the Greenland Ice Sheet (GrIS) to start melting before the Antarctic Ice Sheet (AIS) beyond merely creating two distinct peaks in a model. This sequencing reflects possible discrepancies in the stability of the GrIS and AIS and variable hemisphere-specific climate fluctuations. The scenario where GrIS melts first, contributing to an initial sea level rise peak, followed by AIS melting later in the Last Interglacial (LIG), is consistent with interpretations of fossil reef sequences and climate variability between hemispheres. This suggests that the timing difference in ice sheet melting is grounded in differential ice sheet responses to regional climate conditions rather than being solely a modeling convenience."}
{"question_id": "46780f3f8ab86a46782f68b7ca66e5e1438afc01", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "An image's adversarial distance radius \\( R' \\) was considered \"too big\" if it was more than 33% larger than the certified radius \\( R \\) of the smoothed classifier \\( g \\). Images with such large \\( R' \\) values were excluded from the analysis to avoid trivial attacks where the image is attackable for most classes on \\( g \\). This criterion was applied during the heuristic selection of \\( R' \\) by searching for the smallest adversarial distance on the base classifier \\( f \\) where at least 10% of 500 attacks succeeded, ensuring \\( R' \\) was neither too small nor too large."}
{"question_id": "fc938634e35cc53e7b6cb50564929eabb0fc7afe", "paper_id": "egu/esurf/11-917-2023", "generation": "\"Production versus depth curves\" refer to the non-linear relationship between the growth rate of carbonate producers (such as different coral species) and water depth. These curves quantify how carbonate production rates vary as a function of depth, reflecting environmental controls like water temperature, turbidity, wave energy, and water depth itself. In the Dionisos forward stratigraphic model, each carbonate sediment class is assigned a user-defined production-versus-depth curve that specifies the rate of carbonate sediment production (e.g., in meters per million years) at different depths. These curves are used to simulate carbonate growth patterns realistically, with growth rates typically varying non-linearly with depth according to empirical data from the literature."}
{"question_id": "ec3f80fbff718abc4b5fae665cfdc994570329fb", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "No, a quantitative evaluation of the experiment on Ego4D cannot be performed based on the provided information. The experiment on Ego4D is described as a transfer detection task where F-VLM is applied without further finetuning, and the categories are provided by the user based on visual inspection of the video. The results are presented qualitatively through visualization and examples of novel categories detected, but no quantitative metrics or numerical performance evaluations (such as average precision or recall) are reported for the Ego4D dataset in the provided content."}
{"question_id": "eb715a337474694a5b2fa3212f3936f2979ff998", "paper_id": "openreview/ICLR-2023-conf/kDEL91Dufpa", "generation": "Definition 3.2 classifies methods as sample-contrastive if they minimize the criterion \\( L_c = \\|K^T K - \\text{diag}(K^T K)\\|_F^2 \\), and as dimension-contrastive if they minimize the criterion \\( L_{nc} = \\|K K^T - \\text{diag}(K K^T)\\|_F^2 \\).\n\nEvidence that different contrastive learning methods can be represented using Definition 3.2 includes:\n\n1. **Sample-contrastive methods:**\n   - SimCLR-abs/sq and DCL-sq/abs are explicitly identified as sample-contrastive methods (Proposition 3.2).\n   - The Spectral Contrastive Loss (HaoChen et al., 2021) also falls into the sample-contrastive category, as shown by its criterion:\n     \\[\n     L_{SCL} = -\\frac{2}{N} \\sum_{i=1}^N K_{\\cdot,i}^T K'_{\\cdot,i} + \\|K^T K - \\text{diag}(K^T K)\\|_F^2,\n     \\]\n     which matches the sample-contrastive criterion \\(L_c\\).\n\n2. **Dimension-contrastive methods:**\n   - Barlow Twins, VICReg, and TCR are identified as dimension-contrastive methods (Proposition 3.2).\n   - Barlow Twins\u2019 loss can be rewritten as:\n     \\[\n     L_{BT} = \\sum_{j=1}^M (1 - (K K'^T)_{j,j})^2 + \\lambda \\|K K^T - \\text{diag}(K K^T)\\|_F^2,\n     \\]\n     where the second term corresponds exactly to the dimension-contrastive criterion \\(L_{nc}\\).\n   - VICReg\u2019s covariance penalty term is:\n     \\[\n     c(K) = \\sum_{i \\neq j} \\text{Cov}(K)^2_{i,j} = \\|K K^T - \\text{diag}(K K^T)\\|_F^2 = L_{nc},\n     \\]\n     confirming VICReg as a dimension-contrastive method.\n\n3. **DCL-sq/abs:**\n   - Their criteria involve a repulsive force term that approximates minimizing the maximum similarity between negative pairs, which aligns with minimizing the sample-contrastive criterion \\(L_c\\).\n\n4. **Equivalence and linkage:**\n   - Theorem 3.3 establishes that the sample-contrastive criterion \\(L_c\\) and the dimension-contrastive criterion \\(L_{nc}\\) are equivalent up to row and column normalization of the embedding matrix \\(K\\), further unifying these methods under the framework of Definition 3.2.\n\nThus, multiple well-known contrastive and non-contrastive self-supervised learning methods can be represented and analyzed within the framework of Definition 3.2 by associating them with either the sample-contrastive criterion \\(L_c\\) or the dimension-contrastive criterion \\(L_{nc}\\)."}
{"question_id": "fef7c2c7a69370b666710942e3d931819e7bc02d", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "The accuracy of the surrogate model directly impacts the accuracy of the downstream task, such as inverse problem solving or temporal prediction. For example, in inverse problems where the initial condition is estimated by minimizing the prediction error of the surrogate over a time horizon, a more accurate surrogate leads to better estimation results. The Fourier Neural Operator (FNO), which provides the best prediction accuracy among baseline surrogate models, outperforms U-Net in inverse problem setups, indicating that higher surrogate accuracy improves downstream task performance.\n\nHowever, surrogate accuracy can degrade under certain conditions, such as decreasing diffusion coefficients causing strong discontinuities or decreasing force terms leading to smaller solution scales. This degradation results in increased prediction errors, which in turn reduce the reliability of downstream tasks. Additionally, temporal extrapolation experiments show that errors increase monotonically beyond the training time horizon, indicating that surrogate models with limited temporal accuracy cannot reliably predict future states, thereby limiting downstream task accuracy over extended time frames.\n\nIn summary, higher surrogate accuracy, particularly in capturing spatial and temporal dependencies of PDEs, is crucial for improving the accuracy and reliability of downstream tasks such as inverse inference and temporal extrapolation. Conversely, limitations in surrogate accuracy due to problem complexity or training constraints directly reduce downstream task performance."}
{"question_id": "bc6f50621da1a65a6e46211a4f48751a6da35304", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The paper defines the \"adversarial L2 ball\" around an adversarial example \\(\\tilde{x}\\) in terms of the radius \\(R_{\\text{adv}}\\), which is the radius of an \\(L_2\\) ball centered at \\(\\tilde{x}\\) that is certified by the CERTIFY procedure to have the same adversarial class as \\(\\tilde{x}\\) on the smoothed classifier \\(g\\). Formally, this means that for all points within this \\(L_2\\) ball around \\(\\tilde{x}\\), the smoothed classifier \\(g\\) predicts the same adversarial class as at \\(\\tilde{x}\\).\n\nThis definition differs from the typical expectation that all points within an adversarial ball around the original input \\(x\\) should have a different classification from \\(x\\). Instead, here the ball is centered at the adversarial example \\(\\tilde{x}\\), and the requirement is that all points within this ball share the same adversarial class as \\(\\tilde{x}\\). Intuitively, a larger \\(R_{\\text{adv}}\\) indicates that the smoothed classifier is less confident about the original class at \\(\\tilde{x}\\), since a larger region around \\(\\tilde{x}\\) is consistently classified as the adversarial class, reflecting a stronger attack on \\(g\\)."}
{"question_id": "f87855d105235224a2584b0e0716b794ef647705", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "Yes, the authors took specific steps to address jittering caused by alignment in the processed videos. They noted that the single-frame face alignment technique introduces temporal inconsistency, which can manifest as jittering. To mitigate this, they applied a low-pass Gaussian filter to smooth the estimated facial keypoints before warping the images. Additionally, after estimating camera poses for each video frame using deep face reconstruction, they again applied the low-pass Gaussian filter to smooth the camera pose results, further reducing temporal inconsistency and jittering in the videos."}
{"question_id": "70ee52925bfa4ed21c8798964ec74fcdd0f9845f", "paper_id": "egu/esurf/11-917-2023", "generation": "No, the subaerial erosion rate is not equivalent to the maximum weathering rate. The maximum weathering rate refers to the highest rate of weathering that can occur under subaerial exposure, which is influenced by environmental factors such as precipitation, porosity, and groundwater chemistry. In the Dionisos model, the dissolution rate is incorporated within the maximum subaerial erosion rate, indicating that erosion includes weathering processes but also encompasses mechanical removal of material. Additionally, the model uses user-defined maximum subaerial weathering rates to approximate erosion, with a lower weathering rate employed to limit overestimation of subaerial weathering. Thus, subaerial erosion rate includes but is not limited to the maximum weathering rate."}
{"question_id": "9be6e9b8ef3f3db6f29bafd74243978f78f2f657", "paper_id": "egu/esurf/11-917-2023", "generation": "The phrase \"actual exposed MIS 5e facies are lacking\" refers to the observation that, although fossil MIS 5e reef facies are physically present and exposed at locations such as Lembetabe, the corresponding MIS 5e sequences are sometimes absent or underrepresented in the synthetic well logs produced by the Dionisos stratigraphic model. This discrepancy arises due to a \"preservation bias\" in the model, where multi-meter sections of lithology deposited during the Last Interglacial (LIG, MIS 5e) are removed by erosion and sediment transport processes in the intervening millennia before the present. Consequently, the model outputs may not fully capture the preserved extent of MIS 5e reef facies observed in the field, despite their actual exposure above modern sea level."}
{"question_id": "e7b7d480aa1076d06dccb8bcb2b7f2b1fd0f9c87", "paper_id": "egu/esurf/11-917-2023", "generation": "The model presented in the study has spatial limitations and is primarily applied to a tectonically stable coastline in southwestern Madagascar. While it provides valuable insights into the sensitivity of the Last Interglacial (LIG) geological record and melt patterns, its direct application to other geographical areas, especially those with more complex glacial isostatic adjustment (GIA) and tectonic settings, requires further investigation. The study suggests that additional efforts should be made to test the model in locations with different GIA and tectonic characteristics and where relative sea level (RSL) curves similar to the scenarios explored have been described. Therefore, the model cannot be easily applied to other regions without adaptation and further validation."}
{"question_id": "7e53c05206cc77e9d6e3b28338d1c85336543244", "paper_id": "egu/esd/14-81-2023", "generation": "The authors determine the accuracy of the CMIP6 climate models in simulating the processes by including a large number of models and multiple members per model to cover a wide range of physically plausible climate responses. They use multiple members per model to account for within-model internal variability and multiple models to account for across-model variability and uncertainty. Additionally, they utilize long piControl simulations (450-year control runs for 10 out of 13 models, totaling 4500 years of unforced data) to sample a representative distribution of unforced trends. This approach increases confidence in assessing whether forced trends lie outside the likely range of unforced trends, thereby evaluating the models' ability to simulate climate processes accurately."}
{"question_id": "eeba4b725b3f8ad526cddce373ff444a591594c9", "paper_id": "nlpeer/ARR-22/df4051328ee57291cf600f10ef67af6872fcef0deb3ff7323b89142b68beb16ea1fbe09c44be8ccca24fe30d67a1e6dbfb715a77384c30fbeb37c362c25c743c", "generation": "The difference in performance between the DICTA test set and the new test set, particularly in the character accuracy (CHA) and word accuracy (WOR) scores, can be explained by the composition and diversity of the test sets. The DICTA test set is relatively small and non-diverse, consisting of 22 documents all originating from a single source\u2014Hebrew Wikipedia articles. In contrast, the new test set is larger and more diverse, including texts from eleven different sources such as high-quality Wikipedia articles, edited news stories, and user-generated blog posts, totaling roughly 3.5 times more tokens than DICTA.\n\nThis increased diversity and size in the new test set likely introduce more linguistic variability, including a wider range of vocabulary, styles, and possibly more out-of-vocabulary (OOV) words. Such variability can lead to lower CHA and WOR scores because the system encounters more challenging cases, including foreign names, terms, and unseen combinations of prefix clitics and suffix possessive markers that are not present or less frequent in the DICTA test set. Additionally, some OOV words in the new test set may not appear in dictionaries or training data, further complicating accurate diacritization.\n\nTherefore, the more homogeneous and limited nature of the DICTA test set allows for higher CHA and WOR scores, while the new test set's greater diversity and complexity result in comparatively lower scores."}
{"question_id": "911fa2b76fba7e5ae58fb8322c5940c42acbd848", "paper_id": "openreview/ICLR-2023-conf/xYlJRpzZtsY", "generation": "The proposed taxonomy of generic reasoning errors is comprehensive in that it covers a broad spectrum of error types encountered in step-by-step reasoning generated by large language models (LLMs). It includes nine distinct error categories that address both overall reasoning chain quality and fine-grained step-level inconsistencies. These error types encompass grammar, factuality, hallucination, redundancy, repetition, missing steps, coherency, commonsense, and arithmetic errors. The taxonomy was developed through manual analysis of LLM-generated explanations across diverse reasoning tasks, including logical inference and commonsense reasoning, and was validated using both diagnostic datasets with synthetically perturbed errors and human-annotated datasets. The metrics designed to evaluate these errors are generic, operate on natural language rationales, and consider alignment with the input context and generated explanations. Although the authors acknowledge that the taxonomy may not cover all possible reasoning errors, its design and application demonstrate a broad and systematic approach to capturing a wide range of reasoning errors, supporting its comprehensiveness."}
{"question_id": "de2dc4d1f8b898e5b34a256294729fe7b46f6fda", "paper_id": "openreview/ICLR-2023-conf/ZrEbzL9eQ3W", "generation": "The board games studied use the following win conditions:\n\n- In Connect Four, players win by connecting four of their tokens in a line.\n- In Pentago, players win by connecting a line of five tokens, with each turn ending with a rotation of one of the four quadrants of the board."}
{"question_id": "6c9381de277251ad2ce40cd39b94a872cbc4126e", "paper_id": "egu/esd/14-81-2023", "generation": "The authors' conclusion about the accuracy of the CMIP6 climate models in simulating precipitation changes is not explicitly based on the agreement between observed data and model predictions in terms of residual variability. Instead, the study emphasizes the use of multiple models and ensemble members to capture a wide range of physically plausible climate responses and to account for both within-model internal variability and across-model variability and uncertainty. The inclusion of long unforced control simulations (piControl) is used to sample a representative distribution of unforced trends, thereby increasing confidence in assessing whether forced trends lie outside the likely range of unforced variability. The detection and attribution of anthropogenically forced changes in mean and extreme precipitation are robust despite observational disagreement on the magnitude of change, but there is no direct statement that the accuracy assessment is based on residual variability agreement."}
{"question_id": "5b14dc7213f8e7181d9bf848cef4fb79a7b1ad10", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The performance of scenario generation algorithms is evaluated using the following metrics:\n\n1. Collision Rate (CR): The average number of collisions occurring in scenarios generated by the algorithm before scenario selection.\n\n2. Collision Rate after Scenario Selection (S-CR): The collision rate measured after selecting scenarios, used to assess the effectiveness of the scenario generation algorithm in producing safety-critical scenarios.\n\n3. Overall Score (OS): A composite metric reflecting the safety-critical scenario generation capability of the algorithm before scenario selection.\n\n4. Overall Score after Scenario Selection (S-OS): The overall score measured after scenario selection, indicating the quality of the generated scenarios in terms of safety-criticality.\n\n5. Selection Rate (SR): The rate at which scenarios generated by the algorithm are selected across different autonomous driving (AD) agents, used to evaluate the transferability of the generation algorithms.\n\nThese metrics collectively measure the effectiveness, safety-criticality, and transferability of scenario generation algorithms."}
{"question_id": "4006c4e45f822e15f9ccc27df354f3bcd8298509", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "Using alignment for video preprocessing, as opposed to using a fixed cropping window, involves detecting facial landmarks for each frame and applying image warping and smoothing techniques to achieve consistent alignment across frames. This approach can introduce temporal inconsistency due to frame-by-frame landmark detection and camera pose estimation. To mitigate this, low-pass Gaussian filtering is applied to smooth keypoints and camera poses before warping and alignment. Additionally, alignment based on a parametric 3D face model (3DMM) enforces consistent positioning of facial features (e.g., nose keypoint) in the world coordinate system, which facilitates training of 3D image generative models.\n\nIn contrast, a fixed cropping window does not account for variations in face position or pose across frames, potentially leading to misalignment and less accurate modeling of 3D geometry. The alignment process, despite introducing some temporal inconsistency that requires smoothing, enables better geometric consistency and camera pose estimation, which are critical for 3D-aware video generation. Therefore, alignment improves the quality of 3D geometry representation and camera pose estimation in video preprocessing, whereas fixed cropping windows lack this capability and may result in poorer 3D consistency."}
{"question_id": "9373d254f956bcbffe53a9ba10531f5102ecdb83", "paper_id": "openreview/ICLR-2023-conf/dSYoPjM5J_W", "generation": "The authors justify the claim that gradient-based attacks cause distribution shift between training and testing data by demonstrating that these attacks predominantly perturb the local structure around training nodes rather than uniformly across the graph. Empirical evidence shows that gradient-based methods, such as MetaAttack and PGD, generate adversarial modifications concentrated near training nodes, increasing the distribution discrepancy between training and testing sets. This is supported by the observation that MetaAttack adaptively adjusts its attack focus based on the training set size, attacking training nodes when the training set is small and shifting towards testing nodes as the training set grows, thereby effectively enlarging the distribution shift.\n\nTheoretical support is provided by Theorem 4.1, which states that in a k-regular, homophilous graph with node features sampled from class-dependent normal distributions, a smaller training set size leads to a larger distribution shift when heterophily edges are inserted into the training set. This explains why gradient-based attacks targeting training nodes are more effective when the training set is small.\n\nFurthermore, the authors formalize distribution shift in graph adversarial attacks as the Kullback-Leibler divergence between the conditional feature distributions of training and testing nodes, showing that structural perturbations alter these conditional distributions differently for training and testing sets. Gradient-based attacks increase this divergence by selectively perturbing the training set's local structure, causing the model trained on clean data to fit a biased distribution.\n\nFinally, the adaptivity of MetaAttack arises from its use of meta gradients, which allows it to adjust the location of adversarial edges dynamically, unlike other gradient-based methods that consistently focus on training nodes. This adaptivity enhances the attack's ability to induce distribution shift, thereby justifying the claim that gradient-based attacks are responsible for the observed distribution shift between training and testing data in adversarial graph attacks."}
{"question_id": "ff4b45b2af4e13f58512fc1783fc12dd129feb6f", "paper_id": "egu/esurf/11-917-2023", "generation": "The model incorporates wave erosion indirectly through the characterization of the nearshore wave environment and the use of weathering rates that account for marine conditions. Specifically, the Dionisos model calculates sediment transport using a diffusion equation and approximates hydrodynamic impacts by extracting wave energy and refraction parameters from global wave hindcast data. Marine erosion is quantified by applying a maximum weathering rate of 100 m Myr\u207b\u00b9 to represent mechanical and biological erosion under marine conditions. Additionally, the model includes subaerial weathering rates and dissolution rates, with dissolution incorporated within the subaerial erosion rate. However, the model does not explicitly solve detailed hydrodynamics or wave erosion processes but uses these approximations to represent wave-related erosion effects at the model grid resolution."}
{"question_id": "df9756e054d7db2937ebc51e1ed8477345e57387", "paper_id": "nlpeer/COLING2020/341", "generation": "The logical atom \"nsubj:noun-verb-obj:noun\" represents a double-link property corresponding to the relative frequency of a syntactic construction in which a verb (head) has two dependents: a nominal subject (nsubj) that is a noun and an object (obj) that is also a noun. This atom quantifies how often this specific configuration\u2014verb with a noun subject and a noun object\u2014occurs in a given language, as extracted from Universal Dependencies treebanks."}
{"question_id": "42b9bcc5c85c3d4087d4f57791f953fa732fc625", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The rationale for selecting the scenarios is to identify those with high safety risk and high transferability across different autonomous driving (AD) algorithms. Specifically, after generating raw testing scenarios using multiple scenario generation algorithms, all scenarios are tested on four AD algorithms with a basic observation type. Scenarios that cause collisions in at least two of these algorithms are selected. This selection process ensures that the chosen scenarios have high collision rates, indicating high risk levels, and demonstrate effectiveness in inducing safety-critical conditions across multiple AD algorithms. Consequently, this improves both the effectiveness and efficiency of AD evaluation by focusing on scenarios that are more challenging and broadly applicable for safety testing."}
{"question_id": "cbb83b653ecc965d0b930f4f016e4ff93c485696", "paper_id": "openreview/ICLR-2023-conf/HnSceSzlfrY", "generation": "RPM-Random performs poorly in pure cooperation scenarios because it samples policies without using ranks, leading to unstable performance and large variance in evaluation returns. In pure cooperation tasks (PC 1-3), this random sampling results in low returns, indicating that the absence of rank-based policy selection hinders stable coordination. In contrast, in the Prisoner's Dilemma scenarios (PD 1-3), although RPM-Random still underperforms compared to RPM with ranked sampling, the impact is less severe, and it achieves relatively higher returns. This suggests that the value of \u03c8 and the rank-based sampling mechanism are critical for stable and effective policy selection in pure cooperation tasks, where coordination is essential, whereas the Prisoner's Dilemma scenarios are less sensitive to the lack of ranking in policy sampling."}
{"question_id": "7c6f77a64467e8275e381a36386d66650b13e832", "paper_id": "nlpeer/F1000-22/11-404", "generation": "The term \"AES\" (aging effect of smiling) specifically refers to the phenomenon that smiling faces are estimated to be older than neutral faces in direct age estimations. It does not refer to the retrospective estimation where a smiling face group is estimated to be younger; rather, the direction of the effect changes depending on the method of estimation, with AES denoting the direct estimation effect of smiling faces appearing older."}
{"question_id": "1818a27b73310c09fb736d5e044187417bda0dbc", "paper_id": "nlpeer/ARR-22/eeda0f4ff6c7aef2647481ec15f8ade5a9b32ec32b643e6918a0b8d0ffa1ba44a3f2b9171ba4ba44d5c0faf3199e3bc5f0f740ee31cf48cb966493a96f35ca71", "generation": "The dataset was annotated by domain experts, including a hired linguist who participated in the annotation process. Initially, three annotators annotated the data, and subsequently, one of the annotators, identified as a linguist, annotated job postings in larger batches. Thus, the annotation team included both domain expertise and linguistic expertise."}
{"question_id": "b16ae6d142599eafe257ac83cdf206be914a7a7e", "paper_id": "openreview/ICLR-2023-conf/tyZ1ChGZIKO", "generation": "The Multi-branch Dynamic Selective Frequency module (MDSF) and the Multi-branch Compact Selective Frequency module (MCSF) are concatenated in Figure 1 to combine their complementary strengths for image restoration. MDSF dynamically decouples feature maps into different frequency components using learnable filters and applies channel-wise attention to select the most informative frequency parts locally. In contrast, MCSF enlarges the receptive field by employing multi-scale average pooling operations (global and window-based) to capture more global frequency information efficiently without convolution layers. Concatenating MDSF and MCSF allows the network to leverage both dynamic, local frequency selection and multi-scale, global frequency recalibration, thereby enhancing the recovery of informative frequency components across different spatial scales and improving restoration performance."}
{"question_id": "d07dca8f8e126c43dacdaf145ec4103ef25400f5", "paper_id": "nlpeer/PeerRead-ACL2017/561", "generation": "Not having access to pre-trained embeddings would significantly reduce the performance of the method. The pre-trained bidirectional language model (LM) embeddings provide context-sensitive representations learned from large unlabeled corpora, which encode both semantic and syntactic roles of words in context. These embeddings improve sequence tagging performance by supplementing the task-specific RNN, which is trained only on labeled data. Experiments show that removing the task-specific RNN and relying solely on LM embeddings results in a substantial drop in performance (to 88.17 F1), well below the baseline. This indicates that the LM embeddings alone are insufficient and that the combination with pre-trained embeddings is crucial for achieving state-of-the-art results. Furthermore, the addition of LM embeddings yields consistent improvements over baselines without them, including in scenarios with both small and large labeled datasets. Therefore, without pre-trained embeddings, the method would lose the key advantage of leveraging large-scale unlabeled data, leading to notably poorer performance."}
{"question_id": "aab380aaa605fffff2d765c9cb058cfc03ee1729", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The rationale for only keeping scenarios that cause collisions with at least two algorithms is to ensure that the selected testing scenarios have high transferability across different autonomous driving (AD) algorithms and exhibit high risk levels. This selection criterion improves both the effectiveness and efficiency of AD evaluation by focusing on scenarios that are consistently safety-critical for multiple AD algorithms, thereby providing a more robust and generalizable assessment of AD system safety."}
{"question_id": "6d0c861407de2db08718ca55a383b59284a8e223", "paper_id": "nlpeer/PeerRead-CONLL2016/129", "generation": "The number of selected in-domain sentences is determined by the performance on held-out data."}
{"question_id": "9505c56639fea265e46601d12575e9d9715b9e7a", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "The variational approximation of the latent variable \\( c \\) given the query and support sets \\( D_s^j \\) and \\( x_q^{0:T} \\) is modeled as\n\n\\[\nq_{\\zeta}(c \\mid D_s^j \\cup x_q^{0:T}),\n\\]\n\nwhere the same meta set-embedding model parameterized by \\(\\zeta\\) is used by augmenting the support set \\( D_s^j \\) with the query series \\( x_q^{0:T} \\). This approximation shares the meta-model form used for the prior \\( p_{\\zeta}(c \\mid D_s^j) \\), which is a Gaussian distribution parameterized by the embedding of the support set."}
{"question_id": "69d95e16f2f308754136f8e2245592ca5497ff5f", "paper_id": "openreview/ICLR-2023-conf/xYlJRpzZtsY", "generation": "In the ROSCOE training process, the model is trained using triplets consisting of a reference reasoning step, a positive hypothesis reasoning step, and a hard-negative hypothesis reasoning step. Specifically, the pairs used are:\n\n- A positive pair: the context and the reference reasoning step (denoted as (s, r))\n- A hard-negative pair: the context and a perturbed reference reasoning step (denoted as (s, h))\n\nThe training objective is to minimize the cross-entropy loss with in-batch negatives by maximizing the similarity between the context and the reference reasoning step (positive pair) and minimizing the similarity between the context and the perturbed reference step (hard-negative pair)."}
{"question_id": "876d1ffd9379695a117eb81936e7bb2b0ffb1e9d", "paper_id": "openreview/NeurIPS-2022-dabt/TscdNx8udf5", "generation": "The baseline algorithms, including PID, MPC, and EMPC, were tuned by varying either the weights or the horizon, or both, until closed-loop trajectories with little to no overshoot and fast response times were obtained."}
{"question_id": "83065c1670e7bbe6476efd1959f21480a4f3bf69", "paper_id": "nlpeer/ARR-22/eeda0f4ff6c7aef2647481ec15f8ade5a9b32ec32b643e6918a0b8d0ffa1ba44a3f2b9171ba4ba44d5c0faf3199e3bc5f0f740ee31cf48cb966493a96f35ca71", "generation": "In this work, the distinction between \"skill,\" \"knowledge,\" and \"attitude\" is based on the ESCO taxonomy definitions and their application to job postings:\n\n- **Knowledge** is defined as the outcome of assimilating information through learning, encompassing the body of facts, principles, theories, and practices related to a field of work or study. Knowledge components correspond to hard skills, such as knowing a programming language like Python.\n\n- **Skill** refers to the ability to apply knowledge and use know-how to complete tasks and solve problems. Skills represent the application of knowledge components to specific tasks and are considered skill components.\n\n- **Attitude** corresponds to soft skills and is regarded as part of skill components. Attitudes involve the ability to use knowledge, skills, and personal, social, and/or methodological abilities in work or study situations.\n\nThus, hard skills are primarily knowledge components, while soft skills are attitudes, both of which fall under skill components when applied. The work prioritizes annotating skills over knowledge and skills over attitudes when overlaps occur."}
{"question_id": "d096c58eea777208cfd4ba272dac018b8a808d6c", "paper_id": "openreview/NeurIPS-2022-dabt/TscdNx8udf5", "generation": "The simulation environments were selected to model real-world industrial manufacturing processes with high fidelity, covering a wide range of manufacturing scenarios including beer fermentation, atropine production, penicillin manufacturing, monoclonal antibodies production, and a continuous stirred tank simulation. These environments enable testing of advanced reinforcement learning algorithms in controlled settings without safety concerns. The rationale is to bridge the gap between deep reinforcement learning research and industrial manufacturing by providing realistic, validated simulations based on published dynamic models and industrial data, facilitating research and benchmarking of reinforcement learning methods for process control in manufacturing."}
{"question_id": "aae1c73c7b0de5fb88e34c245782e2ecb4dcb24d", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "The dataset is not balanced across different classes. Both up-steps and down-steps have slightly higher frequency than the other classes, which reflects a typical scenario in most of the urban areas sampled."}
{"question_id": "3546db32608ccae0b45d96e051b10a8967437d6f", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "Only one single person was employed to wear the sensor and take photos for all samples collection in the survey."}
{"question_id": "f5fe5047a045ce5a97066fd72458d8951c846342", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "A total of 27 3D printed replicas were used in the survey. These consisted of 3 replicas each for 9 distinct types of surface discontinuity identified around the targeted urban areas."}
{"question_id": "cfbd6962220a29ddfda999443b628e02ebd2d79b", "paper_id": "egu/esurf/11-849-2023", "generation": "The model and discussion do not explicitly address the effect of slope orientation or aspect (such as north-facing slopes) on solar radiation absorption and glacier distribution in the Elwha versus Quinault valleys. Instead, the asymmetry in glacier extent between the two basins is primarily attributed to differences in precipitation gradients, valley geometry, and associated ice elevation feedbacks. Cloudiness patterns related to orographic precipitation and rain shadow effects influence solar radiation and local air temperatures, with the Elwha valley being less cloudy and potentially receiving more solar radiation, which could enhance glacier melt. However, the influence of slope orientation on solar radiation absorption and glacier distribution is not specifically considered or discussed in the model or results."}
{"question_id": "f2e744ebd60bf15d94cd1b9a5cdc7db9f0c4ad93", "paper_id": "openreview/ICLR-2023-conf/ZrEbzL9eQ3W", "generation": "The playing strength of agents is evaluated by restricting them using a fixed number of MCTS (Monte Carlo Tree Search) steps per move rather than by time limits. Specifically, all agents use the same number of MCTS simulations per move at test time, equal to the number used during training, which is 300 MCTS steps. This approach allows tailoring inference-time compute by scaling the number of MCTS steps inversely with forward-pass compute costs, but the evaluation itself is based on fixed MCTS iteration counts rather than explicit time constraints."}
{"question_id": "836969164a688341782ffa72b87f1348ba1ee4ac", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "A total of 27 3D models were used in the pre-collection stage of the data collection process. This comprised 3 replicas each for 9 distinct types of surface discontinuities identified around the targeted urban areas."}
{"question_id": "e232c66d9986ff1ac6f532437bd94f0b71a44ca5", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "The dataset was collected from ten different locations within the Klang Valley urban area in Malaysia, selected based on judgmental sampling to target areas with a high frequency of surface discontinuities relevant to blind and low vision (BLV) navigation. However, the dataset is limited to these specific Malaysian urban environments and does not encompass the broader diversity of surface conditions found in other regions or nations. The authors acknowledge that there is still much to be done to expand the dataset to represent more diverse regions beyond the sampled locations. Therefore, while the ten locations provide a focused representation of surface discontinuities in the targeted urban areas, they are not sufficient to represent the full variety of surfaces in urban environments more generally."}
{"question_id": "2f75586071f2de4ab14810c7f2bd7f7b4e143fb6", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "Popular meta-learning frameworks such as MAML and Probabilistic MAML are not considered in the experiments because their application to sequential latent variable models (SLVMs) encounters significant challenges related to stability and convergence. Specifically, issues such as vanishing gradients over the complex computation graph of SLVMs make the extension of MAML to these models non-trivial and problematic. This difficulty in training stability and convergence has been noted in the literature and was observed during attempts to apply optimization-based meta-learning methods like MAML to models such as DKF and GRU-res."}
{"question_id": "e7cb5933a3df86f543cb36cb77b3f41cd7ad4021", "paper_id": "nlpeer/COLING2020/1367", "generation": "A rule-based approach, such as the use of finite-state transducers (FSTs) for verb conjugation in Indigenous languages, has the advantage of not requiring large corpora for training, which is critical given that most Indigenous languages lack sufficiently large text corpora to produce accurate statistical or neural models. This makes rule-based methods more suitable for languages with limited data availability, allowing the development of effective language software without relying on extensive datasets."}
{"question_id": "d250649b5c73368021f92321b3d59f4c1d3c762f", "paper_id": "openreview/ICLR-2022-conf/iulEMLYh1uR", "generation": "The msec/example values in Figures 1 and 2 were computed using TPU-V3 hardware."}
{"question_id": "5c790742a803e76547d117cf4a77434d1737b5b1", "paper_id": "egu/esurf/11-849-2023", "generation": "Besides temperature and precipitation, several other factors could influence the distribution of glaciers in a given area:\n\n1. **Precipitation Type:** The form of precipitation (snow versus rain) affects glacier mass balance. Rain does not contribute to glacier accumulation, so areas with a higher proportion of rain relative to snow may have reduced glacier growth.\n\n2. **Cloudiness:** Cloud cover modulates the amount of solar radiation reaching the glacier surface, influencing local air temperatures and melt rates. Spatial patterns in cloudiness, such as those caused by orographic lifting and rain shadows, can create differential melting conditions across a mountain range.\n\n3. **Humidity:** Variations in humidity can affect glacier mass balance by influencing the lapse rate and sublimation rates. Lower humidity, especially on the leeward side of mountains due to descending airflows, can increase the lapse rate and enhance melting.\n\n4. **Sublimation:** The direct loss of ice to the atmosphere without melting can vary spatially and impact glacier mass balance.\n\n5. **Valley Geometry and Topography:** The shape and cross-sectional area of valleys influence glacier thickness and elevation feedbacks. Narrowing valleys can cause glacier thickening and elevation increases, which in turn affect accumulation and growth.\n\n6. **Melt Efficiency:** Differences in the efficiency of melting, potentially influenced by local climate or topographic shading, can limit or enhance glacier growth.\n\n7. **Topographic Shading and Aspect:** Variability in shading due to terrain orientation affects solar radiation receipt and melting patterns.\n\nThese factors interact with temperature and precipitation to determine glacier extent and behavior in complex ways."}
{"question_id": "130985a6f0c94e81204c5a5014faa6017dc2a328", "paper_id": "egu/esd/14-1261-2023", "generation": "The problem under investigation pertains to genuinely nonlinear wave dynamics rather than being limited to a weakly nonlinear context. The study extends previous analyses based on weakly nonlinear theory by employing a fully nonlinear wavemaker model that admits higher-order wave components and captures strong nonlinearities. This fully nonlinear model allows for amplitude dispersion, nonlinear wave\u2013wave interactions, and solitary wave propagation, which go beyond the applicability of weakly nonlinear approaches. It provides a more accurate estimation of the phase-averaged wave velocity field and time-mean velocity field, enabling improved evaluation of wave-induced vertical mixing and mass-transport velocities. Thus, the investigation addresses strong nonlinearity in wave-induced mixing rather than a weakly nonlinear approximation."}
{"question_id": "1fd3a7fbc973d1042fee519c269028acdbb1ccec", "paper_id": "nlpeer/COLING2020/341", "generation": "The antecedent is included in the sum in equation (6) because the single-link property represents the relative frequency of a construction involving one head and one dependent, and the sum accounts for all possible directions of the dependency relation between the head and dependent. Specifically, the single-link property \\( v_\\ell \\text{noun-amod:adj} \\) is calculated as the sum of the frequencies of both \"NOUN ADJ amod\" and \"ADJ NOUN amod\" constructions in language \\(\\ell\\). This summation reflects the total relative frequency of the amod relation regardless of direction.\n\nIn contrast, the implication \"verb-nsubj:noun-obj:noun \u21d2 nsubj:noun-verb-obj:noun\" would represent a directional implication between two specific double-link properties, which involve constructions with one head and two dependents. The single-link property summation in (6) is necessary to capture the overall frequency of a relation without restricting to a particular direction, whereas the implication in question would be a directional logical relation between more complex constructions. Thus, the sum in (6) includes the antecedent to represent the total relative frequency of the single-link property comprehensively."}
{"question_id": "064bd1dff89282732ddcf6c71a98975792d8b3d4", "paper_id": "openreview/ICLR-2023-conf/6xXtM8bFFJ", "generation": "The two-time-scale algorithm, specifically the simSGDA-RR and altSGDA-RR methods, can be extended to the mini-batch setting by partitioning the total number of components \\( n \\) into mini-batches of size \\( b \\geq 1 \\), where \\( n = bq \\) for some integer \\( q \\geq 1 \\). If \\( n \\) is not an exact multiple of \\( b \\), the last mini-batch can have size \\( s \\leq b \\), with \\( n = b(q-1) + s \\).\n\nIn this mini-batch extension, at each epoch \\( k \\), the indices of the components are uniformly randomly shuffled to form a permutation \\( \\sigma_k \\). The mini-batches \\( B_{k,t} \\) for \\( t \\in [q] \\) are defined as the sets of indices:\n\\[\nB_{k,t} := \\{ \\sigma_k(j) : b(t-1) < j \\leq bt, j \\in [n] \\}.\n\\]\n\nThe updates for the variables \\( x \\) and \\( y \\) proceed iteratively over these mini-batches within each epoch. For iteration \\( t \\) in epoch \\( k \\), the updates are:\n\n- For \\( x \\):\n\\[\nx_{k,t} = x_{k,t-1} - \\frac{\\alpha}{b} \\sum_{i \\in B_{k,t}} \\nabla_1 f_i(x_{k,t-1}; y_{k,t-1}).\n\\]\n\n- For \\( y \\), the update depends on the variant:\n  - In simultaneous SGDA-RR (simSGDA-RR):\n  \\[\n  y_{k,t} = y_{k,t-1} + \\frac{\\beta}{b} \\sum_{i \\in B_{k,t}} \\nabla_2 f_i(x_{k,t-1}; y_{k,t-1}).\n  \\]\n  - In alternating SGDA-RR (altSGDA-RR):\n  \\[\n  y_{k,t} = y_{k,t-1} + \\frac{\\beta}{b} \\sum_{i \\in B_{k,t}} \\nabla_2 f_i(x_{k,t}; y_{k,t-1}).\n  \\]\n\nAfter completing all \\( q \\) mini-batch updates within epoch \\( k \\), the initial points for the next epoch \\( k+1 \\) are set as:\n\\[\n(x_{k+1,0}, y_{k+1,0}) = (x_{k,q}, y_{k,q}).\n\\]\n\nThis mini-batch extension preserves the two-time-scale nature by maintaining separate step sizes \\( \\alpha \\) and \\( \\beta \\) for \\( x \\) and \\( y \\), respectively, and by performing updates over mini-batches sampled without replacement within each epoch. The analysis assumes that the mini-batches are mutually disjoint within an epoch, which differs from other mini-batch sampling methods where mini-batches may overlap.\n\nThus, the two-time-scale SGDA-RR algorithms are generalized to the mini-batch setting by replacing single-component gradient updates with averaged gradients over mini-batches, while maintaining the shuffling and update structure per epoch."}
{"question_id": "6610ad96e462f49d6d8f20fee0cdc6dd8a70175a", "paper_id": "egu/esd/14-81-2023", "generation": "If ridge regression (RR)-based fingerprint construction is not applied, the detection method would be more prone to overfitting and produce unphysical coefficient fingerprints characterized by high positive coefficients offset by adjacent negative coefficients. This would reduce the homogeneity and smoothness of the fingerprint, making it less representative of the spatial scales of precipitation change patterns. Consequently, the generalisability of the fingerprint would decrease, leading to poorer performance when applied to observational data not seen during training. Without RR regularisation, the signal-to-noise ratio (SNR) would be lower because the fingerprint would not optimally emphasize regions with higher signal relative to internal variability. RR regularisation optimizes the regression coefficient pattern to maximize the SNR across models, improving robustness and interpretability of the forced response detection. Therefore, omitting RR-based fingerprint construction would reduce the SNR and the robustness of forced change detection."}
{"question_id": "85afe8245083d99893657bc1eeadbcefa12dbf59", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "\"1 dB in PSNR\" refers to an improvement of one decibel in the Peak Signal-to-Noise Ratio, which is a common metric used to measure the quality of lossy compression methods, particularly in image compression. An increase of 1 dB in PSNR indicates a higher fidelity or lower distortion in the compressed image compared to the original, implying better compression performance."}
{"question_id": "add8f70fda4a981fbac7e3f41f938eeecd3ccd4d", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The first kind of rule in Section 4 is called \"Markovian\" because the calculation of the next state probability depends only on the current state probability, without considering previously visited edges. In other words, a random walk under Markovian constraints is performed solely based on the current state, making the process memoryless with respect to the path history. This contrasts with non-Markovian constraints, which require recording previous visited edges to ensure satisfaction."}
{"question_id": "09c36735b520089d4936e6966157cb10f8f1ed0e", "paper_id": "openreview/ICLR-2022-conf/iulEMLYh1uR", "generation": "Yes, when scaling the depth or width of the Vision Transformer (ViT) model, all other hyperparameters are kept fixed based on the default values given by the referenced papers."}
{"question_id": "006b4d78ff2835159d4e1f745a3f9c4f41fe8351", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The Markovian assumption in equation (6) is valid under the condition that the calculation of the next state probability depends only on the current state probability, without consideration of previously visited edges. In the described framework, the random walk is performed under Markovian constraints that include body predicates and temporal relations between the query interval and each body interval. These constraints ensure that the adjacency matrix operators \\( M_{i,CM_i} \\) encode transitions based solely on the current step's relation type and temporal relation with the query interval.\n\nSince the starting entity \\( e_s \\), relation type \\( r \\), and query interval \\( I \\) remain fixed throughout the random walk, the transition probabilities at each step depend only on the current entity and the Markovian constraints defined by the relation and temporal filtering operators. This satisfies the Markovian property, as the next state is determined solely by the current state and the fixed constraints, without requiring memory of the full path history.\n\nNon-Markovian constraints, which involve pairwise temporal relations between body intervals, are handled separately by filtering the random walks after the Markovian random walk process. Thus, the Markovian assumption holds for the random walk process described in equation (6), given the fixed relation type and starting time, with non-Markovian constraints applied subsequently as a filtering step."}
{"question_id": "587b947b50e65e3caa8174633245ab39edbdb0f0", "paper_id": "egu/esurf/11-33-2023", "generation": "The authors did not conduct analyses using detrended data to isolate the effect of the temperature increase. Instead, they performed transient model simulations from 1951 to 2019 to analyze the effect of ground stratigraphies and drainage conditions on the transient response of ground temperatures and ice tables to climate warming, using observed air temperature increases during this period. There is no mention of detrending the data to isolate temperature effects; rather, the simulations incorporate the actual warming trend in the forcing data."}
{"question_id": "317ee5566b85c3b36699add3f268020579e8b718", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The drawbacks of using statistical learning methods such as StreamLearner and TLogic include:\n\n1. They estimate rule confidence by simply counting the number of rule groundings and body groundings, which leads to independent rule learning that ignores interactions between different rules from the same positive example. This means the confidence of some rules may be enhanced or diminished without considering their interdependencies.\n\n2. These methods cannot handle the similarity between different rules. For example, a reliable rule may imply that a similar rule with the same predicates but slightly different temporal patterns should also have high confidence, but statistical methods may assign low confidence to such infrequent rules in the dataset.\n\n3. Their temporal constraints are fixed and based on timestamps rather than intervals, which limits their flexibility and impairs their ability to learn temporal constraints effectively, especially on interval-based temporal knowledge graphs where temporal relations are more complex.\n\n4. The performance of these timestamp-based statistical methods on interval-based temporal knowledge graphs is not demonstrated, indicating potential limitations in handling interval-based temporal information."}
{"question_id": "cf91a671c7d2248b716cf143ff64e032fed4681e", "paper_id": "nlpeer/ARR-22/27046f6633d116b03e48eb94976921b46a86a976ddd2199ef76c90820a95e0d85406daa5ebc9d7b12359157d20f3bd0025e1cbc2c126bb2506f5e83e93f435b5", "generation": "The layer normalization approximation incurs the most significant performance reduction among the approximation components in THE-X. Specifically, it causes an average performance drop of 1.08% on the GLUE benchmark. In token-level tasks such as named entity recognition (NER), the layer norm approximation leads to a notable performance decrease, with an F1 score reduction of 1.9%. This is attributed to the need for more detailed attention score patterns in token-level tasks. The approximation error from layer normalization tends to accumulate as the transformer model stacks more layers, further impacting performance. Additionally, challenges such as attention overflow before normalization complicate the approximation, requiring regularization techniques to mitigate performance degradation. Overall, the layer norm approximation is a critical factor affecting THE-X's performance across different tasks."}
{"question_id": "7110b14e5ab532a6273415a059f6808204376ee6", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "The sequential neural process (SNP) setting is not included as a comparison because SNPs are originally designed for supervised learning of a regression function over time rather than for forecasting. The work extends SNP to realize a meta-version of the sequential latent variable model (SLVM) formulation as a counterpart to be compared with the presented meta-SLVM, but the original SNP formulation does not directly address the forecasting task targeted in this study."}
{"question_id": "27642536b6affc6438fc0e5a5b2ce6b2d5208309", "paper_id": "egu/esd/14-1261-2023", "generation": "The study applies to both nearshore and offshore environments. The numerical model is designed to cover a limited range of the dimensionless parameter kh from 0.5 to 2.0, which includes shallow-water (nearshore) conditions and can be directly compared with previous work. Additionally, the model framework may be modified to cover deep-water (offshore) conditions and pseudo-random ocean waves in large periodic domains, enabling analysis of wave-induced vertical mixing processes in offshore environments. However, deep-water cases with kh > \u03c0 were omitted in the present study due to complete mixing effects within the analyzed time frame. Thus, while the primary focus is on nearshore dynamics, the approach is adaptable to offshore conditions as well."}
{"question_id": "95d0f3eec5444caddab3df7e45aa31db81cabef8", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The operator \\( M_i,CM_i \\in \\{0,1\\}^{|E| \\times |E|} \\) related to step \\( i \\) under corresponding Markovian constraints \\( CM_i = \\{P_i, TR_{i,l+1}\\} \\) represents the adjacency matrix of the knowledge graph under these Markovian constraints. Specifically, the \\((x,y)\\) entry of \\( M_i,CM_i \\), denoted as \\((M_i,CM_i)_{x,y}\\), is defined as the maximum over all facts \\( F \\in F_{y,x} \\) of the product of filtering operators for the predicate \\( P_i \\) and the temporal relation \\( TR_{i,l+1} \\):\n\n\\[\n(M_i,CM_i)_{x,y} = \\max_{F \\in F_{y,x}} f_{P_i}(F) \\cdot f_{TR_{i,l+1}}(F, (e_s, r, ?, I))\n\\]\n\nThis operator encodes whether there exists a fact from entity \\( e_y \\) to entity \\( e_x \\) that satisfies the Markovian constraints at step \\( i \\). It is used to perform constrained random walks on the temporal knowledge graph by iteratively multiplying indicator vectors by these adjacency matrices to find all paths between entities that satisfy the Markovian constraints."}
{"question_id": "916923a8909ab06632f575b7f36db3ac70642419", "paper_id": "egu/esd/14-1261-2023", "generation": "The molecular diffusivity \u03ba_m is given as 1.4 \u00d7 10^(-7) m\u00b2/s. The wave-induced diffusivity \u03ba_v(z) is calculated by the formula:\n\n\u03ba_v(z) = \u03b1 a\u00b3 k \u03c3 [sinh\u00b2(k(z + h)) cosh(k(z + h))] / sinh\u00b3(kh),\n\nwhere \u03b1 = 0.002 is a dimensionless coefficient, a is the wave amplitude, k is the wave number, \u03c3 is the angular frequency, z is the vertical coordinate, and h is the water depth.\n\nFrom the numerical test cases, the wave-induced diffusivity \u03ba_v at the surface can be significantly larger than the molecular diffusivity \u03ba_m. For example, in Table 1 (referenced in the results), \u03ba_v values at the surface range from approximately 0.49 to 128.9 (units consistent with diffusivity), which are orders of magnitude greater than \u03ba_m = 1.4 \u00d7 10^(-7) m\u00b2/s.\n\nTherefore, the wave-induced diffusivity \u03ba_v is substantially larger than the molecular diffusivity \u03ba_m, indicating that wave-induced mixing dominates over molecular diffusion in the studied conditions."}
{"question_id": "9dbf9a9e3f0cc960065255b48616ad3b79759600", "paper_id": "openreview/ICLR-2023-conf/tyZ1ChGZIKO", "generation": "The kernel size of the different decouplers in the Multi-branch Dynamic Selective Frequency (MDSF) module is determined by splitting the input feature map along the channel dimension into multiple groups, where each group is processed by a decoupler with a distinct filter size. Specifically, MDSF applies different filter sizes to these split feature parts to provide various local receptive fields. The filters are learnable low-pass filters generated dynamically for each group, and the corresponding high-pass filters are obtained by subtracting the low-pass filter from an identity kernel. This multi-branch design with varied kernel sizes enables the module to decompose features into separate frequency components dynamically and locally, enhancing frequency diversity and selection."}
{"question_id": "08ee038d964c18feafe50974403477b69a786d82", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The variables and functions in equations (8) to (13) have the following dimensions:\n\n- The parameters \\( h \\), \\( w \\), and \\( b \\) are matrices or vectors with dimensions related to the number of relations \\(|R|\\):\n  - For pairwise relations, \\( h \\), \\( w \\), and \\( b \\) are in \\(\\mathbb{R}^{|R| \\times |R|}\\).\n  - For single relations, \\( h \\), \\( w \\), and \\( b \\) are in \\(\\mathbb{R}^{|R|}\\).\n\n- Specifically:\n  - \\( h_{\\text{order}, i} \\), \\( w_{\\text{order}, i} \\), and \\( b_{\\text{order}, i} \\) for \\( i = 1, 2, 3 \\) are in \\(\\mathbb{R}^{|R| \\times |R|}\\).\n  - \\( h_{\\text{pair}, i} \\), \\( w_{\\text{pair}, i} \\), and \\( b_{\\text{pair}, i} \\) for \\( i = 1, 2, 3 \\) are in \\(\\mathbb{R}^{|R| \\times |R|}\\).\n  - \\( h_{\\text{rec}, i} \\), \\( w_{\\text{rec}, i} \\), and \\( b_{\\text{rec}, i} \\) for \\( i = 1, 2 \\) are in \\(\\mathbb{R}^{|R|}\\).\n\n- The functions \\(\\varphi_{\\text{order}, i}(e_c; h_{\\text{order}, i}, w_{\\text{order}, i}, b_{\\text{order}, i})\\), \\(\\varphi_{\\text{pair}, i}(e_c; h_{\\text{pair}, i}, w_{\\text{pair}, i}, b_{\\text{pair}, i})\\), and \\(\\varphi_{\\text{rec}, i}(e_c; h_{\\text{rec}, i}, w_{\\text{rec}, i}, b_{\\text{rec}, i})\\) output values computed using these parameters with the corresponding dimensions as above.\n\nThus, the dimension of the variables and functions in equations (8) to (13) are either \\(\\mathbb{R}^{|R|}\\) for single-relation parameters or \\(\\mathbb{R}^{|R| \\times |R|}\\) for pairwise-relation parameters."}
{"question_id": "8192e96a224224e5fc15e03019d0ac65253d1492", "paper_id": "nlpeer/F1000-22/10-838", "generation": "The provided chunks do not contain any information regarding a \"range\" parameter or its purpose. Therefore, no answer about the purpose of the \"range\" parameter can be given based on the available content."}
{"question_id": "3b1176248b0cfc5fb6e1786bb4007f98aa2ac210", "paper_id": "openreview/NeurIPS-2022-dabt/UrAYT2QwOX8", "generation": "The first and second disparities are induced through controlling the generative model sampling, depending on the group and label, respectively."}
{"question_id": "d5b246ec5a8edcc34e88a24fb9bfd7d313572647", "paper_id": "openreview/ICLR-2022-conf/pjqqxepwoMy", "generation": "The target of return \\( v^{tar}_t \\) in equation (1) can be estimated by any value learning algorithm such as TD(0), Peng\u2019s Q(\u03bb), or other methods based on Bellman equations. It can also be obtained using Monte-Carlo return if available. In the implementation described, double Q-learning with dueling architecture is employed to compute \\( v^{tar}_t \\) due to its effectiveness and simplicity."}
{"question_id": "8f0ed4f134911b593527e3793459b6d55faf5923", "paper_id": "openreview/NeurIPS-2022-dabt/-VyJim9UBxQ", "generation": "The copyright for the images used in the paper is held by the Reddit users who originally posted the photos. The dataset complies with Reddit's User Agreement and API terms of use, which state that user photos, text, and videos (\"User Content\") are owned by the users and not by Reddit. The dataset does not modify the original content and provides tools to access the data while respecting the rights of the content owners."}
{"question_id": "9776571072ba250ab654c2a326bd48a527e61213", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "Yes, the proposed framework Meta-EGN can potentially be extended to other applications or tasks. The discussion on limitations suggests that while Meta-EGN and EGN perform well on problems like maximum clique (MC) that require more local assignments, their performance may be limited on combinatorial optimization (CO) problems requiring more global assignments, such as minimum vertex cover (MVC). The authors propose as future work the modification of Meta-EGN to better handle CO problems that require global assignments and to improve other advanced Monte Carlo algorithms beyond the maximum independent set (MIS) problem on random regular graphs (RRGs). This indicates that the framework is adaptable and can be extended to enhance other CO problems and algorithms."}
{"question_id": "207945518935728931a4b020daa416e8fc8f1cda", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The relaxations in Table 2 follow the detailed derivation approach of Karalias & Loukas (2020) and Wang et al. (2022), as explicitly stated in the supplementary implementation details. The loss function relaxation for the maximum clique (MC) problem, for example, is derived following the corresponding case study in Karalias & Loukas (2020). Thus, the relaxations presented in Table 2 are not newly derived in this work but are based on previously established formulations from these prior studies."}
{"question_id": "bc784ef2841eb98841522b97ab75da7a7106b99c", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The term \"fine-tuning timing for classical solver\" refers to the computational time allowed or used by a classical optimization solver (such as Gurobi) to find a solution within a specified time limit. In the experimental results table, this is indicated by entries like \"Gurobi9.5 ( \u2264 0.25s)\", \"Gurobi9.5 ( \u2264 0.50s)\", \"Gurobi9.5 ( \u2264 1.00s)\", and \"Gurobi9.5 ( \u2264 2.00s)\", where the number in parentheses denotes the maximum allowed solver runtime in seconds. This timing constraint controls how long the classical solver is permitted to run to obtain a solution, effectively representing the solver's fine-tuning or optimization duration."}
{"question_id": "7603a58573eeaabda0e22ca42e407dd44c83bd3e", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "Action frames are syntactic and semantic structures that represent the core components of an action involving a verb and its arguments. They are derived by extracting elements such as the subject, verb, direct object, and prepositional object from dependency parses of sentences, and then mapping these elements to semantic roles: the subject to an agent, the direct object to a theme, and the prepositional object to a goal. For frames involving prepositional phrases, separate frames are created for each preposition based on corpus statistics. Action frames capture the different contexts or relations in which a verb can appear, reflecting the multiple possible frame relations associated with a single verb according to frame semantics. These frames serve as the basis for modeling the physical implications and relative knowledge of actions and objects."}
{"question_id": "ebd7cf5f3adbc674bd5ce88a563f3fea990873a9", "paper_id": "openreview/NeurIPS-2022-dabt/UrAYT2QwOX8", "generation": "The rationale for training on the first six months of data and validating on the last two months is that this temporal splitting strategy aligns with common practice in the fraud detection domain, where more recent data tends to be more representative of the data distribution encountered when models are deployed in production. This approach ensures that the training data reflects historical patterns, while the validation data captures more current trends, allowing for a realistic assessment of model performance in a dynamic environment."}
{"question_id": "53c8c64ae66712791ba2a355e4cc97a262c61acc", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "The provided text does not explicitly define the rate-distortion function \\( R(D) \\) for probability distributions where the expectation is undefined, such as the Cauchy distribution. The definitions and results given assume the existence of expectations, for example, the distortion constraint is expressed as an expectation \\( E[\\rho(X,Y)] \\), and the rate-distortion function \\( R(D) \\) is defined through minimization of mutual information subject to an expected distortion constraint.\n\nSpecifically, the rate-distortion function \\( R(D) \\) is defined as the infimum of the mutual information \\( I(X;Y) \\) over all conditional distributions \\( Q_{Y|X} \\) such that the expected distortion \\( E_{P_X Q_{Y|X}}[\\rho(X,Y)] \\leq D \\). This formulation inherently requires the expectation of the distortion measure to be well-defined and finite.\n\nNo alternative definition or treatment is provided in the text for cases where the expectation is undefined, such as for heavy-tailed distributions like the Cauchy distribution. Therefore, within the scope of the given material, the rate-distortion function is not defined for distributions where the expectation of the distortion measure does not exist."}
{"question_id": "a9dce4895de5aac10ec1c0b1da92de90c47582f9", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The training time to convergence for the EGN and Meta-EGN models varies depending on the dataset and problem. According to Table 9, the wall clock training times for EGN range from approximately 46 minutes to 733 minutes, while Meta-EGN requires about two to three times longer, ranging from approximately 102 minutes to 1089 minutes. Specifically, for example, on the Twitter dataset, EGN took 46 minutes and 50 seconds, whereas Meta-EGN took 101 minutes and 55 seconds. On the RB500 dataset, EGN took 128 minutes and 39 seconds, and Meta-EGN took 282 minutes and 15 seconds. Thus, the pretraining duration for EGN and Meta-EGN models spans from under one hour to over 12 hours depending on the dataset, with Meta-EGN consistently requiring roughly two to three times more training time than EGN."}
{"question_id": "b4885c9ebb178f8daa578cef0c857a1de41d8d54", "paper_id": "nlpeer/ARR-22/43e8458544e18304f6aa2c1aededf974877f088dd2ccd7d53ef71eb59e54567c0572f715ecdcbaf04c39206950c3f5628198e61ebc5ec09fa1ba0a559102f923", "generation": "The location of NLP conferences significantly influences the diversity of participation. Hosting conferences in different geographic locations encourages attendance from local and nearby researchers, thereby increasing the diversity of participants. This effect is particularly pronounced in low-income countries, where researchers show a stronger preference for attending local events. Conferences held in diverse locations reduce the need for long-distance travel, which not only lowers carbon emissions but also facilitates broader regional participation. Events such as LREC and RANLP are noted for attracting audiences from many countries, demonstrating higher diversity for relatively low travel distances. Conversely, conferences concentrated in a single region tend to limit diversity, as researchers from other regions must undertake longer journeys, which may reduce their participation. Overall, distributing conferences globally fosters increased participation diversity by enabling more researchers from various regions to attend without extensive travel."}
{"question_id": "1be49494b9a1c964df99b3dabe0af0bfdc970713", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The methodology described focuses on meta learning for unsupervised learning in combinatorial optimization (CO) by searching for a good initialization that can be quickly fine-tuned for each individual problem instance. It treats each training instance as a pseudo-test instance and optimizes the initialization to enable effective one-step gradient descent fine-tuning on new instances. This approach is implemented via a meta learning pipeline based on MAML, which is designed to achieve fast adaptation to new tasks with limited fine-tuning steps.\n\nWhile the methodology is framed in the context of combinatorial optimization problems, the core principle\u2014learning a model initialization that facilitates rapid adaptation to new tasks or instances\u2014is fundamentally aligned with the objectives of multi-task learning. Meta learning, particularly MAML, is a general framework originally proposed to enable fast adaptation across multiple tasks by learning shared initial parameters.\n\nTherefore, the methodology can be conceptually applied to multi-task learning scenarios, as it aims to learn a shared initialization that can be efficiently fine-tuned for each specific task or instance. The training objective and optimization strategy are compatible with multi-task learning settings where tasks correspond to different problem instances or domains requiring task-specific adaptation."}
{"question_id": "33ac8263606098fca8bcdc4746cd3f4235387b26", "paper_id": "openreview/ICLR-2022-conf/0jP2n0YFmKG", "generation": "The new method, Graph Parallelism, demonstrates superior scaling efficiency and performance compared to existing distributed training methods such as pure data parallelism and pipeline model parallelism. Specifically, combining graph parallelism with 32-way data parallelism on 256 GPUs achieves a scaling efficiency of 47% for the largest model, which is competitive given the model size and number of GPUs. Pure data parallel training with 32 GPUs attains 75% scaling efficiency only for the smallest model, indicating limitations due to network communication and load imbalance. Furthermore, graph parallelism outperforms pipeline parallelism for the considered models because it evenly distributes nodes, edges, and triplets across GPUs, overcoming load balancing challenges inherent in pipeline parallelism. The combination of graph and pipeline parallelism also shows complementary benefits. In terms of raw computational throughput, the largest model running with graph parallelism on 256 GPUs sustains 3.5 PetaFLOPs, and on a single GPU, the baseline GemNet-T model achieves 32 TeraFLOPs, about 25% of the theoretical peak. These results indicate that graph parallelism enables faster and more efficient training of very large GNNs compared to existing data and model parallelism approaches."}
{"question_id": "6de0c620431f72ce5a6331d7dde1b8df91e24936", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "The criteria for determining which knowledge dimensions to annotate for each frame involve crowdsourcing human judgments on the general relationship that the arguments of the frame exhibit with respect to all considered knowledge dimensions (such as size, weight, strength, rigidness, and speed). Crowd workers are presented with a frame template (e.g., \"x threw y\") and asked to rate the relative relationship between the arguments along each attribute by selecting from the options: x > a y, x < a y, x \u2243 a y, or no general relation. Only frame/attribute combinations that achieve at least two-thirds agreement among workers and are not predominantly rated as having no relation are retained for annotation. This process ensures that the annotated knowledge dimensions for each frame reflect reliable and generalizable human judgments about the relative physical attributes implied by the frame."}
{"question_id": "413aa7a24c99874e0aae31b569348cc6c4e39b14", "paper_id": "nlpeer/F1000-22/10-170", "generation": "The recommendations based on the views expressed on coordination, preparation, and decision-makers include the following:\n\n1. Engage the right kind of professionals in pandemic management, including public health professionals, infectious disease epidemiologists, health policy and systems experts, medical anthropologists, health economists, health communication experts, laboratory scientists, and relevant clinicians, to ensure a science-based professional response.\n\n2. Implement a dedicated public health career track within the health sector to strengthen pandemic preparedness and management.\n\n3. Improve coordination by ensuring tactful decentralization and multisectoral collaboration involving different relevant ministries, government departments, non-governmental actors, religious leaders, cultural activists, the private sector, political parties, community groups, and individuals.\n\n4. Enhance coordination between civil administration and health system personnel at district and sub-district levels to ramp up the COVID-19 response effectively.\n\n5. Ensure transparency in data and decision-making processes, provide correct and contextually appropriate information through trusted community messengers, and tailor communication by scientifically oriented social and behavior change communication experts.\n\n6. Address miscommunication by establishing clear media guidelines for service providers and correcting misinformation regarding evidence-based treatment protocols.\n\n7. Strengthen regulation and accountability in the health sector by curbing corruption, dissolving unholy syndicates, enforcing punitive actions against wrongdoers, and regulating the private sector for cost and quality.\n\n8. Provide adequate training, personal protective equipment (PPE), medical equipment, workplace security, and motivational support to service providers, and involve them in decision-making processes.\n\n9. Ensure political will, good governance, and an evidence-based scientific approach underpin pandemic management efforts.\n\n10. Avoid decisions driven by non-scientific considerations, such as terminating lockdowns for religious holidays despite ongoing outbreaks, by prioritizing expert public health advice.\n\nThese recommendations aim to address the identified shortcomings in pandemic preparedness, coordination, and decision-making to improve the health system's response to COVID-19 and future public health emergencies."}
{"question_id": "21f851f7058a46d8f9904d493b82811edf3aa8f3", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "There is no mention or discussion of connections between this work and existing research on learning data weighting using multi-arm bandits. The paper focuses on a weighted training algorithm called TAWT for cross-task learning, which adaptively assigns weights on tasks or samples to minimize a representation-based task distance between source and target tasks. The weighting strategy is theoretically principled and distinct from other weighting schemes such as importance sampling in domain adaptation. The work is also differentiated from curriculum learning and heuristic weighting strategies in multi-task learning, but no reference or comparison to multi-arm bandit approaches for data weighting is made."}
{"question_id": "1c5c6447a6539cda6bd264b7c657e95e6c314829", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "Yes, the proposed algorithms can be applied to other classes of probability distributions beyond the Gaussian distribution and the \"banana\" source. They have been successfully applied to real-world data sources such as particle physics data (e.g., Z-boson decay dataset with 16-dimensional vectors) and speech data, where improved sandwich bounds on the rate-distortion function were obtained compared to Gaussian sources. Additionally, the algorithms have been used on high-dimensional GAN-generated images and natural images, demonstrating applicability to complex, structured data beyond simple Gaussian models."}
{"question_id": "92b04f60c27edb89dcdc8dcd575bcb9872f0e307", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "Yes, some incorrectly-classified actions and objects are ambiguous for humans. For example, in the error analysis, the phrase \u201cShe caught the runner in first\u201d is correctly interpreted as \u201cshe > speed runner,\u201d but crowd workers chose the sense related to \u201cShe caught the baseball,\u201d where the relation is reversed (\u201cshe < speed baseball\u201d). This illustrates polysemy causing ambiguity. Additionally, underspecified frames, such as \u201cPERSON stopped the fly with {the jar / a swatter}\u201d versus \u201cPERSON stopped their car with the brake,\u201d show that different interpretations by crowd workers reflect complex underlying physics not modeled by the system, contributing to ambiguity. Furthermore, nonsensical comparisons like \u201cHe drove the car for work\u201d highlight cases where the argument is not a concrete object, adding to interpretive difficulty. Thus, the errors often arise from ambiguous or polysemous uses of verbs and objects that are challenging even for human annotators."}
{"question_id": "ddbf5cd6168ece49006281d9def64503e3610f0f", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The provided excerpts do not contain any explicit statement or assumption labeled as \"assumption b\" regarding the nature of \\(\\phi\\), nor do they specify whether \\(\\phi\\) must be a vector. The theoretical framework and proofs discuss \\(\\phi\\) as an element of a function class \\(\\Phi\\), and optimization problems are formulated over \\(\\phi \\in \\Phi\\), but no indication is given that \\(\\phi\\) is required to be a vector. Instead, \\(\\phi\\) appears to be a function or mapping, as it is composed with functions \\(f_t\\) and applied to inputs \\(x_{ti}\\).\n\nTherefore, there is no evidence in the provided content that \\(\\phi\\) must be a vector under any assumption, including assumption b. The formulation treats \\(\\phi\\) as a function in a function space \\(\\Phi\\), without restriction to vector form."}
{"question_id": "04e9ce2f786d7603574645eafe3bfe6e1a603190", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The choice of mirror descent in the proposed Target-Aware Weighted Training (TAWT) method is not strictly necessary. While mirror descent is used in the update rule for the weights \u03c9 because it is a canonical generalization of Euclidean gradient descent to gradient descent on the probability simplex, other optimization methods such as projected gradient descent can also be employed. The update rule using mirror descent has an intuitive interpretation related to adjusting weights along directions where the target and source tasks are more similar, but the method allows for alternative optimization techniques to be used in place of mirror descent."}
{"question_id": "748b3e8ce8fe5bb6fa899f962211e41d18c30cae", "paper_id": "openreview/NeurIPS-2022-dabt/wPEXGTzZJt", "generation": "The bounds presented in Table 1 refer to the performance limits established by baseline models in the evaluation of visual search models. The lower bound is determined by the uniform and center bias models, which represent minimal expected performance: the uniform model assumes fixations are uniformly and independently distributed over the image, while the center bias model accounts for the human tendency to fixate near the center of images, modeled using a Gaussian Kernel Density Estimate (GKDE) based on fixations from the CAT2000 training dataset. The upper bound is given by the Gold Standard model, which predicts fixations of each participant using a GKDE over all fixations from other participants on the same image, representing the best achievable prediction performance. These bounds provide a reference range within which the evaluated models' performances can be compared."}
{"question_id": "a7ad87d54def516b43292de78c758cf6107320f7", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "Yes, the proposed method can be implemented using projected gradient methods instead of mirror descent. The update rule (2.8) employs mirror descent as a canonical generalization of Euclidean gradient descent on the probability simplex, but it is explicitly stated that other optimization methods, such as projected gradient descent, can also be used for updating the weights."}
{"question_id": "fe2486dcff37fd819cb07981aa6a1e026c1f52e5", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The proposed method, Target-Aware Weighted Training (TAWT), improves upon state-of-the-art (SOTA) cross-task learning methods by adaptively assigning trainable weights on source tasks or samples to minimize a representation-based task distance between source and target tasks. Unlike previous approaches that assume the existence of a single shared representation across all tasks, TAWT employs a finer-grained representation-based task distance that depends on the quality of representations for each task individually. This allows for a more informative and interpretable measure of the value of source data relative to the target task and clarifies the necessity of fine-tuning when the task distance is non-zero.\n\nTAWT differs from other weighting schemes common in machine learning, such as importance sampling in domain adaptation, by grounding the weighting in a theoretically principled minimization of task distance. Compared to recent methods that focus on distinguishing transferable versus source-specific features or assume approximately the same underlying representation for all tasks, TAWT does not impose such assumptions and instead learns weights to maximize the proximity between source and target representations.\n\nFurthermore, TAWT is an easy-to-use plugin applicable to existing cross-task learning paradigms like pre-training and joint training, requiring little hyperparameter tuning and incurring minimal computational overhead. It enjoys non-asymptotic learning-theoretic guarantees and empirically achieves significant performance improvements (e.g., an average absolute improvement of 3.1% on BERT across four NLP sequence tagging tasks).\n\nIn contrast to curriculum learning approaches that learn stochastic policies to select tasks, TAWT focuses on learning better representations by assigning suitable weights on source tasks. Compared to heuristic weighting strategies in multi-task learning, TAWT provides a practical algorithm with theoretical guarantees specifically designed for cross-task learning.\n\nOverall, TAWT advances cross-task learning by providing a theoretically grounded, adaptive weighting mechanism that enhances sample efficiency and representation quality beyond existing state-of-the-art methods."}
{"question_id": "caa94f24704e76df0ceac395ee650a45f7a174e1", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The most popular and advanced methods in cross-task learning are pre-training and joint training paradigms. These methods leverage cross-task signals as weak supervision to improve sample efficiency by learning shared or adaptive representations across different tasks. The proposed Target-Aware Weighted Training (TAWT) algorithm can be applied to these paradigms to further enhance their performance by assigning adaptive, trainable weights on source tasks or samples, thereby minimizing a representation-based task distance between source and target tasks."}
{"question_id": "0544f3fb3619a15d1f6076086707d91cea93b334", "paper_id": "openreview/NeurIPS-2022-dabt/wPEXGTzZJt", "generation": "The authors selected the four datasets because, to their knowledge, these are the only publicly available datasets specifically designed for the task of visual search in natural scenes. Each dataset comprises curated search images, target objects, and anonymized participants\u2019 scanpaths, allowing for a standardized evaluation. Additionally, these datasets vary in key characteristics such as the number of participants per image, image content, target presentation, and scanpath length, which enables a comprehensive comparison across different experimental designs. The authors also performed preprocessing to fit these datasets into a common format and criteria, facilitating fair comparison of visual search models. This selection was made to provide a reference point and a common computational framework for evaluating state-of-the-art models, addressing the lack of a unified benchmark in the field."}
{"question_id": "26c5a6b5fdd3b1d0b2d97c1550126b503f144eed", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The chance performance reported in Table 4 is 10%."}
{"question_id": "8f304b893f44b97b98ba8df1ea43ce0fcb657b87", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The expression involving the summation is not explicitly stated to be always positive in the provided proofs. However, the analysis relies on bounding the supremum of differences of loss functions, which are bounded in the interval [0, 1]. This boundedness ensures that the terms inside the summation are controlled and finite.\n\nSpecifically, the loss function \u2113 is bounded in [0, 1], which implies that differences of the form L_t(\u03c6, f_t) \u2212 \u2113(f_t \u25e6 \u03c6(x_ti), y_ti) and similar terms are also bounded. This boundedness allows the use of concentration inequalities such as McDiarmid\u2019s inequality and symmetrization arguments, which require control over the magnitude of the summands but do not require strict positivity.\n\nRegarding taking a power to 1/p, the proofs involve norms and expectations over sums weighted by \u03c9_t and normalized by n_t. The use of powers 1/p is justified in the context of normed spaces or integrals over positive measures, where the summands are nonnegative or their absolute values are considered. Since the loss differences are bounded and the summations are weighted averages, the quantities inside the power 1/p can be interpreted as norms or integrals of nonnegative functions, making the operation mathematically valid.\n\nIn summary, the positivity or nonnegativity required for taking a power 1/p is ensured by the boundedness and structure of the loss functions and the weighted sums, rather than by an explicit statement that the summation is always positive."}
{"question_id": "1874144ac78d10d99982bc7f6446545ac56a4805", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "A fine-grained model analysis was conducted by sampling approximately 1,000 cue-image pairs with 10-12 candidates, which were annotated by three authors into six categories: (a) Visually salient, (b) Visually non-salient, (c) Concept related, (d) Activity, (e) Counting, and (f) Colors. The final category for each instance was determined by majority vote with 98% agreement. The CLIP ViT-B/32 model was evaluated on these categories, and accuracy per category was reported. The results showed that model performance was highest for visually salient and colors categories, degraded for concept related and activity categories, and was much worse for visually non-salient and counting categories. This pattern suggests that models have difficulty with associations requiring common sense reasoning or non-salient visual cues. The annotated data and detailed annotation guidelines were released for future research. Examples and screenshots of the annotation task were also provided to illustrate the different association types."}
{"question_id": "9baa6e51bac226403b63b8bef97ea58737e9f14c", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The increased difficulty of the 10 & 12 task compared to the 5 & 6 task arises primarily from the larger number of candidates, which increases the complexity and the random chance of success for models. While human performance remains stable and robust across both tasks, models experience a significant performance drop as the number of candidates increases. This suggests that humans are able to handle the increased difficulty level effectively, whereas models struggle to discriminate among more candidates. Additionally, training improves model performance more noticeably in the 10 & 12 candidate setting, indicating that models can better exploit supervised data in harder cases with more distractors. The difficulty is also linked to the nature of the associations collected via the game, which are more challenging and require complex reasoning beyond direct visual detection, further impacting model performance but not human accuracy."}
{"question_id": "c5bafca1e41f1bfed6bf6063b321e6bb5102b171", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The poorer performance of the CLIP-ViL model compared to other models is attributed to its use of the image-text matching (ITM) pre-training objective, whereas models like X-VLM and ViLT are fine-tuned specifically for image-text retrieval tasks. This difference in pre-training objectives results in CLIP-ViL achieving lower results, especially when the number of candidates increases (e.g., 10 and 12 candidates), as the ITM objective may be less effective for the association tasks posed by the WinoGAViL benchmark."}
{"question_id": "530a4fe0ab0d3f801dab10132e0e5791d29d2051", "paper_id": "nlpeer/ARR-22/12ea2851de6445bf343d642c6ccc8640bf7a6c28b5f20ba123d6d58f02790c7c7f8ab696e6a6b4e45bfad28bd4bc0419bdcf6358693404dd0c14d202b291503b", "generation": "The results in Table 1 with decoder downsampling are obtained without the two-step decoding method. This is indicated by the statement that increasing the downsampling rate from 3 to 5 degrades translation quality for all architectures, and that employing the two-step decoder matches the decoding speed of subword models but results in much worse overall translation quality. Therefore, the reported translation quality in Table 1 for models with decoder downsampling does not use the two-step decoding method."}
{"question_id": "da72c5aada0a4b421a7919d5ef2739ddb658ae5e", "paper_id": "nlpeer/ARR-22/12ea2851de6445bf343d642c6ccc8640bf7a6c28b5f20ba123d6d58f02790c7c7f8ab696e6a6b4e45bfad28bd4bc0419bdcf6358693404dd0c14d202b291503b", "generation": "The number of samples used to generate the results in Table 2 corresponds to the training, validation, and test set sizes reported for the IWSLT dataset in Table 4. Specifically, the training sets contain approximately 206k to 232k parallel sentences depending on the language pair, with validation sets of about 1.3k sentences and test sets of about 1.1k to 1.2k sentences. Thus, the results in Table 2 were generated using roughly 206,000 to 232,000 training samples, 1,300 validation samples, and approximately 1,100 to 1,200 test samples."}
{"question_id": "c44a73f20a295acb499abf61c0f0b96e4080d9ba", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "Yes, there is a universal quantifier before equation (3.5) in Definition 3.2 (Transferability). Specifically, the definition states that a representation \\(\\phi \\in \\Phi\\) is \\((\\rho, C_\\rho)\\)-transferable from \\(\\omega\\)-weighted source tasks to the target task if there exist \\(\\rho > 0\\), \\(C_\\rho > 0\\) such that for **any** \\(\\bar{\\phi}_\\omega \\in \\bar{\\Phi}_\\omega\\), the inequality in (3.5) holds:\n\n\\[\nL_0^\\star(\\phi) - L_0^\\star(\\bar{\\phi}_\\omega) \\leq C_\\rho \\left( \\sum_{t=1}^T \\omega_t [L_t^\\star(\\phi) - L_t^\\star(\\bar{\\phi}_\\omega)] \\right)^{1/\\rho}.\n\\]\n\nThe phrase \"for any \\(\\bar{\\phi}_\\omega \\in \\bar{\\Phi}_\\omega\\)\" explicitly indicates a universal quantification over \\(\\bar{\\phi}_\\omega\\). The definition applies to each \\(\\phi \\in \\Phi\\) under this condition, implying the universal quantification over \\(\\phi\\) as well."}
{"question_id": "b79e294382978a8a9cebe595211529bcb653e0f2", "paper_id": "nlpeer/COLING2020/1886", "generation": "The topics for n-grams are chosen by first assigning each n-gram to a category, such as topics (e.g., Politics, War), proper names, hashtags, or style-related categories like punctuation marks, all-caps, function words, and emojis. Specifically for topics, the n-grams are mapped to a predefined list of topics (e.g., missile, army \u2192 War). The importance of each topic for the target classes (fake or real news spreaders) is then defined by summing the relevance values (R_v) and frequency values (F_v) of the n-grams included in that topic. To select only the n-grams relevant for one of the target classes, the difference between R_v and F_v for each n-gram is computed, followed by calculating the mean (\u03bc) and standard deviation (\u03c3) of these differences. Only n-grams whose difference exceeds \u03bc + \u03c3 are retained as relevant."}
{"question_id": "3684e20e39c14e7d52c12084515e178aa0789584", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "Associations that are solvable by AI should be kept in the framework to reduce dependence on a specific AI model and to ensure the naturalness and quality of the data. The framework prioritizes associations that are solvable by humans, but does not automatically exclude those solvable by the AI model. This approach prevents the dataset from becoming biased toward only fooling the AI model and maintains associations that are meaningful and intuitive for humans. By including instances that AI can solve, the benchmark remains a dynamic and realistic evaluation tool that reflects a range of difficulty levels, supporting the development of models with better association abilities."}
