import simple_parsing
import pandas as pd
from dataclasses import dataclass
from pathlib import Path
from tabulate import tabulate

ROUGE_ALIGNSCORE_FIELDS = [
  ("aep-gen-rougel-stemmer-fmeasure", "aep_rouge"),
  ("ff-gen-rougel-stemmer-fmeasure", "ff_rouge"),
  ("aff-gen-rougel-stemmer-fmeasure", "aff_rouge"),
  ("aep-gen-alignscore", "aep_align"),
  ("ff-gen-alignscore", "ff_align"),
  ("aff-gen-alignscore", "aff_align"),
]

PROMETHEUS_FIELDS = [
  # ("ff-gen-relevance-prometheus-scores", "ff-re-pro"),
  ("ff-gen-correctness-prometheus-scores", "ff-co-pro"),
  # ("aff-gen-relevance-prometheus-scores", "aff-re-pro"),
  ("aff-gen-correctness-prometheus-scores", "aff-co-pro"),
]

TARGETS = [
  "golden_evidence",
  "rag_05_dense",
  "rag_10_dense",
  "rag_05_full",
  "rag_10_full",
  "notebooklm"
]

def analyze_target(target: str) -> dict:
  stats = {}
  
  try:
    print(f"Reading {target} rouge-alignscore")
    df = pd.read_json(open(f"./out/metrics-{target}.jsonl", "r"), lines=True)
    for field, name in ROUGE_ALIGNSCORE_FIELDS:
      stats[name] = df[field].mean()
  except Exception as e:
    print(f"Error reading {target} rouge-alignscore: {e}")
    for field, name in ROUGE_ALIGNSCORE_FIELDS:
      stats[name] = -1
    pass
  
  try:
    print(f"Reading {target} prometheus")
    df = pd.read_json(open(f"./out/metrics-generations-prometheus-{target}.jsonl", "r"), lines=True)
    for field, name in PROMETHEUS_FIELDS:
      stats[name] = df[field].mean()
  except Exception as e:
    print(f"Error reading {target} prometheus: {e}")
    for field, name in PROMETHEUS_FIELDS:
      stats[name] = -1
    pass
  
  return stats

def main():
  # print mean as table format
  all_stats = {}
  all_stats["target"] = TARGETS
  
  for target in TARGETS:
    stats = analyze_target(target)
    for name, stat in stats.items():
      if name not in all_stats:
        all_stats[name] = []
      all_stats[name].append(stat)

  print("All stats:")
  print(all_stats)
  print()
  print(tabulate(all_stats, headers="keys", tablefmt="github"))
  print()
    
if __name__ == "__main__":
  main()
