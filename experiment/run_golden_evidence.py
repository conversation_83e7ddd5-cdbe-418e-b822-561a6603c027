import json
from tqdm import tqdm
from tqdm.contrib.logging import logging_redirect_tqdm

from data import data_store
from core.openai_service import call_text_response_once
from core import logging
from core.prompts import PROMPT_ANSWER_GENERATION

logger = logging.get_logger()

def main():
  data = data_store.get_qa_data()
  augmented_data = data_store.get_qa_augmented_data()
  data = data.merge(augmented_data[['question_id', 'augmented_answer_free_form']], on='question_id', how='left')
  data = data[data['answer_free_form'].notnull() & data["raw_answer_evidence"].notnull()]
  
  result_path = "experiment/golden_evidence-v2.json"
  result_file = open(result_path, "w")
  
  for row in tqdm(data.to_dict(orient='records')):
    question_id = row['question_id']
    paper_id = row['paper_id']
    question = row['question']
    raw_answer_evidence = "\n\n".join(row['raw_answer_evidence'])

    prompt = PROMPT_ANSWER_GENERATION.build_prompt(question=question, context=raw_answer_evidence)
    generated_answer = call_text_response_once(prompt, temperature=0.0)
    generated_answer = generated_answer.output[0].content[0].text.strip()
    result = {
      "question_id": question_id,
      "paper_id": paper_id,
      "generation": generated_answer
    }
    result_file.write(json.dumps(result) + "\n")

if __name__ == "__main__":
  with logging_redirect_tqdm():
    main()
