{"question_id": "8f2072e6213f44471d3294973c9cfdd790bc7259", "paper_id": "openreview/ICLR-2022-conf/_X90SIKbHa", "generation": "The proposed MST-AM method surpasses BFGS in solving cubic-regularized problems. It is comparable to full-memory methods in the forward root-finding process and converges faster in the backward process, with comparable accuracy. Unlike limited-memory quasi-Newton methods such as L-BFGS, whose performance can be sensitive to the historical length, MST-AM only requires storing two corrected historical iterations, indicating greater robustness and efficiency in memory usage."}
{"question_id": "7ed6482c76ae428708b4675495718652adfed68d", "paper_id": "nlpeer/COLING2020/679", "generation": "Yes, the annotators of the test sets were native English speakers."}
{"question_id": "cdb59f3c15dd8d951c169a9f2fae03e000215bae", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "Yes, the cross-linguistic analysis includes both French and Spanish. The models trained on English data were evaluated on French (Vikidia-Fr) and Spanish (Newsela-Es) datasets, achieving a zero-shot cross-lingual ranking accuracy of over 80% for both languages."}
{"question_id": "465b94530bc99598bbefb742da70cf99236b86d9", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "Classification-based models were not used for the zero-shot experiments in Tables 6 and 7 because performance is generally expected to degrade slightly in a cross-corpus setting compared to a within-corpus evaluation. Therefore, the experiments focused only on regBERT and NPRM, and further experiments with a BERT classifier were not reported."}
{"question_id": "6ceebb857d2674bd38452e8bc30a3d280f2ce647", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "The lower performance on Vikidia-Fr compared to Newsela-ES can be attributed to several factors: the difference in languages, the more diverse content in Vikidia-Fr compared to the domain-specific content in Newsela-ES, and the impact of ranking metrics. Specifically, Vikidia-Fr has only two reading levels, so incorrect ranking of these levels can result in a completely reversed list, which is heavily penalized by the SRC and KTCC metrics. Consequently, a small number of completely incorrectly ranked slugs can lead to low SRC and KTCC scores for Vikidia-Fr, despite potentially high scores in other cases."}
{"question_id": "e76615760f7625a5b984cb961f5d948ab2407178", "paper_id": "nlpeer/PeerRead-ACL2017/193", "generation": "A node in the UCCA scheme can correspond either to a terminal or to several terminals grouped together as a single entity. This implies that a node may represent a single terminal or a non-terminal unit composed of multiple terminals, but not both simultaneously."}
{"question_id": "0081780bb3b2f8ca303dd426ce4ddfd89c13e80d", "paper_id": "nlpeer/ARR-22/0f595b4bd968ef5daacc88c4edb581bf7f683af30b6874ba157c6d2282aabead577041409b949924e4bc5ef54c543d85a10e962be8e70304dea65e1b18441bdb", "generation": "The average duration reported in Table 1 serves to quantify the typical length of the human evaluation process, demonstrating that the developed evaluation method is not only highly reliable but also feasible and low cost. This metric provides an indication of the time efficiency of the evaluation technique."}
{"question_id": "058da91fd7bfe9ecd3163d9d8877e5116cf5cdf6", "paper_id": "openreview/ICLR-2022-conf/C03Ajc-NS5W", "generation": "The generative model G-SphereNet demonstrates success in synthesizing new molecules by achieving the highest Validity rate of 88.18%, significantly outperforming E-NFs, which has a Validity of 39.77%. This indicates that G-SphereNet more effectively captures the dependency between atoms and learns the underlying chemical rules of molecular geometries. Additionally, G-SphereNet attains lower Maximum Mean Discrepancy (MMD) distances for four types of chemical bonds compared to G-SchNet, showing that it models the 3D structural distribution of molecular geometries more accurately. These results collectively provide strong evidence of the model's capability in generating valid and chemically plausible molecular structures."}
{"question_id": "3f958288ac95aea397ff6c9a6854d14d691778a2", "paper_id": "openreview/ICLR-2023-conf/-CoNloheTs", "generation": "The proposed algorithm is primarily theoretical and demonstrates that reconstruction of neural networks is provably achievable. However, it has several practical limitations: it is designed for two- and three-layer networks, which are considered too shallow for practical use, and it requires exact access to the black-box network, which may not be feasible in practice. Additionally, numerical inaccuracies can affect reconstruction accuracy, especially when the parameter \u03b4 is very small. The algorithm will work on any two-layer neural network except for a set of networks with zero Lebesgue measure and operates in polynomial time if the input weights are slightly perturbed. Therefore, while the algorithm can theoretically recover certain neural networks, its practical applicability to real, deeper networks and scenarios without exact black-box access is limited."}
{"question_id": "5f4c9dea82aa176c2e42cd2c59ff3da0fce4a367", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "Yes, the pairwise Euclidean distances between the latent representations of the leaves can be compared to the corresponding branch lengths in a properly fitted phylogenetic tree. This comparison is quantitatively assessed by analyzing the correlation between these distances. For the standard VAE model, the correlation coefficient is 0.79 and the Spearman correlation coefficient is 0.85, while for the Draupnir model, these values are higher, with a correlation coefficient of 0.91 and a Spearman correlation coefficient of 0.94. This indicates a strong correspondence between the latent space distances and the phylogenetic tree branch lengths."}
{"question_id": "cf26cc0cd1000ad63bfba19b5159a20efba34b18", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "The provided information does not address whether the parameters of the BLOSUM matrix can be estimated by the model instead of being pre-computed. It only discusses the effect of removing BLOSUM embeddings as input to the GRU on convergence and accuracy, without mentioning parameter estimation of the BLOSUM matrix itself. Therefore, no conclusion can be drawn regarding the estimation of BLOSUM matrix parameters by the model."}
{"question_id": "b8c9effb94fbab2db127d7262041fecdb031c1dd", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The preprocessing step of converting numerical features to categorical values by using an exponentially-scaled binning method is applied specifically to the 14 conventional features from the Kyoto dataset. The information does not indicate that this conversion is performed for all methods used in the paper, only that it is part of the data preparation for these features. Therefore, it cannot be concluded that the same preprocessing step occurs for all methods used in the paper."}
{"question_id": "ee956c6a1b9b9808fc77d7d1f8f82237123f5000", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "\"Edit boundaries might be unusual\" means that the segments or limits defining where an edit begins and ends were determined automatically and therefore may not align with typical or expected divisions. Despite this potential irregularity in the segmentation, raters were instructed to focus on evaluating the appropriateness of the error type itself, regardless of whether they agreed with the placement of these boundaries."}
{"question_id": "f3d892c65c8fc7edb3624cad07d6ef39115402eb", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "A new set of error categories was designed because the tagset presented in Nicholls (2003) and the CoNLL-2014 tagset are specific to particular datasets and are not suitable for a dataset-agnostic error type classifier. The new set aims to be more generalizable across different datasets, addressing the need for a classification system that is not limited by dataset-specific constraints."}
{"question_id": "962be5303d07e7707c9750241730986cfeb7d754", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "The significance test was performed using the precision, recall, and F0.5 scores of each team in the CoNLL-2014 shared task, evaluated with both automatic and gold references. The test employed bootstrap significance testing as described by Efron and Tibshirani (1993)."}
{"question_id": "49887aceab5099bc8a45f1f01aa437f760c289a5", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "The 90% IG achieved by the mixture model on the real data example is not necessarily indicative of its performance on other datasets. Additional analyses performed on multiple datasets demonstrate that NInGa facilitates model comparison across different datasets, implying that performance metrics such as IG can vary depending on the dataset. Therefore, the 90% IG observed in the real data example should be interpreted within the context of that specific dataset and not generalized without further comparative analysis."}
{"question_id": "4d8419e9aeb2f3d606bca8774d3618d08b70c41f", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "Improving the upper bound for the Information Gain evaluation metric provides a more interpretable and reliable measure of model quality. It allows for a clearer assessment of how much information a likelihood-based model captures relative to a baseline, facilitating better comparison and evaluation of models. This improvement enhances the practical utility of the Normalized Information Gain (NInGa) metric by making it a more effective tool for quantifying and benchmarking model performance."}
{"question_id": "7869279cfc2dc07fcd82704dc07789afa6de5c82", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "The issue lies with the PE oracle itself rather than regularisation improving its performance. The context indicates that to avoid an overconfident GS model, uncertainty is added to the parameter estimation by estimating the GS model in a fully Bayesian manner via the full posterior predictive distribution. The PE approach is described as a special case where the posterior distribution collapses onto a delta distribution, implying that the PE oracle does not account for uncertainty and is therefore inherently overconfident. This suggests that the limitation is intrinsic to the PE oracle framework rather than something that can be resolved by regularisation."}
{"question_id": "96a32bff80b5928198a99a4fc2c2e24cd1a982dd", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The magnitude of the noise in the data is represented by the noise ratio \u03c3, where \u03c3 = 0.1 corresponds to 10% of the labels being flipped."}
{"question_id": "92394e14628bdc9941b0581b43b20ab42dbdd3fd", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "No, the expected label does not always match the most probable label given the noisy data. The pattern described indicates that the learning process initially moves towards the unknown true label but eventually turns to memorize the wrong label, implying a divergence between the expected label and the most probable label under noisy conditions."}
{"question_id": "3b4dcf624027feff21ac63b6e451169e1ca6bf2a", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The proposed criterion for quantifying generalization performance is the difference between the test accuracy and the training accuracy."}
{"question_id": "834016a31e50565175511dcdf3d75a1be44b532c", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The paper defines difficulty, referred to as base difficulty, as the squared norm of the difference between the one-hot label vector \\( e_y \\) and the true conditional distribution \\( p^*(x) \\), expressed mathematically as \\(\\| e_y - p^*(x) \\|^2\\). This measure is large when the input \\( x \\) is ambiguous\u2014meaning \\( p^*(x) \\) has several large components and no one-hot label is close to \\( p^*(x) \\)\u2014or when \\( x \\) is not very ambiguous but the sample label \\( y \\) was drawn from a low-probability class, making the sample \"unlucky.\""}
{"question_id": "a48eb6eab4e9448324227205ae04b8d47a5b181e", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "In addition to accuracy and expected calibration error, the criteria considered to define generalization include the zig-zag score, which measures the behavior of the prediction vector on dimensions other than the training label's class. A large zig-zag score indicates that neighboring samples exert high influence on the learning path, reflecting how much the path deviates towards a different class, potentially close to the optimal label. This score is related to, but distinct from, the C-score, which focuses on the speed of convergence to the correct label. The zig-zag score numerically summarizes the shape of the learning path and is closely correlated with the base difficulty of the sample, providing an additional perspective on generalization beyond accuracy and calibration."}
{"question_id": "67b6a78d6cea6ff4cd6a6cdd262aaf4e4bfea275", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The relationship between the p*() label and the one-hot encoding for the hard sample in Figure 3 is that p* has several large components, indicating ambiguity, so there is no one-hot label near p*. In contrast, for a less ambiguous sample, there is a one-hot label near p*, but the sample was \"unlucky\" and drew y from a low-probability class."}
{"question_id": "fffbbdd88b4cdc0b98de790921df08f7be1eed7d", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The prevalence of this behavior across the dataset is quantified by the noise ratio \u03c3, where \u03c3 = 0.1 indicates that 10% of the labels in the dataset are flipped. This noise ratio is used to represent the proportion of label noise present in the CIFAR and TinyImageNet datasets during the experiments."}
{"question_id": "924a054e5ec561c4d58306dfd312782d7b4f70ca", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "Students are supervised using the smoothed predictions of the teacher network."}
{"question_id": "01163085d0c4776005e14d8621ce2bbdd3cc1c13", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The FilterKD model consistently outperforms models trained using label smoothing (LS) and knowledge distillation with the optimum temperature (KD) across multiple datasets. Specifically, on CIFAR10, FilterKD achieves an accuracy of 95.63%, which is higher than LS (95.34%) and KD (95.39%). On CIFAR100, FilterKD attains 80.09%, surpassing LS (78.07%) and KD (78.40%). Additionally, on the TinyImageNet dataset under varying noise levels, FilterKD maintains higher accuracy than both OHT (a variant of KD) and ESKD (another KD method), especially at higher noise levels (e.g., 40.81% accuracy at 20% noise for FilterKD versus 30.52% for OHT and 36.55% for ESKD). In teacher-to-student transfer on CIFAR100, FilterKD also achieves superior student performance compared to ESKD, with accuracies of 83.03% versus 81.16% (Eff \u2192 Res), 75.79% versus 73.13% (Res \u2192 Mob), and 74.49% versus 72.64% (Res \u2192 VGG). These results demonstrate that FilterKD provides improved performance relative to label smoothing and knowledge distillation with optimum temperature."}
{"question_id": "3493acb3c91a1415959829136fe3e250966fc8f0", "paper_id": "openreview/NeurIPS-2022-dabt/8lQDn9zTQlW", "generation": "The prompt design for BioNLP tasks primarily involves adapting general domain templates that were originally used to tune the T0 model. These biomedical prompts are more \"in-distribution\" because they are based on existing general domain templates populated with biomedical text, rather than being completely novel or specifically tailored for biomedical tasks. This approach contrasts with general NLP tasks, where prompts are directly derived from general domain templates without the need for domain-specific adaptation. Consequently, the biomedical prompt design leverages the structure of general NLP prompts but incorporates domain-specific content, potentially providing an advantage to models like T0 when evaluated on biomedical tasks."}
{"question_id": "0c4afb8ced370f2f67477fe4617ff846513cfb6d", "paper_id": "openreview/NeurIPS-2022-dabt/8lQDn9zTQlW", "generation": "Table 2 includes performance statistics for all language models and datasets, specifically reporting accuracy for all tasks except BIOSSES, for which performance is reported using Pearson\u2019s correlation after casting outputs to numbers."}
{"question_id": "9c04f85fb5baad69d0ae21c1c2c07abc0422bd55", "paper_id": "nlpeer/ARR-22/013b9bf63a6f68fd0c3ecc36f8cbe2ad5bc92ea3bfe5a9f6c15eb056ecc4f858718410182c3765b2dc2695ae29ba08fb5dea5fc495faf2bbb77205bc3f765fcd", "generation": "To ensure a more diverse cross-cultural representation when building an NLP model, concrete steps include: \n\n1. Engaging dedicated communities focused on diverse languages, such as Universal Dependencies, which manually annotate morphosyntactic datasets for over one hundred languages, thereby covering a wide typological diversity.\n\n2. Supporting initiatives like the Masakhane community, which strengthens NLP research for African languages by creating datasets such as MasakhaNER, a Named Entity Recognition dataset for 10 African languages collected by native speakers.\n\n3. Recruiting geo-diverse annotators from multiple continents, including Africa, Asia, and the Americas, who contribute both the data (e.g., images) and the corresponding annotations (e.g., image descriptions), ensuring cultural diversity in the dataset collection and annotation process."}
{"question_id": "36e40e97993a08a2c5e50bfc69c991334be39e6e", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The noise heads are discarded at test time because the interchangeable noise masks are intended to \"soak up\" other sounds, implying that the noise heads serve to absorb or mask noise during training but are not needed during testing."}
{"question_id": "c67443bf273772ac2d4297564f839c0a0229e6eb", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The greyed out portion of Figure 3 represents the two predicted noise masks, which are interchangeable for loss computation during training and are discarded at test time."}
{"question_id": "c83f53bbc1390bf3f6a15aa58e1c559cf391a507", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The task described in Section 4.1 of the paper is the musical instrument sound separation task using the MUSIC dataset."}
{"question_id": "31314c6ad7630579c350af928493caac9c563dbb", "paper_id": "nlpeer/ARR-22/0f8bc751a0472564732a976cd152643d92d5393c2e77c8234f386a801883d09ff9540378b3dab5834544a246c22f292d5b1c4d370219943c9a19a1626a8f8781", "generation": "The minimum threshold (\u03c4_min) is 0.0005 and the maximum threshold (\u03c4_max) is 0.995."}
{"question_id": "c5b7931f3e58dd10d67e388fcd5680c37e267022", "paper_id": "nlpeer/ARR-22/0f8bc751a0472564732a976cd152643d92d5393c2e77c8234f386a801883d09ff9540378b3dab5834544a246c22f292d5b1c4d370219943c9a19a1626a8f8781", "generation": "The threshold values are selected to ensure that the distribution of class labels approximates a ratio of 1:10, with the distituent class being the majority. This choice is a crude estimate that accounts for the much larger sentence lengths in the WSJ-Full section. Additionally, from a linguistic perspective, it is certain that distituents must necessarily outnumber constituents."}
{"question_id": "0c64726cf3b593196fd8f350d0f2c2d4aba98d1e", "paper_id": "nlpeer/ARR-22/7e701552b7cfaad6239d3d5dfcd9da1852a03a66907c789fe65d368f1c2845e723adcecd7f7008849eef13c7ca467be73de53747c3376a6bb46d1911ed1b6409", "generation": "The label mapping of Electra's binary classification head (RTE) is as follows: 0 corresponds to \"not entailment\" and 1 corresponds to \"entailment.\""}
{"question_id": "44279226e8c3ea5497ac4c43754e3e890e0183fb", "paper_id": "openreview/ICLR-2023-conf/XrMWUuEevr", "generation": "The best way to compare the performance of different models or architectures (x, x', x'', etc.) is to evaluate them on the FS-Mol benchmark dataset using the main benchmark setting with a support set size of 16 and a stratified random split. This approach ensures a fair comparison by maintaining consistent experimental conditions across methods."}
{"question_id": "ba25580bbc4ec4f20348cefaf968e1cdea408642", "paper_id": "openreview/ICLR-2023-conf/XrMWUuEevr", "generation": "The difference between the 8 positive and 8 negative examples used in the domain shift experiment in Section 5.3 lies in their activity labels: the positive examples correspond to molecules labeled as active (yn = 1), while the negative examples correspond to molecules labeled as inactive (yn = \u22121)."}
{"question_id": "9ee8e92c0faa3a3d17cff68d75d28be3dca8e8c4", "paper_id": "openreview/ICLR-2022-conf/hcQHRHKfN_", "generation": "The provided chunks do not contain any information regarding the impact of adjusting \\(\\delta\\) on the results of Table 1. The only parameter discussed is \\(\\alpha\\), and its adjustment affects the acceptance of trajectories and the balance between exploration time and diversity score, but no details about \\(\\delta\\) or its influence on Table 1 are given."}
{"question_id": "bf41e9f2b170cb8e1801812167b945e8f56aa8cb", "paper_id": "openreview/ICLR-2022-conf/hcQHRHKfN_", "generation": "The results in Table 1 demonstrate that using behavior-driven exploration alone fails to discover the optimal Nash Equilibrium (NE). This indicates the necessity of incorporating reward-driven exploration to effectively maximize the divergence of both states and actions in problems characterized by numerous equivalent local optima."}
{"question_id": "a81ef48de406906c5a847928da2bc47079136f55", "paper_id": "nlpeer/COLING2020/1681", "generation": "The number of distinct phrases used for phrase addition in the adversarial attack generation is 7."}
{"question_id": "5678b6bf40f5958402473fd66a08dc836eaa98a7", "paper_id": "nlpeer/COLING2020/1681", "generation": "The coreference resolution pipeline described resembles the rule-based approach implemented in Stanford CoreNLP\u2019s Coref-Annotator (Raghunathan et al., 2010; Lee et al., 2011). However, there is no indication that this pipeline is universally accepted in the field. The description suggests it is an idealized pipeline rather than a universally endorsed standard."}
{"question_id": "587b8f363bb9be4e82b38b70f74608f844559b6f", "paper_id": "nlpeer/COLING2020/1681", "generation": "The data augmentation strategy, termed Antecedent-free augmentation (afa), involves identifying sentences where a coreferential \"it\" refers to an antecedent that is not present in the current or previous sentence. Augmentations are created by generating two new training examples in which the gender of the German translation of \"it\" is modified. For example, from the original sentence pair \"I told you before. <SEP> It is red.\" and its German translation \"Ich habe dir schonmal gesagt. <SEP> Es ist rot.\", two new targets are created: \"Ich habe dir schonmal gesagt. <SEP> Er ist rot.\" and \"Ich habe dir schonmal gesagt. <SEP> Sie ist rot.\""}
{"question_id": "1a42a5af41f66bb6428c643d96fd05eba81ce635", "paper_id": "nlpeer/PeerRead-CONLL2016/166", "generation": "Yes, the proposed method accounts for named entities that are not listed on Wikipedia. It generalizes gazetteers to unseen entities by learning patterns from known entities. For example, even if a fictional organization such as \"Dave and Sue Harris Foundation\" is not present in any gazetteer, the method can identify it as an organization by recognizing the pattern \"B-PersonName and B-PersonName B-PersonName Foundation\" as a strong signal for an organization. Additionally, the use of a cross-lingual wikifier grounds words to English Wikipedia entries, and although the disambiguation may not be perfect, it still provides valuable information by linking components of names to relevant categories or types, enabling the system to handle entities not explicitly listed in Wikipedia."}
{"question_id": "1aa55ba3f2f47947ad4df928f35cfc4c7d7035ec", "paper_id": "nlpeer/ARR-22/21dd1d5e232c7abf421302a95ef2c9ef44c7bad069da7ca4129a8cf1bfbd6f660afee123144ebcbd8da698aed212e2ca1151a13bca7aaef50ce59d27d23aa83a", "generation": "The focus of this paper is on intent classification and entity/slot recognition within task-oriented cross-lingual natural language understanding (XNLU)."}
{"question_id": "8fc13b01107e614b030a2c7dbc65aa19d0363778", "paper_id": "nlpeer/ARR-22/21dd1d5e232c7abf421302a95ef2c9ef44c7bad069da7ca4129a8cf1bfbd6f660afee123144ebcbd8da698aed212e2ca1151a13bca7aaef50ce59d27d23aa83a", "generation": "The provided information does not indicate whether the authors ran their experiments multiple times with different random initializations to confirm the results."}
{"question_id": "10dbff5874380289cdab832a0eecab1cc3c34117", "paper_id": "openreview/ICLR-2023-conf/VA1YpcNr7ul", "generation": "The proposed algorithm, DASHA, is related to MARINA and momentum variance reduction methods (MVR). It follows the general structure of MARINA but incorporates the variance reduction strategy from MVR. There is no mention of a direct relation to Stochastic Variance Reduced Gradient (SVRG) or algorithms specifically in that family."}
{"question_id": "92c772c75354552e709f16f3e3b15a31e395f1cf", "paper_id": "openreview/NeurIPS-2022-dabt/heBKnuV42O", "generation": "The rationale for altering the data distribution, despite the knowledge base being compiled from medical papers, is to create a more balanced dataset. Specifically, incidence rates greater than 100% are capped at 100% to prevent a highly imbalanced dataset caused by unrealistically high rates. Additionally, diseases with extremely low incidence rates are assigned a minimum rate of 10% to ensure sufficient representation of these pathologies within the dataset. These adjustments help avoid underrepresentation of rare diseases and overrepresentation of highly frequent ones, thereby improving the balance and utility of the dataset for analysis or modeling purposes."}
{"question_id": "70418ac3cb9f40b039a74031b89324e2b891ccf5", "paper_id": "nlpeer/PeerRead-ACL2017/699", "generation": "The word embeddings are initialized by sampling from a uniform distribution in the interval [ -0.1, 0.1 ]."}
{"question_id": "60c2bf23190bf1120b8652501ff951bae6f3e046", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The rationale for comparing 2-hop homophily (xbx/xtx) with 1-hop homophily (xx) in Figure 3(d) is to evaluate the effectiveness of the 2-hop homophily ratio as a measure of homophily in graph data. The 1-hop homophily (xx) represents the traditional edge homophily ratio, which measures the proportion of edges connecting nodes of the same class. The 2-hop homophily (xbx/xtx) extends this concept by considering paths of length two, capturing the homophily at a higher-order neighborhood level. Comparing these two metrics allows for assessing whether the 2-hop homophily provides additional or complementary information about the class structure in the graph beyond what is captured by the 1-hop homophily. This comparison helps to understand the relationship between local (1-hop) and more global (2-hop) homophily properties in the graph, which is important for analyzing and designing graph-based learning algorithms."}
{"question_id": "9073f9407118eaf0b34170000a57846d672b4a5d", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "The four conversational QA models were selected based on having different model architectures and training strategies."}
{"question_id": "5227809e5dbd6a7ef588b7a84fc243e6cd0eed8d", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "The compensation rate for the annotators hired for the Mechanical Turk crowdsourcing work was calculated at $15 per hour."}
{"question_id": "c3790b0e0925f080c8d91160ae348f35ac367993", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "Word overlap was used instead of an exact match when comparing f1(s\u2217_{j,1}, s_{j,1}) because the prediction and the gold answer may mention the same entity but use different names. This approach accounts for variations in naming while still recognizing that the entities are equivalent."}
{"question_id": "721388955b97db097e963c4b221fe9af9e2da4ae", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "When the absolute accuracy numbers are higher than the F1 scores, it indicates that many conversational QA questions have multiple possible correct answers. Static datasets used in automatic evaluations may not capture all these valid answers, leading to lower F1 scores. In contrast, human evaluation can judge all possible answers, resulting in higher absolute accuracy."}
{"question_id": "672a51e77d6a34b48ad5a93aa1a37139d71e3c72", "paper_id": "nlpeer/ARR-22/1370310ca3e0e0d092a2e8b58f90b0d692331caa6e3ce5ca6dacbef926f2b01ff49c81282f32c554fa66c04979a125b7548c9692c2091bb2673642102d85412d", "generation": "The rationale for analyzing only the self-attention functions in the last layer of the BERT model is to capture the mixing of information at the final stage of the Transformer. By computing the mean attention vector over the final layer heads, the analysis focuses on the ultimate integration of contextual information as represented in the last layer's self-attention modules."}
{"question_id": "7b375e548c69cd6c0b0d75953da0021adb9e2a7e", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The computation time per overall loop for SAC ranges from 870 to 910 milliseconds, while for REDQ it ranges from 2328 to 2400 milliseconds. The computation time per Q-functions update for SAC ranges from 835 to 870 milliseconds, and for REDQ it ranges from 2269 to 2340 milliseconds. \n\nThus, the difference in computation time per overall loop between REDQ and SAC is approximately 1418 to 1530 milliseconds, and the difference per Q-functions update is approximately 1434 to 1505 milliseconds, with REDQ requiring more computation time than SAC."}
{"question_id": "abf4bcae7809ff5b01e8cf7fdb201caa7b8421ac", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The computation time referred to is the process time per overall loop, specifically the time required for executing lines 3 to 10 in Algorithm 2. This includes the entire iteration cycle of the method, not just the Q-functions update steps."}
{"question_id": "4bb993f44c76628b67f41da43c78aa82b50cbc19", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The full name of the algorithm DUVN is Double Uncertainty Value Network."}
{"question_id": "c3b651600b60b22f2a4c805aeb87745aff3c0c84", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The full name of the proposed method \"REDQ\" is Randomized Ensembled Double Q-learning."}
{"question_id": "8a7925cf9978728b68e7bc89204643a94468964a", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "Using Layer Normalization in combination with Dropout suppresses the learning instabilities caused by Dropout alone. Specifically, Layer Normalization reduces the oscillations in the Q-function loss and the variance of the gradient loss with respect to the Q-function parameters. This stabilization of learning enables better Q-value propagation, which consequently improves overall performance."}
{"question_id": "2074c1cd08c7d4b134ac01c5ee57f13765a4cc47", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "Layer normalization, when combined with dropout and variance re-scaling, provides a synergistic effect that improves the performance of dropout in deep reinforcement learning. Specifically, group normalization, which has a similar mechanism to layer normalization, works well when combined with dropout, successfully improving average return and reducing estimation bias. In contrast, batch normalization does not significantly improve performance and leads to unstable Q-function learning. Layer normalization without variance re-scaling also fails to improve performance and results in instability. Therefore, the key advantage of layer normalization over other normalization methods is its ability to maintain stable and effective Q-function learning through variance re-scaling, which enhances the regularization effect of dropout."}
{"question_id": "f567015ed8777554298ac8d5b511b255c317d3da", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "Dropout on its own destabilizes the learning of the Q-functions, causing significant oscillations in the Q-function loss and the variance of the gradient loss with respect to the Q-function parameters. This learning instability prevents effective Q-value propagation and degrades overall performance."}
{"question_id": "9dfb86a40b292918a304254d03b72b6fee37e740", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The target knowledge base (KB) size used for MedMentions in the experiments is 2.3 million (2.3M)."}
{"question_id": "3356f072c76c12c8ebc215b5bd495d5ccbea0126", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The training batches are constructed by sequentially building batches of mentions over the training data, where each batch includes mentions in B, mentions coreferent to those in B, and the set of gold entities for each mention in B. This indicates that the batches contain mentions that are coreferent, which implies that mentions from the same document are included to leverage coreferent mentions. Therefore, the training batches do contain mentions from the same document to maximize the use of coreferent mentions."}
{"question_id": "b509625d37c9da4c7585e00370dc166be8e9be88", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "No, the model does not need to compute \\(\\psi(m_i, e)\\) for all entities in the target knowledge base during inference. Instead, for each mention \\(m\\), the constructed graph \\(E_m\\) includes edges from \\(m\\)'s \\(k\\)-nearest neighbor mentions in the mention set \\(M\\) to \\(m\\), and an edge from \\(m\\)'s nearest entity to \\(m\\). This implies that the model only considers a limited subset of candidate entities\u2014specifically, the nearest entity to the mention\u2014rather than all entities in the knowledge base."}
{"question_id": "4a12daa058e224f39629de8997d5de7c8b0c2d3c", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The complete graph is pruned to generate arborescence (directed minimum spanning tree) structures rooted at entity nodes, which represent pairs of positive examples used for training. This pruning is necessary because the supervised clustering training objective relies on forming a directed minimum spanning tree over mentions and entities. Such coreference links provide a useful inductive bias by reflecting the inherent relationship between the two tasks. Using the complete graph without pruning would not produce this structured representation, which is essential for the training procedure."}
{"question_id": "8d69a05246c31778897996bc35b60061f15554f3", "paper_id": "nlpeer/COLING2020/1550", "generation": "The results presented in the paper are not conclusively transferable to other Fake News related datasets using different tasks or label sets. However, the findings can serve as starting points for further experiments in related fields, indicating that while generalization is not established, the results may inform future research beyond the specific tasks and datasets studied."}
{"question_id": "720c06898bbd974bce657e8eefac71ea8641b762", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "Position embeddings vi p of size dp for relative positions of words are generated by table lookup from a matrix Wp \u2208 Rdp \u00d7 (smax * 2 \u2212 1) of trainable parameters, where i denotes the location of a word in a sentence and smax is the maximum sentence length in the dataset. This process is analogous to the generation of word embeddings."}
{"question_id": "52e2f447dc9bcf3dc208f99a89d4b59da4004923", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "Yes, the position embeddings, specifically the embedding matrix for relative positions (Wp), are randomly initialized."}
{"question_id": "45a145511dd96e98d18e5ac09f454b95ceee5a38", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The left and right neighbors of the trigger word are treated separately during the max-pooling operation applied to the n-gram representations. This separate treatment allows the model to handle multiple event mentions within a sentence by identifying the maximum feature value for each filter on each side of the trigger word independently."}
{"question_id": "8b0ad0aeb866b9064a6bd71b1559fe85d81a612b", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The event-mention representation \\( v_e \\) is used for the subsequent event-linking decision."}
{"question_id": "c8e25c77b2ec42c4f94bc044959aa372dd3f9638", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The design of the proposed method is not arbitrary for all layers of a given ViT model; some layers are fixed as token reorganization layers. Specifically, the token reorganization locations are determined by dividing the ViT into blocks with an equal number of layers. For a ViT with L layers and t token reorganization layers, the separating length is calculated as s = L / (t + 1). The token reorganization layers are then fixed at the layer indices [s + 1, 2s + 1, ..., ts + 1], which evenly partition the ViT into segments."}
{"question_id": "3f32444ad6371e1401f9047615faeed1a6572e73", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "Table 1 shows the classification accuracy results when tokens are directly removed, which leads to severe deterioration in performance. In contrast, Table 2(a) presents results incorporating inattentive token fusion during the ViT training process, a method designed to mitigate information loss by fusing non-topk tokens based on their attentiveness from the [CLS] token. Thus, the key difference is that Table 1 reflects the negative impact of direct token removal, while Table 2(a) demonstrates improved accuracy through the proposed inattentive token fusion approach."}
{"question_id": "d753561800b2ad04d3d8262519328d014142d717", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The rationale for choosing to remove tokens at the 4th, 7th, and 10th layers when processing other ViT models is to evenly divide the ViT into blocks with the same number of layers. This is achieved by calculating the separating length \\( s = \\frac{L}{t+1} \\), where \\( L \\) is the total number of layers and \\( t \\) is the total number of token reorganization layers. The token reorganization layers are then placed at layer indices \\([s+1, 2s+1, \\ldots, ts+1]\\), ensuring that the ViT is cut evenly into segments."}
{"question_id": "5ab449ca87a0bb86111a5e4e594ac959ec7d0e86", "paper_id": "nlpeer/PeerRead-CONLL2016/142", "generation": "Post-editing rates were chosen over prediction (h)ter for intrinsic uncertainty evaluation because post-editing time is a more comprehensive measure of post-editing effort. It captures not only the technical effort, as measured by HTER, but also the cognitive effort involved. Furthermore, post-editing time is more directly applicable in real translation environments, where uncertainty estimates are useful as they relate directly to productivity measures."}
{"question_id": "65ca807b7bfc58200ae0e5c46fcec1e31096cbf5", "paper_id": "openreview/ICLR-2022-conf/fQTlgI2qZqE", "generation": "The evidence does not provide explicit information regarding whether the performance improvement of the parace model observed on the selected synthetic datasets (p = 10) is expected to generalize to other datasets. The context only states that the method was tested on 10 different synthetic datasets to demonstrate its superiority, but it does not address the model's performance on additional or different datasets. Therefore, no conclusion can be drawn about the expected boost in performance on other datasets."}
{"question_id": "496d254bdc722d815fb98ad3903cdc34df700fce", "paper_id": "openreview/ICLR-2023-conf/KDhFkA6MQsW", "generation": "The complexity bound in Theorem 2 is given as \n\n\\[\nO\\left(\\frac{\\Delta_f \\ell^{1/2} \\rho^{1/4}}{\\epsilon^{7/4}} \\log\\left(\\frac{\\ell \\sqrt{d} \\Delta_f}{\\delta \\epsilon^2}\\right)\\right),\n\\]\n\nwhich explicitly includes factors of \\(\\ell^{1/2}\\), \\(\\rho^{1/4}\\), and a logarithmic term involving \\(\\ell\\), \\(\\sqrt{d}\\), \\(\\Delta_f\\), \\(\\delta\\), and \\(\\epsilon\\). The exponent on \\(\\epsilon\\) is \\(7/4 = 1.75\\), consistent with the denominator in the question's expression.\n\nHowever, the expression \\(t = \\frac{\\Delta_f}{\\epsilon^{1.75}} \\log d\\) omits the dependence on the smoothness parameter \\(\\ell\\), the Hessian Lipschitz constant \\(\\rho\\), and the failure probability \\(\\delta\\). It also simplifies the logarithmic term to \\(\\log d\\), ignoring the more complex argument inside the logarithm in Theorem 2.\n\nTherefore, \\(t = \\frac{\\Delta_f}{\\epsilon^{1.75}} \\log d\\) does not match the complexity in Theorem 2 because it neglects the multiplicative factors \\(\\ell^{1/2}\\) and \\(\\rho^{1/4}\\), the dependence on \\(\\delta\\), and the full logarithmic term involving \\(\\ell\\), \\(\\sqrt{d}\\), \\(\\Delta_f\\), \\(\\delta\\), and \\(\\epsilon\\). These omissions lead to an incomplete and less precise characterization of the iteration complexity."}
{"question_id": "dc3fd256c5702edb18e7a21a01836945f7bc0b17", "paper_id": "openreview/ICLR-2023-conf/WlbG820mRH-", "generation": "The limitations on aggregation, combination, and readout components restrict the applicability of the results to GNN models that are at most as expressive as those considered in the paper. Specifically, Theorem 2 and Corollary 2 apply only to GNNs with expressiveness no greater than the models studied, and cannot be directly extended to more expressive models such as DropGNN. Conversely, Theorem 1 and Corollary 1 hold for GNNs that are at least as expressive as the considered models, including minor variations like different piecewise-linear activation functions. However, when moving beyond message-passing neural networks (MPNNs) or spatial-based models, the question of formal verifiability\u2014and thus the applicability of these results\u2014remains open. Therefore, the constraints on these components limit the direct translation of the theoretical results to GNN architectures that differ significantly in expressiveness or structural design."}
{"question_id": "1b3c40fd196db55e9ffea18c2b7d9ffe988c5ad2", "paper_id": "openreview/ICLR-2022-conf/IfNu7Dr-3fQ", "generation": "The kernel \\( k_{split} \\) is used to compute similarity measures between data points or sets within the algorithm. Specifically, it is involved in calculating differences of kernel values between original and probabilistically swapped data points, as well as between elements of sets \\( S \\) and \\( S' \\) relative to these points. These computations are used to assign scores or points to children after probabilistic swapping, indicating that \\( k_{split} \\) serves as a fundamental component for evaluating and guiding the swapping process based on kernel-based similarity assessments."}
{"question_id": "888ba5daeae0d5b3d5120c824c8f61abd5b77ee3", "paper_id": "openreview/ICLR-2022-conf/IfNu7Dr-3fQ", "generation": "The difference in error between the KT+ and ST (Hitch IMQ K) methods when comparing coreset size can be explained by the theoretical integration error guarantees. When compressing from \\( n \\) to \\(\\sqrt{n}\\) points, Theorem 1 and Remark 1 guarantee an integration error of order \\( O(n^{-\\frac{1}{2}} \\sqrt{\\log n}) \\) with high probability for any fixed function \\( f \\in H_{\\text{split}} \\). This represents a near-quadratic improvement over the integration error of order \\(\\Omega(n^{-\\frac{1}{4}})\\) associated with \\(\\sqrt{n}\\) i.i.d. points, which is the baseline error rate for the ST method. The KT+ method achieves this improved error rate because it leverages kernel-based compression techniques that apply to any kernel on any space, including unbounded kernels and those with slowly decaying square roots. Consequently, KT+ attains significantly lower integration error for the same coreset size compared to ST (Hitch IMQ K), reflecting a more efficient compression and better approximation quality."}
{"question_id": "2e474adb8f0c5bd3285e43db4bfb774e7cd5b7a5", "paper_id": "nlpeer/ARR-22/78d7b31f3d19cf16bcc71aa109ca56b923d9082a2919fd8008060641daf4d5ae37d70daea5bb36f4aa83c9c49ad8b44e597aa41960348f3a0abe0abb529f9aea", "generation": "The proposed knowledge prompting method is less effective when using smaller language models as the knowledge source. Specifically, the 1.3B and 0.4B knowledge models do not provide a significant improvement in performance. While the largest GPT-3 model (175B) yields a 10.5% improvement, a relatively large model such as the 6.7B knowledge model still provides a meaningful gain (5.0%), indicating that the knowledge generation model needs to be relatively large to generate useful and reliable knowledge, but it does not necessarily have to be the largest version."}
{"question_id": "5af1f02badf7c044e04f7544f4881486216b9f42", "paper_id": "openreview/ICLR-2023-conf/OTbRTIY4YS", "generation": "The new metric, concept purity, is introduced because the existing metrics provided in Yuan et al. (2020) are not suitable for evaluating the quality of concepts in the context of the Global Explainer for GNNs. Specifically, the prior metrics do not adequately capture the compositionality or the logical structure of the explanations derived from graphical concepts. Concept purity is designed to measure how well a concept corresponds to a single, coherent logical explanation, addressing the limitations of previous metrics that cannot identify compositionality in the returned explanations."}
{"question_id": "a74c71ff53a5ff84cacb938350996a66ceb0ae12", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The training process for the proposal network in Phase I involves training the network to generate object proposals using the same architecture as OLN described in Kim et al. (2021). This phase focuses on learning to produce initial object proposals before proceeding to further refinement or subsequent training phases."}
{"question_id": "4191cd3e5bd482f3d097b689c9857bf3d727f98b", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The depth and normal maps are combined for training the proposal network in Phase-I by either stacking the two geometric cues together and training a single object proposal network on these stacked inputs, or by training two separate object proposal networks on each cue and then merging their pseudo boxes into a single pseudo box pool for Phase-II training. Empirically, ensembling pseudo labels from the two separate networks is slightly better than using stacked inputs for Phase-I training."}
{"question_id": "5739894b5714e42337f53319a265bb28e2f6e18d", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Yes, using RGB images in addition to depth and normal images in the first step would lead to worse performance. When RGB is stacked with geometric cues to train the proposal network, the model tends to rely more heavily on the RGB input, which is a stronger signal in a closed-world setup. This reliance prevents the model from effectively utilizing geometric cues to discover novel objects in the first phase, which is critical for open-world object detection. Consequently, stacking RGB with geometric cues results in inferior performance across multiple benchmarks."}
{"question_id": "a4439f559ec40c32bb7edf1ee7fa3a854ed2b883", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Combining RGB, depth, and normals (GOOD-All) results in only small performance gains in detecting small objects (ARs), which are insufficient to compensate for the losses in detecting medium and large objects (ARm and ARl). Therefore, the overall performance is inferior, indicating that combining these modalities does not significantly improve the model's performance for smaller classes."}
{"question_id": "888f26be8b81ccb011c79b043940eec47d9b414e", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The same object proposal network is trained separately on either the depth input or the normal input using the identical training loss described in Phase-I training. This approach allows the network architecture and training procedure to be consistent while being applied to different geometric cue inputs (depth or normal) for discovering unannotated novel objects."}
{"question_id": "c12d6fb12967631fd985c4b437ee77cd1d2a8e2e", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The provided information does not mention the use of any filtering when combining multiple modalities for the model. It only describes the preprocessing step for RGB images, specifically padding images to sizes divisible by 32 without resizing before feeding them to the DPT-Hybrid model."}
{"question_id": "68cdad6fbee4b667c3a6b10e5761a4ccf660e32b", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "The data does not include an analysis of how the location of a country within its time zone affects the results. However, it is suggested that with larger scale data collection, a finer-grained analysis could be performed to examine the correlation between sunrise and sunset times in specific locations within each country and the times indicated by workers in these locations. This implies that such detailed location-based analysis has not yet been conducted."}
{"question_id": "e7ac4283196f09643f681e08d6093e9313983cf1", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "Yes, the translation of the template \u201cthe <morning> starts at <9.00>\u201d has been adapted to account for languages with grammatical gender. In Italian, for example, the word for morning (mattina) is feminine, requiring the determiner \"la,\" whereas the word for afternoon (pomeriggio) is masculine, requiring the determiner \"il.\" This results in variations in the translated templates, such as \"la mattina\" versus \"il pomeriggio.\""}
{"question_id": "f21290e4ef8f0ead710c484502ad8e103217da11", "paper_id": "openreview/NeurIPS-2022-dabt/76w7bsdViZf", "generation": "Yes, the authors have evaluated other deep classifiers on ImageNet, including versions of VGG (specifically VGG16) and DenseNet (specifically DenseNet161), in addition to the transformer and convolutional neural networks discussed in the main text."}
{"question_id": "cf66689ffef1b230b7bab23901a8bf528a8e97f8", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "The paper incorporates section titles into the Bag of Sentences (BOS) representation by enriching the sentence representation with a Section Title Embedding (STE) or classified STEs. This approach integrates the semantic information of section titles directly into the sentence-level features, thereby enhancing the overall BOS representation."}
{"question_id": "691280cb66aae7b9fee2d8ac0937e5f970437b43", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "Yes, human evaluation results for the text generation task are presented through case analysis of two samples showcased in Figure 3. The evaluation compares extractive summaries predicted by the HiStruct+ model and a baseline model against the gold summary (the abstract of the paper). The HiStruct+ model demonstrates superiority by selecting more informative sentences at later positions, covering key parts of the gold summary, including statistical data, mention of a novel strain, and conclusions. In contrast, the baseline model tends to select the first sentences, focusing on detailed background knowledge but lacking an overview of the proposed work. The HiStruct+ model's summaries provide a more comprehensive introduction to the main content and conclusions, as indicated by highlighted phrases and sentence positions."}
{"question_id": "8e2cb1c95dffd133cc91ab3123074a0853c829fb", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "The authors were motivated to experiment with their model on CNN/DM because the dataset represents a document type with more conspicuous hierarchical structures, characterized by larger hi-depth and hi-width. They hypothesized that their proposed method would perform better on datasets exhibiting these pronounced hierarchical characteristics."}
{"question_id": "a7d741be648d514c67c1a0468a78782b19c6d11c", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Label smoothing in the HP stage (lsHP) is described as a simple yet effective method to manipulate labels, ensuring that a certain amount of energy is reserved for feature adaptation even when the HP training accuracy converges rapidly to over 90%. This approach allows for more feature adaptation without changing the HP-train-accuracy. However, the information does not explicitly state that label smoothing always improves performance; rather, it highlights its usefulness in scenarios where rapid convergence might limit adaptation. There is no mention of cases where label smoothing degrades performance. Therefore, while label smoothing can be helpful in maintaining feature adaptation, it is not explicitly claimed to universally improve performance or to never degrade it."}
{"question_id": "953feae01ae0b8d2066fd035c079f0a5dd581aaf", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Label smoothing does not always improve the performance of the hyperparameter-fine tuning procedure. It is effective when the pretrained features are well-established and the standard hyperparameter tuning converges quickly to a high accuracy (90%+). However, if the hyperparameter training accuracy is too low and the assumption that the initial prediction converges to the labels no longer holds, label smoothing may not provide enhancement."}
{"question_id": "e111e75817d67b6fbeec06d5ba117b3419bf2f0f", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The initial value being discussed in Section 3.2 is the pretrained parameters from the function \\( f(x; B) \\)."}
{"question_id": "ca87a914265cffe46bfb63e2e24a3568efbc7888", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Yes, there are cases where the 'end' histogram does not match e_y when using different head types for hyperparameter tuning. By setting the labels during hyperparameter tuning as \u03b7_HP e_y + (1 \u2212 \u03b7_HP) * u, where u is a uniform K-class categorical distribution, the hyperparameter tuning stage can always reserve at least (1 \u2212 \u03b7_HP) * \u2225e_y \u2212 u\u2225\u00b2 energy for subsequent feature adaptation, even as \u03c4 approaches infinity. This implies that the 'end' histogram is a mixture of e_y and the uniform distribution u, and therefore does not exactly match e_y."}
{"question_id": "ffe260fb92f4c53395118a567f59d32fd365c351", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The degree of separability when adapting a model to a task is influenced by how far the pretrained features are from the optimal features for the downstream task. When the pretrained features are significantly different from the optimal ones, substantial feature adaptation is required to make the originally overlapped features more separable without altering the underlying manifold of the features excessively. This balance is crucial in transfer learning scenarios to achieve desirable feature separability."}
{"question_id": "28241e220ebb3458ef6d5eaf6829f8563161a7c1", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Mild adaptation, characterized by a reasonably large \u03c4, results in the feature vector zT being stretched in a similar direction, increasing its norm (\u2225zT\u22252) while maintaining a small change in cosine similarity. This transformation makes originally overlapped features more separable without significantly altering the manifold structure of the features, which is beneficial in transfer learning scenarios."}
{"question_id": "9b256b585691520864c3cf7d1b8cfb8f863d6663", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The results of the study imply that the pretrained parameters from the function f(x; B) retain all information from the pretraining task, including any inherent bias and noise. This suggests that the pretrained model may carry over undesirable characteristics from the pretraining phase, which has implications for the effectiveness and reliability of subsequent fine-tuning or application."}
{"question_id": "e0bf6addede2ca2d1e76eac67027e72e3ff385f5", "paper_id": "nlpeer/ARR-22/3cfcbbad78a71ca6cf4a5b2bbaee2a7ad8a1de295cf9f4103408dfeaf38a0de01b2b8d23cb94e0ef0b5f76a1947815f0335a7077bdea03ea4a29f6a2bff432cc", "generation": "Hanja is considered an extinct language rather than a script because it is treated as a language entity in the conventions of previous works, as indicated by the statement that \"we will refer to Hanja as a language following the conventions of the previous works.\" This implies that Hanja is conceptualized and analyzed as a language system rather than merely a writing system or script."}
{"question_id": "f29ff7d6be64035f374fe6b3fc470453591154e9", "paper_id": "nlpeer/COLING2020/1570", "generation": "The provided information does not specify whether the annotated mistakes will be released upon the release of ManyNames v2 (MNV2)."}
{"question_id": "36fdc759d8b028d2f3c0c5cb9e8c26b5744962d0", "paper_id": "openreview/ICLR-2023-conf/OM7doLjQbOQ", "generation": "The provided information does not include any direct comparison of the performance of ILA-DA with different intermediate layers to other transferable attack methods such as LinBP or the CTM family. The data only indicates that ILA-DA consistently outperforms ILA across various intermediate layers and that its performance is not highly sensitive to the exact layer choice near the default layer proposed by Huang et al. (2019). No evidence is given regarding how ILA-DA compares to LinBP or CTM methods."}
{"question_id": "99d5ca18b41cac7092cd7ca7cf0888b8a29a3018", "paper_id": "openreview/ICLR-2023-conf/OM7doLjQbOQ", "generation": "The new automated image transformation strategy, AutoDA, can be applied to other transfer-based methods such as MI-FGSM. When applied to attacks without existing data augmentation, like I-FGSM and MI-FGSM, AutoDA significantly improves attack transferability. However, for attacks that already use a fixed augmentation scheme, such as the CTM family, AutoDA does not improve performance and may even reduce the attack success rate due to excessive distortions."}
{"question_id": "9246fb2439ec9512f9298b927660f030736765c0", "paper_id": "openreview/ICLR-2023-conf/T2Ncx_PN2K", "generation": "Yes, the training process for the proposed model is truly end-to-end. All parameters of the modular network are trained jointly by maximizing the likelihood of the output sequence given the input speech over the entire dataset. The training objective, referred to as the RNNT-loss, is computed by marginalizing over all possible alignments between the input frames and output tokens using an efficient forward-backward algorithm, enabling end-to-end optimization of the model parameters."}
{"question_id": "9ff146fb1145a7e6cd038252a41b96f5c6ac0494", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "Yes, synthetic data generated from the CIFAR-100 label space at different scales (1\u00d7, 2\u00d7, 3\u00d7 the size of ImageNet-1K data, i.e., 1.2M, 2.4M, and 3.6M samples) is used to pre-train the model. The model's performance after pre-training on this synthetic data is then evaluated by fine-tuning on the ground-truth CIFAR-100 dataset. This setup allows comparison between models trained from scratch, models pre-trained on ImageNet-1K ground-truth data, and models pre-trained on synthetic data, demonstrating that synthetic data can reduce the performance gap and even outperform ImageNet-1K pre-training at larger synthetic data scales."}
{"question_id": "9f2536e9ca279c79b121794af57c2ad02d6b13c8", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "The use of synthetic data improves model performance primarily when the amount of real ground-truth data is limited, as it can boost few-shot learning. However, the positive impact of synthetic data gradually diminishes as the quantity of real data increases. Therefore, when a large amount of ground-truth data is available, the benefit of synthetic data is reduced."}
{"question_id": "f8b91940d2ce9e4e0df966f18d724e12b5aac0e5", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "In a fully-supervised setting, the use of synthetic data for image recognition results in a significant improvement in performance. Specifically, training with synthetic data alone achieves a top-1 accuracy of 75.3%, which is higher than the 74.1% accuracy obtained when training with real data alone. Furthermore, combining synthetic data with real data further enhances performance, yielding a top-1 accuracy of 76.3%. This indicates that synthetic data not only serves as an effective substitute for real data but also provides complementary information that improves image recognition accuracy when used together with real data."}
{"question_id": "94d3d227741b9bf8258649cb3567dc790b9dca07", "paper_id": "nlpeer/ARR-22/91d9e763eb1470028002e5c97689ada4b74f30be17291a14458f7542c387a9f2a7516e60f4022d887bdbd2165aa8cce6d566db380e5a82af677917c48efc2446", "generation": "The authors use regularization for label embeddings to encourage the label representations to be dispersed uniformly in the unit hypersphere, which helps maintain stable and well-separated label anchors. In contrast, instance embeddings are not regularized in this manner because the label representation serves as a stable anchor that can be consistently updated, providing a more stable reference for comparison. This stability reduces the need for regularization on instance embeddings."}
{"question_id": "e57f2a5a860c3aa8c1e0f8ca5a3375dd735d463c", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "The performance drop after 100 epochs in Figure 4 (c) is negligible, about 0.4%, indicating that the models have achieved convergence around 100 epochs. This convergence corresponds to reaching a local minimum associated with near-optimal architectures, rather than a significant performance collapse."}
{"question_id": "cbd5e6e55ec199de5569c76823febc8a19d28e5e", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "Yes, the NAS method can be trained on a large-scale dataset such as ImageNet. The search and evaluation were performed on the ImageNet dataset using a model with 8 layers during the search phase and 14 layers during evaluation, following the PC-DARTS setting. The method utilized a subset of ImageNet for optimizing parameters and was evaluated over 250 epochs with a larger model configuration."}
{"question_id": "14f24eacc79985de8d643389b87e35ceb5209775", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "The search cost of the proposed method is 0.8 GPU days on a GTX 1080 Ti GPU using the DARTS search space and CIFAR-10 dataset."}
{"question_id": "f18d6c2ac81007f7b42cab31234075307c85ee0a", "paper_id": "nlpeer/ARR-22/6262c349a3980c6850e252b3a44391b1b1318c26fbf29be55c61c0a32b8b626e5581c98234d1ecb410674c00ebcf3ba2adfdce41c2b2d32211721372a8ed1bc0", "generation": "Yes, the training languages used for the non-baseline models in the cross-lingual experiments include German. Specifically, the models are trained on speech in 8 languages with one speaker per language, which includes the Nancy dataset (English) and the CSS10 dataset. The CSS10 dataset contains a German subset from the speaker Karlsson, which is also used for fine-tuning. Thus, German is part of the training languages for the non-baseline models."}
{"question_id": "c0701214e646badb3bbb44e4985982c8e9e65fc7", "paper_id": "nlpeer/ARR-22/83dc885bef279cbb7a3b5b173494428b6cd043d34527016519250477d274fd1793fa0bddd2bd4bbb3edfa9709ddc85e90825a0554bfa8eceb8cb34d813c06c53", "generation": "To ensure the accuracy of the confidence network, the model is designed to avoid using high-layer hidden states for predicting confidence. Instead, the confidence level of each translation is approximated by counting the number of hints requested, with the model initially not receiving any hints and only asking for clues when it is uncertain about its decision. This approach prevents the model from lazily learning complex examples and encourages it to rely on its own confidence estimation rather than direct access to ground-truth labels."}
{"question_id": "ba8b96d10b44463b1ec163db65a29b6145f8729a", "paper_id": "openreview/ICLR-2023-conf/ySCL-NG_I3", "generation": "The learning curves for the HMR model in the rigid protein docking task demonstrate the progression of model performance over the course of training. The model selection criterion was based on the best validation average precision (AP) score in binding site prediction. This indicates that the training dynamics were monitored through the AP metric on a validation set, ensuring that the model chosen for testing on the DB5.5 dataset exhibited optimal binding site prediction capability. The learning curve likely shows an initial phase of improvement in AP as the model learns relevant features, followed by a plateau as the model converges. The selection of the model with the highest validation AP suggests that the training process effectively balanced learning and generalization, avoiding overfitting while maximizing predictive accuracy for the docking task."}
{"question_id": "0eb6095e3dbae2dd6e1abc90265e56378f49fa1a", "paper_id": "openreview/ICLR-2022-conf/ZTsoE8G3GG", "generation": "Yes, when using Algorithm 2 with larger vocabulary sizes, individual atoms can be added to the partial graph. The model is capable of either adding an entire motif in one step or generating atoms and bonds one-by-one, allowing it to generate arbitrary structures including individual atoms."}
{"question_id": "ff310f12cf0c134c9763ec3389c106e6c16dc65c", "paper_id": "openreview/ICLR-2022-conf/ZTsoE8G3GG", "generation": "The metric \"score\" refers to the raw performance score on the GuacaMol optimization benchmarks. The metric \"quality\" is defined as the absence of undesirable substructures in the generated molecules."}
{"question_id": "be0cd13d8445fb87a73943d5acf2e5089a02876c", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "The optimization described in equations 1-4 is performed over the geo-centric map using the accumulated information from observations and successive predictions. The policy is constructed to select goals from unobserved map locations based on this accumulated information. Therefore, the optimization does not occur solely over the set of map locations observed so far but also incorporates unobserved locations within the global map."}
{"question_id": "3f6d76b052a19c42fdc0f3fa7a03e99d76e053d5", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "Navigation tasks do not require the complete semantic layout of unseen regions to be solved; rather, they can leverage spatial associations between semantic entities encoded from prior knowledge. For example, an agent can infer the likely presence of certain objects in unobserved spaces based on partial observations and semantic relationships, such as expecting a fridge near an oven or a sofa in a different room. This inference is guided by internal mechanisms that quantify the uncertainty of information from unobserved areas, allowing decision making that balances prior knowledge and exploration. Thus, navigation can be accomplished by combining observed information with inferred semantic layouts of unseen regions."}
{"question_id": "46357f5d8816d410e6100ea03a5fde2f576ae270", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "The extracted pattern set, P, exhibits a biased similarity distribution characterized by a predominance of highly similar patterns, indicating class imbalance."}
{"question_id": "67314f99bdc98da9611efbf0de1f4660e36f629c", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "The sampling method used to extract patterns with a time window involves computing an average daily pattern from historical data, consisting of 288 speed data points representing 24 hours at 5-minute intervals for each vertex. Patterns are then extracted by slicing these daily patterns using a given window size \\( T' \\), resulting in a set of patterns \\( P \\) where the number of patterns is \\( |P| = N \\times \\left\\lfloor \\frac{288}{T'} \\right\\rfloor \\)."}
{"question_id": "030389c23b9697a71ca59dec3bcab088d7f20ced", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "No, equation 1 is a summation from j=1 to k, not from j=0 to k."}
{"question_id": "05fe45ed14b202a953eb1a25a0c4552d1d2ff42d", "paper_id": "openreview/ICLR-2022-conf/k7efTb0un9z", "generation": "The proposed method, GNS, can be applied to simpler models such as multilayer perceptrons (MLPs). This is demonstrated by the evaluation of training a 3-layer MLP with a hidden size of 128 on the Fashion MNIST dataset, where GNS achieved the best test accuracy of 89.8%."}
{"question_id": "280960bc073f24e47cd5b63da7388c21eb12d9be", "paper_id": "openreview/ICLR-2023-conf/pWVASryOyFw", "generation": "The CNN classifier was selected for sentiment analysis experiments to ensure a fair comparison, as it was pre-trained for two epochs following the methodology of Liu et al. (2019)."}
{"question_id": "d59bc31fea9ec1c2594f0ed7813ed2d9348abc75", "paper_id": "nlpeer/COLING2020/939", "generation": "The task described in the paper focuses on predicting direct hypernyms, whereas taxonomy induction involves constructing or extending a taxonomy, which may include predicting indirect hypernyms through methods such as the transitive closure of WordNet. Thus, the key difference is that the paper emphasizes direct hypernym prediction, while taxonomy induction encompasses broader hierarchical relationship construction, including indirect hypernym prediction."}
{"question_id": "fd9af00fe3f20196d71e9e364f55c157d4cd2cd3", "paper_id": "nlpeer/COLING2020/939", "generation": "To ensure that paths were not shared between the training and test sets, hyponyms that appeared as hyponyms in the training set were excluded from the test and validation sets. Additionally, both validation and test sets were filtered to include only hyponym queries that were unseen anywhere in the full taxonomy paths of the training data."}
{"question_id": "d8ac040e919b01e19818a6416896dd66bd58e69d", "paper_id": "nlpeer/COLING2020/939", "generation": "The success of the path-based model can be attributed to framing the generation task as direct hypernym prediction by reversing the target hypernym path, which simplifies the decoder's learning process. Additionally, training with the more difficult objective of entire hypernym path prediction may result in a stronger model. The attention mechanism also plays a crucial role by preventing the model from \"forgetting\" the source hyponym during the decoding of long hypernym paths."}
{"question_id": "06b380902968cd38bbb66c2a75d9372c2f039f2f", "paper_id": "openreview/NeurIPS-2022-dabt/in7XC5RcjEn", "generation": "A graph with an average diameter in the range of 10 to 50 is indicative of the presence of long-range relationships because such a large diameter implies that the graph size is sufficiently large to separate the performance of local message-passing graph neural networks (MP-GNNs) from models that explicitly model long-range interactions (LRIs). Specifically, the PascalVOC-SP dataset, which has an average diameter of approximately 27.62 \u00b1 2.13, is cited as having significantly larger graph metrics compared to datasets like MNIST with much smaller diameters (6.03 \u00b1). This substantial difference in diameter supports the conclusion that larger graph sizes, reflected in larger average diameters, are necessary to capture and evaluate long-range relationships effectively."}
{"question_id": "3611098cfd2590d775531ef564d87617713fe8bf", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The rationale for using ESE methods on user-generated text is that user-generated text data is abundant and largely unlabeled, and enabling NLP applications such as semantic search and question answering over this data requires entities mined from these unlabeled sources. Additionally, user-generated text has distinctive characteristics compared to well-written text, making it necessary to study and apply ESE methods specifically to this type of data."}
{"question_id": "9d285bc752521120d3b45a5b35069f1365c8f603", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "Set expansion approaches are used to construct a dictionary from customer reviews because they enable the enhancement of a small seed set of entities with new entities that belong to the same semantic concept. This is particularly important in domains where training data is scarce, as set expansion methods can learn to rank new entity candidates with limited supervision, thereby effectively expanding the entity set relevant to the domain of interest."}
{"question_id": "ae75b890d30e2879b6a6571bbc634ee4e6157e30", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "Measuring the performance of a method using MAP at gold-k is particularly appropriate for evaluation on benchmarks designed to stress test methods. This approach includes more instances of multifaceted, vague, and non-named entities that would otherwise be ignored in smaller k evaluations, thereby providing a more comprehensive assessment of a method's effectiveness in challenging scenarios."}
{"question_id": "23c1d98a22e68ab8a92b7b1cd2fee83fa79e9a86", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The optimal depth of a ranked list of entities returned by an entity search engine approach for a user to find all relevant entities is 200. Entities with rank greater than 200 are labeled as all negatives, indicating that relevant entities are expected to be found within the top 200 ranks."}
{"question_id": "f2564b011de1ce166a9e4410c3857b5a9c2496ce", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The significant performance difference between CGExpan and LM-Base can be explained by their differing treatment of entities with diverse contexts. CGExpan, a state-of-the-art method, scores each candidate entity by selecting one positive concept and multiple negative concepts, which tends to penalize entities that belong to multiple concepts (multi-faceted entities) or are mentioned in different contexts (vague entities). This characteristic leads to lower recall for such challenging entities, especially in user-generated text. In contrast, simpler baseline methods like LM-Base do not impose such penalties and therefore achieve better performance on user-generated text benchmarks, with LM-Base obtaining the best overall performance despite not being optimal on well-curated benchmarks."}
{"question_id": "8952c8598f43e3e36131d56d62db44fded0352d3", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The improved performance of the lm-base model compared to the CGExpan model on user-generated datasets is attributed to the fact that CGExpan penalizes entities belonging to multiple concepts (multi-faceted entities) or mentioned in different contexts (vague entities). User-generated text often contains such entities with diverse contexts, which negatively affects the performance of methods like CGExpan that rely on selecting one positive concept and multiple negative concepts. In contrast, the lm-base model is less affected by these characteristics, leading to better recall and overall performance on user-generated datasets."}
{"question_id": "fd627c54f792bc91ab0db4a51e8311a23d8af60d", "paper_id": "nlpeer/ARR-22/82520f1d31572d488cfeae02712db642750fda4063e5eab8a771a000f6e17e1397ab18af73e5362e4d660b3dcc78b2260e121fdefc4c1664448e1c30066d5cb9", "generation": "Trait scores for prompts that did not have them in the original dataset were obtained by using the trait-specific scores provided by Mathias and Bhattacharyya (2018a)."}
{"question_id": "c0ddf26bae180b57c24cd4b90e7a0da4a0676425", "paper_id": "openreview/ICLR-2022-conf/KTPuIsx4pmo", "generation": "A \"real-world pushing task\" refers to an experimental setup in which a UR5 robotic arm is used to physically push an object with the objective of moving it to a designated pink target area. The task is conducted in a tangible environment and monitored using an RGB camera to capture the interaction."}
{"question_id": "c0f788c455af0bee35fb5b9cc36af6a5ec6aaf3f", "paper_id": "nlpeer/ARR-22/212dff6380418cd7c050ec757b31e6b5b65bbe922aa30ffbd5950fe7a04ca737b7c7b3d706f1cd1502d7932b61d2b7c079924793e45e437526230c1e9c0626ed", "generation": "Additional fine-tuning on CLIP was not included in the experiments because fine-tuning the last few layers of CLIP with a classification head consistently resulted in worse performance compared to using extracted features for classification with an SVM."}
{"question_id": "e26fc7a2455a2acb2de4d608a7ca7bf1c8fb62a1", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "For a neural module to be \"in charge of solving one specific subproblem\" means that each module is responsible for addressing a distinct and well-defined component of the overall problem. There exists a one-to-one and onto mapping between the set of subproblems and the set of neural modules, such that each module corresponds uniquely to a particular subproblem. For example, in a robotic task, one module might specifically handle finding an object's grasping point. This modular design ensures that each neural module specializes in solving a single, specific subtask within the broader problem framework."}
{"question_id": "bf76bbb77fabe1a9105b55efcd591d179958b2c6", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "To optimize the reward with respect to the possible combinations of modules means to perform an exhaustive search over all potential combinations of the initialized modules and select the combination that yields the highest reward. This process involves evaluating the reward associated with each possible module combination and identifying the configuration that maximizes the reward value."}
{"question_id": "1b206d1d36f66f3d336a33e34858567e8a593ab0", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The system architecture is a modular neural network designed to solve complex tasks by decomposing them into specific subproblems, each handled by a dedicated neural module. Each module \\( m_i \\) corresponds one-to-one with a subproblem \\( F_i \\), and all tasks requiring \\( F_i \\) share the same module \\( m_i \\). The overall network for a task is constructed by chaining these modules sequentially, replicating a graph structure where the state is factorized into module-specific components. Each module receives only the relevant subset of the state variables necessary for its subproblem.\n\nIn the robotics domain, the architecture includes distinct modules for processing the robot state, static objects, target objects, and agent dynamics, arranged in a fixed order reflecting the task structure. For example, the static object module processes five input channels through two convolutional blocks with ReLU activations and max pooling, producing a representation passed to subsequent modules. The target object module preprocesses its input similarly, concatenates with the static object module output, and applies another convolutional block. The agent module preprocesses its input, concatenates with the target object module output, and passes this through separate actor and critic multi-layer perceptrons (MLPs) with a single hidden layer of 64 tanh units.\n\nThe robot module processes robot and goal states through two hidden tanh layers of 64 units each, concatenates this with the object module output, and passes the result through a linear output layer. For continuous control with PPO, separate networks are used for the actor and critic, maintaining the same graph structure. The critic additionally receives the action as input to the robot module.\n\nIn discrete 2-D tasks, the graph structure processes static objects first, then target objects, and finally agent dynamics. At each depth \\( d \\), the agent selects from \\( k_d \\) modules, each a small neural network taking the module-specific state and the previous module's output as input. For example, the obstacle module uses a single hidden tanh layer with 32 units; the object module preprocesses its input with a tanh layer of 32 units, concatenates with the obstacle module output, and applies another tanh layer of 32 units; the robot module follows similarly.\n\nThus, the architecture is a hierarchical, modular neural network with specialized modules for subproblems, sequentially connected to reflect task structure, employing convolutional and fully connected layers with tanh and ReLU activations, and separate actor-critic networks for reinforcement learning control."}
{"question_id": "5219ee2947eb66850d9df883d32c6549b914d086", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The system contains three modules."}
{"question_id": "a3566edd083568caf0264186c9b8e1658c31e561", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The rationale behind the design of the different modules is to assign each neural module mi to solve a specific subproblem Fi, establishing a one-to-one and onto mapping from subproblems to modules. This design ensures that all tasks requiring the solution of Fi share the same module mi. Each module receives only the subset of state components necessary to solve its corresponding subproblem, thereby limiting its input to relevant information and requiring it to output only the solution to that subproblem. This modular approach avoids the brittle dependencies induced by chaining structures, where changes in one module can have cascading effects on subsequent modules. By factoring the state into module-specific components and restricting each module's input accordingly, the architecture promotes generalization to unseen combinations of modules in lifelong learning settings, where modules must perform well after training on only a few tasks in sequence."}
{"question_id": "b6cb81cf492f5369fa4051c1d2e90b05b0aa9247", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The modules were partitioned according to specific subproblems \\( F_i \\), with a one-to-one and onto mapping from subproblems to modules. Each module \\( m_i \\) is responsible for solving one particular subproblem and receives only the subset of the state components relevant to that subproblem. For example, in the robotics domain, robot modules receive only the robot-related state components. The architecture assumes a factorization of the state into module-specific components, such that each module processes only the necessary variables for its subproblem.\n\nThe modular network is constructed by chaining modules in sequence, replicating a graph structure where the processing order follows the nature of the task. For discrete 2-D tasks, the graph structure processes static objects first, then target objects, and finally agent dynamics. In the robotics domain, the obstacle module processes the obstacle state first, followed by the object module which pre-processes the object state and concatenates it with the obstacle module output, and lastly the robot module which processes the robot and goal states, concatenates with the object module output, and includes the action input for the critic network.\n\nThus, the partitioning of modules is hierarchical and sequential, with each module specialized for a subproblem and receiving only the relevant subset of the overall state, enabling modular composition tailored to each task."}
{"question_id": "be6ee11df60dadea667438571e3ed15560c3cb04", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The MOMA-LRG dataset differs from the MOMA dataset in several key aspects: it introduces a new dataset with an order of magnitude more annotations and includes longer videos from a greater variety of scenes. Additionally, MOMA-LRG employs activity graphs as the overarching graphical representation across all three levels of hierarchy, whereas MOMA uses activity graphs only at the atomic level. Furthermore, MOMA-LRG is directly motivated by the limitations of vision-language models (VLMs) and aims to provide a single overarching task for evaluating VLMs on complex activity recognition."}
{"question_id": "1740b93cc1257022895a050e975d38feebe0f904", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The activity graph is the key abstraction of MOMA-LRG and serves as an all-encompassing and human-interpretable representation of human activities that captures temporal changes and compositionality. This indicates that the activity graph enables evaluation of activities at different levels of granularity by representing both the temporal dynamics and the compositional structure of activities."}
{"question_id": "e774f0bb72932f381463769b74f98a8f360db732", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "GraphVLM is introduced as a framework for evaluating vision-language models (VLMs) on activity parsing, which consists of an activity parsing model and a transfer learning paradigm. It is designed to work with activity graphs that serve as an abstraction on top of the MOMA dataset, enabling structured representation and analysis of activities within the dataset. Thus, GraphVLM operationalizes the activity graphs by providing a model and learning approach specifically tailored for parsing and understanding the structured activity information encoded in these graphs."}
{"question_id": "0dcfbcf7b77777639a682294aaf99c3fff25cd20", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The authors ensure that the natural language sentences produced from the \"ground truth\" activity graphs accurately describe the scene by using graphical annotations with two specific conventions. First, predicate classes are structured as [src] [predicate] [trg], where \"src\" is the source entity and \"trg\" is the target entity. This structure allows for straightforward conversion of graphical annotations into natural language sentences. For example, an outgoing predicate edge labeled [src] talking to [trg] from the entity \"cashier\" to the entity \"customer\" can be directly converted into the sentence \"the cashier is talking to a customer.\" Second, all annotations are expressed in the present continuous tense, such as \"the player is throwing a frisbee,\" which resembles live narration. This tense choice aligns the annotations with natural, real-time descriptions of the scene, similar to those found in existing video-language datasets derived from instructional videos. Together, these conventions enable accurate and natural language descriptions that reflect the dynamic activities depicted in the scene."}
{"question_id": "a37fa0862f28bd4005c14092d19b86f84c30c983", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The videos and classes for the study were selected based on the criteria that the classes are common and the videos are long. This selection was motivated by the fact that complex human activities in real-world settings are usually hierarchical and compositional across space and time."}
{"question_id": "507cdeff564fe9a3d5fe258fe00eef340d135d9b", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The data for the dataset was selected with ethical considerations that included obtaining informed consent from all participants and ensuring that the data collection process complied with privacy regulations. Additionally, the dataset was curated to avoid any personally identifiable information, thereby protecting participant anonymity and confidentiality."}
{"question_id": "e5d8459c3ebc7cdeb1a56ddced28a7467921a917", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The dataset source is YouTube videos."}
{"question_id": "62968052606a9490b4add4170997a2c69f19d4ec", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "Yes, the dataset is licensed under a Creative Commons license, specifically the Attribution 4.0 International (CC BY 4.0) license."}
{"question_id": "5c090b48e2d8b39f413f602a716b92676b7e7ba7", "paper_id": "openreview/ICLR-2023-conf/8efJYMBrNb", "generation": "The baselines were used with default parameters, indicating that no adjustable parameters were modified to improve performance in the comparison with BetaAlign."}
{"question_id": "5ac9e91c8d313af9a0771c5e3e46e95d2a7c2315", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "The new evaluation metric proposed in the paper is the partial match precision, recall, and F1. This metric measures the overlap between predicted spans and ground truth answer spans by treating each as a string and computing the length of the longest common substring (LCS) between each prediction and ground truth pair. The partial retrieved score (sretij) is defined as the length of the LCS divided by the length of the predicted span, and the partial relevant score (srelij) is defined as the length of the LCS divided by the length of the ground truth span."}
{"question_id": "8c6b063b9a5318af6557db02c0c7dbc93f8939be", "paper_id": "openreview/NeurIPS-2022-dabt/HYELrdRdJI", "generation": "The 9k multi-view sets are constructed from the 16k real captured views. Specifically, the multi-view sets are formed by combining sampled views from the total 16k views, with each multi-view set containing between two to six views."}
{"question_id": "3318142bc7bd1401191fcc4a9712243c0df0f1df", "paper_id": "openreview/NeurIPS-2022-dabt/HYELrdRdJI", "generation": "High inter-class view similarity refers to the phenomenon where different objects appear similar or identical when observed from specific viewpoints. In such cases, humans are unable to accurately classify the object at a fine-grained, instance-level from these views, leading to uncertainty in assigning class labels. These particular views, where human judgment is uncertain, are termed uninformative views. The discrepancy between the definitive one-hot class labels and the inherent human uncertainty in these views results in multi-view label noise, meaning that the labels associated with multiple views of the same object may be inconsistent or noisy due to this uncertainty."}
{"question_id": "61588ca196125738d21c2d191ecac13249af297d", "paper_id": "openreview/ICLR-2022-conf/oiZJwC_fyS", "generation": "The algorithm is tested on compressing the linear layers of convolutional neural networks, which are typically larger and more complex than simple fully connected layers. This indicates that the algorithm can be applied and tested on larger networks."}
{"question_id": "42aec356c91c672d813f540951f0b79d9f57705f", "paper_id": "openreview/ICLR-2022-conf/oiZJwC_fyS", "generation": "DeepNN is a deep neural network architecture with layers of size 28 \u00d7 28, 512, 256, 128, and 10."}
{"question_id": "7cd7d4d944b16a2603aaaf3ebb5628edd813a346", "paper_id": "nlpeer/ARR-22/3cbf91f8e2f7e62be51109688c84a2b424e6018ee3c976fbe55e5331bc9ab2f4c46491d006087aed3be35a3370c945c100bd718e2c1131de19eac3dbe93326bc", "generation": "The criteria for delimiter placement in the sentence aggregation model is determined by the output \u03b4i, where \u03b4i = 1 indicates that the neighboring facts should be mentioned separately and thus the neighboring sentences should not be fused, while \u03b4i = 0 indicates that the facts should be aggregated and their corresponding sentences should be fused. Training examples are created by setting \u03b4i = 0 for sentence pairs that were originally aggregated (i.e., resulted from splitting a single sentence) and \u03b4i = 1 otherwise."}
{"question_id": "b49d17a4084f479d1c1f3e920a863be370968f66", "paper_id": "nlpeer/ARR-22/3cbf91f8e2f7e62be51109688c84a2b424e6018ee3c976fbe55e5331bc9ab2f4c46491d006087aed3be35a3370c945c100bd718e2c1131de19eac3dbe93326bc", "generation": "The per-triple templates were developed manually by hand-crafting a single template for each predicate. This involved creating a specific template for each of the 354 predicates, including those not seen in the test set, and transforming triples into facts using these single-triple templates."}
{"question_id": "86b7bff4eb8f5701bb87715221c22a2db29eaae1", "paper_id": "openreview/ICLR-2022-conf/sPIFuucA3F", "generation": "The data tuple (xt, at, rt) in Algorithm 1, line 4, is retrieved from the offline dataset Dn."}
{"question_id": "e1369f11b53bb858522bacf4bf2e9d8448dc1ef5", "paper_id": "openreview/ICLR-2022-conf/EnwCZixjSh", "generation": "It is important for the metric to be insensitive to the number of layers when evaluating the rank correlations because the number of layers is a factor of variation in the GIN configuration that affects model weights and perturbations applied. Insensitivity ensures that the metric's evaluation of rank correlation reflects the true relationship between the metric and model performance rather than being confounded by architectural differences such as layer depth. This allows for a fair and consistent comparison of metric effectiveness across different GIN configurations."}
{"question_id": "eb715a337474694a5b2fa3212f3936f2979ff998", "paper_id": "openreview/ICLR-2023-conf/kDEL91Dufpa", "generation": "Yes, different contrastive learning methods can be represented using Definition 3.2, as evidenced by the analysis of their criteria:\n\n1. **DCL-sq / DCL-abs**:  \n   The criterion for DCL-sq / abs is given by  \n   \\[\n   L_{DCL} = \\sum_{i=1}^N -\\log \\left( \\frac{e^{f(K_{\\cdot,i}^T K'_{\\cdot,i})/\\tau}}{\\sum_{j \\neq i} e^{f(K_{\\cdot,i}^T K_{\\cdot,j})/\\tau}} \\right) = \\sum_{i=1}^N -\\frac{f(K_{\\cdot,i}^T K'_{\\cdot,i})}{\\tau} + \\log \\left( \\sum_{j \\neq i} e^{f(K_{\\cdot,i}^T K_{\\cdot,j})/\\tau} \\right),\n   \\]\n   where \\(f(x) = x^2\\) for DCL-sq or \\(f(x) = |x|\\) for DCL-abs, and embeddings are \\(\\ell_2\\)-normalized column-wise. The second term is a LogSumExp (LSE) over negative pairs' similarities, which approximates the max operator. Minimizing this criterion encourages a diagonal Gram matrix, consistent with the sample-contrastive criterion in Definition 3.2.\n\n2. **SimCLR-sq / SimCLR-abs**:  \n   The SimCLR criterion is  \n   \\[\n   L_{SimCLR} = \\sum_{i=1}^N -\\log \\left( \\frac{e^{f(K_{\\cdot,i}^T K'_{\\cdot,i})/\\tau}}{e^{f(K_{\\cdot,i}^T K'_{\\cdot,i})/\\tau} + \\sum_{j \\neq i} e^{f(K_{\\cdot,i}^T K_{\\cdot,j})/\\tau}} \\right) = \\sum_{i=1}^N -\\frac{f(K_{\\cdot,i}^T K'_{\\cdot,i})}{\\tau} + \\log \\left( e^{f(K_{\\cdot,i}^T K'_{\\cdot,i})/\\tau} + \\sum_{j \\neq i} e^{f(K_{\\cdot,i}^T K_{\\cdot,j})/\\tau} \\right).\n   \\]\n   Although the positive pair similarity appears in the denominator, minimizing this loss also requires minimizing the similarities between negative pairs, which leads to a diagonal Gram matrix. Thus, SimCLR-sq and SimCLR-abs are also sample-contrastive methods consistent with Definition 3.2.\n\n3. **Spectral Contrastive Loss (SCL)**:  \n   The SCL criterion is  \n   \\[\n   L_{SCL} = -\\frac{2}{N} \\sum_{i=1}^N K_{\\cdot,i}^T K'_{\\cdot,i} + \\sum_{j \\neq i} (K_{\\cdot,i}^T K_{\\cdot,j})^2 = -2 \\sum_{i=1}^N K_{\\cdot,i}^T K'_{\\cdot,i} + \\| K^T K - \\operatorname{diag}(K^T K) \\|_F^2,\n   \\]\n   which explicitly penalizes off-diagonal elements of the Gram matrix, enforcing a diagonal structure. This formulation directly falls into the sample-contrastive category as per Definition 3.2.\n\nIn summary, the criteria of DCL-sq/abs, SimCLR-sq/abs, and Spectral Contrastive Loss all aim to minimize off-diagonal similarities in the Gram matrix of embeddings, thereby conforming to the sample-contrastive framework formalized in Definition 3.2."}
{"question_id": "cd38abc68b46b12d953fddc8838eb77978963fca", "paper_id": "openreview/ICLR-2023-conf/kDEL91Dufpa", "generation": "In Proposition 3.1, DCL and SimCLR are defined as follows, assuming that the matrix \\( K \\) is \\(\\ell_2\\) normalized column-wise (i.e., embeddings are normalized):\n\n- **DCL (Decoupled Contrastive Loss):**  \n\\[\nL_{\\text{DCL}} = \\sum_{i=1}^N -\\log \\left( \\frac{e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau}}{\\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau}} \\right) = \\sum_{i=1}^N \\left( -\\frac{K_{\\cdot,i}^T K'_{\\cdot,i}}{\\tau} + \\log \\left( \\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau} \\right) \\right).\n\\]\n\n- **SimCLR (Simple Contrastive Learning):**  \n\\[\nL_{\\text{SimCLR}} = \\sum_{i=1}^N -\\log \\left( \\frac{e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau}}{e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau} + \\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau}} \\right) = \\sum_{i=1}^N \\left( -\\frac{K_{\\cdot,i}^T K'_{\\cdot,i}}{\\tau} + \\log \\left( e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau} + \\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau} \\right) \\right).\n\\]\n\nHere, \\( K_{\\cdot,i} \\) and \\( K'_{\\cdot,i} \\) denote the \\(i\\)-th columns of matrices \\( K \\) and \\( K' \\), respectively, and \\(\\tau\\) is a temperature parameter."}
{"question_id": "19e742075a73b79f8d1593d22060ea47535a485a", "paper_id": "openreview/ICLR-2022-conf/8uz0EWPQIMu", "generation": "The purpose of Figure 3a is to demonstrate the Standard accuracy pattern observed across the experimental configurations, where all top-to-bottom rankings consistently outperform the random ranking, which in turn outperforms all bottom-to-top rankings."}
{"question_id": "4b00407f8b0e58aafdaee12f078de8b313872f04", "paper_id": "openreview/ICLR-2022-conf/8uz0EWPQIMu", "generation": "Yes, the proposed alternate method differs from the original linear model proposed by Dalvi et al. (2019). While Dalvi et al.'s neuron ranking algorithm distributes neurons equally among labels regardless of their actual importance, the alternate method computes the mean absolute value of the weights associated with each neuron and ranks neurons based on this value from highest to lowest. This approach allows the ranking to reflect the true importance of neurons for each label, avoiding the equal distribution constraint and empirically providing better results, especially for large label sets."}
{"question_id": "c908b12fc3ea26161680a836fc0ee29b02fd4e96", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "Region classification can be performed by computing Visual-Language Model (VLM) scores using cosine similarity between the region visual features and text embeddings of both the known categories (CB) and novel categories (CN). Specifically, for a given region feature vector \\( v_b \\), the scores \\( w(v_b) \\) are obtained by applying a Softmax function over the cosine similarities between \\( v_b \\) and the text embeddings of all class names in the combined vocabulary \\( CB \\cup CN \\), scaled by a fixed temperature \\( T \\). This approach allows classification even when novel categories have a large vocabulary of class names not present in the image, as the model evaluates similarity across the entire set of text embeddings at inference time."}
{"question_id": "3e6d53b8861714d6727e6f1a924eb2046baac6a7", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "The accuracy of the model decreases when novel categories with large vocabularies are added to the set of candidate categories. This decline occurs because the model's softmax weighting function, which relies on cosine similarity between the visual feature vector and the text embeddings of all candidate categories, must normalize over a larger combined set of base and novel categories. As the number of novel categories increases, the denominator in the softmax function grows, leading to a dilution of the probability mass assigned to each category and thus reducing classification accuracy."}
{"question_id": "1eafcdeb90458c749f5b2e6dcdaaa06a4ba58abd", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "The phrase \"but train the detector head with \\( r(\\cdot) \\) online in a single stage\" means that, unlike previous methods which crop and resize RGB image regions and precompute their embeddings in an offline process, the current approach integrates the training of the detector head and the function \\( r(\\cdot) \\) directly within the main training pipeline. This training occurs simultaneously and continuously (\"online\") during the single-stage training procedure, without separating the embedding computation into a distinct offline step."}
{"question_id": "f117dce3beae4a1fc909bbadebcc235634d017c0", "paper_id": "openreview/NeurIPS-2022-dabt/7w-a8PYPlP", "generation": "The seismic forward modeling applied absorbing boundary conditions."}
{"question_id": "601d6dade2b1d6724ae69aafc64a71bafd79062e", "paper_id": "openreview/NeurIPS-2022-dabt/7w-a8PYPlP", "generation": "The published code will include the forward modeling algorithm used to generate the data, as it follows the algorithm available at the specified URL: https://csim.kaust.edu.sa/files/SeismicInversion/Chapter.FD/lab.FD2.8/lab.html."}
{"question_id": "5420a636705116e4e99d17572011f028d54a72b2", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "Prior work related to this paper has primarily used the standard Root Mean Square Error (RMSE) on test data as a metric. However, this approach is recognized as insufficient because it does not capture important optimization criteria in Scientific Machine Learning (ML), such as adherence to the underlying physics. Consequently, the paper emphasizes the need for additional metrics that go beyond RMSE, including those that evaluate physical properties and the ability of ML surrogates to extrapolate to unseen time steps and parameters. These novel metrics aim to provide a deeper and more holistic understanding of the surrogate's behavior from both data and physics perspectives."}
{"question_id": "fcd1a1d599ae695d923bfabe4f62e5e457ca2de1", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "The training time for the machine learning (ML) surrogate model represents the highest computational demand. However, once the ML models are trained, their predictions can be computed multiple orders of magnitude more efficiently than numerical PDE solvers. Therefore, despite the initial high training cost, the surrogate model significantly speeds up the evaluation of the dynamic system during inference, outweighing the time requirements for training."}
{"question_id": "fef7c2c7a69370b666710942e3d931819e7bc02d", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "The accuracy of the surrogate directly impacts the accuracy of the downstream task because surrogate models are used to approximate complex, non-differentiable physical simulation models. If the surrogate model accurately represents the underlying physical system, it enables effective optimization, control, sensitivity analysis, and inverse inference. Conversely, inaccuracies in the surrogate model can lead to errors in these downstream tasks, as the surrogate's predictions guide the decision-making and analysis processes. Therefore, the fidelity of the surrogate is critical to ensuring reliable and precise outcomes in subsequent applications."}
{"question_id": "8397ceb9d91201c9f2eb30de3c87e1e8243a827d", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The conceptual model presented in Figure 2 illustrates the relationship between gamified learning elements, motivation, social influence, and recycling intention among youths. It highlights how game components such as leaderboards, levels, points, and rewards serve as engaging and purposeful activities that enhance intrinsic and extrinsic motivations. Social influence, particularly competition and peer comparison through leaderboards, plays a significant role in encouraging youths to participate in recycling. The model integrates these factors to explain how gamification can effectively improve recycling intention."}
{"question_id": "174eff40340ae0616a2328f75efd8cf8431b3150", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The conceptual model in Figure 2 was developed through a two-level Experience-Grounded Learning (EGL) activity involving 100 youths aged 16\u201323 from one high school and two colleges in Selangor, Malaysia. Participants were recruited via activity posters and word-of-mouth from teachers and lecturers, provided written consent, and engaged in the EGL activity. Their crafts were evaluated by art teachers and ranked on a leaderboard, with top-ranked participants receiving certificates and prizes. Following this, 29 students demonstrating excellent achievement, commitment, and attention were purposively selected for focus group discussions (FGD), which contributed to the development of the conceptual model."}
{"question_id": "127423c37403d9d9d34a21c17bfb33234b5f944a", "paper_id": "openreview/ICLR-2023-conf/tYIMtogyee", "generation": "The authors did not use a validation set to tune the hyperparameters on the QM9 dataset."}
{"question_id": "73faf71e5e746272235608cd46a8fda2b309ef70", "paper_id": "openreview/ICLR-2023-conf/tYIMtogyee", "generation": "The authors refer to tuning \"approximately\" 5 values on the QM9 dataset as the process of adjusting a small number of hyperparameters or model parameters\u2014around five in total\u2014during the experimental setup. This indicates that the tuning involves a limited set of key parameters rather than an extensive or exhaustive search over many variables."}
{"question_id": "41338acb67bcf333b2817c15b8b74290fb3d9327", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "The downstream tasks of natural language inference, question answering, and semantic role labeling have been tested using an encoder transferred from language 1 to language 2 without any parameter updates."}
{"question_id": "d3ce17368ff699857360c15972ad48cb235350b8", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "Setting the dimension of the word and discourse vector to 10 causes the entire token distribution to become close to the Zipfian distribution because this specific dimensionality was found empirically to produce such an effect. The choice of dimension 10 aligns the model's token distribution with the statistical properties characteristic of natural language, as represented by the Zipfian distribution."}
{"question_id": "46780f3f8ab86a46782f68b7ca66e5e1438afc01", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "An image's radius was considered \"too big\" if its adversarial distance R\u2032 was more than 33% larger than the certified radius R of the image x."}
{"question_id": "bc6f50621da1a65a6e46211a4f48751a6da35304", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The paper defines the \"adversarial L2 ball\" around an adversarial example \\(\\tilde{x}\\) as the radius \\(R_{\\text{adv}}\\) of an L2 ball centered at \\(\\tilde{x}\\) within which all points are certified to have the same adversarial class as \\(\\tilde{x}\\) on the smoothed classifier \\(g\\). This means that every sample inside this L2 ball shares the identical adversarial classification assigned to \\(\\tilde{x}\\), rather than having different classifications compared to \\(\\tilde{x}\\). Thus, the adversarial L2 ball represents a region of consistent adversarial classification around the adversarial example, as certified by the smoothing procedure with parameters \\(\\sigma\\) and \\(\\alpha\\)."}
{"question_id": "d7f32782ecbb103a3971b1e3a918576549b44723", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The motivation for building provably robust adversarial examples is to address the limitations of empirically robust adversarial examples, which are defined by large input regions verified through the expectation over transformations (EoT). Although empirically robust examples can exhibit high EoT values (e.g., 95%), the measure is not uniform, as specially chosen subregions within these examples can have very low EoT scores. Provably robust adversarial examples aim to provide stronger guarantees of robustness that are not subject to such non-uniformity in verification."}
{"question_id": "77d9dbb3a3af5156b369e66924d4bcf14f794893", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The criteria used to evaluate \"provability\" in the paper is the ability to formally prove that a large connected neural network input region, defined by a set of perturbations of an input, contains only adversarial examples."}
{"question_id": "2a76dc6fa246d4afcfe9aea7050a47aa3949d755", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "No, the camera pose is not concatenated with the latent code in the same way as the EG3D method. Instead, the appearance code \\( z_a \\) and the camera pose \\( c \\) are projected into an intermediate appearance code \\( w_a \\) for content synthesis."}
{"question_id": "****************************************", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "Yes, the authors observed temporal inconsistency, which can be interpreted as jittering, in the processed videos. To address this issue, they applied a low-pass Gaussian filter to smooth the results."}
{"question_id": "f87855d105235224a2584b0e0716b794ef647705", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "Yes, the authors addressed jittering in the processed videos by applying a low-pass Gaussian filter to smooth the estimated keypoints before warping the images."}
{"question_id": "4006c4e45f822e15f9ccc27df354f3bcd8298509", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "Using alignment for video preprocessing instead of a fixed cropping window can lead to temporal jittering artifacts. This occurs because alignment may introduce inconsistencies in the spatial positioning of the subject across frames, disrupting temporal coherence. In contrast, a fixed cropping window maintains consistent spatial framing throughout the video, thereby preserving temporal stability."}
{"question_id": "d88d8f911add3ead1f649741f7c22700199f9094", "paper_id": "nlpeer/ARR-22/ed81d66dc55496061fd4c97530be7b966d2e734e8435092b2c0c6764f54ea145e62f67555e480d996aebb93772e0de2877ccaad504e0f2228a1395c68d157eda", "generation": "Yes, the code to reproduce all results from the paper will be made available to the public at the following GitHub repository: https://github.com/facebookresearch/quip."}
{"question_id": "1fd3a7fbc973d1042fee519c269028acdbb1ccec", "paper_id": "nlpeer/COLING2020/341", "generation": "The antecedent is included in the sum in #6 to represent the combination of both the dominant and alternative word orders within the language. Specifically, the notation v`p + v`q denotes that p is an alternative word order of q in the language `, thereby capturing both the original (antecedent) and the reordered structures. This approach contrasts with using a single transformation such as \"verb-nsubj:noun-obj:noun => nsubj:noun-verb-obj:noun,\" which would only represent a direct substitution without accounting for the coexistence of multiple word orders. Including the antecedent in the sum thus allows for a more comprehensive representation of word order variation by summing the contributions of both the dominant and alternative orders."}
{"question_id": "df9756e054d7db2937ebc51e1ed8477345e57387", "paper_id": "nlpeer/COLING2020/341", "generation": "The logical atom \"nsubj:noun-verb-obj:noun\" represents a construction involving a verb as the head, with two dependents: a nominal subject (nsubj) that is a noun, and a direct object (obj) that is also a noun. The subscripts indicate that the head is a verb, and the dependents are connected to the head by their dependency relations (nsubj and obj) combined with their part-of-speech tags (noun). This construction is used to analyze the relative frequency of occurrences where one verb head simultaneously governs two noun dependents in the roles of subject and object."}
{"question_id": "ff4b45b2af4e13f58512fc1783fc12dd129feb6f", "paper_id": "egu/esurf/11-917-2023", "generation": "The model incorporates mechanical and biological erosion under marine conditions by using a maximum weathering rate of 100 m Myr\u207b\u00b9. This rate accounts for weathering decay in the marine environment, which implies that wave erosion, as a component of marine mechanical erosion, is included in the model."}
{"question_id": "70ee52925bfa4ed21c8798964ec74fcdd0f9845f", "paper_id": "egu/esurf/11-917-2023", "generation": "The subaerial erosion rate is not explicitly stated as equivalent to the maximum weathering rate. However, within the Dionisos environment, a maximum subaerial weathering rate is considered, and a value of 100 m Myr\u207b\u00b9 is utilized for maximum subaerial weathering. This indicates that the maximum subaerial weathering rate is a defined parameter, but there is no direct statement equating the subaerial erosion rate to this maximum weathering rate."}
{"question_id": "a8d6ed01ff1866040e47a7082ff97ea95a6edd03", "paper_id": "egu/esurf/11-917-2023", "generation": "Yes, there is a scientific reason for the Greenland Ice Sheet (GrIS) to start melting before the Antarctic Ice Sheet (AIS). This reasoning is based on possible discrepancies in the stability of the GrIS and AIS, as well as variable hemisphere-specific climate fluctuations. These differences in climate behavior between the Northern and Southern Hemispheres can lead to asynchronous melting patterns of the two ice sheets, consistent with interpretations of fossil reef sequences from Western Australia."}
{"question_id": "fc938634e35cc53e7b6cb50564929eabb0fc7afe", "paper_id": "egu/esurf/11-917-2023", "generation": "\"Production versus depth curves\" refer to graphical representations that illustrate the non-linear relationship between the growth rate of carbonate producers and the depth at which they occur. These curves depict how carbonate production varies as a function of water depth, highlighting that the rate of carbonate accumulation does not change linearly with increasing depth."}
{"question_id": "9be6e9b8ef3f3db6f29bafd74243978f78f2f657", "paper_id": "egu/esurf/11-917-2023", "generation": "The phrase \"actual exposed MIS 5e facies are lacking\" refers to the absence or scarcity of corresponding MIS 5e sequences within synthetic well logs, despite the presence of preserved exposed fossil MIS 5e reef facies at locations such as Lembetabe. This discrepancy is attributed to a \"preservation bias,\" meaning that while the fossil reef facies are physically exposed and identifiable at the surface, their representation in subsurface synthetic well log data is sometimes missing or incomplete."}
{"question_id": "e7b7d480aa1076d06dccb8bcb2b7f2b1fd0f9c87", "paper_id": "egu/esurf/11-917-2023", "generation": "The model presented in the study has spatial limitations and is not easily applied to other geographical areas without further analysis. It provides initial insights into the sensitivity of the Last Interglacial (LIG) geological record but requires additional investigation, particularly in regions with more diverse glacial isostatic adjustment (GIA) and tectonic settings. Specific efforts should focus on testing locations where relative sea-level (RSL) curves similar to the modeled scenario have been described, indicating that the model's applicability depends on the geological and geophysical context of the area under consideration."}
{"question_id": "eeba4b725b3f8ad526cddce373ff444a591594c9", "paper_id": "nlpeer/ARR-22/df4051328ee57291cf600f10ef67af6872fcef0deb3ff7323b89142b68beb16ea1fbe09c44be8ccca24fe30d67a1e6dbfb715a77384c30fbeb37c362c25c743c", "generation": "The difference in performance between the DICTA test set and the new test set, particularly in the cha and wor scores, can be explained by the composition and diversity of the datasets. The DICTA test set is relatively small and non-diverse, consisting solely of 22 documents originating from a single source\u2014Hebrew Wikipedia articles. In contrast, the new test set was created from a larger variety of texts, including high-quality Wikipedia articles, edited news stories, and user-generated blog posts. This greater diversity in the new test set likely introduces more variability and complexity, which can affect the cha and wor scores, leading to differences in performance compared to the more homogeneous DICTA test set."}
{"question_id": "9373d254f956bcbffe53a9ba10531f5102ecdb83", "paper_id": "openreview/ICLR-2023-conf/dSYoPjM5J_W", "generation": "The authors justify the claim that the gradient-based attack causes the shift between test and training data by explaining the distinct focus of the two loss components involved. The training loss (Ltrain) is primarily related to the local structure of the training nodes, and thus, when computing the gradient \u2207ALatk, only the adjacency matrix parts associated with training nodes\u2014specifically the Train-Train and Train-Test connections\u2014are modified. In contrast, the self-supervised loss (Lself) relies on pseudo labels of unlabeled nodes, leading to modifications mainly around the unlabeled nodes. This differential focus in gradient computation and adjacency modification explains how the gradient-based attack induces a structural shift that affects the relationship between training and test data."}
{"question_id": "6c9381de277251ad2ce40cd39b94a872cbc4126e", "paper_id": "egu/esd/14-81-2023", "generation": "Yes, the authors conclude that the accuracy of the CMIP6 climate models in simulating the processes relevant to the spatial pattern of forced change in mean and extreme precipitation is supported by the agreement between observed data and model predictions. This conclusion is strengthened by the consistency in the variability of the model-forced response estimates and observed forced response estimates, as well as the comparable distributions of the residuals from the linear fit to both observed and modeled forced response estimates. The consistency in residual variance justifies the use of the model-derived fingerprint on observations and reduces the likelihood of spurious detection, thereby supporting the models' accuracy."}
{"question_id": "6610ad96e462f49d6d8f20fee0cdc6dd8a70175a", "paper_id": "egu/esd/14-81-2023", "generation": "If RR-based fingerprint construction is not applied, the detection or signal-to-noise ratio (SNR) is reduced. This is because RR-based fingerprint construction enhances the SNR by effectively isolating the forced climate signal from internal variability. Without this method, the fingerprint used for detection and attribution contains more noise relative to the forced signal, impairing the robustness of detection and attribution of forced changes in the water cycle."}
{"question_id": "7c6f77a64467e8275e381a36386d66650b13e832", "paper_id": "nlpeer/F1000-22/11-404", "generation": "The term \"AES\" (aging effect of smiling) refers specifically to the phenomenon that smiling faces are estimated to be older than neutral faces in direct age estimations."}
{"question_id": "69d95e16f2f308754136f8e2245592ca5497ff5f", "paper_id": "openreview/ICLR-2023-conf/xYlJRpzZtsY", "generation": "In the \"roscoe training\" process, the model is trained using pairs consisting of the context and reference reasoning steps as positive samples (s, r), which are treated as similar pairs. Additionally, pairs consisting of the context and perturbed reference steps (s, h) are used as hard-negative pairs, which are treated as different."}
{"question_id": "911fa2b76fba7e5ae58fb8322c5940c42acbd848", "paper_id": "openreview/ICLR-2023-conf/xYlJRpzZtsY", "generation": "The proposed taxonomy covers several reasoning errors and the designed metrics evaluate a spectrum of criteria, including those in the taxonomy. Although it is acknowledged that the taxonomy does not cover all possible reasoning errors, the metrics are generic enough to work on natural language rationales and consider alignment with both the input context and the generated explanation. This indicates that the taxonomy and metrics are sufficiently broad and adaptable, supporting their comprehensiveness in evaluating generic reasoning errors."}
{"question_id": "aab380aaa605fffff2d765c9cb058cfc03ee1729", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The rationale for only keeping scenarios that cause collision with at least two algorithms is to ensure high transferability across autonomous driving (AD) algorithms and to maintain high risk levels. This approach improves both the effectiveness and efficiency of AD evaluation by focusing on scenarios that are consistently challenging and relevant to multiple algorithms."}
{"question_id": "42b9bcc5c85c3d4087d4f57791f953fa732fc625", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The scenarios used in the paper were selected because they represent the most representative and challenging driving scenarios of pre-crash traffic, as summarized by the National Highway Traffic Safety Administration (NHTSA). These scenarios are safety-critical traffic testing scenarios that capture key conditions relevant to evaluating traffic safety."}
{"question_id": "5b14dc7213f8e7181d9bf848cef4fb79a7b1ad10", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The performance of scenario generation algorithms is evaluated using metrics at three levels: Safety level, Functionality level, and Etiquette level. Within each level, several specific metrics are designed to focus on different aspects of performance. An overall score is then calculated as a weighted sum of all these evaluation metrics."}
{"question_id": "de2dc4d1f8b898e5b34a256294729fe7b46f6fda", "paper_id": "openreview/ICLR-2023-conf/ZrEbzL9eQ3W", "generation": "The win conditions used in the board games studied are as follows: In Connect Four, players win by connecting four of their tokens in a line. In Pentago, players win by connecting a line of five tokens."}
{"question_id": "f2e744ebd60bf15d94cd1b9a5cdc7db9f0c4ad93", "paper_id": "openreview/ICLR-2023-conf/ZrEbzL9eQ3W", "generation": "The playing strength of agents is evaluated by restricting them using a fixed number of MCTS steps (iterations) rather than by time limits. Specifically, the evaluation uses a fixed number of 300 MCTS steps both at training and testing time."}
{"question_id": "6d0c861407de2db08718ca55a383b59284a8e223", "paper_id": "nlpeer/PeerRead-CONLL2016/129", "generation": "The number of selected in-domain sentences is determined by the performance on held-out data."}
{"question_id": "d07dca8f8e126c43dacdaf145ec4103ef25400f5", "paper_id": "nlpeer/PeerRead-ACL2017/561", "generation": "Not having access to pre-trained language model (LM) embeddings would result in a decrease in performance for the supervised sequence tagging tasks. Specifically, the overall F1 score for the CoNLL 2003 Named Entity Recognition (NER) task would drop from 91.93% to 90.87%, representing more than a 1% absolute decrease. Similarly, for the CoNLL 2000 Chunking task, the F1 score would decrease from 96.37% to 95.00%. Thus, the absence of pre-trained LM embeddings leads to a significant reduction in accuracy, indicating that the context-sensitive representations captured by these embeddings are crucial for achieving state-of-the-art performance."}
{"question_id": "9505c56639fea265e46601d12575e9d9715b9e7a", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "The variational approximation of the posterior density \\( p(c \\mid D_s^j, x_q^{0:T}) \\) is given by \\( q_{\\zeta}(c \\mid D_s^j \\cup x_q^{0:T}) \\), which shares the same meta set-embedding model and is formed by augmenting the support set \\( D_s^j \\) with the query set \\( x_q^{0:T} \\)."}
{"question_id": "7110b14e5ab532a6273415a059f6808204376ee6", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "The sequential neural process (SNP) setting is not included as a comparison because few-shot learning with the rest of the structured latent variable models (SLVMs) has not yet been reported in the literature. Instead, the study selected the deep Kalman filter (DKF) as a representative SLVM and extended it into a feed-forward meta-formulation via a variant of the SNP (meta-DKF) for comparison."}
{"question_id": "2f75586071f2de4ab14810c7f2bd7f7b4e143fb6", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "Popular meta-learning frameworks like MAML and Probabilistic MAML are not considered in the experiments because their extensions to state-space latent variable models (SLVMs) face significant challenges related to stability and convergence. Specifically, issues such as vanishing gradients over the complex computation graph make the application of MAML to models like DKF and GRU-res non-trivial, as noted in the literature."}
{"question_id": "064bd1dff89282732ddcf6c71a98975792d8b3d4", "paper_id": "openreview/ICLR-2023-conf/6xXtM8bFFJ", "generation": "The two-time-scale algorithm, specifically simSGDA-RR and altSGDA-RR, can be extended to the mini-batch setting by employing mini-batches of size \\( b \\geq 1 \\) in the update steps. The extension assumes that the total number of components \\( n \\) is an integer multiple of the mini-batch size \\( b \\), i.e., \\( n = bq \\) for some integer \\( q \\geq 1 \\). If \\( n \\) is not a multiple of \\( b \\), the method can be adapted by having \\( q-1 \\) mini-batches of size \\( b \\) and one mini-batch of size \\( s \\leq b \\).\n\nThe algorithm proceeds as follows:\n\n1. At each epoch \\( k \\), a random permutation \\( \\sigma_k \\) of the indices \\(\\{1, \\ldots, n\\}\\) is sampled uniformly.\n\n2. The indices are partitioned into \\( q \\) mini-batches \\( B_{kt} = \\{\\sigma_k(j) : b(t-1) < j \\leq bt\\} \\) for \\( t = 1, \\ldots, q \\).\n\n3. For each mini-batch \\( B_{kt} \\), the updates are performed by averaging the gradients over the mini-batch:\n\n   - The primal variable \\( x \\) is updated as\n   \\[\n   x_{kt} = x_{k,t-1} - \\frac{\\alpha}{b} \\sum_{i \\in B_{kt}} \\nabla_1 f_i(x_{k,t-1}, y_{k,t-1}).\n   \\]\n\n   - For the dual variable \\( y \\), two update schemes are considered:\n\n     - Simultaneous update (simSGDA-RR):\n     \\[\n     y_{kt} = y_{k,t-1} + \\frac{\\beta}{b} \\sum_{i \\in B_{kt}} \\nabla_2 f_i(x_{k,t-1}, y_{k,t-1}).\n     \\]\n\n     - Alternating update (altSGDA-RR):\n     \\[\n     y_{kt} = y_{k,t-1} + \\frac{\\beta}{b} \\sum_{i \\in B_{kt}} \\nabla_2 f_i(x_{kt}, y_{k,t-1}).\n     \\]\n\n4. At the end of the epoch, the initial points for the next epoch are set as\n\\[\n(x_{k+1,0}, y_{k+1,0}) = (x_{k,n/b}, y_{k,n/b}).\n\\]\n\nThis mini-batch extension generalizes the original two-time-scale algorithms, which correspond to the special case \\( b=1 \\). The convergence analysis and theoretical guarantees for the mini-batch versions are provided in the extended theorems (Theorems 4 and 5) in the appendix."}
{"question_id": "587b947b50e65e3caa8174633245ab39edbdb0f0", "paper_id": "egu/esurf/11-33-2023", "generation": "The provided information does not indicate that the authors conducted analyses using detrended data to isolate the effect of the temperature increase. It only states that the ERA5 reanalysis dataset was used to simulate the evolution of ground thermal regime and ground ice content from 1951 to 2019, during which mean air temperatures increased. There is no mention of detrended data analyses."}
{"question_id": "1818a27b73310c09fb736d5e044187417bda0dbc", "paper_id": "nlpeer/ARR-22/eeda0f4ff6c7aef2647481ec15f8ade5a9b32ec32b643e6918a0b8d0ffa1ba44a3f2b9171ba4ba44d5c0faf3199e3bc5f0f740ee31cf48cb966493a96f35ca71", "generation": "The experts who annotated the dataset include a female annotator with a professional background in Linguistics and two male annotators with a background in Computer Science."}
{"question_id": "83065c1670e7bbe6476efd1959f21480a4f3bf69", "paper_id": "nlpeer/ARR-22/eeda0f4ff6c7aef2647481ec15f8ade5a9b32ec32b643e6918a0b8d0ffa1ba44a3f2b9171ba4ba44d5c0faf3199e3bc5f0f740ee31cf48cb966493a96f35ca71", "generation": "In this work, \"knowledge\" refers to hard skills, which are considered knowledge components. \"Skill\" refers to the application of these hard skills, constituting skill components. \"Attitude\" corresponds to soft skills, which are also part of the skill components. Thus, knowledge represents the theoretical understanding or hard skills, skill denotes the practical application of these hard skills, and attitude encompasses the soft skills integrated within the skill components."}
{"question_id": "5c790742a803e76547d117cf4a77434d1737b5b1", "paper_id": "egu/esurf/11-849-2023", "generation": "Besides temperature and precipitation, factors that could influence the distribution of glaciers in a given area include precipitation type (rain versus snow), cloudiness, humidity, sublimation, solar radiation, lapse rate variations, descending airflow affecting humidity, topographic shading, and valley geomorphology. These factors affect glacier accumulation and melt by modulating solar radiation reaching the glacier surface, altering local air temperatures, influencing humidity and lapse rates, and creating spatial variability in ablation patterns."}
{"question_id": "cfbd6962220a29ddfda999443b628e02ebd2d79b", "paper_id": "egu/esurf/11-849-2023", "generation": "Yes, the orientation of the grid, with a higher proportion of north-facing slopes in the Elwha area compared to the Quinault area, affects the absorption of solar radiation. This difference in slope aspect influences the spatial variability of solar radiation absorption, which in turn impacts glacier distribution. Specifically, north-facing slopes receive less solar radiation due to shading effects, leading to reduced ablation and thus favoring glacier persistence and distribution in those areas."}
{"question_id": "836969164a688341782ffa72b87f1348ba1ee4ac", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "A total of 27 3D models were used in the pre-collection stage of the data collection process. This is because 3 replicas were constructed for each of the 9 distinct types of pathway surfaces, resulting in 3 \u00d7 9 = 27 replicas."}
{"question_id": "aae1c73c7b0de5fb88e34c245782e2ecb4dcb24d", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "The dataset is not balanced across different classes, as both up-steps and down-steps have slightly higher frequency than the other classes."}
{"question_id": "e232c66d9986ff1ac6f532437bd94f0b71a44ca5", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "The selection of sampling locations was guided by the observation that certain urban areas, particularly newly developed ones adhering to strict building regulations, differ from older areas where regulations may not have been properly followed. Sampling focused on locations with a high frequency of targeted surface discontinuities, as identified with the assistance of BLV volunteers. This targeted approach suggests that the chosen ten locations were selected to capture the diversity of surface conditions relevant to the study, implying that ten locations can be sufficient to represent the variety of surfaces in urban environments when chosen based on regulatory history and observed surface discontinuities."}
{"question_id": "f5fe5047a045ce5a97066fd72458d8951c846342", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "A total of 27 3D replicas were used in the survey, consisting of 3 replicas each for 9 distinct types of pathway surfaces."}
{"question_id": "3546db32608ccae0b45d96e051b10a8967437d6f", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "Only one single person was employed to take photos for the survey described in Section 2.1."}
{"question_id": "916923a8909ab06632f575b7f36db3ac70642419", "paper_id": "egu/esd/14-1261-2023", "generation": "The wave-induced diffusivity at the surface, denoted as \u03ba_v(0), is given in relation to its molecular counterpart, \u03ba_m, in Table 1. The values of the ratio \u03ba_v(0) / \u03ba_m range from 0.36 to 128.9, indicating that the wave-induced diffusivity \u03ba_v can be either smaller or significantly larger than the molecular diffusivity \u03ba_m depending on the wave parameters. Thus, the mixing efficiency represented by \u03ba_v varies over a wide range relative to \u03ba_m, from less than one to more than two orders of magnitude greater."}
{"question_id": "27642536b6affc6438fc0e5a5b2ce6b2d5208309", "paper_id": "egu/esd/14-1261-2023", "generation": "The study applies to both nearshore and offshore environments. It covers a range of conditions from shallow water to deep water, specifically for kh values between 0.5 and 2.0, which correspond to intermediate water depths relevant to surfzone dynamics. Deep-water conditions with kh greater than \u03c0 were excluded due to complete mixing effects within the analyzed time frame. Thus, the model is applicable to nearshore (shallow to intermediate depths) as well as some offshore (deeper) conditions within the specified kh range."}
{"question_id": "130985a6f0c94e81204c5a5014faa6017dc2a328", "paper_id": "egu/esd/14-1261-2023", "generation": "The problem under investigation is genuinely nonlinear. The approach described employs a fully nonlinear wavemaker model that allows for the modeling of non-breaking waves with strong nonlinearities, including amplitude dispersion, nonlinear wave\u2013wave interactions, and solitary wave propagation. This method goes beyond the applicability of weakly nonlinear approaches, indicating that the problem is treated within a fully nonlinear framework rather than a weakly nonlinear context."}
{"question_id": "09c36735b520089d4936e6966157cb10f8f1ed0e", "paper_id": "openreview/ICLR-2022-conf/iulEMLYh1uR", "generation": "Yes, when changing the depth or width of the model, all other hyperparameters are kept fixed based on the default values given by the referenced papers."}
{"question_id": "d250649b5c73368021f92321b3d59f4c1d3c762f", "paper_id": "openreview/ICLR-2022-conf/iulEMLYh1uR", "generation": "The msec/example in Figures 1 and 2 was computed using 64 TPU-V3 hardware."}
{"question_id": "e7cb5933a3df86f543cb36cb77b3f41cd7ad4021", "paper_id": "nlpeer/COLING2020/1367", "generation": "A rule-based approach has the advantage of not requiring large corpora to produce accurate models, which is particularly beneficial for Indigenous languages where existing corpora are typically insufficiently large to support effective statistical or neural methods."}
{"question_id": "cf91a671c7d2248b716cf143ff64e032fed4681e", "paper_id": "nlpeer/ARR-22/27046f6633d116b03e48eb94976921b46a86a976ddd2199ef76c90820a95e0d85406daa5ebc9d7b12359157d20f3bd0025e1cbc2c126bb2506f5e83e93f435b5", "generation": "The layer norm approximation leads to a slight degradation in performance across the tasks discussed in the paper. Specifically, the approximation results in a small drop in accuracy or effectiveness, indicating that while it may offer computational benefits, it introduces some loss in task performance."}
{"question_id": "08ee038d964c18feafe50974403477b69a786d82", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The variables and functions in equations 8-13 have the following dimensions:\n\n- \\( h_l^i \\in \\mathbb{R}^d \\), where \\( d \\) is the feature dimension.\n- \\( h_l^0 \\) is initialized as the zero vector in \\(\\mathbb{R}^d\\).\n- \\( X_l \\in \\mathbb{R}^{|R| \\times d} \\), the embedding matrix for the target predicate \\( P_h \\).\n- The embedding lookup function \\( f_{\\text{embdd}}(X, p) := u_p^T X \\) produces a vector in \\(\\mathbb{R}^d\\), where \\( u_p \\) is a one-hot indicator vector.\n- \\( W_P \\in \\mathbb{R}^{|R| \\times d} \\)\n- \\( W_{TR} \\in \\mathbb{R}^{|TR| \\times d} \\)\n- \\( W'_{TR} \\in \\mathbb{R}^{|TR| \\times 2d} \\)\n- \\( W_{Len} \\in \\mathbb{R}^{L \\times d} \\)\n- \\( b_P \\in \\mathbb{R}^{|R|} \\)\n- \\( b_{TR}, b'_{TR} \\in \\mathbb{R}^{|TR|} \\)\n- \\( b_{Len} \\in \\mathbb{R}^L \\)\n\nHere, \\(|R|\\) denotes the number of rules, \\(|TR|\\) denotes the number of target relations, \\(L\\) is the rule length, and \\(d\\) is the feature dimension."}
{"question_id": "006b4d78ff2835159d4e1f745a3f9c4f41fe8351", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The Markovian assumption in equation 6 is not valid if the current random walk step depends on a given relation type and starting time that never change during the random walk. This is because the Markovian assumption requires that the next state probability depends only on the current state probability, without consideration of previous visited edges or fixed parameters that persist throughout the walk. Since the relation type and starting time remain constant and influence the step selection, the process involves memory of these fixed conditions, violating the Markovian property which assumes memorylessness. Therefore, the calculation of the next state probability cannot be solely based on the current state probability under these conditions."}
{"question_id": "add8f70fda4a981fbac7e3f41f938eeecd3ccd4d", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The first kind of rule in Section 4 is called \"Markovian\" because the calculation of the next state probability depends solely on the current state probability, without consideration of previously visited edges. This implies that the random walk is memoryless, relying only on the present state to determine the next state, which is the defining characteristic of a Markov process."}
{"question_id": "95d0f3eec5444caddab3df7e45aa31db81cabef8", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The operator \\( M_i \\), under the corresponding Markovian constraints \\( C M_i = \\{ P_i, T R_i, l + 1 \\} \\), represents an adjacency matrix related to step \\( i \\). Specifically, the entry \\( (M_i, C M_i)_{x,y} \\) is defined as the maximum value of the function \\( f_{C M_i}(F) \\) over the set of facts \\( F \\in F_{y,x} \\), where \\( F_{y,x} \\) denotes the set of facts from entity \\( e_y \\) to entity \\( e_x \\). This operator encodes the adjacency matrix under Markovian constraints, with the maximum entry value set to 1."}
{"question_id": "317ee5566b85c3b36699add3f268020579e8b718", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The drawbacks of using statistical learning methods such as StreamLearner and TLogic include: (1) they estimate rule confidence by independently counting the number of supporting paths in the graph, which ignores interactions between different rules derived from the same positive example, potentially leading to over- or underestimation of confidence; (2) they cannot account for the similarity between different rules, so a reliable rule's confidence does not enhance the confidence of a similar but infrequent rule; and (3) their performance on interval-based temporal knowledge graphs (tKGs) is not demonstrated, despite the more complex temporal relations present in interval-based tKGs compared to timestamp-based ones."}
{"question_id": "85afe8245083d99893657bc1eeadbcefa12dbf59", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "\"1 dB in PSNR\" refers to an increase or difference of one decibel in the Peak Signal-to-Noise Ratio measurement, which quantifies the quality of a reconstructed or compressed image relative to the original."}
{"question_id": "53c8c64ae66712791ba2a355e4cc97a262c61acc", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "The provided context defines the rate-distortion (r-d) function as the infimum of the mutual information \\( I(X;Y) \\) over all random transforms \\( Q_{Y|X} \\) such that the expected distortion \\( \\mathbb{E}[\\rho(X,Y)] \\) is less than or equal to a threshold \\( D \\geq 0 \\). This definition inherently requires the expectation of the distortion to be well-defined.\n\nFor probability distributions where the expectation is undefined, such as the Cauchy distribution, the standard definition of the rate-distortion function based on the expected distortion \\( \\mathbb{E}[\\rho(X,Y)] \\leq D \\) cannot be directly applied because the expectation does not exist. The provided information does not specify an alternative or generalized definition of the rate-distortion function for such cases.\n\nTherefore, within the given context, the rate-distortion function is defined only for distributions where the expected distortion is well-defined, and no explicit definition is provided for distributions like the Cauchy distribution where the expectation is undefined."}
{"question_id": "1c5c6447a6539cda6bd264b7c657e95e6c314829", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "The proposed algorithms are described in a general setting that includes a general (i.e., discrete, continuous, or neither), unknown memoryless source. The source and reproduction spaces are assumed to be standard Borel spaces. Additionally, the standard assumption is made that various expectations with respect to the true data distribution \\( P_X \\) exist and can be approximated by sample averages. When the source alphabet is finite, this assumption automatically holds.\n\nTherefore, the algorithms are not restricted to Gaussian distributions or the \"banana\" source but can be applied to a broad class of probability distributions, including discrete, continuous, or other types of memoryless sources, as long as the assumptions about the source and reproduction spaces and the existence of expectations are satisfied."}
{"question_id": "33ac8263606098fca8bcdc4746cd3f4235387b26", "paper_id": "openreview/ICLR-2022-conf/0jP2n0YFmKG", "generation": "The new method, graph parallelism, outperforms pipeline parallelism, a form of model parallelism, for the considered models. This is because graph parallelism achieves better load balancing across GPUs by evenly distributing nodes, edges, and triplets within each batch, whereas pipeline parallelism struggles with load balancing due to varying graph sizes. Additionally, combining graph and pipeline parallelism shows complementary benefits. Although pipeline parallelism might be advantageous for very deep GNNs due to its inter-GPU communication overhead being independent of the number of blocks, overall, graph parallelism demonstrates superior performance compared to pipeline parallelism. Therefore, the new method runs faster than at least one existing distributed training method, pipeline parallelism."}
{"question_id": "ebd7cf5f3adbc674bd5ce88a563f3fea990873a9", "paper_id": "openreview/NeurIPS-2022-dabt/UrAYT2QwOX8", "generation": "The rationale for training on the first six months of data and validating the models on the last two months is that more recent data tends to be more faithful to the data\u2019s distribution when models are deployed in production. This approach aligns with common practice in the fraud domain and the strategy used with the original dataset, ensuring that the validation reflects the data characteristics the model will encounter in real-world use."}
{"question_id": "3b1176248b0cfc5fb6e1786bb4007f98aa2ac210", "paper_id": "openreview/NeurIPS-2022-dabt/UrAYT2QwOX8", "generation": "The first and second disparities are obtained by controlling the generative model sampling, specifically depending on the group and the label, respectively."}
{"question_id": "8f0ed4f134911b593527e3793459b6d55faf5923", "paper_id": "openreview/NeurIPS-2022-dabt/-VyJim9UBxQ", "generation": "The copyright for the images, referred to as \"User Content,\" is held by the users who created and uploaded them, not by Reddit."}
{"question_id": "413aa7a24c99874e0aae31b569348cc6c4e39b14", "paper_id": "nlpeer/F1000-22/10-170", "generation": "Recommendations include increasing budgetary allocation and improving efficiency, implementing targeted policy approaches, and ensuring a science-based professional response involving relevant experts such as public health professionals, infectious disease epidemiologists, health policy and systems experts, medical anthropologists, health economists, health communication experts, laboratory scientists, and clinicians. Special emphasis should be placed on multisectoral collaboration and coordination, extending engagement to religious leaders, cultural activists, the for-profit private sector, non-governmental organizations, political parties, and community groups and individuals. Additionally, establishing a dedicated public health career track within the health sector is advised for long-term preparedness and effective decision-making."}
{"question_id": "b4885c9ebb178f8daa578cef0c857a1de41d8d54", "paper_id": "nlpeer/ARR-22/43e8458544e18304f6aa2c1aededf974877f088dd2ccd7d53ef71eb59e54567c0572f715ecdcbaf04c39206950c3f5628198e61ebc5ec09fa1ba0a559102f923", "generation": "The location of conferences significantly influences the diversity of participation in NLP research. Holding conferences across different global locations encourages attendance from researchers in nearby countries, thereby increasing regional diversity. Conferences that rotate locations tend to attract a more diverse set of participants by enabling local researchers to attend more easily. This effect is evident in the observed higher diversity indices for events like EACL and LREC, which achieve greater diversity at comparable or lower travel distances. Conversely, conferences held repeatedly in the same region, such as some ACL meetings, are associated with longer travel distances but do not necessarily yield higher diversity. Therefore, the strategic geographic distribution of conference venues promotes increased diversity by facilitating regional participation."}
{"question_id": "d5b246ec5a8edcc34e88a24fb9bfd7d313572647", "paper_id": "openreview/ICLR-2022-conf/pjqqxepwoMy", "generation": "The target of return, denoted by \\( v^{tar}_t \\), can be estimated by any value learning algorithm such as TD(0) or Peng\u2019s Q(\u03bb). Generally, \\( v^{tar}_t \\) can always be given by Bellman equations."}
{"question_id": "7603a58573eeaabda0e22ca42e407dd44c83bd3e", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "Action frames refer to the different semantic frames or contexts that a verb, particularly a common action verb, can invoke according to frame semantics. Each action frame represents a distinct implication relation associated with the verb, capturing various possible meanings or scenarios in which the verb can be used."}
{"question_id": "6de0c620431f72ce5a6331d7dde1b8df91e24936", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "The criteria for determining which knowledge dimensions to annotate for each frame involve removing frame/attribute combinations and object pairs that received less than two-thirds agreement among annotators or were selected by at least two-thirds of workers as having no relation. This process results in retaining only those frames and object pairs with sufficient consensus, yielding roughly 400\u2013600 usable frames and 2100\u20132500 usable object pairs per attribute."}
{"question_id": "92b04f60c27edb89dcdc8dcd575bcb9872f0e307", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "Yes, the incorrectly-classified actions/objects are ambiguous for humans. The example given shows that different crowd workers provided conflicting interpretations for the same action-object pair, such as \"PERSON stopped the fly with {the jar / a swatter}\" where the weight relationship is unclear, versus \"PERSON stopped their car with the brake,\" where the weight relationship is clear. This indicates that the classification is underspecified and ambiguous even to human annotators."}
{"question_id": "748b3e8ce8fe5bb6fa899f962211e41d18c30cae", "paper_id": "openreview/NeurIPS-2022-dabt/wPEXGTzZJt", "generation": "The bounds presented in Table 1 represent the interquartile range of the mean scanpath length, which is expressed in the number of fixations."}
{"question_id": "0544f3fb3619a15d1f6076086707d91cea93b334", "paper_id": "openreview/NeurIPS-2022-dabt/wPEXGTzZJt", "generation": "The authors justify the use of only four datasets for evaluating the visual search models by stating that, to their knowledge, these are the only publicly available datasets specifically designed for the task of visual search in natural scenes."}
{"question_id": "21f851f7058a46d8f9904d493b82811edf3aa8f3", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "This work differs from existing research on learning data weighting using multi-arm bandits. While prior approaches in curriculum learning employ stochastic policies to learn suitable weights for deciding which task to study next, this work, referred to as TAWT, focuses on learning better representations by assigning more suitable weights on source tasks rather than on task selection strategies. Thus, the connection lies in the general theme of weighting tasks, but the methodology and objectives are distinct."}
{"question_id": "04e9ce2f786d7603574645eafe3bfe6e1a603190", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The choice of mirror descent is not strictly necessary for the proposed method, as other optimization methods, such as projected gradient descent, can also be used. Mirror descent is employed because it is a canonical generalization of Euclidean gradient descent to gradient descent on the probability simplex."}
{"question_id": "a7ad87d54def516b43292de78c758cf6107320f7", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "Yes, the proposed method can be implemented using projected gradient methods instead of mirror descent."}
{"question_id": "ddbf5cd6168ece49006281d9def64503e3610f0f", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The assumption states that \\(\\phi\\) and \\(\\psi\\) are vector-valued functions in \\(\\Phi\\). Therefore, it is necessary for \\(\\phi\\) to be a vector-valued function under assumption b."}
{"question_id": "c44a73f20a295acb499abf61c0f0b96e4080d9ba", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "No, there is no \\(\\forall\\) quantifier such as \\(\\forall \\phi \\in \\Phi\\) explicitly stated before equation 3.5 in definition 3.2 (transferability) of the paper."}
{"question_id": "8f304b893f44b97b98ba8df1ea43ce0fcb657b87", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The expression \\(\\sum_{t=1}^T \\omega_t [L^*_t(\\phi) - L^*_t(\\bar{\\phi}_\\omega)]\\) is a weighted sum of differences in risks, where each term \\(L^*_t(\\phi) - L^*_t(\\bar{\\phi}_\\omega)\\) represents the excess risk of the representation \\(\\phi\\) relative to the optimal \\(\\bar{\\phi}_\\omega\\) on the \\(t\\)-th source task. Since \\(\\bar{\\phi}_\\omega\\) is defined as an optimal representation minimizing the weighted sum of risks, it follows that for any \\(\\phi\\), the difference \\(L^*_t(\\phi) - L^*_t(\\bar{\\phi}_\\omega)\\) is non-negative or zero for each task \\(t\\). Consequently, the weighted sum \\(\\sum_{t=1}^T \\omega_t [L^*_t(\\phi) - L^*_t(\\bar{\\phi}_\\omega)]\\) is always non-negative.\n\nBecause this sum is non-negative, it is mathematically valid to take its power to \\(1/\\rho\\) (where \\(\\rho > 0\\)) as in the definition of transferability. The positivity ensures that the expression inside the power is well-defined and real-valued, allowing the polynomial control of the target risk difference by the source risk differences raised to the power \\(1/\\rho\\)."}
{"question_id": "caa94f24704e76df0ceac395ee650a45f7a174e1", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The most popular and advanced methods in cross-task learning include representation learning frameworks that aim to learn a shared representation across different tasks, as exemplified by works such as Baxter (2000), Maurer et al. (2016), Tripuraneni et al. (2020, 2021), and Du et al. (2021). Additionally, meta representation learning algorithms that distinguish between source-specific and transferable features, such as the approach by Liu et al. (2020), are notable. Model agnostic meta-learning (Finn et al., 2017) also represents an advanced theoretical framework for analyzing representations under the assumption of a common underlying representation across tasks, as discussed by Chua et al. (2021). Furthermore, task weighting strategies that optimize the weighting of source tasks to maximize representation proximity, such as the TAWT algorithm, provide a practical and theoretically guaranteed approach distinct from heuristic weighting in multi-task learning and curriculum learning methods."}
{"question_id": "fe2486dcff37fd819cb07981aa6a1e026c1f52e5", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "This method, Target-Aware Weighted Training (TAWT), differs from the most advanced methods in cross-task learning in several key aspects. Unlike approaches such as Liu et al. (2020), which distinguish between source-specific and transferable features to encourage learning generalizable features, TAWT is based on a representation-based task distance that measures the quality of representations for each task individually rather than assuming a shared representation. This finer-grained task distance provides a more interpretable measure of the value of source data and the necessity of fine-tuning.\n\nCompared to the theoretical framework by Chua et al. (2021), which assumes all tasks share approximately the same underlying representation, TAWT does not impose any a priori assumptions on the proximity of source and target representations. Instead, it seeks an adaptive weighting scheme to maximize the proximity between source and target tasks.\n\nFurthermore, TAWT differs from task weighting in curriculum learning, which typically learns weights to decide the order of task study, by focusing on learning better representations through assigning suitable weights on source tasks. It also contrasts with heuristic weighting strategies in multi-task learning by providing a practical algorithm with theoretical guarantees specifically designed for cross-task learning.\n\nOverall, TAWT offers a theoretically principled, representation-based weighting strategy that enhances sample efficiency in cross-task learning by minimizing the representation-based task distance, setting it apart from existing advanced methods that rely on stronger assumptions or heuristic weighting schemes."}
{"question_id": "da72c5aada0a4b421a7919d5ef2739ddb658ae5e", "paper_id": "nlpeer/ARR-22/12ea2851de6445bf343d642c6ccc8640bf7a6c28b5f20ba123d6d58f02790c7c7f8ab696e6a6b4e45bfad28bd4bc0419bdcf6358693404dd0c14d202b291503b", "generation": "The results in Table 2 were generated using 100 samples for MBR decoding."}
{"question_id": "1874144ac78d10d99982bc7f6446545ac56a4805", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "A fine-grained model analysis was conducted by sampling approximately 1,000 cue-image pairs with 10 to 12 candidates each, categorizing the associations into six types: (a) visually salient, (b) visually non-salient, (c) concept related, (d) activity, (e) counting, and (f) colors. The model CLIP ViT-B/32 was evaluated on these categories, and accuracy per category was reported. The analysis revealed that model performance was highest for visually salient and color-based cues, degraded for concept-related and activity cues, and was much worse for visually non-salient and counting cues. This pattern suggests that models struggle particularly with associations requiring common sense reasoning rather than direct visual detection. The annotated data from this analysis has been released for future research."}
{"question_id": "9baa6e51bac226403b63b8bef97ea58737e9f14c", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The 10 and 12 candidate tasks are more difficult than the 5 and 6 candidate tasks because the random expected Jaccard index is lower for the former (24% and 17%) compared to the latter (38% and 34%). This indicates a lower random chance of success, making the task inherently harder. Consequently, the model benefits more from supervised training in these harder cases, improving performance by 7%, whereas training does not change performance with 5 and 6 candidates. Despite this increased difficulty for the model, humans achieve similar performance on both tasks, suggesting that the increased number of candidates primarily affects the model's ability to exploit supervised data rather than human performance."}
{"question_id": "c5bafca1e41f1bfed6bf6063b321e6bb5102b171", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The poorer performance of the CLIP-ViL model compared to other models is attributed to its use of the ITM (image-text matching) pre-training objective, whereas X-VLM and ViLT are fine-tuned specifically for image-text retrieval."}
{"question_id": "3684e20e39c14e7d52c12084515e178aa0789584", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "Associations that are solvable by AI should be kept in the framework because the framework aims to motivate human annotators to produce instances that can trick the AI model, rather than relying on iterative model feedback to generate adversarial examples. By allowing annotators only one chance to fool the model, the generated data remains less dependent on the specific AI model and includes associations that are both solvable by humans and potentially solvable by AI. This approach ensures that the dataset reflects a realistic challenge for models without artificially restricting it to only those associations that are currently unsolvable by AI."}
{"question_id": "26c5a6b5fdd3b1d0b2d97c1550126b503f144eed", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The chance performance reported in Table 4 corresponds to the random expected Jaccard index values of 38%, 34%, 24%, and 17% for 5, 6, 10, and 12 candidates, respectively."}
{"question_id": "b79e294382978a8a9cebe595211529bcb653e0f2", "paper_id": "nlpeer/COLING2020/1886", "generation": "The topics for n-grams are chosen by detecting them using the Empath lexicon."}
