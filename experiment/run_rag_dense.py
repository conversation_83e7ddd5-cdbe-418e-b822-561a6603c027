import json
from typing import List
from tqdm.contrib.logging import logging_redirect_tqdm
import numpy as np

from data import data_store
from processor.chunker import DocumentChunk
from core.openai_service import generate_text_embeddings, call_text_response_once
from core.prompts import PROMPT_ANSWER_GENERATION
from core import logging, utils

logger = logging.get_logger()

TOP_K = 10

def create_rag_prompt(question: str, retrieved_chunks: List[DocumentChunk]) -> str:
  context_parts = []
  
  for chunk in retrieved_chunks:
    
    heading_context = " > ".join(chunk.heading_context) if chunk.heading_context else "No heading context"
    
    chunk_text = \
f"""
Section: {heading_context}
Content: {chunk.content}
"""

    context_parts.append(chunk_text)

  context = "\n\n".join(context_parts)
  
  return PROMPT_ANSWER_GENERATION.build_prompt(question=question, context=context)

def dense_retrieval(embedding: List[float], all_chunks: List[DocumentChunk], top_k: int = 10) -> List[DocumentChunk]:
  # Calculate cosine similarity
  index_scores = []
  for i, chunk in enumerate(all_chunks):
    score = np.dot(embedding, chunk.embedding) / (np.linalg.norm(embedding) * np.linalg.norm(chunk.embedding))
    index_scores.append((i, score))
  
  # Sort and retrieve top-k
  top_indices = sorted(index_scores, key=lambda x: x[1], reverse=True)[:top_k]
  top_chunks = [all_chunks[i] for i, _ in top_indices]
  
  return top_chunks

def retrieve_relevant_chunks(question: str, all_chunks: List[DocumentChunk], top_k: int = 10) -> List[DocumentChunk]:
  question_embedding = generate_text_embeddings(question)
  retrieved_chunks = dense_retrieval(question_embedding, all_chunks, top_k=top_k)
  return retrieved_chunks

def process_single_question(question: str, paper_id: str, top_k: int = 10) -> str:
  all_chunks: List[DocumentChunk] = data_store.load_chunked_data(paper_id)
  retrieved_chunks = retrieve_relevant_chunks(question, all_chunks, top_k=top_k)
  rag_prompt = create_rag_prompt(question, retrieved_chunks)
  response = call_text_response_once(rag_prompt, temperature=0.0)
  generated_answer = response.output[0].content[0].text.strip()
    
  return generated_answer
   
def get_processed_questions(result_file):
  try:
    open(result_file, "r")
    return set([json.loads(line)["question_id"] for line in open(result_file, "r")])
  except:
    return set()
   
def main():
  result_file = f"experiment/rag_{TOP_K:02d}_dense.jsonl"
  processed_questions = get_processed_questions(result_file)
  result_file = open(result_file, "a+")
  
  qa_data = data_store.get_qa_data()
  qa_data = qa_data[qa_data["answer_free_form"].notnull()]
  
  def process_row(row):
    question_id = row['question_id']
    paper_id = row['paper_id']
    question = row['question']
    
    if question_id in processed_questions:
      logger.info(f"Skipping question {question_id} for paper {paper_id}, already processed")
      return
    
    generated_answer = process_single_question(question, paper_id, TOP_K)
    
    result = {
      "question_id": question_id,
      "paper_id": paper_id,
      "generation": generated_answer
    }
    result_file.write(json.dumps(result) + "\n")
  
  params = [(row,) for row in qa_data.to_dict(orient='records')]
  utils.run_paralellism(process_row, params, thread_count=16, batch_size=100)
    
    
if __name__ == "__main__":
  with logging_redirect_tqdm([logger]):
   main()