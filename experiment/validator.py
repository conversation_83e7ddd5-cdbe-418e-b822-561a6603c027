# validate that all question is processed
from data import data_store
import pandas as pd

FILE_PATH = "experiment/golden_evidence.jsonl"

if __name__ == "__main__":
  question = data_store.get_answer_generation_questions()
  data = pd.read_json(FILE_PATH, lines=True)
  
  for question_id in question["question_id"]:
    if question_id not in data["question_id"].values:
      print(f"Missing question {question_id}")
      
  for question_id in data["question_id"]:
    if question_id not in question["question_id"].values:
      print(f"Extra question {question_id}")
    
