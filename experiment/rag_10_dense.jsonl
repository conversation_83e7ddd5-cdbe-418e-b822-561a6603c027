{"question_id": "7ed6482c76ae428708b4675495718652adfed68d", "paper_id": "nlpeer/COLING2020/679", "generation": "Yes, six of the nine annotators who participated in the evaluation were native English speakers."}
{"question_id": "e76615760f7625a5b984cb961f5d948ab2407178", "paper_id": "nlpeer/PeerRead-ACL2017/193", "generation": "In the UCCA scheme, a node (or unit) corresponds either to a terminal (a single token) or to several terminals (not necessarily contiguous) viewed as a single entity. This means that a node can represent a single token (terminal) or a complex semantic unit comprising multiple tokens (non-terminal). Therefore, a node is either a terminal or a non-terminal, but not both simultaneously."}
{"question_id": "6a2461eebef2649b0832b8d91cb8ed490edbf2d3", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "The paper does not explicitly state the reason for choosing Ordinary Least Squares (OLS) regression over Support Vector Regression (SVR) for the regression-based systems. However, it describes the regression models as using non-contextual word-level embeddings input into an OLS model, and contrasts this with the neural regressor (regBERT) fine-tuned with mean squared error loss. The choice of OLS likely reflects a baseline linear regression approach for comparison with the neural model, as OLS is a standard, interpretable, and computationally efficient method for regression tasks. Additionally, the pairwise ranking approach employs SVMRank for ranking rather than SVR for regression, indicating a methodological distinction between ranking and regression models. Thus, OLS was used as a straightforward, established baseline regression method rather than SVR, which was not mentioned or evaluated in the experiments."}
{"question_id": "a3bcdc5b71130202e27b2a0b4a8485392efedec5", "paper_id": "openreview/ICLR-2023-conf/rLguqxYvYHB", "generation": "No, estimating the feature map function \\(\\phi_A\\) does not pose specific challenges when the treatments are discrete. The proposed method explicitly separates the treatment features \\(\\phi_A(a)\\) and the covariate features \\(\\phi_X(x)\\) in a tensor product form, which is particularly advantageous when the covariate dimension is much higher than the treatment dimension. For discrete treatments, Assumption 1 ensures that there is a non-zero probability of observing all elements in the treatment and covariate space, facilitating uniform convergence and consistency of the causal estimator. This implies that the estimation of \\(\\phi_A\\) is well-posed and stable in discrete treatment settings."}
{"question_id": "cdb59f3c15dd8d951c169a9f2fae03e000215bae", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "Yes, the cross-linguistic analysis includes both French and Spanish. The zero-shot cross-lingual evaluation was conducted using models trained on English data and tested on Spanish (Newsela-Es) and French (Vikidia-Fr) datasets. The multilingual BERT (mBERT) model, pre-trained on multiple languages including English, French, and Spanish, was used as the base for these experiments. The results demonstrated that the pairwise ranking approach performs well in cross-lingual scenarios for both French and Spanish, achieving a zero-shot cross-lingual ranking accuracy of over 80%."}
{"question_id": "7088d27d671a415164b81660a60173ea7602d968", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "A bidirectional gated recurrent unit (GRU) decoder is used for sequences of the same length to model the sequential dependencies along the alignment length while incorporating both sequence-specific and position-specific information. Specifically, at each position in the sequence alignment, the GRU input is a concatenation of the latent vector representing the entire sequence and a BLOSUM embedding that encodes amino acid preferences at that position. This architecture allows the GRU to capture complex dependencies and context within the sequence, producing a logit vector at each position that specifies the probabilities of the possible characters. Using a GRU enables the model to effectively decode the latent representations into sequence characters by leveraging the sequential structure of the alignment, even when all sequences have the same fixed length."}
{"question_id": "6ceebb857d2674bd38452e8bc30a3d280f2ce647", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "The lower performance on Vikidia-Fr compared to Newsela-Es can be attributed to several factors: \n\n1. Language difference: Vikidia-Fr is in French, whereas Newsela-Es is in Spanish, which inherently introduces cross-lingual challenges.\n\n2. Domain differences: Newsela-Es shares the same domain as Newsela-En (news articles), while Vikidia-Fr contains more diverse content, which may affect model performance.\n\n3. Number of reading levels: Vikidia-Fr has only two reading levels, whereas Newsela-Es has ten. Ranking metrics such as SRC and KTCC penalize Vikidia-Fr predictions more heavily because an incorrect ranking in a two-level dataset results in a completely reversed list, leading to a large penalty. In contrast, with more reading levels as in Newsela-Es, small ranking errors are less severely penalized.\n\nThus, the combination of language differences, domain diversity, and the limited number of reading levels in Vikidia-Fr contributes to its lower performance relative to Newsela-Es."}
{"question_id": "0081780bb3b2f8ca303dd426ce4ddfd89c13e80d", "paper_id": "nlpeer/ARR-22/0f595b4bd968ef5daacc88c4edb581bf7f683af30b6874ba157c6d2282aabead577041409b949924e4bc5ef54c543d85a10e962be8e70304dea65e1b18441bdb", "generation": "The average duration reported in Table 1 represents the average time taken by workers to complete a Human Intelligence Task (HIT) and the average time taken to assess dialogues during the human evaluation of dialogue systems. This information helps to quantify the time investment required from workers for each dialogue assessment under different experimental conditions (Free run 1, Free run 2, and Ice-breaker)."}
{"question_id": "465b94530bc99598bbefb742da70cf99236b86d9", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "Classification-based models were not used for the zero-shot experiments because there is no inherent means within classification to distinguish between ties, where the model predicts the same class for two documents of different reading levels. This makes it infeasible to use classifiers as rankers in cross-lingual or zero-shot settings where ranking order is critical. Additionally, regression and ranking approaches provide better performance than classification in monolingual, within-corpus settings, further motivating their use over classification for zero-shot experiments."}
{"question_id": "cf26cc0cd1000ad63bfba19b5159a20efba34b18", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "No, the parameters of the BLOSUM matrix are not estimated by the model; they are pre-computed and used as fixed input. The BLOSUM matrix B is given and used to compute weighted averaged BLOSUM vectors for each position in the multiple sequence alignment (MSA). These averaged BLOSUM vectors are then processed by a neural network to produce BLOSUM embeddings that provide position-specific information. The model relies on these pre-computed BLOSUM vectors and embeddings rather than estimating or learning the BLOSUM matrix parameters during training."}
{"question_id": "058da91fd7bfe9ecd3163d9d8877e5116cf5cdf6", "paper_id": "openreview/ICLR-2022-conf/C03Ajc-NS5W", "generation": "The evidence that the generative model is successful in synthesizing new molecules is demonstrated by the experimental results in the targeted molecule discovery task. The G-SphereNet model outperforms the previous state-of-the-art method G-SchNet in all evaluated metrics for generating molecular geometries with desirable quantum properties, such as low HOMO-LUMO gaps and high isotropic polarizability. Both models use the same pretraining and fine-tuning pipeline, indicating that the superior performance of G-SphereNet reflects its stronger ability to search and generate valid molecular geometries with good properties. Additionally, G-SphereNet achieves higher validity rates in ablation studies, with up to 88.18% validity when using combined local and global features and incorporating distance, angle, and torsion information. Visual illustrations of sample molecular geometries generated by G-SphereNet further support its capability to synthesize novel molecules with targeted quantum properties."}
{"question_id": "****************************************", "paper_id": "openreview/ICLR-2023-conf/-CoNloheTs", "generation": "The proposed algorithm is designed for exact reconstruction of two- and three-layer ReLU neural networks under mild general position assumptions. It operates in polynomial time and query complexity, allowing recovery of the network parameters and architecture up to an affine transformation. The algorithm can handle any two-layer network except for a measure-zero set and extends to three-layer networks with the additional assumptions that the number of first-layer neurons is smaller than the input dimension and that the second layer has non-zero partial derivatives, conditions valid for most practical networks with more second-layer neurons than first-layer neurons.\n\nHowever, the work is primarily theoretical and focuses on exact access to the black-box network outputs. Practical limitations include the shallow depth of networks considered (two or three layers) and the requirement for exact membership queries, which may not be feasible in real-world scenarios due to numerical inaccuracies and limited output precision. Despite these limitations, the algorithm demonstrates that exact recovery of real neural networks with these architectures is provably achievable in polynomial time, suggesting that it can be used to recover real neural networks within the specified assumptions and constraints."}
{"question_id": "815b242c673ecb7aea2ccec4f6c83ab1191a9124", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "A Multi-Layer Perceptron (MLP) cannot be used to decode the aligned sequences instead of a Gated Recurrent Unit (GRU) decoder because the decoding task requires modeling sequential dependencies along the alignment length. The GRU, as a recurrent neural network, processes input sequences position by position and captures contextual information across the sequence length, which is essential for generating the likelihood parameters of each position in the sequence. In contrast, an MLP lacks the recurrent structure necessary to model such sequential dependencies and positional context effectively. Therefore, the Draupnir model employs a bidirectional GRU decoder to incorporate both sequence-specific latent vectors and position-specific BLOSUM embeddings, enabling it to produce accurate probabilistic character predictions at each position in the multiple sequence alignment."}
{"question_id": "5f4c9dea82aa176c2e42cd2c59ff3da0fce4a367", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "Yes, the pairwise distances of the latent representations can be compared to the distances in a fitted phylogenetic tree. A quantitative analysis comparing the Euclidean distances between the latent representations of the leaves and the corresponding branch lengths in the phylogenetic tree was performed for both a standard VAE and the Draupnir model. The results showed that Draupnir achieved a higher correlation coefficient (0.91) and Spearman correlation coefficient (0.94) compared to the standard VAE (correlation coefficient 0.79 and Spearman correlation coefficient 0.85), indicating that the latent space distances in Draupnir better reflect the evolutionary distances represented by the phylogenetic tree. This demonstrates that the latent variables under the tree-structured Ornstein-Uhlenbeck prior capture evolutionary relationships more accurately than a standard Gaussian prior."}
{"question_id": "de0f53c58cedd98fd958715bb1a2f5a3d24e829d", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The source and destination IP addresses and port numbers were discarded because they are part of the 10 additional features in the original Kyoto-2006+ dataset, which might be useful for models designed to focus on connections between nodes in the system (e.g., graph-based models). However, for the proposed benchmark and preprocessing, the focus was on the 14 conventional features, and the additional features including IP addresses and port numbers were not retained in the main feature set. This choice was likely made to simplify the dataset and make it suitable for language-modeling approaches, as well as to maintain a consistent preprocessing pipeline across methods."}
{"question_id": "e2ee4a1059cbb3c736b7b00cd902bbbd428423e8", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The authors have not considered formulating the Darpa dataset as a dynamic graph for network intrusion detection. The related work section mentions that some approaches model network intrusion detection problems using graph neural networks (GNNs) [29], but there is no indication that the Darpa dataset specifically has been formulated or used as a dynamic graph in their study. The focus of the authors is primarily on the Kyoto-2006+ dataset and its temporal distribution shifts rather than on the Darpa dataset or dynamic graph formulations."}
{"question_id": "264d5ee47c97b02b5a597533254b78c97cb41f8b", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "There is no information in the provided content regarding the Darpa dataset or any limitations related to it that would prevent it from being a graph dataset. The chunks do not mention the Darpa dataset or discuss its characteristics or suitability for graph-based modeling."}
{"question_id": "ee956c6a1b9b9808fc77d7d1f8f82237123f5000", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "\"Edit boundaries might be unusual\" refers to the fact that the start and end points of edits\u2014i.e., the segments of text identified as needing correction\u2014are determined automatically by an alignment algorithm rather than manually annotated by humans. Because this automatic edit extraction process approximates human edits using computational methods (such as a linguistically-enhanced alignment algorithm with merging rules), the exact spans of text marked as edits may not always align perfectly with human intuition or standard annotation practices. Consequently, the boundaries of these edits may appear atypical or unconventional, but the evaluation of error type appropriateness focuses on the classification of the edits themselves rather than on the precise correctness of their boundaries."}
{"question_id": "1d2837da9c9a557b18715b0482bea0532e3ac176", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The performance of the proposed Transformer model (BERT for Anomalies) is lower in the IID split than in the NEAR split because, within the IID split, the anomalies (outliers) are modeled quite poorly by the language model. This results in a slightly lower IID performance compared to the NEAR split. Specifically, the model struggles to recognize anomalies well in the IID data, which leads to reduced anomaly detection effectiveness. In contrast, in the NEAR split, the model models outliers very well, achieving higher PR-AUC for outliers. This discrepancy is highlighted in the monthly evaluation where parts of the IID split show poor modeling of outliers, explaining the lower performance of BERT on IID relative to NEAR."}
{"question_id": "d256c384aa446ef6ba7d69269df08e3dbbdb2db9", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "Constructing the phylogenetic tree required as input for the algorithm involves inferring the tree topology, the labels of the tree\u2019s edges, and the composition of the ancestral sequences. This process is naturally represented as a binary tree with known extant sequences as leaf nodes and unknown ancestral sequences as internal nodes. The edges are labeled by positive real numbers representing time differences or amounts of change between nodes, forming a patristic distance matrix. The inference of the tree and its parameters is a complex task addressed by the field of phylogenetics, which employs heuristic methods such as maximum parsimony or probabilistic evolutionary models. Thus, constructing the phylogenetic tree is a non-trivial and computationally involved step that requires specialized phylogenetic inference methods."}
{"question_id": "b8c9effb94fbab2db127d7262041fecdb031c1dd", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "Yes, the same preprocessing step of converting numerical features to categorical ones through binning is applied uniformly to all methods for a fair comparison. Specifically, three float numerical features\u2014connection duration, number of source bytes, and number of destination bytes\u2014are converted into categorical bins. This preprocessing is done to make the dataset suitable for BERT-based models, whose vocabularies would otherwise become too large. The paper also reports experiments without this preprocessing for some methods, noting that performance varies depending on the method and split, but the categorical binning is the standard preprocessing applied across all methods in the main experiments."}
{"question_id": "962be5303d07e7707c9750241730986cfeb7d754", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "The significance test was performed using precision, recall, and F0.5 scores obtained from evaluating each team in the CoNLL-2014 shared task with both gold and automatic references. The test was conducted by means of bootstrap significance testing with 1,000 iterations."}
{"question_id": "a36f298f8941bf93ad6bdc2ef8db6471e6ca4156", "paper_id": "nlpeer/PeerRead-ACL2017/193", "generation": "The UCCA transition scheme offers capabilities that address structural properties not fully supported by existing parsers for Abstract Meaning Representation (AMR) and Semantic Dependency Parsing (SDP). Specifically, UCCA parsing supports reentrancy, discontinuous nodes, and non-terminal nodes, enabling the construction of general directed acyclic graphs (DAGs) with multiple parents and discontinuous semantic units. In contrast, existing transition-based AMR parsers are limited to a subset of reentrancies and discontinuities and cannot produce arbitrary DAG parses because they may remove nodes before their parents have been predicted. SDP parsers, while addressing a wide range of semantic phenomena and supporting discontinuous units and reentrancy, rely on bilexical dependencies and require head selection for every relation, which can be problematic in constructions without clear heads, such as coordination. UCCA's use of non-terminal nodes avoids this limitation.\n\nFurthermore, UCCA parsing does not require separate alignment of input tokens to graph nodes, unlike AMR parsing, where alignment is not explicitly marked and must be detected automatically with limited accuracy. The UCCA transition set includes operations such as NODE creation for non-terminal units, LEFT-REMOTE and RIGHT-REMOTE edges for remote (reentrant) edges, and SWAP for handling discontinuities, which collectively allow for more general and flexible DAG parsing.\n\nTherefore, integrating the UCCA transition scheme into existing AMR and SDP parsing frameworks could potentially improve their performance by enabling the parsing of more general graph structures, better handling of reentrancies and discontinuities, and avoiding the need for explicit token-to-concept alignment or forced head selection. This would allow these parsers to capture semantic phenomena more comprehensively and accurately."}
{"question_id": "96a32bff80b5928198a99a4fc2c2e24cd1a982dd", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The noise magnitude in the data is represented by the parameter \u03c3, which denotes the noise level for all samples. Specifically, the input signal for class k is sampled as \\( x | y = k \\sim \\mathcal{N}(\\mu_k, \\sigma^2 I) \\), where \u03c3 controls the variance of the Gaussian noise added to the data. In the experiments described, \u03c3 takes values such as 2 in the toy Gaussian dataset setup and varies from 0 to 0.8 in noisy-label classification tasks, where \u03c3 = 0.1 corresponds to 10% label noise. Thus, the noise magnitude in the data is quantified by \u03c3, with typical values ranging from 0 (no noise) up to 0.8 (high noise) depending on the experimental setting."}
{"question_id": "834016a31e50565175511dcdf3d75a1be44b532c", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The paper defines base difficulty as the Euclidean distance between the one-hot label vector and the unknown true label distribution \\( p^*(x) \\), specifically \\(\\| e_y - p^*(x) \\|_2\\). This quantity is large if the sample \\( x \\) is ambiguous\u2014meaning \\( p^* \\) has several large components and no one-hot label is close to \\( p^* \\)\u2014or if the sample is not ambiguous but the assigned label \\( y \\) was \"unlucky,\" drawn from a low-probability class. Base difficulty serves as a proxy to characterize how challenging a sample is to learn and is used to analyze the evolution of the network's predicted distribution \\( q(x) \\) during training."}
{"question_id": "7869279cfc2dc07fcd82704dc07789afa6de5c82", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "The issue with the point estimate (PE) oracle itself is that it is sensitive to the sparse distribution of neural data combined with few responses per stimulus, which results in an overconfident and non-robust estimation of the Gold Standard (GS) parameters. This leads to the paradoxical outcome where the Null model outperforms the GS model for the majority of neurons when using the PE approach. Attempts to improve the GS model by other approaches such as MAP estimation or regularization did not outperform the Bayesian posterior predictive GS model. Instead, a full Bayesian treatment that incorporates posterior predictive distributions yields a more robust and data-efficient upper bound estimator that consistently outperforms the PE approach across different numbers of repeats and signal-to-noise ratios. Therefore, the problem lies fundamentally with the PE oracle's lack of robustness rather than a lack of regularization, and Bayesian methods provide a principled solution."}
{"question_id": "3b4dcf624027feff21ac63b6e451169e1ca6bf2a", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The proposed criterion for quantifying generalization performance is the average L2 distance between the target supervision distribution \\( p_{\\text{tar}} \\) and the ground truth distribution \\( p^* \\) over the input samples, expressed as \\( \\mathbb{E}_x \\left[ \\| p_{\\text{tar}}(x) - p^*(x) \\|_2 \\right] \\). Smaller values of this average L2 distance correspond to better generalization performance. This is supported by a theoretical bound showing that the variance of the risk estimate is dominated by a term proportional to the squared L2 distance between \\( p_{\\text{tar}} \\) and \\( p^* \\), implying that minimizing this distance leads to a better approximation of the true risk and thus a better learned model. Empirical experiments on a synthetic Gaussian dataset further confirm that smaller L2 distances between \\( p_{\\text{tar}} \\) and \\( p^* \\) correlate strongly with higher test accuracy and lower expected calibration error."}
{"question_id": "49887aceab5099bc8a45f1f01aa437f760c289a5", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "The 90% Normalized Information Gain (NInGa) performance achieved by the mixture model on the real data example is indicative of strong model performance, but it does not necessarily guarantee the same level of performance on other datasets. Additional analyses were performed on multiple datasets to demonstrate how NInGa facilitates model comparison across different datasets (Section 3.2). This suggests that while the 90% NInGa value reflects a high-quality fit for the specific dataset evaluated, performance may vary depending on dataset characteristics. The metric NInGa is designed to be comparable across datasets when the Null and Gold Standard models are kept consistent, allowing for meaningful cross-dataset comparisons. Therefore, the 90% NInGa indicates excellent performance on the evaluated dataset and provides a framework for assessing performance on other datasets, but it does not imply identical performance levels universally."}
{"question_id": "92394e14628bdc9941b0581b43b20ab42dbdd3fd", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The expected label does not always match the most probable label given noisy data. During training with noisy or corrupted labels, the model's prediction initially moves towards the unknown true label distribution \\( p^* \\), which may differ from the provided noisy label. This is evidenced by the observed \"zig-zag\" learning path, where the prediction first approaches the true label distribution before eventually converging to memorize the incorrect noisy label. Experiments with label flipping show that early in training, the model can spontaneously correct some noisy labels, recovering the true original label more often than random chance. However, if training continues without intervention, the model ultimately memorizes the noisy labels. Thus, the expected label (the model's soft prediction approximating \\( p^* \\)) can differ from the most probable label given the noisy data (the corrupted label), especially before overfitting occurs."}
{"question_id": "4d8419e9aeb2f3d606bca8774d3618d08b70c41f", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "Improving the upper bound for the Information Gain evaluation metric, specifically Normalized Information Gain (NInGa), provides several practical benefits:\n\n1. **Interpretable Performance Scale:** It enables placing model likelihoods on an interpretable and bounded scale between a lower and upper bound, facilitating meaningful assessment of model performance relative to the best possible approximation of the true neural response distribution.\n\n2. **Robustness to Data Limitations:** A robust upper bound estimator, such as the Bayesian oracle based on posterior predictive distributions, is data-efficient and reliable even with small datasets, sparse neural responses, low signal-to-noise ratios, and high shared variability. This robustness mitigates failures observed with naive point estimate approaches.\n\n3. **Accurate Model Comparison:** By providing a reliable upper bound, it allows for fair and consistent comparison of neural encoding models across different datasets that may vary in the number of stimulus repeats and noise characteristics.\n\n4. **Identification of Model Limitations:** The improved upper bound helps in diagnosing which parameters of the neural response distribution the model predicts well and where it fails, guiding future model improvements.\n\n5. **Conservative and Reliable Estimates:** Optimizing prior hyper-parameters for the upper bound model yields a more conservative and accurate estimate of the maximum achievable performance, preventing overestimation of model quality.\n\n6. **Facilitation of Likelihood-Based Metrics:** It supports the use of full likelihood-based evaluation metrics that consider the entire response distribution rather than just the mean, providing a more comprehensive assessment of neural encoding models.\n\nOverall, improving the upper bound enhances the interpretability, reliability, and utility of Information Gain metrics in evaluating and comparing neural encoding models."}
{"question_id": "f3d892c65c8fc7edb3624cad07d6ef39115402eb", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "A new set of error categories was designed because existing approaches, such as the tagset presented in Nicholls (2003) or the CoNLL-2014 tagset, were limited by their reliance on specific training corpora and annotation standards, which reduced their generalizability. For example, classifiers trained on one corpus (e.g., FCE) performed poorly on others (e.g., NUCLE) due to differing annotation standards. Therefore, a dataset-agnostic, rule-based framework was developed to enable consistent and flexible error type classification across different datasets. This new framework balances informativeness and practicality by avoiding overly narrow distinctions that complicate categorization, such as differentiating between modal and tense errors, and allows evaluation at multiple levels of granularity through prefixes indicating missing, replacement, or unnecessary edits."}
{"question_id": "8f2072e6213f44471d3294973c9cfdd790bc7259", "paper_id": "openreview/ICLR-2022-conf/_X90SIKbHa", "generation": "The proposed methods, specifically the short-term recurrence Anderson mixing variants ST-AM, MST-AM, and RST-AM, differ from Newton and standard quasi-Newton methods such as BFGS, L-BFGS, Chord, and Levenberg\u2013Marquardt in several key aspects:\n\n1. **Memory Efficiency and Historical Information Storage**  \n   - Unlike full-memory quasi-Newton methods (e.g., BFGS) that require storing a large number of historical iterations to form secant equations, leading to significant memory overhead, MST-AM only needs to store two corrected historical iterations.  \n   - Limited-memory quasi-Newton methods like L-BFGS reduce memory by discarding the oldest iteration but may lose local superlinear convergence properties and are sensitive to the choice of historical length, which is problem-dependent and not known a priori. MST-AM avoids this sensitivity by carefully incorporating historical information through orthogonalization, maintaining equivalence to full-memory AM in ideal cases (strongly convex quadratic optimization or SPD linear systems) without loss of historical information.\n\n2. **Computational Cost and Flexibility**  \n   - Newton-like methods and standard quasi-Newton methods often have heavy memory and computational costs, especially in large-scale, high-dimensional, or nonconvex stochastic optimization problems.  \n   - ST-AM and its variants do not require direct access to the matrix but only the residual, making them appealing when explicit matrix access is difficult or costly. This contrasts with Newton and quasi-Newton methods that typically require Hessian or approximate Hessian information.  \n   - ST-AM can be extended directly to unconstrained optimization problems where gradients are available but Hessians are too expensive to compute, unlike nonlinear conjugate gradient methods that rely on line search.\n\n3. **Convergence and Theoretical Properties**  \n   - MST-AM and ST-AM have theoretical guarantees and can achieve fast convergence rates comparable to full-memory Anderson mixing and Krylov subspace methods.  \n   - Experiments show MST-AM can outperform BFGS in solving cubic-regularized quadratic minimization problems and is competitive with full-memory methods in nonlinear equation solving and root-finding tasks.  \n   - RST-AM, an extension for stochastic optimization, inherits advantages of AM and ST-AM, including fast convergence in quadratic optimization and theoretical guarantees in stochastic settings, which standard second-order methods lack when only first-order information is accessible.\n\n4. **Practical Performance**  \n   - MST-AM surpasses BFGS in certain nonlinear problems and matches or outperforms other methods such as Broyden\u2019s method and full-memory AM in both forward and backward processes in deep equilibrium models.  \n   - The memory footprint of ST-AM methods is close to that of first-order methods, making them competitive optimizers in machine learning contexts where first-order methods like SGD with momentum and adaptive learning rate methods dominate due to low memory and iteration cost.\n\nIn summary, the proposed ST-AM family offers a memory-efficient, flexible, and theoretically sound alternative to Newton and standard quasi-Newton methods, with competitive or superior performance in nonlinear and stochastic optimization problems, while avoiding the heavy memory and computational burdens typical of classical quasi-Newton approaches."}
{"question_id": "c67443bf273772ac2d4297564f839c0a0229e6eb", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The greyed out portion of Figure 3 represents the noise heads in the CLIPSep-NIT model. These noise heads are used to model sounds that are less relevant to the image query during training. At test time, the noise heads are discarded, and only the query heads are used for sound separation. The noise heads help the model to better handle noisy unlabeled data by assigning irrelevant or background sounds to the noise heads, thereby improving the separation of the target sounds."}
{"question_id": "c322ea8d24002e229a330243b37f745a157a764e", "paper_id": "egu/esd/14-185-2023", "generation": "The reference numbers for the delta to the DIC pool, as mentioned in Table 1, can be verified in Weaver et al. (2001, Sect. 3e), which describes the participation of the remineralized MOS-C tracer (MOS_DIC) in the inorganic ocean carbon cycle and the air\u2013sea gas exchange process in the UVic ESCM."}
{"question_id": "36e40e97993a08a2c5e50bfc69c991334be39e6e", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The noise heads are discarded at test time because the model is designed to use only the query heads for inference to specify the target sound by an input query. During training, the noise heads help handle noisy data by separating noise from the target sounds, but at inference, only the query heads are needed to extract the desired sound. This approach avoids the need for any post-selection process, unlike other models such as MixIT, and ensures that the output corresponds directly to the specified target sound."}
{"question_id": "06dc05622c2bee5d4a078d2ef7542457179ac90b", "paper_id": "nlpeer/ARR-22/6d1b7e47f044e29d515db7e4762ace8e3f089fe88c2a9de074e8d71f0f78da6169be5756ec9320b5354d2f0ebef3352fedbf5dd82012a7eafde0b0dfbfc311df", "generation": "The baseline model achieves significantly better performance on ACE 2004 and ACE 2005 compared to Yu et al. (2020) because these two datasets contain a high proportion of nested entities, and the baseline employs a span-based approach with a biaffine decoder that is well-suited for nested NER tasks. In contrast, OntoNotes 5 and CoNLL 2003 are flat NER corpora, where the baseline's performance is on par or slightly inferior to Yu et al. (2020). The span-based model architecture and the biaffine decoding mechanism in the baseline provide a stronger modeling capability for nested entities, leading to improved precision and overall F1 scores on ACE datasets, while on flat datasets the advantage is less pronounced, resulting in similar performance to Yu et al. (2020)."}
{"question_id": "31314c6ad7630579c350af928493caac9c563dbb", "paper_id": "nlpeer/ARR-22/0f8bc751a0472564732a976cd152643d92d5393c2e77c8234f386a801883d09ff9540378b3dab5834544a246c22f292d5b1c4d370219943c9a19a1626a8f8781", "generation": "The minimum and maximum threshold values are \u03c4_min = 0.0005 and \u03c4_max = 0.995."}
{"question_id": "e7e5b24e35bd512176a8587170677228842e2e24", "paper_id": "egu/esd/14-185-2023", "generation": "No, coastal seaweeds, which have a very low surface-to-volume ratio, are unlikely to be competitive in iron uptake against the mostly small and specialized open ocean phytoplankton that have a high surface-to-volume ratio, especially in iron-limited areas. The macroalgae growth model used in the study does not consider iron limitation because iron is a micronutrient needed in low quantities and iron limitation on macroalgae is not widely discussed. Additionally, the MOS platform could be designed with an iron supply for macroalgae, implying that natural iron uptake by macroalgae may be insufficient in iron-limited open ocean environments. This contrasts with small phytoplankton, which are adapted to efficiently uptake iron due to their high surface-to-volume ratio."}
{"question_id": "c83f53bbc1390bf3f6a15aa58e1c559cf391a507", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "Section 4.1 of the paper describes the task of text-queried sound separation. This task involves separating target sounds from audio mixtures based on input queries, which can be either text or image queries. The goal is to extract the sounds corresponding to the given query from noisy, unlabeled video data. The models are trained to handle noisy data in the wild, where audio may contain off-screen sounds and background noise, by using noise invariant training (NIT) with additional noise heads to separate query-relevant sounds from noise. The evaluation is performed on datasets such as VGGSound and MUSIC, measuring performance with metrics like signal-to-distortion ratio (SDR)."}
{"question_id": "0c4afb8ced370f2f67477fe4617ff846513cfb6d", "paper_id": "openreview/NeurIPS-2022-dabt/8lQDn9zTQlW", "generation": "Table 2 represents values related to an example instance from the BIOSSES dataset. Specifically, it includes the following key-value pairs:\n\n- id: 6\n- document_id: 7\n- text_1: \"Recently, it was reported that expression of IDH1R132H su...\"\n- text_2: \"the mechanism was clarified by yet another genomic survey...\"\n- label: 1.6\n\nAdditionally, a series of numerical values ranging from 1.9 to 4.0 in increments of 0.1 are listed, likely representing a range of similarity scores or related metrics associated with the example instance."}
{"question_id": "fffbbdd88b4cdc0b98de790921df08f7be1eed7d", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The zig-zag learning path behavior is representative and prevalent across the dataset, particularly for samples with higher base difficulty. Quantitative analysis using the defined zig-zag score shows a significant correlation between base difficulty and the extent of zig-zagging during training, as demonstrated in multiple toy datasets (Figure 14). In the CIFAR10 dataset, samples with flipped (wrong) labels, which are known to have high base difficulty, exhibit significantly higher zig-zag scores compared to the average (Figure 13). Additionally, random samples from each class in CIFAR10 reveal that easy samples with correct labels tend to have fast-converging learning paths, while samples with wrong labels or ambiguous samples with flat true label distributions show pronounced zig-zag patterns (Figures 15 and 16). Thus, the zig-zag pattern is a common phenomenon, especially for difficult or noisy samples, and is not limited to isolated cases."}
{"question_id": "67b6a78d6cea6ff4cd6a6cdd262aaf4e4bfea275", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "For the hard sample in Figure 3, the relationship between the true label distribution p* and the one-hot encoding e_y is characterized by a distinctive \"zig-zag\" learning path. Initially, the model's output probability q moves toward the unknown true label distribution p*, indicating a spontaneous refinement of the incorrect one-hot label. However, as training progresses, q eventually veers away from p* and converges toward the one-hot vector e_y. This behavior contrasts with easy and medium samples, which move more directly toward e_y. Thus, for the hard sample, p* differs significantly from the one-hot encoding, and the learning trajectory reflects an initial attraction to p* before ultimately memorizing the incorrect one-hot label."}
{"question_id": "27a8c35fcd38d0a141fb5248ad93038196553dfb", "paper_id": "egu/esd/14-185-2023", "generation": "The authors do not explicitly assume that iron is sourced from the platform when considering the growth of macroalgae in their model. They state that iron limitation on macroalgae is not widely discussed, especially for the genus Saccharina, and that iron is a micronutrient needed in low quantities. They note that the MOS (macroalgae open-ocean mariculture and sinking) platform could be designed with an iron supply for the macroalgae, in which case MOS could be considered to include a targeted variant of the ocean iron fertilization concept. However, the need for iron is not considered in their macroalgae growth model."}
{"question_id": "924a054e5ec561c4d58306dfd312782d7b4f70ca", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "Students are supervised using refined labels that go beyond the traditional one-hot vectors. These supervisory signals can be:\n\n1. One-hot labels: Traditional labels where the correct class is indicated by a 1 and all others are 0.\n\n2. Distributions of human annotations: Label vectors that summarize multiple annotators' opinions, providing a distribution over classes rather than a single hard label.\n\n3. Label smoothing: A convex combination of the one-hot label and a uniform distribution over classes, which softens the target label.\n\n4. Knowledge distillation (KD) outputs: Soft labels derived from a teacher network's output probabilities, which provide sample-specific refined supervision signals.\n\n5. Early-stopped knowledge distillation (ESKD): Labels obtained from a teacher network stopped early during training to avoid overfitting, yielding supervisory signals closer to the true underlying distribution.\n\n6. Ground truth distributions (p*): The true categorical distribution of labels given the input, used as an ideal supervisory signal in experiments.\n\nAmong these, knowledge distillation and its variants (including self-distillation) are emphasized as providing better supervision by approximating the true label distribution more closely than one-hot or label smoothing, leading to improved student performance. The supervisory signal quality is measured by its L2 distance to the ground truth distribution p*, with smaller distances correlating with better generalization."}
{"question_id": "a48eb6eab4e9448324227205ae04b8d47a5b181e", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "In addition to accuracy (ACC) and expected calibration error (ECE), the quality of supervision signals is also evaluated by the L2 distance between the target supervision distribution \\( p_{\\text{tar}} \\) and the ground truth distribution \\( p^* \\). This L2 distance serves as a criterion to measure how well the supervision approximates the true risk, which in turn influences generalization performance. Furthermore, Appendix B generalizes this measure by considering bounds based on total variation (L1) and Kullback-Leibler (KL) divergences, indicating that these divergences are also relevant criteria for assessing the quality of supervision and thus generalization."}
{"question_id": "01163085d0c4776005e14d8621ce2bbdd3cc1c13", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The Filter-KD model outperforms both label smoothing and knowledge distillation (KD) with optimized temperature in terms of accuracy and calibration. Specifically, in self-distillation experiments on ResNet18, Filter-KD achieves higher accuracy and lower expected calibration error (ECE) compared to ordinary KD and label smoothing (OHT), as shown in Table 3: for CIFAR10, Filter-KD attains 95.63% accuracy with an ECE of 0.007, surpassing KD (95.42%, 0.027) and OHT (95.34%, 0.026); for CIFAR100, Filter-KD reaches 80.09% accuracy with an ECE of 0.029, outperforming KD (78.83%, 0.067) and OHT (78.07%, 0.053). Furthermore, in the teacher-to-student distillation setting on CIFAR100 (Table 2), Filter-KD consistently yields higher student accuracy than ESKD and other baselines when the teacher is larger than the student, even when the temperature \u03c4 is optimized for each method. This demonstrates that the refined supervisory signals provided by Filter-KD lead to improved generalization performance over traditional label smoothing and KD methods with optimal temperature settings."}
{"question_id": "c5b7931f3e58dd10d67e388fcd5680c37e267022", "paper_id": "nlpeer/ARR-22/0f8bc751a0472564732a976cd152643d92d5393c2e77c8234f386a801883d09ff9540378b3dab5834544a246c22f292d5b1c4d370219943c9a19a1626a8f8781", "generation": "The threshold values are selected to ensure an appropriate distribution of class labels between constituents and distituents, reflecting linguistic and statistical considerations. Specifically, the lower and upper bounds of the threshold are chosen so that the distribution of class labels approximates a ratio of about 1:10, with the distituent class being the majority. This ratio is a crude estimate that accounts for the skewness of random spans, which are more likely to represent distituents, especially given the larger sentence lengths in the WSJ-Full section. From a linguistic standpoint, it is certain that distituents must necessarily outnumber constituents. The thresholds thus serve to treat outside strings satisfying the upper and lower bounds as gold-standard examples of constituents and distituents, respectively, facilitating the bootstrapping and fine-tuning of the outside model."}
{"question_id": "fcf91acb3ff79184eb4af002b876fec65732620c", "paper_id": "egu/esd/14-185-2023", "generation": "The partial pressure of carbon dioxide (pCO2) for the measurement of dissolved inorganic carbon (MOS_DIC) was calculated within the UVic Earth System Climate Model (UVic ESCM) framework, where MOS_DIC is treated as a tracer participating in the inorganic ocean carbon cycle. The outgassing of MOS_DIC at the ocean surface follows the air\u2013sea gas exchange process implemented in the UVic ESCM, as described in Weaver et al. (2001, Sect. 3e). This process calculates the air\u2013sea exchange flux of MOS-C based on the concentration of MOS_DIC reaching the surface, which is influenced by ocean circulation and remineralization dynamics. The ocean-retained fraction of MOS-captured carbon (FR) is computed using the ratio of carbon retained (sum of DIC remineralized and carbon in sunken biomass) to the total carbon captured in cumulative sunken biomass, as given by Equation (23):\n\nFR = (DIC remineralized + C_Sunk Biomass) / C_captured\n\nThus, pCO2 related to MOS_DIC is indirectly determined through the modeled air\u2013sea gas exchange fluxes driven by the MOS_DIC tracer concentration at the ocean surface within the UVic ESCM."}
{"question_id": "3493acb3c91a1415959829136fe3e250966fc8f0", "paper_id": "openreview/NeurIPS-2022-dabt/8lQDn9zTQlW", "generation": "The prompt design for biomedical NLP tasks in the BioNLP domain, as implemented in the B IG B IO framework, differs from general NLP tasks primarily in the formulation and adaptation of prompts to the biomedical context. Specifically, biomedical Named Entity Recognition (NER) tasks are formulated as text translation tasks, where the output is a list of all entities found in the prompted text, similar to the TANL approach. This contrasts with typical classification or generation prompts used in general NLP tasks.\n\nAdditionally, the biomedical prompts used in B IG B IO are largely based on general domain templates available in PromptSource but are populated with biomedical text, making them more \"in-distribution\" for models like T0 that were tuned on these general templates. This suggests that biomedical prompt design often involves adapting existing general NLP prompt templates to the biomedical domain rather than creating entirely novel prompt structures.\n\nFurthermore, no iterative prompt tuning was performed to improve the performance of these biomedical prompt templates, indicating a reliance on the initial adaptation of general templates rather than extensive prompt engineering specific to biomedical tasks.\n\nIn summary, the key differences are:\n1. Biomedical NER tasks are framed as text translation tasks outputting entity lists, unlike typical classification prompts in general NLP.\n2. Biomedical prompts are adapted from general domain templates but populated with domain-specific biomedical text.\n3. There is limited iterative prompt tuning in biomedical prompt design, relying instead on adapted general templates."}
{"question_id": "0258b0f39ec3f7316f9d299a25a7cd36274e9631", "paper_id": "openreview/ICLR-2023-conf/_01dDd3f78", "generation": "No, the concepts were not ranked according to the TCAV_Q score. Instead, the ranking was based on the Concept Gradients (CG) method, which extends beyond the linear assumption of TCAV by modeling concepts with non-linear functions and using gradient-based attribution. The CG method calculates concept importance by taking the inner product of the target model's gradient and the normalized gradient of the concept model, providing a more accurate and expressive measure of concept relevance. The results show that CG consistently outperforms TCAV in both local and global concept importance attribution, as demonstrated in the quantitative analyses and tables comparing recall metrics."}
{"question_id": "a81ef48de406906c5a847928da2bc47079136f55", "paper_id": "nlpeer/COLING2020/1681", "generation": "The number of distinct phrases used for phrase addition in the adversarial attack generation is eight."}
{"question_id": "bf41e9f2b170cb8e1801812167b945e8f56aa8cb", "paper_id": "openreview/ICLR-2022-conf/hcQHRHKfN_", "generation": "The results in Table 1 demonstrate that without intrinsic rewards, the RSPO algorithm may slow down convergence, fail to discover non-trivial local optima due to insufficient exploration, or get stuck during exploration because of low data efficiency. This highlights the importance of intrinsic rewards in promoting effective exploration and enabling RSPO to discover diverse and high-quality local optima in complex environments."}
{"question_id": "cd020940c9b12a598dae5fc4fde1d63c2d88d88d", "paper_id": "egu/esd/14-185-2023", "generation": "The potential for longer permanence of CO2 storage through sediment carbonate dissolution induced by high respiratory dissolved inorganic carbon (DIC) at the ocean bottom is not explicitly addressed in the provided content. However, the fate of sunken macroalgal biomass and its remineralization at the ocean bottom is discussed, highlighting that microbial remineralization consumes oxygen and creates benthic oxygen minimum zones (OMZs). When macroalgal biomass does not undergo microbial remineralization, the captured CO2 can be permanently stored without leakage, thereby increasing the carbon dioxide removal (CDR) potential of macroalgae open-ocean mariculture and sinking (MOS). This suggests that the permanence of CO2 storage is strongly influenced by the extent of remineralization processes.\n\nNo direct mention is made of sediment carbonate dissolution as a mechanism for enhancing CO2 storage permanence in relation to respiratory DIC accumulation. The discussion focuses primarily on organic carbon remineralization and its effects on oxygen and nutrient dynamics, as well as on potential side effects such as benthic OMZ formation and impacts on marine ecology. Therefore, while sediment carbonate dissolution could theoretically contribute to longer-term CO2 storage, this process has not been explicitly considered or analyzed in the study."}
{"question_id": "9c04f85fb5baad69d0ae21c1c2c07abc0422bd55", "paper_id": "nlpeer/ARR-22/013b9bf63a6f68fd0c3ecc36f8cbe2ad5bc92ea3bfe5a9f6c15eb056ecc4f858718410182c3765b2dc2695ae29ba08fb5dea5fc495faf2bbb77205bc3f765fcd", "generation": "To ensure a more diverse cross-cultural representation when building an NLP model, the following concrete steps can be taken:\n\n1. **Data Collection and Curation:**\n   - Use data sources that are appropriate and relevant for the target culture of the downstream NLP application to mitigate cultural bias.\n   - Curate large, general-purpose datasets to be as unbiased as possible and carefully document them.\n   - Recruit geo-diverse annotators from different regions and cultures to provide both data and annotations, as exemplified by initiatives like MasakhaNER for African languages.\n   - Ensure datasets represent diverse sources and multiple perspectives in terms of annotations, especially for subjective tasks where cultural differences affect interpretation.\n   - Release all annotations, including disagreeing ones, to capture cultural nuances and allow models to generalize better.\n\n2. **Annotation Practices:**\n   - Employ a diverse pool of annotators to reduce cultural bias.\n   - Carefully document the annotation process to maintain transparency and reproducibility.\n   - Consider the values, ideologies, and power relations that influence data collection and annotation to avoid hegemonic biases.\n\n3. **Model Training Strategies:**\n   - Apply data sampling techniques such as exponential smoothing of language sampling rates to reduce skew towards high-resource languages and improve representation of low-resource languages.\n   - Use group-aware optimization methods like Group Distributionally Robust Optimization (group DRO) or Worst-Case-Aware Curriculum Learning to optimize worst-case performance across languages or cultural groups, thereby improving parity.\n   - Incorporate methodologies that account for model updates for different groups to reduce representation disparity and cultural biases.\n   - Balance training objectives to avoid overemphasizing majority cultures, which can lead to cultural homogenization, while ensuring minority cultures receive adequate representation.\n   - Define cultural groups at an appropriate level of granularity to effectively capture cultural diversity without compromising privacy.\n\n4. **Transfer Learning and Pre-training:**\n   - Consider cultural factors when selecting source languages for model transfer to improve performance on target languages, acknowledging that semantic spaces may not be isomorphic due to cultural conceptualization differences.\n   - Combine improved data sampling with group-aware training methods during pre-training of multilingual language models to enhance cross-cultural parity.\n\n5. **Community Involvement and Documentation:**\n   - Engage with members of the cultures relevant to the NLP application to ensure culturally appropriate representation and to judge which cultural aspects can be challenged or preserved.\n   - Foster diverse and open communities dedicated to language and cultural diversity in NLP research to facilitate scalability and inclusivity.\n\nThese steps collectively address the challenges of cultural bias and representation disparity in NLP models by improving the diversity and cultural relevance of training data, annotation processes, and model training methodologies."}
{"question_id": "1a42a5af41f66bb6428c643d96fd05eba81ce635", "paper_id": "nlpeer/PeerRead-CONLL2016/166", "generation": "Yes, the proposed model accounts for named entities not explicitly listed on Wikipedia through the use of gazetteer features derived from Wikipedia titles and a method that generalizes to unseen entities. Specifically, the gazetteer features are applied by matching partial phrases and marking words according to their position in the phrase (beginning or inside). This approach allows the model to recognize patterns indicative of named entities even if the exact entity name is not present in the gazetteers. For example, the phrase structure learned from known entities such as \u201cBill and Melinda Gates Foundation\u201d enables the model to identify a fictional organization like \u201cDave and Sue Harris Foundation\u201d as an organization, despite it not being listed in Wikipedia. Additionally, the wiki\ufb01er links words and phrases to English Wikipedia entries, and even when exact matches are unavailable (e.g., \u201cAlbrecht Lehmann\u201d not being an entry), the model can still link components (e.g., \u201cAlbrecht\u201d and \u201cLehmann\u201d) to relevant entity types such as people, providing valuable language-independent features for NER."}
{"question_id": "b9d3ed7981d9f1e47fea48aaf2b4037fe7b25658", "paper_id": "openreview/ICLR-2023-conf/_01dDd3f78", "generation": "The purpose of finetuning the same network for the concept model g is to ensure that g is similar to the target model f in terms of both model architecture and weights. This similarity leads to better alignment in the utilization of input representations between f and g, which benefits the accuracy and reliability of concept importance attribution via gradients. Specifically, finetuning g from the pretrained weights of f allows g to converge to a solution closer to f, reducing the risk of misalignment in gradients \u2207g(x) and \u2207f(x) that could cause underestimation of concept importance. Additionally, freezing certain layers during finetuning can further constrain g to maintain similarity with f. This approach ensures that the concept model g relies on the same information as f for prediction, improving the quality of concept gradient (CG) interpretation."}
{"question_id": "587b8f363bb9be4e82b38b70f74608f844559b6f", "paper_id": "nlpeer/COLING2020/1681", "generation": "The data augmentation strategy, termed Antecedent-free augmentation (afa), involves identifying sentences where a coreferential pronoun \"it\" refers to an antecedent that is not present in the current or previous sentence. For these candidate sentences, two new training examples are created by modifying the gender of the German translation of \"it\" to masculine (\"er\") and feminine (\"sie\"), while keeping the source sentence unchanged. This augmentation is performed using a coreference resolution tool to identify antecedents and coreferential pronouns. The augmented dataset, consisting of the original candidates plus the gender-modified examples, is used to fine-tune an already trained concatenation transformer model. This approach aims to break the strong prior bias towards the neuter pronoun \"es\" in German translations and improve coreferential pronoun translation performance. The augmentation process also involves using Spacy\u2019s dependency parser to determine the correct case of the pronouns, which is necessary due to case distinctions in masculine pronouns. Fine-tuning is conducted on approximately 207,000 augmented samples, with learning rate tuning to balance performance across masculine, feminine, and neuter pronouns."}
{"question_id": "0c64726cf3b593196fd8f350d0f2c2d4aba98d1e", "paper_id": "nlpeer/ARR-22/7e701552b7cfaad6239d3d5dfcd9da1852a03a66907c789fe65d368f1c2845e723adcecd7f7008849eef13c7ca467be73de53747c3376a6bb46d1911ed1b6409", "generation": "ELECTRA's binary classification head (RTE) uses a token classification approach where each token in the input sequence is tagged as either a \"good\" gap or not. Specifically, all the gaps in the training data are replaced with the first intended target answer and labeled as positive (\"good\"), while the remaining tokens are labeled as negative (\"not good\"). Thus, the label mapping for the binary classification head is:\n\n- Positive label: Tokens corresponding to good gaps (expected to be effective during testing).\n- Negative label: Tokens corresponding to non-gaps or unsuitable gaps."}
{"question_id": "9ee8e92c0faa3a3d17cff68d75d28be3dca8e8c4", "paper_id": "openreview/ICLR-2022-conf/hcQHRHKfN_", "generation": "The provided chunks do not contain any information about the impact of adjusting the parameter \\(\\delta\\) on the results of Table 1. There is no mention or analysis of \\(\\delta\\) in relation to Table 1 or its results in the extracted content. Therefore, no conclusion or factual statement can be made regarding the impact of adjusting \\(\\delta\\) on Table 1 based on the given information."}
{"question_id": "1aa55ba3f2f47947ad4df928f35cfc4c7d7035ec", "paper_id": "nlpeer/ARR-22/21dd1d5e232c7abf421302a95ef2c9ef44c7bad069da7ca4129a8cf1bfbd6f660afee123144ebcbd8da698aed212e2ca1151a13bca7aaef50ce59d27d23aa83a", "generation": "The focus of this paper is on intent classification and entity/slot recognition within task-oriented cross-lingual natural language understanding (XNLU)."}
{"question_id": "ba25580bbc4ec4f20348cefaf968e1cdea408642", "paper_id": "openreview/ICLR-2023-conf/XrMWUuEevr", "generation": "In the domain shift experiment described in Section 5.3, the 8 positive and 8 negative examples per task used to build the support set are randomly selected molecules from the training set of the Tox21 dataset. The positive examples are molecules labeled as active for a given task, while the negative examples are molecules labeled as inactive for that task. These support sets are then used during test time to predict the activities of the Tox21 test set molecules under the few-shot learning setting."}
{"question_id": "44279226e8c3ea5497ac4c43754e3e890e0183fb", "paper_id": "openreview/ICLR-2023-conf/XrMWUuEevr", "generation": "The best way to compare the performance of different models or architectures, including variations such as x, x', x'', is to conduct multiple training reruns and evaluate across multiple support set samplings for each task. Performance metrics such as area under the precision-recall curve (AUC-PR) and the difference to a random classifier (\u2206 AUC-PR) should be computed and averaged over these runs and samplings. Statistical significance of performance differences can be assessed using paired non-parametric tests such as the Wilcoxon rank sum test. Additionally, error bars representing standard deviations or standard errors across training reruns, support set draws, and tasks should be reported to capture sources of variation. Task-wise model comparisons can be visualized using scatterplots where each point corresponds to a task, and the relative performance of models is indicated by color coding. This approach allows for robust and statistically sound comparison of model variants and architectures."}
{"question_id": "9824d5fa73a188c99c7d977a3dda3d2b24856f9d", "paper_id": "openreview/ICLR-2023-conf/_01dDd3f78", "generation": "The results of the experiments on the myocardial infarction complications dataset regarding mortality risk have been evaluated by comparing the global Concept Gradient (CG) scores with existing medical literature on how each complication affects the risk of death. The alignment between the CG scores and the severity of mortality risk descriptions in the medical literature supports the validity of the interpretations. This comparison serves as an expert review, as the medical literature cited includes authoritative sources and established clinical findings. Therefore, the experimental results have been reviewed and validated against expert medical knowledge."}
{"question_id": "5678b6bf40f5958402473fd66a08dc836eaa98a7", "paper_id": "nlpeer/COLING2020/1681", "generation": "The coreference resolution pipeline depicted in Table 1 is an idealized, hypothetical pipeline that sequentially resolves and translates a pronoun through three steps: markable detection, coreference resolution, and language translation. It resembles the rule-based approach implemented in Stanford CoreNLP\u2019s Coref-Annotator (Raghunathan et al., 2010; Lee et al., 2011). However, it is not universally accepted as a standard in the field. Neural machine translation (NMT) models currently do not decouple these individual steps, and the pipeline serves primarily as a conceptual framework to isolate and evaluate specific steps in pronoun translation rather than a universally adopted methodology."}
{"question_id": "70418ac3cb9f40b039a74031b89324e2b891ccf5", "paper_id": "nlpeer/PeerRead-ACL2017/699", "generation": "The word embeddings are randomly initialized with a uniform distribution in the range [-0.1, 0.1]."}
{"question_id": "89fb9729921ad950b90987550b32f9ede60c8a8c", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "Yes, something went wrong during the training of the Deep Gaussian Process (DGP) for the interpolation experiment on the Mauna Loa CO2 dataset. The DGP with RBF kernels failed to capture the data trend, leading to mean reversion and over-estimation of the prediction uncertainty. In contrast, the Deep Variational Implicit Process (DVIP) model was able to capture the data trend in the missing gaps and produced very smooth prior samples."}
{"question_id": "8fc13b01107e614b030a2c7dbc65aa19d0363778", "paper_id": "nlpeer/ARR-22/21dd1d5e232c7abf421302a95ef2c9ef44c7bad069da7ca4129a8cf1bfbd6f660afee123144ebcbd8da698aed212e2ca1151a13bca7aaef50ce59d27d23aa83a", "generation": "The authors do not explicitly state that they ran their experiments multiple times with different random initializations to confirm the results. The experimental setup mentions using fixed hyperparameters between experiments and a minimalist setup to focus on methods rather than hyperparameter tuning or custom architecture design, but there is no mention of multiple runs or random seed variations for result confirmation."}
{"question_id": "b97a690598219404cca037101ff05ec348b5e525", "paper_id": "openreview/NeurIPS-2022-dabt/heBKnuV42O", "generation": "There is no indication or statement in the provided information that the proprietary medical knowledge base or the telemedicine software (rule-based AD system) will be open-sourced. The dataset DDXPlus, which is synthesized using the proprietary medical knowledge base and the commercial rule-based AD system, is released under a CC-BY license. However, the knowledge base and the rule-based AD system themselves remain proprietary and are provided by Dialogue Health Technologies Inc., with no mention of plans for open-source release."}
{"question_id": "65df6e41f1c8c77eec8b264ef0a3dcd104abb9dc", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The higher average out-degree of normal users compared to fraudsters is because fraudsters tend to fill fewer emergency contacts in general. This behavior aligns with their primary purpose of defrauding the platform, as filling more emergency contacts is not helpful for committing fraud. Normal users, on the other hand, are more likely to provide a larger number of emergency contacts, reflecting more genuine social connections."}
{"question_id": "d49df57b22ec381fed263033d6a02678f16a18c1", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "The width of the model, defined as the number of units per layer, does not significantly affect the performance when the prior functions are the same for all units. Specifically, increasing the width of the Bayesian neural network (BNN) prior in the Variational Implicit Processes (VIP) framework, for example from a standard size to 200 units per layer, does not improve results. This indicates that using a more flexible BNN prior by increasing the number of units per layer does not lead to better performance. Instead, performance improvements are more effectively achieved by increasing the depth (number of layers) of the model rather than its width, as deeper models (with more layers) tend to perform better or comparably without overfitting."}
{"question_id": "428b48f2d5cfea8890c3fe80599575b25565a976", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "Using only one mixture component (R = 1) during training and using 100 mixture components (R = 100) during testing is a strategy employed in the Deep Variational Implicit Processes (DVIP) framework. During training, setting R to 1 simplifies the computation and reduces computational cost by propagating a single Monte Carlo sample through the network. In contrast, during testing, using R = 100 Monte Carlo samples allows the approximation of the predictive distribution \\( q(f_L \\cdot, \\star) \\) as a Gaussian mixture with 100 components, which is more flexible and better captures uncertainty than a single Gaussian predictive distribution. This approach balances computational efficiency during training with improved predictive flexibility and accuracy during testing."}
{"question_id": "5227809e5dbd6a7ef588b7a84fc243e6cd0eed8d", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "The provided evidence chunks do not contain any information regarding the compensation rate for the annotators hired for the Mechanical Turk crowdsourcing work."}
{"question_id": "10dbff5874380289cdab832a0eecab1cc3c34117", "paper_id": "openreview/ICLR-2023-conf/VA1YpcNr7ul", "generation": "The proposed algorithm DASHA is related to momentum variance reduction methods (MVR), which are in the family of variance reduction techniques. Specifically, DASHA is inspired by MARINA and MVR methods (Cutkosky & Orabona, 2019; Tran-Dinh et al., 2021; Liu et al., 2020). It incorporates a variance reduction strategy borrowed from MVR rather than SVRG directly. In the stochastic setting, DASHA uses an MVR-like strategy for gradient estimation. Moreover, DASHA-MVR (Algorithm 1) achieves optimal oracle and communication complexity with compression, improving upon previous state-of-the-art methods such as VR-MARINA (online). Thus, while DASHA is not explicitly described as an SVRG variant, it belongs to the broader class of variance reduction algorithms, specifically leveraging momentum-based variance reduction techniques."}
{"question_id": "b879c4d1344942fd8fab9fbe8fc495f4ae67c0b0", "paper_id": "openreview/ICLR-2023-conf/VA1YpcNr7ul", "generation": "The provided chunks do not contain any explicit definition or statement specifying that \\(\\omega\\) is an element of \\(\\mathbb{R}\\) in Definition 1.1. There is no direct mention or formal declaration of \\(\\omega\\)'s domain or type in the excerpts related to Definition 1.1 or elsewhere in the provided text. Therefore, it cannot be confirmed from the given information that \\(\\omega \\in \\mathbb{R}\\) in Definition 1.1."}
{"question_id": "e0385760ba4f37eeba3376cf4dd4ed0727cedb81", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "The rationale for choosing the prior in the model is to use an implicit process (IP) prior that can flexibly represent distributions over functions, such as Bayesian neural networks (BNNs) or Gaussian processes (GPs). This choice enables the model to adapt the prior parameters to the observed data, which is a key factor for accurate predictions. Specifically, the model employs a variational implicit process (VIP) prior that approximates the IP prior with a Gaussian process, allowing prior adaptation through the dependence of samples on the IP prior parameters. This adaptation improves predictive performance compared to fixed priors. Moreover, the use of IP priors generalizes Gaussian processes, enabling more flexible and expressive function priors, including deep architectures formed by concatenating IPs (deep variational implicit processes, DVIP). The linear model approximation of the GP prior facilitates computational scalability and mini-batch training, making the approach practical for large datasets. Thus, the prior is chosen to balance flexibility, adaptability to data, and computational tractability."}
{"question_id": "9073f9407118eaf0b34170000a57846d672b4a5d", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "The four conversational question answering (CQA) models were selected based on their different model architectures and training strategies. Specifically, the chosen models include:\n\n1. BERT: A simple baseline model that concatenates the previous two turns of question-answer pairs, the current question, and the passage as input to predict the answer span.\n\n2. GraphFlow: A model that employs a recurrent graph neural network on top of BERT embeddings to model dependencies between the question, conversational history, and passage.\n\n3. HAM (History Attention Mechanism): A model that uses a history attention mechanism to softly select the most relevant previous turns in the conversation.\n\n4. ExCorD: A model that trains a question rewriting module on the CANARD dataset to generate context-independent questions, using both original and rewritten questions to train the QA model; it achieves state-of-the-art performance on the QuAC dataset.\n\nThese models were chosen to represent a range of architectural designs and training approaches for direct comparison in human evaluation and analysis."}
{"question_id": "81b292ac9640d75024251269649ea71272b1710c", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "When both Deep Gaussian Processes (DGP) and Deep Variational Implicit Processes (DVIP) use the same Gaussian Process (GP) priors, the performance of DVIP is similar to that of DGP, as DVIP generalizes DGP in this setting. However, the tradeoff lies in computational cost: approximately sampling from the GP prior in DVIP is significantly more expensive than the inducing point approach used in DGP. Consequently, while DVIP can achieve comparable predictive accuracy, its runtime is higher when using GP priors. This increased computational cost makes it impractical to add extra layers in DVIP with GP priors, whereas DGPs using inducing points remain more efficient. Therefore, the tradeoff is that DVIP with GP priors matches DGP performance but at a substantially higher computational cost and longer runtime."}
{"question_id": "92c772c75354552e709f16f3e3b15a31e395f1cf", "paper_id": "openreview/NeurIPS-2022-dabt/heBKnuV42O", "generation": "The rationale for altering the original disease incidence and prevalence rates from the knowledge base (KB), despite the KB being compiled from extensive medical literature and expert review, is to address limitations in the data generation process and to produce a more balanced dataset. Specifically, some diseases have incidence rates exceeding 100%, which would lead to a highly imbalanced dataset dominated by a few pathologies (e.g., URTI, Viral pharyngitis, and Anemia). To prevent this, incidence rates are capped at a maximum of 100%. Conversely, some diseases have extremely low incidence rates, resulting in very few synthesized patients with those pathologies. To ensure adequate representation of these rare diseases, incidence rates are also capped at a minimum of 10%. These adjustments help mitigate the imbalance inherent in the original epidemiological data and improve the diversity and representativeness of the synthesized patient dataset."}
{"question_id": "4bb993f44c76628b67f41da43c78aa82b50cbc19", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The full name of the algorithm DUVN is \"Double Uncertainty Value Network.\""}
{"question_id": "7b375e548c69cd6c0b0d75953da0021adb9e2a7e", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The computation time per update for REDQ is approximately 2200\u20132300 milliseconds, whereas for SAC it is approximately 800\u2013900 milliseconds. Thus, REDQ requires more than twice the computation time per update compared to SAC."}
{"question_id": "721388955b97db097e963c4b221fe9af9e2da4ae", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "When the absolute accuracy numbers are higher than the F1 scores, it indicates that the automatic evaluation using gold answers cannot capture all possible correct answers to open-ended or multi-answer questions, leading to lower F1 scores. In contrast, human evaluators can judge the correctness of answers more flexibly, recognizing multiple valid answers, which results in higher accuracy numbers. Thus, the discrepancy arises because automatic evaluations are limited by static gold answers, whereas human evaluation can account for the variability and multiplicity of correct responses."}
{"question_id": "c3b651600b60b22f2a4c805aeb87745aff3c0c84", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The full name of the proposed method \"REDQ\" mentioned in the paper is \"Randomized Ensembled Double Q-learning.\""}
{"question_id": "96525f380b4694ec387b707fa87e78c972a12b4c", "paper_id": "openreview/NeurIPS-2022-dabt/FPgCB_Z_0O", "generation": "Training on the DARTset dataset demonstrates significant effectiveness in improving hand pose estimation and mesh reconstruction performance, particularly when combined with real-world datasets. Cross-dataset evaluations using two mesh reconstruction methods, CMR and METRO, show that mixed training with DARTset and FreiHAND improves performance on the FreiHAND test set by reducing PA-MPVPE errors by approximately 8.9% for CMR and similar improvements for METRO. This indicates that DARTset complements challenging real-world datasets by providing a wider and more continuous hand pose distribution, which enhances generalizability.\n\nFurthermore, ablation studies reveal that including accessories in DARTset improves model accuracy: Integral Pose improves by 7.8% in PA-MPJPE, and CMR improves by 5.9% in PA-MPJPE and 7.2% in PA-MPVPE when trained with accessories. Benchmarking on DARTset itself shows competitive results for state-of-the-art methods, with Integral Pose achieving a PA-MPJPE of 3.52 cm and CMR achieving a PA-MPVPE of 3.46 cm, indicating the dataset's suitability for training high-performance models.\n\nOverall, training on DARTset, especially when mixed with real-world data, enhances model robustness and accuracy due to its diverse hand poses, rich textures, and inclusion of accessories, making it a valuable resource for advancing 3D hand pose estimation and mesh reconstruction tasks."}
{"question_id": "c3790b0e0925f080c8d91160ae348f35ac367993", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "Word overlap was used instead of an exact match when comparing F1(s\u2217_{j,1}, s_{j,1}) to determine whether two entities are the same because the prediction may mention the same entity as the gold answer but with slightly different names. Using word-level F1 allows for detecting entity equivalence even when the entity mentions differ in wording but share overlapping words, thereby avoiding false positives in detecting invalid questions. This approach accounts for variations in entity naming that exact match would fail to capture."}
{"question_id": "672a51e77d6a34b48ad5a93aa1a37139d71e3c72", "paper_id": "nlpeer/ARR-22/1370310ca3e0e0d092a2e8b58f90b0d692331caa6e3ce5ca6dacbef926f2b01ff49c81282f32c554fa66c04979a125b7548c9692c2091bb2673642102d85412d", "generation": "The rationale for analyzing the self-attention functions in the last layer of the BERT model is that fine-tuning BERT on specific tasks does not significantly change the correlations of attention patterns to human attention, and attention functions in earlier layers remain largely stable. Additionally, attention flow, which captures the layered propagation structure across all layers, produces stronger correlations with human attention than selecting the highest correlating head from only the last attention layer. This suggests that raw attention weights, including those from individual heads in the last layer, do not reliably correspond to token relevance. Therefore, focusing on the last layer's self-attention functions alone is insufficient, motivating the use of attention flow to better capture the importance scores across layers."}
{"question_id": "9dfb86a40b292918a304254d03b72b6fee37e740", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The target knowledge base (KB) size used for MedMentions in the experiments is 2.3 million entities."}
{"question_id": "1cd2ab406e0d21ae6597092cde6d7a2fca652f82", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The provided information does not explicitly address whether dynamic nodes need to be included in the data for the dynamic graph approach to be effective. However, the dataset DGraph is described as a real-world dynamic graph in the finance domain, containing about 3 million nodes and 4 million dynamic edges, with temporal dynamics being a key characteristic. The temporal dynamics of edges and nodes are emphasized as important for detecting anomalies such as fraudsters, indicating that capturing temporal changes in the graph is crucial.\n\nMoreover, the dataset preserves missing values on node features and includes background nodes (nodes without borrowing behavior), which are essential for maintaining graph connectivity and providing semantic information. The temporal dynamics of edges, such as the average time interval of out-edges, differ between anomalous and normal nodes, suggesting that dynamic temporal information is important for effective anomaly detection.\n\nWhile the text highlights the importance of temporal dynamics and dynamic edges, it does not explicitly state that nodes themselves must be dynamic (i.e., changing over time) for the dynamic graph approach to be effective. The focus is more on the dynamic nature of edges and temporal information associated with nodes.\n\nIn summary, the effectiveness of the dynamic graph approach relies on incorporating temporal dynamics and dynamic edges, but there is no direct evidence that nodes themselves must be dynamic. The inclusion of temporal information related to nodes and edges is critical for the approach's success."}
{"question_id": "abf4bcae7809ff5b01e8cf7fdb201caa7b8421ac", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The computation time referred to is the process time per update loop, specifically the time required for executing the overall update loop (lines 3\u201310 in Algorithm 2) and the Q-functions update (lines 4\u20139 in Algorithm 2). Process times measured in milliseconds show that REDQ requires approximately 2200\u20132400 msec per overall loop, whereas SAC requires about 800\u2013900 msec. This indicates that REDQ runs more than two times slower than SAC in terms of the process time per update."}
{"question_id": "f567015ed8777554298ac8d5b511b255c317d3da", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "Dropout on its own does not work effectively for the proposed method because it destabilizes the learning of the Q-functions. Specifically, using dropout without additional engineering leads to significant oscillations in the Q-function loss and the variance of its gradient with respect to the parameters, causing learning instability. This instability hampers proper Q-value propagation and degrades overall performance. The proposed method addresses this issue by combining dropout with layer normalization, which suppresses these oscillations and stabilizes learning, thereby improving performance, especially in complex environments."}
{"question_id": "8a7925cf9978728b68e7bc89204643a94468964a", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The combination of layer normalization and dropout improves overall performance in reinforcement learning, especially in complex environments such as Ant and Humanoid. The primary benefit of using layer normalization with dropout is that layer normalization suppresses the learning instability caused by dropout. Dropout alone destabilizes the learning of Q-functions, leading to significant oscillations in the Q-function loss and the variance of the gradient loss with respect to Q-function parameters. Layer normalization effectively reduces these oscillations, stabilizing the learning process. This stabilization enables better Q-value propagation, which consequently improves overall performance. Additionally, the variance re-scaling component of layer normalization is important for this synergistic effect. Other normalization methods, such as batch normalization, do not provide similar benefits and can result in unstable Q-function learning. Group normalization, which is mechanistically similar to layer normalization, also works well when combined with dropout, but layer normalization with variance re-scaling remains a key factor for the observed performance improvements."}
{"question_id": "b509625d37c9da4c7585e00370dc166be8e9be88", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "No, the model does not need to compute \\(\\psi(m_i, e)\\) for all entities in the target knowledge base during inference. Instead, a graph \\(G\\) is constructed for each mention \\(m\\) by adding edges from \\(m\\) to its \\(k\\)-nearest neighbor mentions and to its nearest entity, based on the dissimilarity function. This approach uses nearest neighbor search (e.g., via FAISS) to efficiently identify a limited set of candidate entities and mentions for each mention node, rather than exhaustively computing \\(\\psi(m_i, e)\\) for all entities in the knowledge base."}
{"question_id": "60c2bf23190bf1120b8652501ff951bae6f3e046", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The rationale for comparing 2-hop homophily (with background nodes as intermediate nodes, denoted as \"xBx\" or with target nodes as intermediate nodes, \"xTx\") to 1-hop homophily (direct connections, \"xx\") in Figure 3(d) is to investigate the role of background nodes in the connectivity and semantic relationships within the DGraph. The comparison reveals that 2-hop connections involving background nodes as intermediates have a higher homophily ratio than direct 1-hop connections. Moreover, the homophily ratios of 2-hop connections are greater than those of two directly connected nodes. This indicates that background nodes, despite being difficult to separate by node features, play a crucial role in maintaining network connectivity and contain abundant semantic information that enhances the similarity (homophily) between nodes. Therefore, analyzing 2-hop homophily helps to understand how background nodes contribute to the graph structure and can potentially improve fraud detection performance by leveraging these indirect relationships."}
{"question_id": "3356f072c76c12c8ebc215b5bd495d5ccbea0126", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The training batches are constructed by sequentially building mini-batches of mentions \\( B \\subset M \\) over the training data, where each mention \\( m_i \\in B \\) has its gold entity \\( e_i^* \\) defined. The graph \\( G_B \\) built for each mini-batch includes nodes consisting of (a) each mention \\( m_i \\in B \\), (b) each mention coreferent to \\( m_i \\in B \\), and (c) the set of gold entities for each \\( m_i \\in B \\). This indicates that the batches include not only the mentions in the batch but also their coreferent mentions, which may come from the same or different documents, to explicitly model mention coreference relationships.\n\nThus, the batches are designed to leverage coreferent mentions by including mentions that are coreferent to those in the batch, regardless of document boundaries. The procedure does not restrict mentions to be from the same document but rather includes all coreferent mentions to maximize the modeling of coreference.\n\nTherefore, training and inference batches contain mentions that are coreferent, which may be from the same or different documents, to leverage coreferent mentions as much as possible."}
{"question_id": "4a12daa058e224f39629de8997d5de7c8b0c2d3c", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The complete graph is not used for training because it includes edges that cross cluster boundaries, which is undesirable. Specifically, constructing a fully connected graph over mentions and entities results in noise due to ignoring the directionality of nearest neighbor relationships and forces all pairs of mentions to be connected, even if they belong to different clusters. The pruning procedure removes edges with weights above a dissimilarity threshold and those that violate constraints ensuring that each cluster contains at most one entity and maintains connectivity consistent with coreference structure. This pruning yields a minimum spanning arborescence rooted at the entity node, which better models mention coreference relationships by preserving directed paths from the entity to mentions within the same cluster and minimizing dissimilarity. Thus, pruning the graph enforces structural constraints and reduces noise, leading to more accurate and meaningful positive training examples for optimizing the mention and entity encoders."}
{"question_id": "8d69a05246c31778897996bc35b60061f15554f3", "paper_id": "nlpeer/COLING2020/1550", "generation": "The results presented in the paper are primarily task- and dataset-specific. The study focuses on the stance detection task related to Fake News Detection, which is treated as a multi-class classification problem on the FNC-1 and the extended FNC-1 ARC data sets. These data sets have particular characteristics, such as heavily skewed class distributions and heterogeneity in the extended data set, which influence model performance and hyperparameter preferences.\n\nThe paper explicitly states that while the findings provide strong insights and starting points for further experiments in related fields, they do not claim that these findings are directly transferable to other Fake News related data sets using different tasks or label sets. The specific advantage of certain models, such as RoBERTa outperforming XLNet, is attributed to the nature of the stance detection task being segment-level rather than token-level, which may not hold for other tasks.\n\nTherefore, the conclusions and hyperparameter recommendations are robust within the context of the examined Fake News stance detection tasks and data sets but should be cautiously applied to other tasks or data sets without further validation."}
{"question_id": "2074c1cd08c7d4b134ac01c5ee57f13765a4cc47", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "Layer normalization offers distinct advantages over other regularization schemes for improving the performance of dropout in deep reinforcement learning, particularly in high update-to-data (UTD) ratio settings and complex environments such as Ant and Humanoid. The key advantages are:\n\n1. **Stabilization of Learning Instability Caused by Dropout:** Dropout alone destabilizes Q-function learning, leading to significant oscillations in Q-function loss and gradient variance. Layer normalization effectively suppresses these oscillations, stabilizing the learning process. This stabilization enables better Q-value propagation, which improves overall performance.\n\n2. **Synergistic Effect with Dropout:** The combination of dropout and layer normalization produces a synergistic effect that significantly enhances performance compared to using either technique alone. This synergy is especially pronounced in complex environments, where the combined use leads to higher average returns and reduced estimation bias.\n\n3. **Variance Re-scaling Component:** The variance re-scaling aspect of layer normalization is critical for its effectiveness. Variants of layer normalization without variance re-scaling fail to stabilize learning and do not improve performance significantly, indicating that this component is a primal factor in the synergy with dropout.\n\n4. **Superiority Over Other Normalization Methods:** Batch normalization does not improve performance or reduce estimation bias in high UTD settings and leads to unstable Q-function learning when combined with dropout. Group normalization, which shares a similar mechanism to layer normalization, only improves performance when combined with dropout but is less effective than layer normalization. Thus, layer normalization is uniquely effective in stabilizing dropout Q-functions.\n\n5. **Effectiveness in High UTD Ratio Settings:** Unlike previous normalization methods introduced in reinforcement learning, layer normalization combined with dropout is effective in challenging high UTD ratio settings, where other normalization methods like batch normalization fail to contribute to performance improvement.\n\nIn summary, layer normalization uniquely stabilizes the learning dynamics introduced by dropout through variance re-scaling, enabling a synergistic improvement in Q-function learning stability and overall reinforcement learning performance that other normalization or regularization methods do not achieve."}
{"question_id": "52e2f447dc9bcf3dc208f99a89d4b59da4004923", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "No, the embedding matrix for relative positions (W_p) is randomly initialized, as stated in the hyperparameter and model training description. Specifically, W_w (word embeddings) is initialized with pre-trained embeddings, while W_p (position embeddings) and all other model parameters are randomly initialized."}
{"question_id": "dfa7d8d8808a8928555e5e665068db90d3261334", "paper_id": "openreview/ICLR-2022-conf/fQTlgI2qZqE", "generation": "Yes, it is valid to assume that finite evaluations of one arm are sufficient to obtain an accurate reward. This is stated explicitly as Assumption 1 in the proposed interaction detection method, which assumes that a finite number m of evaluations of one arm suffice to obtain an accurate reward estimate. This assumption underpins the construction of confidence intervals and the convergence guarantees of the UCB algorithm used for detecting the k strongest pairwise interactions."}
{"question_id": "986801b0e009ad637f5bc4d62c8af27a2580f7b9", "paper_id": "nlpeer/ARR-22/3282bdf968d7f87ae69c4ae7fdd35d4e9a46fa83f56d8bee0296c16dcf23b23cb1e4f20f510c7d01fb7be2309c08dd29cc240b54bdcbc3d66969041b5c84077a", "generation": "The degree of formality in Hindi formality transfer is controlled by a parameter \u03bb, which modulates the style change magnitude. As \u03bb increases, sentences become more formal, demonstrated by lexical and honorific changes. For example, the English word \"job\" (\u091c\u0949\u092c) in informal sentences is converted to Persian-derived \"\u0928\u094c\u0915\u0930\u0940\" or high Sanskrit-derived \"\u01d3\u0928\u092f\u0941\u093f\u00c8\u0924\" in more formal sentences, accompanied by the use of honorifics such as \"\u0906\u092a\u0915\u0940\" and verb forms like \"\u092c\u0924\u093e\u090f\u0902.\" This indicates that the formality transfer effectively incorporates Persian and Sanskrit lexical forms and honorific usage to increase formality in Hindi. The DIFFUR models, especially DIFFUR - MLT, achieve the best control over style transfer, allowing large style variation with \u03bb without loss in semantic content, thus maintaining the degree of formality and appropriate lexical substitutions consistently."}
{"question_id": "8b0ad0aeb866b9064a6bd71b1559fe85d81a612b", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The event-mention representation \\( v_e \\) serves as a compact, fixed-size vector encoding the semantics of an individual event mention. It is generated by processing sentential features derived from a convolutional neural network applied to the sentence containing the event mention, combined with lexical-level features from the trigger word and its immediate context. This representation captures both the sentence-level context and the lexical semantics of the event trigger, enabling the model to effectively characterize the event mention for subsequent tasks. Specifically, \\( v_e \\) is used as the input representation for the event-linking decision component of the model, where it facilitates the comparison and coreference resolution between pairs of event mentions."}
{"question_id": "45a145511dd96e98d18e5ac09f454b95ceee5a38", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The left and right neighbors of the trigger word are incorporated into the event-mention representation by concatenating their word embeddings with the sentential features. Specifically, after the convolution and piecewise max-pooling steps produce sentential features \\( v_{sent} \\in \\mathbb{R}^{2 \\times d_c} \\), these features are concatenated with the word embeddings of the trigger word and its immediate left and right neighbors, resulting in a combined vector \\( v_{sent+lex} \\in \\mathbb{R}^{2 \\times d_c + 3 \\times d_w} \\). This concatenation encourages the model to take into account the lexical semantics of the trigger and its surrounding context, which can be strong indicators for coreference. The combined vector \\( v_{sent+lex} \\) is then processed by an additional hidden layer to generate the final event-mention representation \\( v_e \\)."}
{"question_id": "c8e25c77b2ec42c4f94bc044959aa372dd3f9638", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The design of the proposed method is not arbitrary for all layers of a given ViT model; rather, the token reorganization modules are incorporated at specific, fixed layers. For example, in DeiT-S and DeiT-B (which have 12 layers), the token identification modules are incorporated into the 4th, 7th, and 10th layers. For LV-ViT-S (with 16 layers), they are incorporated into the 5th, 9th, and 13th layers. This placement strategy cuts the ViT into blocks with the same number of layers, where the reorganization layers are evenly spaced by calculating a separating length \\( s = L/(t+1) \\) (with \\( L \\) being the total number of layers and \\( t \\) the number of token reorganization layers), and the reorganization layers are placed at indices \\([s+1, 2s+1, \\ldots, ts+1]\\).\n\nFurthermore, moving the reorganization modules into shallower layers (e.g., before the third layer) significantly deteriorates accuracy, indicating that early layers are not suitable for token reorganization due to unreliable attention maps at shallow stages. In contrast, placing the reorganization modules in deeper layers (beyond the third layer) has only marginal influence on accuracy, suggesting stable performance as long as the modules are not placed in shallow layers.\n\nTherefore, the method fixes the token reorganization layers at specific deeper layers rather than arbitrarily applying them to all layers."}
{"question_id": "d753561800b2ad04d3d8262519328d014142d717", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The rationale for choosing to remove tokens at the 4th, 7th, and 10th layers in Vision Transformer (ViT) models such as DeiT-S and DeiT-B is based on a strategy to evenly distribute token reorganization layers throughout the network. Specifically, for a ViT with \\(L\\) layers and \\(t\\) token reorganization layers, the network is divided into blocks of equal length \\(s = L/(t+1)\\), and the token reorganization layers are placed at the indices \\([s+1, 2s+1, \\ldots, ts+1]\\). For DeiT-S and DeiT-B, which have 12 layers, this calculation results in token reorganization at the 4th, 7th, and 10th layers, effectively cutting the ViT evenly.\n\nThis approach allows progressive reduction and fusion of less informative tokens as the network deepens, reducing computational cost while preserving important information. The early interaction and information exchange between tokens in these intermediate layers make it possible to discard or fuse inattentive tokens without significant loss of accuracy. This method also facilitates the gradient back-propagation through inattentive tokens for better attentive token identification during training."}
{"question_id": "65ca807b7bfc58200ae0e5c46fcec1e31096cbf5", "paper_id": "openreview/ICLR-2022-conf/fQTlgI2qZqE", "generation": "Yes, the boost in performance of the ParaACE model is expected to be seen in other datasets as well. The experiments demonstrate that ParaACE improves predictive performance and significantly reduces model size across various synthetic and real-world datasets. For synthetic datasets, ParaACE improves normalized RMSE by 26.0% on average compared to the over-parameterized baseline. For real-world datasets such as Elevators, Parkinsons, Skillcraft, Bike sharing, and Cal housing, the detected interactions used by ParaACE help improve predictive accuracy and achieve substantial model compression. Additionally, ParaACE is sample efficient, performing well even with smaller training sample sizes, which suggests its effectiveness generalizes beyond the tested datasets."}
{"question_id": "1b3c40fd196db55e9ffea18c2b7d9ffe988c5ad2", "paper_id": "openreview/ICLR-2022-conf/IfNu7Dr-3fQ", "generation": "The kernel \\( k_{\\text{split}} \\) in the generalized kernel thinning algorithm serves as an auxiliary kernel used in the KT-SPLIT step to divide the input point sequence into \\( 2^m \\) candidate coresets of size approximately \\( \\frac{n}{2^m} \\). Specifically, KT-SPLIT uses \\( k_{\\text{split}} \\) to guide the non-uniform random splitting of the input points into these candidate coresets, ensuring that each coreset approximates the original distribution with controlled integration error.\n\nThe choice of \\( k_{\\text{split}} \\) affects the theoretical guarantees of the algorithm. When \\( k_{\\text{split}} \\) is a square-root kernel of the target kernel \\( k \\), the generalized kernel thinning recovers the original ROOT KT algorithm. More generally, \\( k_{\\text{split}} \\) defines the reproducing kernel Hilbert space (RKHS) \\( \\mathcal{H}_{\\text{split}} \\) in which the integration error bounds for the KT-SPLIT coresets are established. The algorithm guarantees near-quadratic integration error rates for functions in \\( \\mathcal{H}_{\\text{split}} \\), and by using a perturbed kernel \\( k'_{\\text{split}} \\), it can extend these guarantees to functions outside \\( \\mathcal{H}_{\\text{split}} \\).\n\nComputationally, \\( k_{\\text{split}} \\) is used to compute swapping thresholds and parameters during the splitting process, influencing the probabilistic swapping of points between candidate coresets to reduce the maximum mean discrepancy (MMD) with respect to the input distribution.\n\nIn summary, \\( k_{\\text{split}} \\) plays a central role in the initial division of the input data into candidate coresets, controls the theoretical error bounds of the thinning procedure, and influences the probabilistic mechanisms that refine these coresets before the final selection and improvement step using the target kernel \\( k \\)."}
{"question_id": "2e474adb8f0c5bd3285e43db4bfb774e7cd5b7a5", "paper_id": "nlpeer/ARR-22/78d7b31f3d19cf16bcc71aa109ca56b923d9082a2919fd8008060641daf4d5ae37d70daea5bb36f4aa83c9c49ad8b44e597aa41960348f3a0abe0abb529f9aea", "generation": "The proposed generated knowledge prompting method requires the knowledge generation model to be relatively large in order to generate useful and reliable knowledge. Experiments show that smaller knowledge generation models, such as those with 1.3 billion or 0.4 billion parameters, do not provide significant improvement. In contrast, larger models like the 6.7 billion parameter GPT-3 yield moderate improvement (5.0%), while the largest 175 billion parameter GPT-3 model produces the greatest improvement (10.5%). Therefore, the method is less effective when using smaller language models as the knowledge source and benefits from larger-scale models to generate high-quality knowledge."}
{"question_id": "720c06898bbd974bce657e8eefac71ea8641b762", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "Position embeddings are generated by table lookup from a trainable parameter matrix \\( W_p \\in \\mathbb{R}^{d_p \\times (s_{\\max} \\times 2 - 1)} \\), where \\( d_p \\) is the embedding size and \\( s_{\\max} \\) is the maximum sentence length in the dataset. Each relative position of a word with respect to the event mention is assigned an embedding vector \\( v_i^p \\) by indexing into this matrix, analogous to how word embeddings are obtained from the word embedding matrix \\( W_w \\). This means that for each distinct relative distance to the event trigger, a unique position embedding vector is learned and stored."}
{"question_id": "3f32444ad6371e1401f9047615faeed1a6572e73", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "Table 1 is not explicitly provided in the extracted chunks, but based on the context and typical presentation in the paper, Table 1 likely presents baseline or initial results of the DeiT-S model without token reorganization or with a different experimental setup.\n\nTable 2(a), explicitly shown in the chunks, presents the results of EViT on DeiT-S with different keep rates and image sizes. It compares the Top-1 and Top-5 accuracy, throughput (images per second), and MACs (Giga Multiply-Accumulate operations) for EViT variants relative to the baseline DeiT-S. The results demonstrate that EViT achieves significant speedup (increased throughput and reduced MACs) while maintaining comparable or slightly reduced accuracy depending on the keep rate and image size. For example, at a keep rate of 0.5 and image size 304, EViT-DeiT-S achieves a Top-1 accuracy of 81.0% (higher than the baseline 79.8%), Top-5 accuracy of 95.6%, throughput of 2905 images/s (vs. 2923 for baseline), and reduced MACs of 4.4G (vs. 4.6G baseline).\n\nIn summary, the difference is that Table 2(a) reports the performance of the EViT method applied to DeiT-S under various keep rates and image sizes, showing improved or comparable accuracy with increased efficiency, whereas Table 1 (implied) likely reports baseline or initial results without such token reorganization techniques. Table 2(a) thus provides detailed evaluation of the EViT approach on DeiT-S, highlighting the trade-offs between accuracy and computational efficiency."}
{"question_id": "888ba5daeae0d5b3d5120c824c8f61abd5b77ee3", "paper_id": "openreview/ICLR-2022-conf/IfNu7Dr-3fQ", "generation": "The difference in error between the KT+ and standard thinning (ST) methods for the Hinch posterior using the IMQ kernel arises because KT+ uniformly improves upon the MMD error of ST, even when ST exhibits better-than-i.i.d. accuracy. KT+ achieves significantly smaller integration error for functions both inside the reproducing kernel Hilbert space (RKHS), such as k(X\u2032, \u00b7), and outside the RKHS, including the first and second moments and the benchmark continuous integrand family (CIF) function, across nearly every coreset size setting.\n\nThis improvement occurs despite the IMQ kernel not admitting a square-root kernel with fast-decaying tails, which traditionally complicates error reduction. KT+ leverages a generalized kernel thinning approach that provides better-than-i.i.d. maximum mean discrepancy (MMD) guarantees and improved integration error decay rates compared to ST. Empirically, KT+ shows higher MMD decay rates and lower integration errors than ST for the same coreset sizes, as demonstrated in the experiments with the Hinch posterior.\n\nThus, the explanation for the error difference is that KT+ employs a more effective thinning strategy that reduces MMD and integration errors more rapidly with increasing coreset size than ST, even under challenging kernel conditions like the IMQ kernel used in the Hinch experiments."}
{"question_id": "87b3ef59cb4832acb7306d8368503784307b1adc", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The provided document does not contain explicit information comparing proposal-based object detectors to DETR or other similar object detectors. It discusses the use of proposal-based detectors (such as OLN built on Faster R-CNN) and proposal-free detectors (such as FCOS) within the GOOD framework for open-world object detection, highlighting that GOOD is model-agnostic and compatible with both types. However, it does not provide a direct comparison of advantages of proposal-based detectors over DETR or similar architectures. Therefore, no specific advantages of proposal-based object detectors compared to DETR and similar detectors can be extracted from the given text."}
{"question_id": "ecbcef296be692e081bf8e60e3d63dcbf2090dbf", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "There is no indication that a language expert was consulted to verify the accuracy of the translations of English time expressions into other languages. The discussion notes that the methods are language-agnostic but designed based on English, and that automatic translation of time expressions and templates from English to other languages may introduce errors. Additionally, some language-specific issues are mentioned, such as the gender variation in Italian time expressions and semantic differences in Hebrew and Korean, but no mention is made of expert verification of translations."}
{"question_id": "f21290e4ef8f0ead710c484502ad8e103217da11", "paper_id": "openreview/NeurIPS-2022-dabt/76w7bsdViZf", "generation": "Yes, the authors have evaluated additional pretrained models on ImageNet, including DenseNet161 and VGG16, alongside other architectures such as Swin Transformer and ConViT. These models were assessed without finetuning on the Hard ImageNet and RIVAL20 datasets. The evaluation showed that all models, including DenseNet161 and VGG16, exhibited higher accuracies under ablation, lower Relative Foreground Sensitivity (RFS) scores, and lower saliency alignment on Hard ImageNet compared to RIVAL20. This indicates that these models were indeed trained on ImageNet and subsequently evaluated on these datasets to study their reliance on spurious features."}
{"question_id": "4191cd3e5bd482f3d097b689c9857bf3d727f98b", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Depth and normal maps are not combined by stacking as input for training a single proposal network in Phase-I. Instead, two separate object proposal networks are trained independently on depth and normal maps. The pseudo boxes generated by these separately trained networks are then merged into a single pseudo box pool for Phase-II training. During merging, if the Intersection over Union (IoU) of two pseudo boxes from the different networks is larger than 0.5, they are considered overlapping, and the box with the lower objectness score is filtered out. This pseudo label ensembling approach is empirically shown to be slightly better than stacking the geometric cues together for Phase-I training."}
{"question_id": "68cdad6fbee4b667c3a6b10e5761a4ccf660e32b", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "The study acknowledges that the grounding of time expressions to times may vary within a language due to differences in time zones across countries where the language is spoken. For example, Portuguese speakers in Brazil and Portugal may perceive time expressions differently because of their different time zones. However, the research treats language as a proxy for culture and assumes similarity in grounding across speakers of the same language, which is challenged by such intra-language geographic variation.\n\nThe paper also notes that using country as a proxy for culture is not applicable because corpora and language models are available for languages rather than countries. Consequently, model predictions for each language are likely dominated by the country with the larger number of speakers or contributors, which may not faithfully represent speakers in other countries sharing the same language.\n\nRegarding temporal and seasonal factors, some workers mentioned that interpretations of time expressions depend on the season and local sunrise and sunset times. The data was collected in October, and the study compared night start and end times against average October sunrise and sunset times in the capitals of each country. There was some correspondence between average sunset time and night start time in the US, India, and Italy, but no such match was found between sunrise time and the end of night or beginning of morning.\n\nThe study suggests that finer-grained analysis correlating sunrise and sunset times in specific locations within each country with workers' indicated times would be interesting but was not performed due to limited data.\n\nIn summary, while the study recognizes that location within a time zone and seasonal factors can affect the interpretation of time expressions, it does not provide detailed data or analysis explicitly showing how the location within a country's time zone affects the results. The assumption of language-based grounding and the dominance of data from countries with more speakers limit the granularity of such geographic effects in the current results."}
{"question_id": "c12d6fb12967631fd985c4b437ee77cd1d2a8e2e", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Yes, when combining multiple modalities for the model, filtering is applied to merge the pseudo boxes generated by object proposal networks trained on different modalities. Specifically, if the Intersection over Union (IoU) of two pseudo boxes is larger than 0.5, they are considered overlapping, and the pseudo box with the lower objectness score is filtered out. This filtering process is used to merge pseudo boxes from depth and normal modalities in the GOOD-Both model, as well as in other ensembling experiments unless otherwise specified."}
{"question_id": "b248a530072224a71459f4ce7aa708f9990067c2", "paper_id": "openreview/ICLR-2023-conf/KDhFkA6MQsW", "generation": "The parameter \\(\\mu\\) used in the experiments is set to either \\(0.001\\) or \\(0.01\\), depending on the specific algorithm and experiment. Specifically:\n\n- For the cubic regularization problem experiment, \\(\\mu = 0.001\\) for ZO-Perturbed-AGD and ZO-Perturbed-AGD-ANCF (with \\(\\mu = 0.001\\) or \\(\\mu = 0.01\\) in some cases for ANCF).\n- For the quartic function experiment, \\(\\mu\\) is set to \\(0.001\\) for ZO-Perturbed-AGD and \\(0.01\\) for ZO-Perturbed-AGD-ANCF.\n\nThus, the values of \\(\\mu\\) used in the numerical experiments are \\(0.001\\) and \\(0.01\\)."}
{"question_id": "496d254bdc722d815fb98ad3903cdc34df700fce", "paper_id": "openreview/ICLR-2023-conf/KDhFkA6MQsW", "generation": "The complexity stated as \\( t = \\frac{\\delta_f}{\\epsilon^{1.75}} \\log d \\) does not match the complexity in Theorem 2 because Theorem 2 involves a more detailed parameter setting and complexity dependence that includes additional factors and exponents on \\(\\epsilon\\), as well as dependencies on other problem parameters such as \\(\\rho\\), \\(\\kappa\\), and constants related to the algorithm.\n\nSpecifically, Theorem 2 sets parameters as follows:\n\n- The step size \\(\\eta = \\frac{1}{4\\ell}\\),\n- The momentum parameter \\(\\theta = \\frac{1}{4\\sqrt{\\kappa}}\\),\n- The curvature parameter \\(\\gamma = \\frac{\\theta^2}{\\eta}\\),\n- The perturbation radius \\(s = \\frac{\\gamma}{4\\rho}\\),\n- The iteration count for perturbation steps \\(T' = 32 \\sqrt{\\kappa} \\log\\left(\\frac{\\ell \\sqrt{d}}{\\delta_0 \\sqrt{\\rho \\epsilon}}\\right)\\),\n- The energy decrease \\(E = \\frac{s \\epsilon^3}{\\rho} c_A^{-7}\\),\n- The perturbation radius \\(r' = \\frac{\\delta_0 \\epsilon}{32} \\sqrt{\\frac{\\pi}{\\rho d}}\\),\n\nwhere \\(c_A\\) is a large enough constant.\n\nThe total number of random perturbations required to escape saddle points is bounded by\n\n\\[\n384 \\frac{(f(x_0) - f^*) \\sqrt{\\rho}}{\\epsilon^3},\n\\]\n\nand the probability of failure is controlled by \\(\\delta\\).\n\nThis complexity reflects a dependence on \\(\\epsilon^{-3}\\) (or powers close to 3) rather than \\(\\epsilon^{-1.75}\\), and also includes logarithmic factors in \\(d\\) and other parameters such as \\(\\rho\\) and \\(\\kappa\\). The presence of these additional terms and the higher exponent on \\(\\epsilon\\) means that the simpler form \\( t = \\frac{\\delta_f}{\\epsilon^{1.75}} \\log d \\) underestimates the true complexity characterized in Theorem 2.\n\nTherefore, the mismatch arises because Theorem 2's complexity accounts for more nuanced dependencies on problem parameters and algorithmic constants, resulting in a higher-order polynomial dependence on \\(\\epsilon\\) and more intricate logarithmic factors in \\(d\\), which are not captured by the simpler expression \\( t = \\frac{\\delta_f}{\\epsilon^{1.75}} \\log d \\)."}
{"question_id": "5af1f02badf7c044e04f7544f4881486216b9f42", "paper_id": "openreview/ICLR-2023-conf/OTbRTIY4YS", "generation": "The new metric, Concept Purity, is introduced because the existing metrics used in Yuan et al. (2020) are not sufficient to evaluate the quality of concept representations in the context of global explanations for graph neural networks (GNNs). Concept Purity specifically measures how well the embedding clusters local explanations according to their annotated typology (e.g., motifs), which is essential for assessing the interpretability and meaningfulness of the learned concepts. While Yuan et al. (2020) focus on global explanations, they do not provide metrics that evaluate the clustering quality of concept embeddings. Concept Purity addresses this gap by quantifying the homogeneity of clusters with respect to ground-truth or user-defined labels, thus providing a more fine-grained and interpretable assessment of the concept representations extracted by the explainer. Additionally, the original metric based on graph edit distance proposed in Magister et al. (2021) was computationally expensive, so Concept Purity was adapted to use annotation-based evaluation, making it more practical for the datasets considered."}
{"question_id": "888f26be8b81ccb011c79b043940eec47d9b414e", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The same network architecture, OLN (Kim et al., 2021), is used for both depth and normal by training separate object proposal networks on the predicted depth maps and normal maps extracted from RGB images using pretrained Omnidata models. These networks are trained independently on each geometric cue modality (depth or normal) during Phase-I to generate pseudo boxes for novel object detection. For the combined approach (GOOD-Both), the pseudo boxes generated by the separately trained proposal networks on depth and normal maps are merged by filtering out overlapping boxes based on an Intersection over Union (IoU) threshold of 0.5, retaining the box with the higher objectness score. Thus, the same OLN architecture is applied separately to each geometric cue input, enabling the network to exploit both depth and normal information for open-world object detection."}
{"question_id": "5ab449ca87a0bb86111a5e4e594ac959ec7d0e86", "paper_id": "nlpeer/PeerRead-CONLL2016/142", "generation": "Post-editing rates were chosen over prediction HTER for intrinsic uncertainty evaluation because post-editing time is a more complete measure of post-editing effort. It captures not only technical effort, as HTER does, but also cognitive effort. Additionally, post-editing time is more directly applicable in real translation environments where uncertainty estimates could be useful, as it relates directly to productivity measures."}
{"question_id": "e7ac4283196f09643f681e08d6093e9313983cf1", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "Yes, the translation of the template has been adapted to account for languages with grammatical gender. For example, in Italian, the time expressions \"morning\" (mattina) is feminine, whereas \"afternoon\" (pomeriggio) is masculine, which results in variation in the determiner used\u2014\u201cla <time_exp>\u201d for feminine and \u201cil <time_exp>\u201d for masculine. This adaptation reflects the grammatical gender differences in the language."}
{"question_id": "cf66689ffef1b230b7bab23901a8bf528a8e97f8", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "The paper incorporates section titles into the BOS token representation by generating section title embeddings (STE) or classified section title embeddings (classified STE) and adding them to the initial sentence representations. Specifically, the section title embedding is produced by feeding the tokenized section title into the pre-trained Transformer language model (TLM) and summing the last hidden states of all tokens in the title to form a single embedding. For classified STE, typical section title classes are manually predefined, and all intra-class section titles are replaced with the embedding of their corresponding class. This embedding (STE or classified STE) is then added to the BOS token embedding of the sentence, enriching the sentence representation with section title information before it is fed into the hierarchical contextual learning layers."}
{"question_id": "a74c71ff53a5ff84cacb938350996a66ceb0ae12", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "In Phase-I training, the proposal network is trained on geometric cues such as depth or normal maps using the base class bounding box annotations. The training loss used is as given in Equation 2 of the paper. The architecture employed for the proposal network is OLN, which is built on top of Faster RCNN but replaces the classification heads with objectness score prediction heads that predict centerness and IoU of each bounding box proposal at two stages. The optimizer used is SGD with an initial learning rate of 0.01 and a batch size of 16. The training is conducted for 8 epochs. The proposal network generates pseudo boxes by detecting bounding boxes on the geometric cue inputs, which are then filtered to exclude those overlapping with base class annotations before being used in Phase-II training."}
{"question_id": "fced44b64830046f3b8766306510296277091b7a", "paper_id": "nlpeer/ARR-22/3282bdf968d7f87ae69c4ae7fdd35d4e9a46fa83f56d8bee0296c16dcf23b23cb1e4f20f510c7d01fb7be2309c08dd29cc240b54bdcbc3d66969041b5c84077a", "generation": "The provided information specifically details the degree of formality and the use of Persian and Sanskrit forms only for Hindi. In Hindi, as sentences become more formal, English loanwords such as \"job\" (\u091c\u0949\u092c) are replaced by Persian-derived terms (\u0928\u094c\u0915\u0930\u0940) or high Sanskrit-derived terms (\u01d3\u0928\u092f\u0941\u093f\u00c8\u0924), and honorifics are introduced (e.g., \u0906\u092a\u0915\u0940, \u092c\u0924\u093e\u090f\u0902). However, there is no explicit description or analysis of the degree of formality or the use of Persian/Sanskrit forms for the other Indic languages studied (Bengali, Kannada, Telugu, Gujarati) or for Swahili and Spanish in the provided content. The evaluation and results for these languages focus on automatic and human evaluation metrics of formality transfer performance but do not provide linguistic details on formality markers or lexical substitutions analogous to the Hindi example."}
{"question_id": "5739894b5714e42337f53319a265bb28e2f6e18d", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Yes, using RGB images in addition to depth and normal images in the first step (Phase-I) leads to worse performance. When RGB is stacked with geometric cues to train the proposal network, the model tends to rely more heavily on RGB inputs because RGB provides a stronger signal in closed-world setups. This reliance causes the model to underutilize geometric cues, which are crucial for discovering novel objects in open-world detection. Empirical results show that stacking RGB with geometric cues results in inferior performance across multiple benchmarks.\n\nAlternatively, training a separate object proposal network on RGB inputs and merging its pseudo boxes with those from geometric cues (referred to as \"GOOD-All\") does not improve performance and can even degrade it compared to using geometric cues alone (\"GOOD-Both\"). This degradation is attributed to RGB-based proposal networks favoring smaller detection boxes, which often correspond to small objects, textures, or parts of larger objects, potentially harming the detection of larger objects and overall performance.\n\nQuantitative comparisons demonstrate that the gains in detecting small objects from RGB pseudo boxes are insufficient to compensate for losses in detecting medium and large objects, leading to an overall inferior performance of methods incorporating RGB pseudo boxes in Phase-I."}
{"question_id": "691280cb66aae7b9fee2d8ac0937e5f970437b43", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "Yes, human evaluation results for the extractive summarization task are presented through case analyses of sample summaries from the arXiv and PubMed datasets. In these evaluations, the HiStruct+ model's extractive summaries are compared with those generated by a baseline model and the gold standard abstracts. The evaluation involves extracting the top-7 sentences with the highest predicted scores and combining them in their original order to form the final summary.\n\nFor the arXiv sample, the baseline model tends to select the first sentences, focusing on detailed background knowledge but lacking an overall overview of the proposed work. In contrast, the HiStruct+ model selects sentences from later positions that introduce the main content and the overall progression of the work, concluding with sentences that summarize and provide future work outlooks.\n\nFor the PubMed sample, the baseline also favors the first sentences, including excessive background information and less focus on key findings. The HiStruct+ model selects more informative sentences from later in the document, covering key aspects such as reported statistics, mention of a novel strain, and highlighting the conclusion. This selection better aligns with the content of the gold summary, emphasizing the overall topic and conclusions of the work.\n\nThese human evaluations demonstrate the superiority of the HiStruct+ model in producing more informative and representative extractive summaries compared to the baseline."}
{"question_id": "dc3fd256c5702edb18e7a21a01836945f7bc0b17", "paper_id": "openreview/ICLR-2023-conf/WlbG820mRH-", "generation": "The paper restricts its analysis to Message Passing Neural Networks (MPNN) where the aggregation functions compute a simple sum of their inputs, and the combination and readout functions are represented by neural networks with ReLU activation only. These specific architectural choices define the expressiveness of the considered GNN models.\n\nThe applicability of the results to other GNN models depends critically on their expressiveness relative to the MPNN model studied. Theorem 1 and Corollary 1, which establish the impossibility of formal verification for output reachability and adversarial robustness properties of graph-classifier MPNN on inputs with unbounded size and degree, extend only to GNN models that are at least as expressive as the considered MPNN. Minor modifications, such as using other piecewise-linear activation functions, do not invalidate the results.\n\nHowever, for GNN models that differ substantially from the MPNN or spatial-based framework\u2014such as spectral-based GNNs or more expressive variants like DropGNN\u2014the results do not directly apply. The formal verifiability question must be re-examined for these models, as their differing aggregation, combination, or readout mechanisms may alter expressiveness and thus the fundamental limits of formal verification.\n\nIn summary, the limitations on aggregation, combination, and readout components restrict the results to a class of GNNs with expressiveness comparable to the studied MPNN. Extending these results to other GNN architectures requires further investigation, particularly for models with fundamentally different or more expressive components."}
{"question_id": "a4439f559ec40c32bb7edf1ee7fa3a854ed2b883", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Combining RGB with depth and normals does not improve the overall performance of the model for smaller classes. When RGB is stacked with geometric cues (depth and normals) to train the proposal network, the model tends to rely more on RGB, which is a stronger signal in closed-world setups, leading to inferior performance in open-world detection. Specifically, adding RGB pseudo boxes to those from geometric cues (method named \"GOOD-All\") results in no performance gains or even worsens performance on benchmarks such as VOC to ADE20K. The gains in detecting small objects (AR_s) from RGB are too small to compensate for losses in detecting medium and large objects, leading to overall inferior performance compared to using geometric cues alone (method \"GOOD-Both\"). \n\nFurthermore, RGB-based object proposal networks favor smaller detection boxes, which may correspond to small objects or just textures and parts of larger objects, potentially hurting detection quality. Geometric cues (depth and normals) are stronger in discovering novel objects and generalize better across supercategories, while RGB is relatively stronger only in detecting smaller objects but does not complement geometric cues effectively when combined.\n\nTherefore, combining RGB with depth and normals does not improve and may even degrade performance for smaller classes in open-world object detection."}
{"question_id": "8e2cb1c95dffd133cc91ab3123074a0853c829fb", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "The authors chose to experiment with their model on the CNN/DailyMail dataset because it represents a dataset with less conspicuous hierarchical structure compared to PubMed and arXiv. Specifically, CNN/DailyMail has a smaller average hierarchical width (hi-width = 1.33), indicating fewer sentences per paragraph or section, and a shallower hierarchical depth (hi-depth = 3) since paragraphs are not further grouped into sections. This characteristic makes CNN/DailyMail an exemplary dataset to evaluate the effectiveness of their method on documents with less pronounced hierarchical structure."}
{"question_id": "28241e220ebb3458ef6d5eaf6829f8563161a7c1", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Mild adaptation, characterized by a reasonably large \u03c4, results in the feature representation \\( z_T \\) being stretched (i.e., an increase in \\(\\| z_T \\|_2\\)) in a direction similar to the original features \\( z_0 \\) (with little change in cosine similarity). This stretching makes originally overlapped features more separable without significantly altering the manifold of the features. Such behavior is desirable in most transfer learning scenarios because it enhances feature separability while preserving the overall structure of the feature space."}
{"question_id": "726e56359927a7fc14be9019ea69e50929fa9e67", "paper_id": "nlpeer/ARR-22/dd0a83852cb35c8d0ac0c40da6b7ebeb385f9c7e4816ef51f43abd6910a9adacc0daf460dfda4e56f37fa542a749961a78ddbbe74af0326f41e5cd38e867b121", "generation": "The provided evidence does not explicitly discuss or report the effects of using predicted negation cues instead of gold cue information for scope resolution. The experiments and results described focus on improvements in cue detection and scope resolution performance using different models and pre-training strategies, but scope resolution is consistently performed using gold cue information as stated in the experimental settings. Therefore, no direct information is available regarding the impact of using predicted cues rather than gold cues for scope resolution."}
{"question_id": "789b3799040a63d59e93a029bab4459c7ff3aa2c", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The phrase \"information exchanges between the two parts of the network\" in Fig. 1 refers to the interaction between the backbone feature extractor \\( f(x; B) \\) and the task head \\( g(z; v) \\) during finetuning. Specifically, it describes how the feature representations \\( z \\) produced by the backbone change in response to updates in the task head parameters \\( v \\), and conversely, how the task head adapts based on the features extracted by the backbone. This interaction is characterized by the empirical neural tangent kernel (NTK) of the backbone and the gradients of the task head predictions with respect to the features, as formalized in Equation (2). The equation decomposes the update of the feature representation \\( z \\) into three components: a slow-changing term related to the backbone NTK, a direction term determined by the gradient of the task head, and an energy term related to the difference between the predicted probabilities and the true labels. Thus, the \"information exchanges\" capture the dynamic feedback loop where the backbone features and the task head parameters influence each other\u2019s updates during finetuning."}
{"question_id": "e0bf6addede2ca2d1e76eac67027e72e3ff385f5", "paper_id": "nlpeer/ARR-22/3cfcbbad78a71ca6cf4a5b2bbaee2a7ad8a1de295cf9f4103408dfeaf38a0de01b2b8d23cb94e0ef0b5f76a1947815f0335a7077bdea03ea4a29f6a2bff432cc", "generation": "Hanja is considered an extinct language rather than merely a script because it represents a distinct linguistic system used in ancient Korea, characterized by its unique lexical, semantic, and syntactic features that differ significantly from modern Korean and modern Chinese. Although Hanja is based on Chinese characters, it evolved to incorporate the basic syntax of classical Chinese mixed with colloquial Korean elements, resulting in a language that is no longer understood or used in contemporary communication. This linguistic distinctiveness and the cessation of its use as a living language classify Hanja as an extinct language rather than just a writing system or script."}
{"question_id": "9246fb2439ec9512f9298b927660f030736765c0", "paper_id": "openreview/ICLR-2023-conf/T2Ncx_PN2K", "generation": "The training process for the proposed model TOLSTOI is not truly end-to-end. Although the RNN-Transducer (RNN-T) architecture itself is an end-to-end ASR model, the adaptation approach involves a separate imputation model that generates proxy speech representations (the last layer vectors h from the speech module) from text sequences. This imputation model is trained independently to model the distribution P(H|Y), where H denotes the speech module output vectors and Y the text tokens. After training this lightweight imputation model, it is used to create a proxy parallel dataset by imputing speech representations for the target text data. The RNN-T model is then fine-tuned on this augmented dataset by updating only the language module and joint network parameters, while keeping the speech module parameters fixed. Thus, the overall adaptation process involves a modular two-step procedure\u2014first training the imputation model separately, then fine-tuning the RNN-T model\u2014rather than a single, unified end-to-end training."}
{"question_id": "ca87a914265cffe46bfb63e2e24a3568efbc7888", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Yes, there are cases where the 'end' histogram does not match the label vector \\( e_y \\) when using different head types for hyperparameter tuning. Specifically, when the head is only probed for a few updates or initialized randomly (referred to as the \"strong\" case), the resulting feature representation \\( z_T \\) can change in an unpredictable way and differ significantly from the initial representation \\( z_0 \\). In this scenario, the features' manifold is substantially altered, and the head's output after hyperparameter tuning does not align well with \\( e_y \\). This contrasts with the \"tiny\" and \"mild\" cases, where the head is probed longer, leading to feature representations and manifolds more compatible with \\( e_y \\). Thus, the mismatch between the 'end' histogram and \\( e_y \\) occurs particularly in the strong adaptation regime with limited or random head initialization."}
{"question_id": "6ed4842f06973b3b3b83a068d590e3a5421678f8", "paper_id": "openreview/ICLR-2023-conf/T2Ncx_PN2K", "generation": "Using a transformer-based acoustic model, specifically a Conformer encoder, in the RNN-Transducer (RNN-T) architecture has been empirically evaluated and shown to be effective for the TOLSTOI adaptation method. The Conformer-Transducer model, which uses 10 Conformer blocks with attention and convolutional modules, demonstrated significant reductions in word error rates (WER) on target domains with minimal catastrophic forgetting compared to shallow fusion. This indicates that the transformer-based Conformer encoder can improve the model architecture by better distributing acoustic features in the neighborhood and enhancing adaptation performance relative to the bidirectional LSTM encoder."}
{"question_id": "99d5ca18b41cac7092cd7ca7cf0888b8a29a3018", "paper_id": "openreview/ICLR-2023-conf/OM7doLjQbOQ", "generation": "The new automated image transformation strategy introduced in ILA-DA is specifically designed to enhance the Intermediate Level Attack (ILA) framework by promoting reference diversity through learnable augmentations. It applies three novel augmentation techniques, including automated data augmentation, reverse adversarial update, and attack interpolation, to create more powerful adversarial examples and improve transferability.\n\nWhile the paper demonstrates that ILA-DA can fine-tune simple baseline attacks such as the Iterative Fast Gradient Sign Method (I-FGSM) to outperform state-of-the-art transfer-based attacks, there is no explicit mention or evidence that this automated image transformation strategy is directly applied or tested on other transfer-based methods such as MI-FGSM or DIM. The related works section describes MI-FGSM and DIM as existing methods that incorporate fixed data augmentation techniques (momentum, random resizing, zero-padding), but does not indicate that the learnable augmentation approach of ILA-DA has been integrated with or evaluated on these methods.\n\nTherefore, the automated image transformation strategy of ILA-DA is currently applied within the ILA framework and its variants, and there is no documented application or validation of this strategy on other transfer-based attacks like MI-FGSM and DIM."}
{"question_id": "9ff146fb1145a7e6cd038252a41b96f5c6ac0494", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "Yes, there are examples of synthetic and ground-truth data used to compare model performance. Specifically, in the training-from-scratch setting on the CIFAR-100 dataset, a synthetic dataset of 50,000 images (500 images per class) was generated to train a ResNet-50 model from random initialization, achieving a top-1 accuracy of 28.74% on the CIFAR-100 test set. This performance was compared to that of a pre-trained CLIP model, which performed significantly better. Additionally, it was found that training with only 9,500 real images (95 images per category) achieved comparable performance to the 50,000 synthetic images, indicating that synthetic data are less data-efficient than real data. This comparison illustrates the difference in effectiveness between synthetic and ground-truth (real) data for image classification tasks."}
{"question_id": "08c2ff08d58f88bfead47fc3783d34333d02f023", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The rationale behind choosing a label-smoothing coefficient of 0.9 (\u03b7_HP = 0.9) during the head probing (HP) stage is to reserve a certain amount of \"energy\" for subsequent feature adaptation in finetuning. Specifically, by setting the labels during HP as a mixture \u03b7_HP e_y + (1 \u2212 \u03b7_HP) * u, where u is a uniform K-class categorical distribution, the HP stage retains at least (1 \u2212 \u03b7_HP) * \u2225e_y \u2212 u\u2225\u00b2 energy for feature adaptation even as the number of HP epochs \u03c4 approaches infinity. This reserved energy prevents the model's prediction p_0 from fully converging to the hard labels e_y, allowing for a milder and more effective adaptation of features during finetuning.\n\nChoosing \u03b7_HP = 0.9 means that 10% of the label distribution is smoothed uniformly, which is particularly helpful when the HP training accuracy converges very quickly to over 90%, indicating that the features are already well aligned with the task. The smoothing thus ensures that the features continue to adapt rather than becoming fixed too early. However, if label smoothing is applied equally in both HP and finetuning (\u03b7_HP = \u03b7_FT = 0.9), the reserved energy disappears because the labels in both phases are the same, resulting in similar performance to the baseline without smoothing.\n\nTherefore, the choice of 0.9 balances maintaining high label confidence while reserving sufficient energy for feature adaptation, improving downstream performance when pretrained features are already strong and HP converges rapidly."}
{"question_id": "f8b91940d2ce9e4e0df966f18d724e12b5aac0e5", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "In a fully-supervised setting where models are trained from scratch using synthetic data, synthetic data deliver inferior performance compared to real data and are much less data-efficient. For example, training a ResNet-50 model from random initialization on a synthetic CIFAR-100 dataset of 50,000 images (500 images per class) achieves only 28.74% top-1 accuracy on the CIFAR-100 test set, which is significantly lower than the performance of a pre-trained CLIP model. To match this performance using real data, approximately five times fewer real images (around 9,500 images, or 95 images per category) are required, indicating that synthetic data are less effective and require substantially larger amounts to achieve comparable results. Furthermore, increasing the amount of synthetic data beyond this scale does not yield further performance gains. This performance gap is attributed to the lower quality and diversity of synthetic data relative to real-world data."}
{"question_id": "36fdc759d8b028d2f3c0c5cb9e8c26b5744962d0", "paper_id": "openreview/ICLR-2023-conf/OM7doLjQbOQ", "generation": "ILA-DA consistently outperforms other transferable attack methods, including LinBP and the CTM family, across different choices of intermediate layers. Specifically, experiments on ResNet50, VGG19, and Inception V3 show that ILA-DA achieves higher attack success rates than ILA without augmentation and other state-of-the-art attacks such as MI-CT-FGSM and NI-CT-FGSM. The performance of ILA-DA is robust to the selection of intermediate layers near the default layer proposed by Huang et al. (2019), exhibiting an inverted U-curve pattern that indicates low sensitivity to layer choice. Furthermore, ILA-DA does not require stacking multiple attack methods to achieve high transferability, unlike the CTM family, and it improves the attack success rate significantly when combined with existing methods. Overall, ILA-DA demonstrates superior and more stable transferability performance compared to LinBP and CTM family methods across various intermediate layers."}
{"question_id": "f29ff7d6be64035f374fe6b3fc470453591154e9", "paper_id": "nlpeer/COLING2020/1570", "generation": "Yes, the annotated mistakes will be released upon the release of ManyNames v2. The dataset includes the raw verification annotations in addition to the consistent response sets, allowing users to access information about different inadequacy types and errors identified during the verification process."}
{"question_id": "9b256b585691520864c3cf7d1b8cfb8f863d6663", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Yes, the results of the study suggest several implications beyond the general empirical impression. The study provides a detailed theoretical and empirical analysis of feature adaptation during finetuning, revealing a non-trivial trend in how the \"energy\" and \"direction\" of learning influence the change in pretrained features. Specifically, it shows that mild adaptation of features is often beneficial when pretrained features are not perfectly aligned with downstream tasks, and that controlling the degree of adaptation\u2014such as by early stopping during head probing, using label smoothing, or manipulating the task head capacity\u2014can improve downstream performance. The study also demonstrates that the relationship between the number of head probing epochs and feature adaptation is more complex than simple Euclidean or cosine distance metrics suggest, involving quadratic trends in feature norms and inner products. Furthermore, it highlights that different downstream datasets and pretraining conditions require different adaptation strategies, such as preserving earlier backbone layers when pretrained features are less suitable. These findings provide a new perspective on understanding and controlling feature adaptation, offering practical guidelines and opening avenues for future research on quantitatively analyzing the discrepancy between pretraining and downstream tasks."}
{"question_id": "6f797e6284c2b0ebd83dc98348c33626ac517dbb", "paper_id": "openreview/NeurIPS-2022-dabt/Zx5qJzNesn0", "generation": "Yes, there are differences between the locations in the benchmark besides the weather. Specifically, the crop varieties used differ between locations to better match local growing conditions. For fertilization experiments, an RM 90 maize variety is used, while for crop planning experiments, an RM 100 maize and MG 3 soybean are used to better suit the longer local growing conditions in Rock Springs (central Pennsylvania) and New Holland (southeast Pennsylvania). These maturities are widely used by producers in these areas and are recommended for the respective locations. Thus, the benchmark accounts for location-specific crop maturity groups in addition to weather differences."}
{"question_id": "c0701214e646badb3bbb44e4985982c8e9e65fc7", "paper_id": "nlpeer/ARR-22/83dc885bef279cbb7a3b5b173494428b6cd043d34527016519250477d274fd1793fa0bddd2bd4bbb3edfa9709ddc85e90825a0554bfa8eceb8cb34d813c06c53", "generation": "The accuracy of the confidence network is ensured by jointly learning the confidence estimate with the training of the neural machine translation (NMT) model in an unsupervised manner. The confidence is defined as the number of hints the NMT model needs to produce a correct translation, where more hints indicate lower confidence. During training, the model is allowed to ask for hints\u2014ground truth labels\u2014at a slight penalty cost, encouraging it to translate independently when confident and request hints only when uncertain. This setup approximates confidence by counting the hints used.\n\nA confidence estimation network (ConNet) is added in parallel with the original prediction branch, taking the decoder's multi-layer hidden states as input and outputting a scalar confidence value between 0 and 1 via a sigmoid function. The confidence value modulates the interpolation between the model's predicted probability distribution and the ground truth distribution, adjusting the softmax prediction probabilities accordingly. The translation loss is then computed using these modified probabilities, which guides the confidence network to produce well-calibrated confidence estimates that reflect the model's uncertainty accurately.\n\nThis approach enables the confidence network to learn a reliable measure of confidence without degrading translation performance, as demonstrated by its superior performance in quality estimation tasks and its ability to detect potential risks such as noisy samples and out-of-domain data."}
{"question_id": "14f24eacc79985de8d643389b87e35ceb5209775", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "The search cost of the proposed \u039b-DARTS method is approximately 0.8 GPU days on a GTX 1080 Ti GPU when performing the search on the DARTS search space and the CIFAR-10 dataset. This cost is about twice that of the first-order DARTS (0.4 GPU days) but 20% less than the second-order DARTS (1.0 GPU days) and about 40% less than SDARTS-ADV (approximately 1.3 GPU days). By using forward or backward finite difference approximations in an interleaved fashion, the cost can be further reduced by 25% to around 0.6 GPU days, making it comparable to DrNAS with progressive search."}
{"question_id": "0eb6095e3dbae2dd6e1abc90265e56378f49fa1a", "paper_id": "openreview/ICLR-2022-conf/ZTsoE8G3GG", "generation": "No, individual atoms are not added to the partial graph when using Algorithm 2 with larger vocabulary sizes. Instead, the model either adds an entire motif in one step or generates atoms and bonds one-by-one. This means that with larger vocabularies, the model can add whole motifs rather than individual atoms, allowing it to generate arbitrary structures including unusual rings. The motifs are atom-disjoint, and attaching a motif to a partial graph requires adding only a single bond, without the need for a motif-specific attachment point vocabulary."}
{"question_id": "ffe260fb92f4c53395118a567f59d32fd365c351", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The degree of separability when adapting a model to a task is influenced primarily by the interaction between the pretrained backbone features and the task head capacity, as well as the initial training accuracy and loss at the beginning of finetuning, which determine the \"energy\" available for feature adaptation.\n\nKey factors include:\n\n1. **Pretrained Feature Quality and Adaptation Extent**:  \n   - If pretrained features are near optimal for the downstream task, only tiny or mild adaptation is needed, preserving feature separability.  \n   - When pretrained features are far from optimal, strong adaptation is required, which can significantly alter feature representations and potentially reduce separability.\n\n2. **Task Head Capacity and Structure**:  \n   - A low-capacity task head (e.g., a linear layer) may struggle to achieve high training accuracy, limiting the reduction of adaptation energy and thus preserving pretrained features but possibly limiting separability improvement.  \n   - A high-capacity task head (e.g., multi-layer MLP or large layers appended to the backbone) can achieve near-perfect training accuracy quickly, reducing the energy for feature adaptation and potentially distorting pretrained features, which affects separability.  \n   - Increasing task head complexity (e.g., two-layer MLP) increases training accuracy after head probing and reduces feature adaptation, but this does not necessarily improve downstream performance or separability.\n\n3. **Backbone Depth and Layer Reinitialization**:  \n   - Treating later layers of the backbone as part of the task head (reinitializing them) increases task head capacity and changes the amount of inherited pretrained information.  \n   - Preserving earlier layers of the pretrained backbone tends to maintain beneficial features and better separability, especially when pretrained features are good.  \n   - When pretrained features are poor, discarding later layers and relying more on the task head can improve adaptation and separability.\n\n4. **Initial Training Accuracy and Energy**:  \n   - The initial training accuracy and loss at the start of finetuning determine the adaptation energy, which controls how much features change.  \n   - Higher initial energy leads to larger Euclidean and cosine distances between adapted and original features, affecting separability.  \n   - Mild adaptation (moderate energy) is usually beneficial for maintaining or improving separability.\n\n5. **Feature Adaptation Dynamics**:  \n   - The direction and magnitude of feature changes during finetuning, influenced by the gradient norms and task head updates, affect how features separate after adaptation.  \n   - Strong adaptation can cause large, inconsistent directional changes in features, potentially harming separability.\n\nIn summary, the degree of separability after adaptation depends on balancing the pretrained feature quality, the capacity and structure of the task head, the extent of backbone layer reuse or reinitialization, and the initial training conditions that govern the energy available for feature adaptation. Mild adaptation with appropriate task head capacity and preserving useful pretrained layers generally leads to better separability."}
{"question_id": "b9dc0dacfa9d5676e09c6d6308a65cb2885392cd", "paper_id": "nlpeer/ARR-22/24714d27941075cbad95c02db3ac730df71d355f85f5d247a62c9453ea29004b15cfb386663c82ce55ba17d652253064f4ff70f21dd5c08d8e39986ab22c45ce", "generation": "The results of the Montreal Forced Aligner (MFA) applied to singing recordings have not been explicitly stated as manually checked for accuracy. The MFA tool was used to obtain alignments to lyrics for all singing recordings, from which the ground-truth pitch alignment was derived based on shared lyrics in the paired data of PopBuTFy. However, there is no mention of manual verification or manual checking of the alignment accuracy after applying the Montreal Forced Aligner."}
{"question_id": "94d3d227741b9bf8258649cb3567dc790b9dca07", "paper_id": "nlpeer/ARR-22/91d9e763eb1470028002e5c97689ada4b74f30be17291a14458f7542c387a9f2a7516e60f4022d887bdbd2165aa8cce6d566db380e5a82af677917c48efc2446", "generation": "The authors use regularization specifically for label embeddings to address the anisotropy problem and prevent model degeneration by promoting uniformity and dispersion of label representations on the unit hypersphere. The label embedding regularizer (LER) encourages the label representations to be uniformly dispersed, maintaining low inter-label similarity and thus ensuring that the feature space of each class is more distinct and less prone to collapse. This regularization is applied because label embeddings serve as anchors in the semantic space and require a well-distributed representation to effectively align with instance embeddings.\n\nIn contrast, instance embeddings are aligned to their corresponding label embeddings through the instance-centered contrastive loss (ICL), which encourages each text representation to be close to its label representation while pushing away mismatched pairs. The instance embeddings are learned dynamically from input data and supervised signals, and their alignment with well-regularized label embeddings inherently guides their distribution. Therefore, explicit regularization for instance embeddings is not necessary, as the contrastive losses already enforce meaningful and discriminative instance representations by leveraging the supervision from the regularized label embeddings."}
{"question_id": "ba8b96d10b44463b1ec163db65a29b6145f8729a", "paper_id": "openreview/ICLR-2023-conf/ySCL-NG_I3", "generation": "The learning curves for the Harmonic Molecular Representation (HMR) model on the rigid protein docking task demonstrate progressive improvement in multiple evaluation metrics over the course of training. Specifically, the training was conducted for 80 epochs with a batch size of 4, using the Adam optimizer and a cosine annealing learning rate scheduler starting at 5 \u00d7 10^\u22124.\n\nThe curves show that the total loss steadily decreases as training progresses, indicating effective optimization of the model parameters. Concurrently, key performance metrics such as F-score, Area Under the Curve (AUC), and Average Precision (AP) increase, reflecting enhanced predictive accuracy and model robustness. The validation metrics follow a similar upward trend, with the model achieving its best validation AP score at a certain epoch, which was then selected for testing on the DB5.5 dataset.\n\nThis pattern of decreasing loss and increasing performance metrics suggests that the HMR model effectively learns to encode molecular features relevant for rigid protein docking, with stable convergence and generalization capability demonstrated by the validation results."}
{"question_id": "953feae01ae0b8d2066fd035c079f0a5dd581aaf", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Label smoothing during the head probing (HP) stage does not always improve the performance of the hyperparameter-fine tuning procedure. It is particularly effective when the pretrained features are already good and the standard HP training accuracy converges very quickly to above 90%, as it reserves some \"energy\" for subsequent feature adaptation, leading to better generalization without changing the HP-train-acc. However, when the HP-train-acc is too low, indicating that the pretrained features are not suitable for the downstream task, the assumption that the model's prediction converges to the labels no longer holds, and label smoothing (lsHP) may fail to bring improvement. Additionally, if label smoothing is applied in both HP and fine-tuning (FT) phases with the same smoothing factor, the reserved energy disappears, resulting in performance similar to the baseline without label smoothing. Moreover, using label smoothing in both phases with improper settings can cause features to adapt in opposite directions, harming generalization. Therefore, label smoothing is beneficial only under specific conditions and requires careful tuning of smoothing parameters to ensure correct feature adaptation direction."}
{"question_id": "cbd5e6e55ec199de5569c76823febc8a19d28e5e", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "Yes, the \u039b-DARTS NAS method can be trained on a large-scale dataset such as ImageNet. The search and evaluation results on ImageNet were conducted using a setting proposed by PC-DARTS, involving a search model with 8 layers (6 normal and 2 reduction cells) and 16 initial channels. The search utilized subsets of the ImageNet dataset (10% for optimizing operation parameters \u03c9 and 2.5% for architecture parameters \u03b1) with a batch size of 384 and a learning rate of 0.5, which was linearly decayed to zero. The evaluation was performed on a model with 14 layers and 48 initial channels for 250 epochs. The method demonstrated improved top-1 and top-5 accuracy compared to baselines, including DARTS, \u03b2-DARTS, and DrNAS.\n\nFurthermore, the use of forward/backward finite difference approximations in \u039b-DARTS reduces the search cost to approximately 0.6 GPU days, making it computationally feasible and comparable to DrNAS for large-scale applications such as ImageNet. This low-cost approximation maintains the effectiveness of the method with negligible performance drop, supporting its applicability to large datasets."}
{"question_id": "ff310f12cf0c134c9763ec3389c106e6c16dc65c", "paper_id": "openreview/ICLR-2022-conf/ZTsoE8G3GG", "generation": "The metric \"score\" is a task-specific real number in the range [0, 1], with higher values indicating better performance, reflecting how well the generated molecules match a target molecular profile. The \"quality\" metric is defined as the absence of undesirable substructures in the optimized molecules, evaluated post-hoc using the same quality filters as in Brown et al. (2019). Quality measures the reasonableness of the molecules, ensuring that high quality scores correspond to molecules that do not exploit or bypass the quality filters."}
{"question_id": "f18d6c2ac81007f7b42cab31234075307c85ee0a", "paper_id": "nlpeer/ARR-22/6262c349a3980c6850e252b3a44391b1b1318c26fbf29be55c61c0a32b8b626e5581c98234d1ecb410674c00ebcf3ba2adfdce41c2b2d32211721372a8ed1bc0", "generation": "Yes, the training languages used for the non-baseline models in the cross-lingual experiments do have overlap with German. The models were pretrained on speech in 8 languages, including German, as indicated by the use of the Karlsson dataset (German, 29 hours) and other datasets such as CSS10 which contains German subsets. Additionally, German was used as the benchmark language for evaluation, and the low-resource models were fine-tuned on a 30-minute subset of the German Karlsson dataset. This confirms that German was included in the training or fine-tuning data for the non-baseline models."}
{"question_id": "e57f2a5a860c3aa8c1e0f8ca5a3375dd735d463c", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "The performance drop after 100 epochs in Figure 4(c) is attributed to the performance collapse phenomenon in DARTS, which is caused by low layer alignment and vanishing gradients. Specifically, the optimal architectures for different layers become vastly different due to the residual connections and ResNet-like structure of the search space. Shallower layers favor convolution operations, while deeper layers favor skip-connections because skip-connections allow gradients to flow more effectively, resulting in larger gradient magnitudes. As the search progresses, architecture parameters increasingly favor skip-connections, leading to saturation points dominated by skip-connections and a consequent decline in performance. This effect is exacerbated by deeper networks and longer search durations, as vanishing gradients become more severe. However, \u039b-DARTS mitigates this issue by harmonizing operation selection among cells, resulting in negligible performance drop (about 0.4%) after 100 epochs, indicating convergence to near-optimal architectures."}
{"question_id": "a7d741be648d514c67c1a0468a78782b19c6d11c", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Label smoothing during head probing (lsHP) can improve performance in scenarios where the pretrained features are already strong and the standard head probing (HP) training accuracy converges very quickly to above 90%. In such cases, lsHP reserves some adaptation energy, allowing features to adapt more during finetuning without reducing HP-train-accuracy, which leads to better generalization.\n\nHowever, lsHP does not always improve performance. When the HP-train-accuracy is low, indicating that the pretrained features are not well suited for the downstream task, the assumption that the model's prediction converges to the labels no longer holds, and lsHP may fail to bring enhancement or even degrade performance. For example, in the Sup-C10 case, lsHP fails when the pretraining features are not suitable.\n\nAdditionally, if label smoothing is applied in both the head probing and finetuning phases with the same smoothing factor (\u03b7_HP = \u03b7_FT), the reserved energy disappears, and the performance is similar to the baseline without smoothing. Using \"opposite energy\" settings (where smoothing factors differ in a way that causes feature adaptation in opposite directions) can lead to larger adaptation but worse generalization.\n\nTherefore, label smoothing can improve performance under certain conditions but can degrade or fail to improve performance when the pretrained features are weak or when smoothing is applied improperly in both phases."}
{"question_id": "7a4e6842b9fed6c17b9fc508c5e7f7bdc1614d7c", "paper_id": "openreview/ICLR-2023-conf/ySCL-NG_I3", "generation": "The design of network architectures for molecular representation learning should consider the following strategies:\n\n1. **Bypassing Equivariance Requirements:** Traditional geometric deep learning (GDL) approaches require equivariant networks to ensure molecular representations transform appropriately under rotation and translation. However, this can limit network expressive power. Therefore, it is desirable to develop representations that properly encode 3D molecular structure while bypassing the equivariance requirement.\n\n2. **Multi-Resolution Feature Encoding:** Molecular representations should capture features at various resolutions to accommodate different tasks. For example, high-level holistic features are needed for molecular property prediction, while fine-grained features are necessary for describing specific molecular interfaces. Efficient multi-resolution message passing mechanisms are ideal for encoding molecules of distinct sizes, especially large macromolecules.\n\n3. **Surface-Based Representation:** Utilizing the molecular surface as a high-level representation is advantageous because it encodes both geometric shape and chemical properties relevant to intermolecular interactions. Representing molecular features as functions distributed on the molecular surface manifold allows integration of both geometry and chemistry.\n\n4. **Harmonic Message Passing:** Implementing message passing in the spectral domain using harmonic analysis on the molecular surface manifold enables efficient multi-range communication between distant surface regions. This approach uses Laplace-Beltrami eigenfunctions as basis functions and applies neural network-learned spectral filters to propagate features over the surface, facilitating multi-scale feature extraction.\n\n5. **Stacking Message Passing Blocks:** Multiple harmonic message passing blocks can be stacked to improve representation quality, allowing the network to learn complex feature interactions across the molecular surface.\n\nThese strategies collectively guide the design of network architectures that effectively encode molecular structures and their chemical properties for downstream tasks such as molecular property prediction and protein docking."}
{"question_id": "e111e75817d67b6fbeec06d5ba117b3419bf2f0f", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The initial value being discussed in Section 3.2 is \\( q_0 \\), which represents the model's prediction on the training samples before finetuning. Specifically, \\( q_0 = X B_0^\\top v_0 \\), where \\( v_0 \\) is the initialized task head parameter, \\( B_0 \\) is the backbone parameter before finetuning, and \\( X \\) is the input data matrix. The analysis focuses on how the choice of \\( v_0 \\) influences \\( q_0 \\), which in turn affects the adaptation of features \\( z \\) during finetuning. Two critical points for \\( q_0 \\) in feature adaptation are identified as \\( q_0 = Y \\) and \\( q_0 = \\frac{1}{2} Y \\), where \\( Y \\) is the target output. These critical points correspond to specific behaviors in the learning dynamics and feature changes during finetuning."}
{"question_id": "9f2536e9ca279c79b121794af57c2ad02d6b13c8", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "The use of synthetic data does not improve the performance of a model when a large amount of ground-truth (real) data is available, particularly in the setting where the model is trained from scratch. Synthetic data deliver inferior performance and are much less data-efficient compared to real data in this scenario. For example, training a ResNet-50 model from scratch on 50k synthetic images for CIFAR-100 achieves only 28.74% top-1 accuracy, which is much lower than the performance of a pre-trained CLIP model on real data. Moreover, it requires around five times more synthetic data to match the performance achieved by a smaller amount of real data (e.g., 9.5k real images). Increasing the amount of synthetic data beyond a certain point does not yield further performance gains.\n\nHowever, synthetic data show promising results in other settings such as zero-shot classification, few-shot learning, and large-scale model pre-training for transfer learning. In particular, synthetic data can be effective for model pre-training, where large-scale synthetic datasets can achieve comparable or even superior transfer learning performance relative to ImageNet pre-training, especially when using ViT-based backbones and self-supervised pre-training methods.\n\nIn summary, synthetic data are not effective for improving model performance when large amounts of real labeled data are available for direct training from scratch, but they can be beneficial in zero-shot, few-shot, and pre-training transfer learning contexts."}
{"question_id": "280960bc073f24e47cd5b63da7388c21eb12d9be", "paper_id": "openreview/ICLR-2023-conf/pWVASryOyFw", "generation": "The CNN classifier was selected for the sentiment analysis experiments to ensure a fair comparison with prior work by Liu et al. (2019), as the CNN classifier was pre-trained for two epochs following their methodology."}
{"question_id": "9d285bc752521120d3b45a5b35069f1365c8f603", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "Set expansion approaches are used to construct a dictionary from customer reviews because these reviews are typically unlabeled and contain a limited number of seed entities provided by experts. The goal is to enhance the initial small seed set by discovering and ranking new entities that belong to the same semantic concept within the corpus. This is particularly important in low-resource settings where training data is scarce. Set expansion methods leverage contextual features and patterns from the corpus or probe pre-trained language models to bootstrap and rank candidate entities, thereby achieving high coverage of relevant entities in user-generated text domains such as customer reviews. This enables applications like semantic search to effectively identify and highlight mentions of relevant concepts (e.g., amenities) in reviews, improving the exploration and retrieval of pertinent information for users."}
{"question_id": "3611098cfd2590d775531ef564d87617713fe8bf", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The rationale for using entity set expansion (ESE) methods on user-generated text is that user-generated text domains, such as reviews, contain abundant but largely unlabeled data that is critical for enabling downstream natural language processing (NLP) applications like semantic search and question answering. Unlike well-curated text, user-generated text exhibits distinctive characteristics including a higher prevalence of multifaceted entities (entities belonging to multiple concepts), non-named entities (common noun phrases rather than proper names), and vague entities (concepts with subjective or poorly defined semantics). These characteristics make existing ESE benchmarks and methods, which focus primarily on named entities in well-formed text, insufficient for user-generated text. Therefore, ESE methods are needed to expand small seed sets of entities in low-resource settings by learning to rank new entity candidates with limited supervision, thereby facilitating the extraction of relevant entities from user-generated text for various NLP applications."}
{"question_id": "fd9af00fe3f20196d71e9e364f55c157d4cd2cd3", "paper_id": "nlpeer/COLING2020/939", "generation": "To ensure that paths were not shared between the training and test sets, the validation and test sets were filtered to include only hyponym queries that were unseen anywhere in the full taxonomy paths of the training data. Additionally, hyponyms appearing as hyponyms in the training set were excluded from the test and validation sets to prevent models from merely copying. This filtering ensured that all evaluated queries were equally new to both path-based and non-path models, thereby preventing any overlap of paths between training and evaluation sets."}
{"question_id": "be0cd13d8445fb87a73943d5acf2e5089a02876c", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "The optimization in equation (1) for active training selects locations from the accumulated uncertainty estimates in the global map, which includes both observed and hallucinated regions. Similarly, the goal selection policy in equation (2) chooses goals from any map region the agent has observed or hallucinated. Therefore, the optimization does not happen exclusively over the geocentric map locations observed so far but also includes unobserved (hallucinated) areas within the global map."}
{"question_id": "ae75b890d30e2879b6a6571bbc634ee4e6157e30", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "Measuring the performance of a method using mean average precision at gold-k (MAP@k_g), where k_g equals the concept size (number of entities in the concept), provides an estimate of recall that is crucial for assessing effectiveness in real-world settings with commonly large concept sizes. This evaluation metric adapts to different concept sizes and includes more instances of multifaceted, vague, and non-named entities that are typically present in user-generated text but may be ignored in smaller fixed-k evaluations. Consequently, MAP@k_g serves as a complementary metric designed to stress test entity set expansion (ESE) methods in scenarios where coverage is important, such as knowledge base population. It helps practitioners measure the suitability and robustness of ESE methods, especially for concepts with large entity sets and in domains relying on user-generated text, where top-20 predictions do not adequately represent the diversity and complexity of entities."}
{"question_id": "d59bc31fea9ec1c2594f0ed7813ed2d9348abc75", "paper_id": "nlpeer/COLING2020/939", "generation": "The task described in the paper focuses specifically on hypernym prediction, which involves identifying the correct direct hypernym (target node) in a taxonomy for a given synset (source node) based on its embedding. This is formulated as a sequence generation problem where the model predicts the taxonomy path from the direct hypernym to the root node in WordNet. The task is restricted to predicting direct hypernym relations rather than multiple types of semantic relations.\n\nIn contrast, taxonomy induction refers to the broader task of constructing or extending a taxonomy, which may involve extracting is-a relations from text, binary classification of hypernym relations between word pairs, or assembling a hierarchical structure from heterogeneous evidence. Taxonomy induction typically encompasses multiple subtasks and may involve learning various semantic relations to build or enrich the taxonomy.\n\nThus, the key difference is that the paper's task is a focused relation prediction problem targeting direct hypernym identification within an existing taxonomy, whereas taxonomy induction is a more general task aimed at creating or expanding taxonomic structures, potentially involving multiple relation types and sources of evidence."}
{"question_id": "d8ac040e919b01e19818a6416896dd66bd58e69d", "paper_id": "nlpeer/COLING2020/939", "generation": "The success of the path-based model, particularly hypo2path, can be attributed to several factors:\n\n1. **Formulation as a Sequence Generation Task:** The model treats hypernym prediction as generating the entire hypernym path in the WordNet taxonomy, rather than predicting only the direct hypernym. This more difficult auxiliary task strengthens the model by encouraging it to learn richer hierarchical information.\n\n2. **Use of Reversed Path Generation (hypo2path rev):** Reversing the target sequence to generate hypernyms starting from the direct hypernym improves performance by framing each generation step as direct hypernym prediction, which the decoder can learn more easily.\n\n3. **Encoder-Decoder Architecture with Attention:** The use of an LSTM-based sequence-to-sequence model with Luong-style attention helps the decoder maintain focus on the source hyponym during the generation of long hypernym paths, preventing forgetting and improving accuracy.\n\n4. **Training on Valid Paths from WordNet:** The model generates valid hypernym paths that exist in WordNet, ensuring that predictions are consistent with the taxonomy structure.\n\n5. **Learning from Full Hypernym Paths:** Training on entire hypernym paths rather than truncated paths or single hypernyms leads to stronger performance, as it captures more comprehensive hierarchical relationships.\n\n6. **Embedding Representations:** The model uses synset embeddings as input, which, despite some limitations, provide a meaningful representation of concepts in the taxonomy.\n\nThese factors collectively enable the path-based model to outperform benchmark models, achieving state-of-the-art results in hypernym prediction tasks."}
{"question_id": "46357f5d8816d410e6100ea03a5fde2f576ae270", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "The extracted patterns are representative traffic speed patterns derived from historical traffic data, characterized by repeating and highly correlated leading and trailing segments even over short-term periods. Each pattern corresponds to a sliced segment of an average daily traffic speed profile, which consists of 288 data points representing 24 hours at 5-minute intervals for each vertex in the traffic network. These patterns are obtained by segmenting the daily patterns with a fixed window size, resulting in a large initial set of patterns. Due to class imbalance caused by many similar patterns, clustering-based undersampling using cosine similarity is applied to group highly similar patterns (above a similarity threshold \u03b4) into clusters. The cluster centers serve as balanced and representative patterns, which are then used as keys for memory access in the forecasting model. These patterns effectively capture typical traffic behaviors and enable accurate matching and retrieval for traffic speed prediction, especially under rapidly changing traffic conditions."}
{"question_id": "7f3ceaefa9722ecb3ce14f4c48d0191a6893f607", "paper_id": "nlpeer/F1000-22/11-222", "generation": "The lupeol and pristimerin used in this study were obtained as plant-derived extracts from Cayman Chemicals (MI, USA)."}
{"question_id": "9d42f16a53845fd4e37c5676307657425faf1538", "paper_id": "nlpeer/ARR-22/841d64f4eb3d5e3d6ea89751ea19897bb8424acf856c9dd1574ef7d2803ff7d155df1b9cc201df8b4fc8c1984f6ca8bdfaf94036960348047e8da35b0003ae34", "generation": "Yes, it is feasible to generate a lattice graph for a large dataset. The lattice encoding method described can compactly represent a massive number of generation candidates within a single data structure, enabling efficient exploration of diverse outputs. The number of nodes in the lattice is bounded by the number of node expansions during search, which is controlled by the search budget, ensuring manageable memory usage. The algorithm's runtime for summarization tasks is on the order of 1 to 10 seconds per instance, and the process can be parallelized across examples to improve efficiency. Experiments were conducted on datasets with 100 data instances each, sampling 1,000 candidates per instance, resulting in evaluation over 100,000 generated outputs per dataset, demonstrating scalability to large-scale decoding."}
{"question_id": "06b380902968cd38bbb66c2a75d9372c2f039f2f", "paper_id": "openreview/NeurIPS-2022-dabt/in7XC5RcjEn", "generation": "A graph with an average diameter in the range of 10 to 50 is considered sufficient to indicate the presence of long-range relationships because such graph sizes imply that nodes can be separated by many hops, necessitating information propagation over long distances. Specifically, datasets like Peptides-func and Peptides-struct, which have average diameters around 57, demonstrate that long-range dependencies are essential for performing well on their tasks. The substantial graph statistics, including average nodes, average shortest paths, and average diameter, support the requirement for long-range signal handling. Additionally, the PCQM-Contact dataset, despite having smaller graph sizes (average diameter around 10), involves tasks that explicitly require modeling interactions between distant nodes (more than 5 hops apart), further illustrating that an average diameter in this range can reflect the need for long-range interaction modeling. These observations are supported by experimental results showing that fully-connected models enabling long-range interaction propagation outperform local message passing GNNs on such datasets. Thus, an average diameter between 10 and 50 is indicative of graph tasks that depend on long-range signal propagation and global structural information."}
{"question_id": "0d68ad6ddb3ddfccd1c2d71ae7fc8a724843e891", "paper_id": "nlpeer/F1000-22/10-72", "generation": "The econometric models and economic theories used to write equations (2) through (10) are based on the neoclassical investment model and dynamic panel data estimation techniques. Specifically:\n\n1. The theoretical foundation is the neoclassical investment model adapted by Agosin & Machado (2005), which incorporates adjustment of capital stock and adaptive expectations of economic growth. This model has been applied in studies examining the relationship between foreign direct investment (FDI) and domestic investment (DI) (e.g., Budang & Hakim, 2020; Oulay, 2019; Pilbeam & Oboleviciute, 2012).\n\n2. The econometric approach employs the system Generalized Method of Moments (system-GMM) estimator, which addresses issues of endogeneity, unobserved country-specific effects, and dynamic panel bias. The system-GMM combines regressions in first differences and levels, using internal instruments based on lagged values of dependent and independent variables under the assumption of no second-order serial correlation in the first-differenced errors and weak exogeneity of regressors (Al-sadiq, 2013; Arellano & Bond, 1991; Arellano & Bover, 1995; Blundell & Bond, 1998).\n\n3. The equations incorporate interaction terms to account for different levels of country development (developing countries, economies in transition, developed countries), reflecting that the effects of FDI on DI may differ by development status. This classification indirectly captures macroeconomic conditions and is based on United Nations (2020) criteria.\n\n4. The short-run effects of FDI on DI for different country groups are expressed through linear combinations of estimated coefficients (\u03b2 parameters), as shown in equations (3), (4), and (5), with the null hypothesis for combined group effects stated in equation (6).\n\nIn summary, the equations are derived from a neoclassical investment theoretical framework operationalized through a dynamic panel data model estimated by system-GMM, incorporating country development level interactions to capture heterogeneous effects of FDI on domestic investment."}
{"question_id": "fd627c54f792bc91ab0db4a51e8311a23d8af60d", "paper_id": "nlpeer/ARR-22/82520f1d31572d488cfeae02712db642750fda4063e5eab8a771a000f6e17e1397ab18af73e5362e4d660b3dcc78b2260e121fdefc4c1664448e1c30066d5cb9", "generation": "For prompts 7 and 8, which did not have trait-specific scores in the original ASAP Automatic Essay Grading dataset, the trait-specific scores were obtained from the trait-specific scores provided by Mathias and Bhattacharyya (2018a)."}
{"question_id": "bf76bbb77fabe1a9105b55efcd591d179958b2c6", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "Optimizing the reward with respect to the possible combinations of modules means selecting the combination of neural modules that, when composed into a policy, yields the highest expected return for the current task. This process involves performing a discrete search over all possible module combinations without modifying the module parameters, rolling out each resulting policy for a fixed number of episodes, and evaluating their average returns. The combination that achieves the highest average return is chosen as the best set of modules to solve the task. This approach ensures minimal modification to existing modules, preserving knowledge from earlier tasks while enabling effective reuse and adaptation of modules for new tasks."}
{"question_id": "d539111f12ea7af828c1637c34c8e6fcb06f589a", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "The framework described uses an ensemble of segmentation models to estimate epistemic uncertainty by measuring the variance between the outputs of the models in the ensemble. Specifically, the model f outputs a probability distribution over semantic classes (logits), and epistemic uncertainty is approximated from the variance of these probabilistic outputs across the ensemble members. This approach leverages the probabilistic interpretation of the model outputs and the variance in predicted probabilities to identify areas of high uncertainty.\n\nThe method explicitly formulates uncertainty estimation in the space of model outputs as probability distributions (logits), averaging over sampled model weights and computing variance between these outputs. There is no indication that the framework operates or is designed to operate directly in the semantic feature space (i.e., intermediate feature representations before the final classification layer).\n\nGiven that the uncertainty estimation relies on the probabilistic outputs and their variance, which are well-defined in the logit or probability space, applying the same variance-based ensemble framework directly in the semantic feature space would not be straightforward or necessarily effective. The semantic feature space may not have a direct probabilistic interpretation, and variance in this space may not correspond clearly to epistemic uncertainty relevant for semantic prediction.\n\nTherefore, the framework as described is specifically tailored to operate in the space of logits (probability outputs), and it is unlikely that it would work equally well if applied directly in the semantic feature space without modifications to account for the different nature of the representations."}
{"question_id": "f2564b011de1ce166a9e4410c3857b5a9c2496ce", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The significant performance difference between CGExpan and LM-Base can be explained by the characteristics of user-generated text and the design choices of these methods. CGExpan, a state-of-the-art (SOTA) method, scores each candidate entity by selecting one positive concept and multiple negative concepts, which tends to penalize entities that are multifaceted (belonging to multiple concepts) or vague (mentioned in diverse contexts). This design avoids selecting ambiguous contexts but consequently leads to suboptimal ranking of such entities, which are prevalent in user-generated text. In contrast, LM-Base is a simpler baseline that uses Hearst patterns to prompt language models without additional mechanisms like concept name guidance or iterative expansion. LM-Base better captures entities with diverse and multifaceted contexts typical of user-generated text, resulting in higher recall and overall better performance on these benchmarks. Thus, CGExpan's approach of penalizing ambiguous or multifaceted contexts negatively impacts its performance, while LM-Base's simpler, more flexible approach is more effective for user-generated text."}
{"question_id": "c0f788c455af0bee35fb5b9cc36af6a5ec6aaf3f", "paper_id": "nlpeer/ARR-22/212dff6380418cd7c050ec757b31e6b5b65bbe922aa30ffbd5950fe7a04ca737b7c7b3d706f1cd1502d7932b61d2b7c079924793e45e437526230c1e9c0626ed", "generation": "Additional fine-tuning of CLIP was not included in the experiments because fine-tuning the last few layers of CLIP with a classification head on top consistently resulted in worse performance compared to using extracted features for classification with SVM. This outcome is attributed to the relatively small size of the labeled dataset, which is insufficient for effective fine-tuning of CLIP for the claim detection task."}
{"question_id": "3f6d76b052a19c42fdc0f3fa7a03e99d76e053d5", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "The navigation tasks, specifically object goal navigation in unseen environments, require the semantic layout of unseen regions to be effectively solved. The proposed approach actively learns to predict (hallucinate) semantics in both observed and unobserved areas of the map around the agent, enabling the agent to leverage semantic priors and uncertainty estimates about unobserved spaces. This allows the agent to infer the likely presence and spatial associations of target objects in unobserved regions, guiding goal selection and navigation decisions beyond the agent\u2019s immediate field of view. Traditional methods that rely solely on observed areas or build accurate 3D metric maps without semantic prediction of unseen regions are less suitable for target-driven tasks in novel environments. Thus, incorporating semantic layout predictions of unseen regions is essential for successful navigation in these tasks."}
{"question_id": "5219ee2947eb66850d9df883d32c6549b914d086", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The system contains four modules of each of three types, totaling twelve modules. Specifically, for the discrete 2-D tasks, the architecture uses four modules for each of the three task components: static object, target object, and agent dynamics. Each task policy contains one module of each type, chained in the order static object \u2192 target object \u2192 agent."}
{"question_id": "23c1d98a22e68ab8a92b7b1cd2fee83fa79e9a86", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The optimal depth of a ranked list of entities returned by an entity search engine approach should correspond to the actual size of the concept's entity set, which can be substantially larger than the commonly used fixed evaluation depths of 10 to 50 entities. For example, in user-generated text domains such as Tripadvisor and Wiki, the median concept sizes are 121 and 205 entities, respectively. Therefore, limiting evaluation or retrieval to a small fixed number (e.g., fewer than 50 entities) is insufficient to capture the full recall of relevant entities. An optimal depth should thus be adaptive and reflect the varying and often larger concept sizes to ensure comprehensive coverage of relevant entities."}
{"question_id": "8952c8598f43e3e36131d56d62db44fded0352d3", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The improved performance of the LM-Base model compared to the CGExpan model on user-generated text datasets can be attributed to several factors related to the characteristics of user-generated text and the design of the methods:\n\n1. **Handling of Multifaceted and Vague Entities:**  \n   CGExpan employs a mechanism that selects one positive concept and multiple negative concepts to score candidate entities. This approach tends to penalize entities that belong to multiple concepts (multifaceted entities) or that appear in diverse and ambiguous contexts (vague entities). In contrast, LM-Base does not implement such restrictive concept guidance, allowing it to better accommodate entities with diverse or ambiguous contexts common in user-generated text.\n\n2. **Avoidance of Penalizing Ambiguous Contexts:**  \n   CGExpan\u2019s design intentionally avoids selecting ambiguous contexts to improve ranking precision, which inadvertently leads to lower recall for entities with multifaceted or vague characteristics. LM-Base, being a simpler baseline that uses Hearst patterns to prompt language models without additional concept name guidance or iterative expansion, is less likely to penalize such entities.\n\n3. **Robustness to User-generated Text Characteristics:**  \n   User-generated text exhibits higher degrees of multifacetedness, non-named entities, and vagueness compared to well-curated benchmarks. LM-Base\u2019s simpler approach is more robust to these challenging characteristics, resulting in higher recall and better overall performance on user-generated datasets.\n\n4. **Performance on Diverse Contexts:**  \n   LM-Base outperforms CGExpan on concepts where entities have diverse contexts, as exemplified by the \"seating arrangement\" concept, where CGExpan retrieved frequently co-occurring but incorrect entities, whereas LM-Base retrieved more appropriate entities.\n\nIn summary, LM-Base\u2019s simpler, less restrictive design allows it to better handle the multifaceted, vague, and diverse nature of entities in user-generated text, leading to improved performance over CGExpan on such datasets."}
{"question_id": "be6ee11df60dadea667438571e3ed15560c3cb04", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The MOMA-LRG dataset differs from the MOMA dataset in several key aspects:\n\n1. Scale and Variety: MOMA-LRG contains an order of magnitude more annotations and longer videos from a greater variety of scenes compared to MOMA.\n\n2. Hierarchical Representation: MOMA-LRG introduces the abstraction of the activity graph as a single universal representation of human activities that encompasses video understanding at three hierarchical levels\u2014activity, sub-activity, and atomic action\u2014whereas MOMA only represents the atomic level.\n\n3. Annotation Schema: MOMA-LRG provides a new annotation schema that can be easily converted from natural language to graphical annotations, enabling few-shot learning capabilities.\n\n4. Dataset Size and Classes: MOMA-LRG includes 148 hours of videos with 1,412 activity instances from 20 activity classes, 15,842 sub-activity instances from 91 sub-activity classes, and 161,265 atomic action interaction instances. It contains 104,564 actor instances from 26 classes, 47,494 object instances from 225 unique classes, 1,037,319 relationships from 52 classes, and 704,230 relationships from 13 classes. In contrast, MOMA has 66 hours of video, 373 activity instances from 17 classes, and fewer annotations overall.\n\n5. Motivation and Application: MOMA-LRG is motivated by the limitations of video-language models (VLMs) and is designed to enable training and evaluation of VLMs on complex activity parsing tasks through activity graph generation, whereas MOMA does not explicitly address this.\n\n6. Role Classification and Predicate Differentiation: MOMA-LRG classifies each actor\u2019s role and differentiates between static and dynamic predicates, facilitating the learning of perceptual causality through state transitions, features not emphasized in MOMA.\n\nIn summary, MOMA-LRG is a significantly expanded and refined dataset that introduces hierarchical activity graphs, richer annotations, and a new framework to support few-shot learning and evaluation of video-language models, extending and improving upon the original MOMA dataset."}
{"question_id": "05fe45ed14b202a953eb1a25a0c4552d1d2ff42d", "paper_id": "openreview/ICLR-2022-conf/k7efTb0un9z", "generation": "Yes, the proposed method can be applied to simpler models such as multilayer perceptrons (MLPs). An evaluation of training a 3-layer MLP on Fashion MNIST with hidden size 128 demonstrated that the graph neural network scheduler (GNS) is applicable in this simpler scenario, achieving the best test accuracy of 89.8%, outperforming other optimizers and schedulers."}
{"question_id": "1740b93cc1257022895a050e975d38feebe0f904", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "Yes, the activity graph is an abstraction introduced in MOMA-LRG that extends and improves upon the original MOMA dataset by providing a single universal representation of human activities encompassing multiple levels of granularity. It captures hierarchical and compositional aspects of activities through three levels: activity, sub-activity, and atomic action. This abstraction enables the evaluation of models on different levels of granularity, allowing for multi-level activity recognition via activity graph generation. Unlike MOMA, which focused primarily on the atomic level, MOMA-LRG's activity graph integrates activity, sub-activity, and atomic action levels into a unified framework for comprehensive video understanding and evaluation."}
{"question_id": "a3566edd083568caf0264186c9b8e1658c31e561", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The rationale behind the design of the different modules is to assign each neural module \\( m_i \\) the responsibility of solving one specific subproblem \\( F_i \\) within a compositional reinforcement learning task, ensuring a one-to-one and onto mapping from subproblems to modules. This modular decomposition allows all tasks requiring the same subproblem \\( F_i \\) to share the corresponding module \\( m_i \\). Each module receives only the subset of the state components relevant to its subproblem, thereby focusing exclusively on processing the necessary information without interference from irrelevant task components. This design avoids brittle dependencies and cascading effects present in simple chained architectures, where modules must pass through all information to subsequent modules. By restricting inputs to module-specific state components, the architecture facilitates compatibility of module outputs across different depths and enables rapid generalization to unseen task combinations with less data. This modular approach also supports lifelong learning by allowing flexible adaptation and reuse of modules while maintaining stability to prevent forgetting."}
{"question_id": "c0ddf26bae180b57c24cd4b90e7a0da4a0676425", "paper_id": "openreview/ICLR-2022-conf/KTPuIsx4pmo", "generation": "A real-world pushing task involves a robotic arm, specifically a UR5 robot equipped with an RGB camera, tasked with pushing a designated target object into a specified target area on a physical table. The target area is a pink rectangular region measuring 12 cm by 12 cm. The robot receives only sequences of 128 \u00d7 128 RGB pixel images as input, without access to robot joint or end-effector state information, and outputs incremental 3D movements of the robot end-effector constrained within specified ranges. The task is considered successful if the robot pushes the correct target object so that its center lands within the center of the pink target area within a limited number of steps (typically 50 steps). The environment includes distractor objects that differ between demonstration and execution scenes, increasing task complexity. The real-world pushing task is more challenging than simulated versions due to observation and action noise, misaligned paired data, and differing perspectives between human demonstrations and robot execution. The task is used to evaluate meta-imitation learning methods that learn from human video demonstrations without requiring robot action data during training."}
{"question_id": "030389c23b9697a71ca59dec3bcab088d7f20ced", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "No, equation (1) is a summation from j = 1 to k, not from j = 0 to k."}
{"question_id": "e26fc7a2455a2acb2de4d608a7ca7bf1c8fb62a1", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "For a neural module to be \"in charge of solving one specific subproblem\" means that each module \\( m_i \\) is responsible for addressing a distinct subproblem \\( F_i \\) within the overall reinforcement learning task. There is a one-to-one and onto mapping between subproblems and modules, such that all tasks requiring the solution to subproblem \\( F_i \\) share the same module \\( m_i \\). Each module processes only the subset of the state components relevant to its assigned subproblem and outputs the solution to that subproblem. This design ensures that modules focus exclusively on their specific functional role (e.g., object detection, grasping point identification) without needing to handle unrelated task information or pass extraneous data to subsequent modules. Consequently, the modules can be composed sequentially to form a complete policy, with each module contributing a specialized solution to a component of the overall task."}
{"question_id": "1b206d1d36f66f3d336a33e34858567e8a593ab0", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The system employs a modular neural policy architecture designed for compositional reinforcement learning tasks. Each neural module in the architecture is responsible for solving a specific subproblem, with a one-to-one mapping between subproblems and modules. The overall policy network for a task is constructed by chaining these modules in sequence, reflecting the underlying graph structure of the task components.\n\nUnlike a simple chained architecture where the entire input is passed through each module sequentially, this architecture decomposes the input state into distinct task components and feeds each component separately to its corresponding module. This design ensures that each module receives only the relevant subset of the state necessary to solve its assigned subproblem, avoiding distractor features and reducing brittle dependencies among modules.\n\nFor example, in the discrete 2-D tasks, the architecture processes the static object first, then the target object, and finally the agent dynamics. The static object module processes channels corresponding to static objects through convolutional blocks with ReLU activations and max pooling. The target object module preprocesses the target object channel, concatenates it with the static object module output, and applies further convolutional processing. The agent module processes the agent channel, concatenates it with the target object module output, and passes it through separate multi-layer perceptrons for the actor and critic networks.\n\nSimilarly, in the robotic manipulation domain, the architecture processes the obstacle state first, followed by the object state, and finally the robot and goal states. Each module consists of fully connected layers with tanh activations, concatenating outputs from previous modules to form the input for subsequent modules. The actor and critic networks have separate parameters but share the same graph structure.\n\nThis modular design enables effective recombination and reuse of modules to solve unseen tasks, facilitates faster generalization by training modules on decomposed state representations, and reduces catastrophic forgetting when combined with appropriate lifelong learning methods."}
{"question_id": "b6cb81cf492f5369fa4051c1d2e90b05b0aa9247", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The modules were partitioned according to distinct subproblems or task components, with a one-to-one and onto mapping from subproblems to modules. Specifically, in the discrete 2-D tasks, the architecture assumed three types of modules corresponding to the underlying graph structure: static object modules, target object modules, and agent modules. Each module type received only the module-specific subset of the state relevant to its subproblem. For example, static object modules consumed input channels corresponding to static objects, target object modules processed the target object channel and combined it with the static object module output, and agent modules processed the agent channel combined with the target object module output. This decomposition ensured that each module focused solely on the relevant task component without distractor features from other components. The modules were then chained in sequence following the order static object \u2192 target object \u2192 agent, reflecting the compositional structure of the tasks. This partitioning allowed modules to be specialized and reusable across tasks, facilitating zero-shot generalization to unseen task combinations."}
{"question_id": "67314f99bdc98da9611efbf0de1f4660e36f629c", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "The sampling method used to extract patterns with a time window involves first computing an average daily traffic speed pattern consisting of 288 data points per vertex (corresponding to 24 hours with 5-minute intervals). Then, this daily pattern is sliced into segments using a given window size \\( T' \\), resulting in multiple traffic patterns. Specifically, the pattern set \\( P \\) is formed by slicing the daily patterns into windows of length \\( T' \\), where the number of patterns is \\( |P| = N \\times \\lfloor 288 / T' \\rfloor \\), with \\( N \\) being the number of vertices. This approach effectively samples the traffic data by segmenting the continuous daily speed data into fixed-length time windows."}
{"question_id": "e5d8459c3ebc7cdeb1a56ddced28a7467921a917", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The videos in the MOMA-LRG dataset were collected from YouTube."}
{"question_id": "62968052606a9490b4add4170997a2c69f19d4ec", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "Yes, the dataset is planned to be licensed under a Creative Commons license, specifically the Attribution 4.0 International (CC BY 4.0) license."}
{"question_id": "c68719740a590d79c10dfd14e5cb4e1f51938871", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "The low performance of the \"DESCRIPTION\" answer type in Table 5 is attributed to the inherent difficulty of predicting answers in this class. The \"DESCRIPTION\" class was also identified by the annotators as the most challenging category, indicating that the semantic and structural complexity of description-type answers makes them harder for the model to predict accurately."}
{"question_id": "61588ca196125738d21c2d191ecac13249af297d", "paper_id": "openreview/ICLR-2022-conf/oiZJwC_fyS", "generation": "Yes, the algorithm can be tested on larger networks. Experiments conducted on larger datasets such as CIFAR using CIFAR-VGG and an altered version of AlexNet demonstrate that the Neural Path K-means compression method retains good performance. In most cases, it achieves slightly better accuracy and lower deviation than baseline pruning methods, although its performance degrades when retaining very few weights. This indicates the algorithm's applicability and effectiveness on deeper and larger neural network architectures beyond smaller datasets like MNIST and Fashion-MNIST."}
{"question_id": "507cdeff564fe9a3d5fe258fe00eef340d135d9b", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The ethical considerations taken into account when selecting the data for the MOMA-LRG dataset included the following protocols: (1) Taxonomy selection was carefully conducted to ensure that each activity class is gender-neutral, culturally inclusive, and respectful toward people from different socioeconomic backgrounds. (2) Video selection was performed by a diverse team of researchers from different ethnicities and genders who examined and discussed each video to ensure diversity and the absence of offensive content. Additionally, keywords from multiple languages (including English, Chinese, French, and Japanese) were used to search for videos to enhance diversity and reduce potential geographic and cultural bias. These measures aimed to mitigate ethical issues and biases associated with the dataset."}
{"question_id": "3318142bc7bd1401191fcc4a9712243c0df0f1df", "paper_id": "openreview/NeurIPS-2022-dabt/HYELrdRdJI", "generation": "High inter-class view similarity refers to the phenomenon where different objects appear similar or nearly identical in certain views. This similarity causes uncertainty in class labels for those specific views, as even humans cannot accurately classify the object from these views alone. Such views are termed uninformative views. Because the one-hot class labels assigned to these views do not align with human judgment due to this ambiguity, it results in inconsistencies known as multi-view label noise. This noise arises from the difficulty in distinguishing objects at the fine-grained (instance-level) categorization when views are visually ambiguous, thereby complicating the learning process for multi-view object classification."}
{"question_id": "6159c6e153be58a55c17f3cda104c7ebdd581acc", "paper_id": "openreview/ICLR-2023-conf/8efJYMBrNb", "generation": "The rationale for using the CS-error metric instead of SPS metrics lies in the precise definition and normalization of alignment accuracy. The column score (CS) quantifies accuracy by counting the number of alignment columns in the inferred alignment that exactly match the \u201ctrue\u201d alignment in both coordinates and characters. This strict criterion ensures that a match is only counted if the aligned positions and their characters correspond exactly, providing a direct measure of agreement between inferred and true multiple sequence alignments (MSAs). The CS is normalized to the range [0, 1] by dividing by the total number of columns in the true MSA, and the CS-error is defined as 1.0 minus the CS-score, reflecting the level of disagreement between the inferred and true MSAs. This metric thus offers a clear, normalized, and interpretable measure of alignment error, emphasizing exact column matches rather than partial or pairwise similarity scores, which may be less stringent or less directly comparable across alignments."}
{"question_id": "86b7bff4eb8f5701bb87715221c22a2db29eaae1", "paper_id": "openreview/ICLR-2022-conf/sPIFuucA3F", "generation": "In Algorithm 1, line 4, the data tuple \\((x_t, a_t, r_t)\\) is retrieved directly from the offline dataset \\(D_n\\)."}
{"question_id": "8c6b063b9a5318af6557db02c0c7dbc93f8939be", "paper_id": "openreview/NeurIPS-2022-dabt/HYELrdRdJI", "generation": "The 16k real captured views in the MVP-N dataset are combined to construct the 9k multi-view sets. Specifically, multi-view sets are formed by sampling and grouping these individual views. For the training set, 20 informative and 20 uninformative views per object are manually sampled from Collection A, and ten multi-view sets are constructed for each object, each containing two to six views with at least one informative and one uninformative view. For the validation and test sets, 40 informative and 120 uninformative views per object are randomly sampled from Collections A and B, and 100 multi-view sets are constructed for each object, each containing two to six views with only a single informative view. This construction ensures that the multi-view sets represent diverse combinations of the original captured views while maintaining a balance of informative and uninformative views."}
{"question_id": "a37fa0862f28bd4005c14092d19b86f84c30c983", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The selection criteria for videos and classes in the study were designed to mitigate potential ethical issues and reduce bias. Specifically, the activity classes in the taxonomy were carefully chosen to ensure they are gender-neutral, culturally inclusive, and friendly toward people from different socioeconomic backgrounds. For video selection, a diverse team of researchers from different ethnicities and genders examined and discussed each video to ensure diversity and the absence of offensive content. Additionally, keywords from multiple languages, including but not limited to English, Chinese, French, and Japanese, were used to search for videos, thereby enhancing the robustness and generalization of the models and significantly reducing potential dataset bias."}
{"question_id": "b49d17a4084f479d1c1f3e920a863be370968f66", "paper_id": "nlpeer/ARR-22/3cbf91f8e2f7e62be51109688c84a2b424e6018ee3c976fbe55e5331bc9ab2f4c46491d006087aed3be35a3370c945c100bd718e2c1131de19eac3dbe93326bc", "generation": "The per-triple templates were developed manually by one of the authors using only the input data, without relying on human-written references. The templates are single-sentence, mostly clear-cut verbalizations of the predicates. The approach involved creating a single-triple template for each predicate, for example, transforming a triple with the predicate \"instrument\" into the template \"<s> plays <o>\". The templates were hand-crafted to introduce domain knowledge efficiently while minimizing manual workload. Although the authors considered extracting templates automatically from training data by delexicalizing single-triple examples, this option was rejected due to noise in the data and the unavailability of such data in a zero-shot setting. In some cases, the most general version of a template was chosen to maintain applicability across different object types. Minor disfluencies resulting from template filling were acknowledged and could be addressed in subsequent pipeline steps. The templates were created for all predicates in the datasets used, including unseen predicates in the test sets."}
{"question_id": "19e742075a73b79f8d1593d22060ea47535a485a", "paper_id": "openreview/ICLR-2022-conf/8uz0EWPQIMu", "generation": "Figure 3a illustrates the Standard pattern observed in the accuracy results of neuron ranking evaluations across different configurations. In this pattern, all top-to-bottom rankings consistently outperform the random ranking, which in turn outperforms all bottom-to-top rankings. The figure serves to demonstrate one of the three distinct accuracy patterns identified in the experiments, highlighting the expected behavior where better-ranked neurons yield higher probing accuracy."}
{"question_id": "42aec356c91c672d813f540951f0b79d9f57705f", "paper_id": "openreview/ICLR-2022-conf/oiZJwC_fyS", "generation": "DeepNN is a deep neural network architecture with layers of size 28 \u00d7 28, 512, 256, 128, and 10. It is used to evaluate the performance of compression methods by compressing all hidden layers from the input to the output."}
{"question_id": "f117dce3beae4a1fc909bbadebcc235634d017c0", "paper_id": "openreview/NeurIPS-2022-dabt/7w-a8PYPlP", "generation": "The seismic forward modeling applied absorbing boundary conditions."}
{"question_id": "5ac9e91c8d313af9a0771c5e3e46e95d2a7c2315", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "The new evaluation metric proposed in the paper for multi-span question answering is a partial match metric that measures the overlap between predicted answer spans and ground truth answer spans. This metric computes partial retrieved and partial relevant scores based on the length of the longest common substring (LCS) between each predicted span and each ground truth span, normalized by the length of the predicted span and the ground truth span, respectively. Specifically, for each predicted span \\( p_i \\) and ground truth span \\( t_j \\), the partial retrieved score \\( s^{ret}_{ij} \\) is defined as:\n\n\\[\ns^{ret}_{ij} = \\frac{\\text{len}(\\text{LCS}(p_i, t_j))}{\\text{len}(p_i)}\n\\]\n\nand the partial relevant score \\( s^{rel}_{ij} \\) is defined as:\n\n\\[\ns^{rel}_{ij} = \\frac{\\text{len}(\\text{LCS}(p_i, t_j))}{\\text{len}(t_j)}\n\\]\n\nPrecision and recall are then computed by taking the maximum partial retrieved score for each prediction across all ground truth answers and the maximum partial relevant score for each ground truth answer across all predictions, respectively. The final partial match precision, recall, and F1 score are micro-averaged over all questions. This metric allows for a graded evaluation of answer overlap rather than requiring exact span matches, which is particularly suitable for multi-span answers."}
{"question_id": "ec3f80fbff718abc4b5fae665cfdc994570329fb", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "No, a quantitative evaluation of the experiment on Ego4D cannot be performed. The model was not trained on Ego4D and was evaluated for transfer detection using user-provided category names based on visual inspection of the video. The results are presented qualitatively through visualizations showing detection of many novel and common objects under challenging conditions, but no quantitative metrics or numerical performance evaluations are reported for Ego4D."}
{"question_id": "f5c277093cecafb8c4d4588dfbae6b5279b14a79", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "No, the re-annotation was performed on the basic MultiSpanQA dataset containing only multiple-span answers. The expanded dataset was created by adding single-span question\u2013answer pairs and unanswerable instances sampled from the Natural Questions (NQ) dataset, applying the same preprocessing but without additional re-annotation."}
{"question_id": "e774f0bb72932f381463769b74f98a8f360db732", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "GraphVLM is a framework designed to evaluate and adapt Video-Language Models (VLMs) for the task of activity parsing, which is defined as the generation of activity graphs. Activity graphs serve as a unified, dynamic graphical representation of human activities encompassing three hierarchical levels: activity, sub-activity, and atomic action. These graphs capture temporal changes, compositionality, and fine-grained actor-centric interactions in multi-object multi-actor settings.\n\nGraphVLM leverages the MOMA-LRG dataset, which extends the original MOMA dataset by introducing activity graphs as the single universal representation of human activities across all three levels of granularity. Unlike MOMA, which focused primarily on the atomic level, MOMA-LRG uses activity graphs to represent activities at the activity, sub-activity, and atomic action levels, enabling a comprehensive hierarchical video understanding task.\n\nGraphVLM incorporates a video stream, a text stream, and shared video tokenizers across all levels of the MOMA hierarchy to perform activity parsing. It includes a graph-to-language module that converts the structured activity graph annotations into natural language sentences, facilitating the use of pre-trained language models for text encoding. This conversion enables effective leveraging of the natural language capabilities of VLMs while maintaining the structured knowledge embedded in the activity graphs.\n\nThus, GraphVLM operationalizes the activity graphs abstraction introduced in MOMA-LRG by providing a model-agnostic, lightweight approach to adapt and evaluate VLMs on the overarching task of activity graph generation, bridging the gap between graphical representations of activities and language-based video understanding models."}
{"question_id": "fcd1a1d599ae695d923bfabe4f62e5e457ca2de1", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "Yes, the time requirements for training the surrogate model significantly outweigh the benefits in terms of speeding up the evaluation of the dynamic system during inference. The highest computational demand originates from the ML training time, which is orders of magnitude larger than the time required for a single inference. However, once trained, the ML surrogate models, such as the Fourier Neural Operator (FNO), can predict solutions much more efficiently\u2014multiple orders of magnitude faster than classical numerical PDE solvers. This efficiency gain in inference time makes the surrogate models valuable for rapid predictions despite the initial high training cost."}
{"question_id": "601d6dade2b1d6724ae69aafc64a71bafd79062e", "paper_id": "openreview/NeurIPS-2022-dabt/7w-a8PYPlP", "generation": "Yes, the published code includes the algorithms used to generate the data. The data generation pipeline is described in detail, including the synthesis of velocity maps from three different priors (mathematical representations, natural images, and geological reservoirs) and the seismic forward modeling process. The forward modeling algorithm is based on finite difference methods with absorbing boundary conditions and a Ricker wavelet source function, originally implemented in MATLAB and rewritten in Python for efficiency and compatibility with neural networks. The code and related information are available on the public Github repository (https://github.com/lanl/openfwi), ensuring reproducibility of the benchmarks."}
{"question_id": "c908b12fc3ea26161680a836fc0ee29b02fd4e96", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "Region classification in the presence of a large vocabulary of novel categories not present in the image is performed by leveraging the text embeddings of both base and novel categories within a shared embedding space. Specifically, the system replaces the standard fixed K-way classifier with a text-embedding region classifier that computes cosine similarity between the region embedding and the text embeddings of all categories, including novel ones. At inference time, the text embeddings are expanded from the base category set \\( C_B \\) to include the novel category set \\( C_N \\), enabling open-vocabulary detection.\n\nThe region embedding \\( r_b \\) is obtained from the frozen backbone features using ROI-Align and the detector head. The classification scores \\( z(r_b) \\) are computed by applying a softmax over the cosine similarities between \\( r_b \\) and the text embeddings of the base categories plus a \"background\" embedding during training. At test time, the softmax is computed over the expanded set \\( C_B \\cup C_N \\), allowing recognition of novel categories.\n\nAdditionally, the system uses a vision and language model (VLM) pooling layer applied to cropped backbone features to obtain a VLM region embedding \\( v_b \\). The VLM scores \\( w(v_b) \\) are computed similarly by cosine similarity with text embeddings of both base and novel categories. The final detection scores for each region and category are obtained by combining the detector classification scores and the VLM scores via a geometric mean weighted by parameters controlling the influence of VLM scores for base and novel categories.\n\nThis approach enables open-vocabulary recognition at the region level without requiring the novel categories to be present in the image during training, as the frozen backbone features maintain discriminative and locality-sensitive representations transferable to novel classes through their alignment with text embeddings."}
{"question_id": "7cd7d4d944b16a2603aaaf3ebb5628edd813a346", "paper_id": "nlpeer/ARR-22/3cbf91f8e2f7e62be51109688c84a2b424e6018ee3c976fbe55e5331bc9ab2f4c46491d006087aed3be35a3370c945c100bd718e2c1131de19eac3dbe93326bc", "generation": "The sentence aggregation model places delimiters between facts based on whether the neighboring facts should be mentioned separately or aggregated into a single sentence. Formally, given an ordered sequence of facts \\( F^o \\), the model outputs a sequence of sentence delimiters \\( A(F^o) = \\{\\delta_1^o, \\delta_2^o, \\ldots, \\delta_{n-1}^o\\} \\), where each delimiter \\(\\delta_i \\in \\{0,1\\}\\). A delimiter value \\(\\delta_i = 1\\) indicates that the neighboring facts should be separated into different sentences (i.e., no fusion), while \\(\\delta_i = 0\\) indicates that the neighboring facts should be aggregated and fused into a single sentence. The model is trained on synthesized sentences from the WIKI FLUENT corpus, where \\(\\delta_i = 0\\) corresponds to pairs of sentences originally aggregated (resulting from splitting a single sentence), and \\(\\delta_i = 1\\) otherwise."}
{"question_id": "eb715a337474694a5b2fa3212f3936f2979ff998", "paper_id": "openreview/ICLR-2023-conf/kDEL91Dufpa", "generation": "Yes, different contrastive learning methods can be represented using Definition 3.2, which classifies methods into sample-contrastive and dimension-contrastive based on the regularization criterion they minimize.\n\nDefinition 3.2 states:\n- A method is sample-contrastive if it minimizes the contrastive criterion \\( L_c = \\| K^T K - \\text{diag}(K^T K) \\|_F^2 \\), which penalizes similarity between different pairs of samples.\n- A method is dimension-contrastive if it minimizes the non-contrastive criterion \\( L_{nc} = \\| K K^T - \\text{diag}(K K^T) \\|_F^2 \\), which penalizes off-diagonal terms of the covariance matrix of embeddings.\n\nEvidence for this representation includes:\n\n1. **Sample-contrastive methods:**\n   - SimCLR (including variants SimCLR-abs and SimCLR-sq), DCL-sq/abs, and Spectral Contrastive Loss are explicitly shown to minimize \\( L_c \\) or equivalent criteria, thus falling into the sample-contrastive category.\n   - For example, the Spectral Contrastive Loss criterion is expressed as:\n     \\[\n     L_{SCL} = -2 \\sum_{i=1}^N K_{\\cdot,i}^T K'_{\\cdot,i} + \\sum_{i \\neq j} (K_{\\cdot,i}^T K_{\\cdot,j})^2 = -2 \\sum_{i=1}^N K_{\\cdot,i}^T K'_{\\cdot,i} + \\| K^T K - \\text{diag}(K^T K) \\|_F^2,\n     \\]\n     which directly involves the sample-contrastive criterion \\( L_c \\).\n\n2. **Dimension-contrastive methods:**\n   - Barlow Twins, VICReg, and TCR are shown to minimize \\( L_{nc} \\).\n   - For instance, Barlow Twins\u2019 loss can be rewritten as:\n     \\[\n     L_{BT} = \\sum_{j=1}^M (1 - (K K'^T)_{j,j})^2 + \\lambda \\| K K^T - \\text{diag}(K K^T) \\|_F^2,\n     \\]\n     where the second term is exactly the dimension-contrastive criterion \\( L_{nc} \\).\n   - VICReg\u2019s covariance regularization term \\( c(K) = \\sum_{i \\neq j} \\text{Cov}(K)^2_{i,j} = \\| K K^T - \\text{diag}(K K^T) \\|_F^2 = L_{nc} \\) confirms its dimension-contrastive nature.\n\n3. **Theoretical equivalence:**\n   - Theorem 3.3 establishes that the sample-contrastive and dimension-contrastive criteria \\( L_c \\) and \\( L_{nc} \\) are equivalent up to row and column normalization of the embedding matrix \\( K \\), further unifying these methods under the framework of Definition 3.2.\n\nThus, the classification of contrastive learning methods into sample-contrastive and dimension-contrastive categories using Definition 3.2 is supported by explicit formulations of their loss functions and theoretical analysis."}
{"question_id": "e1369f11b53bb858522bacf4bf2e9d8448dc1ef5", "paper_id": "openreview/ICLR-2022-conf/EnwCZixjSh", "generation": "It is important for the metric to be insensitive to the number of layers when evaluating rank correlations because metrics computed using different GIN (Graph Isomorphism Network) configurations, including variations in the number of layers, should produce consistent and comparable evaluations of generative models. Insensitivity to the number of layers ensures that the metric's performance and the resulting rank correlations are stable and not unduly influenced by architectural hyperparameters of the neural network used for feature extraction. This stability allows for objective comparison across models and experiments without bias introduced by the choice of network depth, facilitating reliable assessment of fidelity and diversity in graph generative models."}
{"question_id": "3e6d53b8861714d6727e6f1a924eb2046baac6a7", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "When novel categories with large vocabularies are added to the set of candidate categories, the accuracy of the model on novel categories (measured by Mask AP r) remains stable or is not compromised. Specifically, increasing the feature pyramid capacity, which enhances the representation learned upon frozen backbone features, benefits standard detection on base categories without degrading open-vocabulary detection performance on novel categories. For example, enlarging the feature pyramid leads to improvements on all categories, including a slight improvement of 0.1 AP r on novel categories. This indicates that the model maintains or slightly improves accuracy on novel categories even as the vocabulary size increases."}
{"question_id": "b0767779541047ab4deb8c71f900288615ddd5a7", "paper_id": "openreview/ICLR-2022-conf/5hLP5JY9S2d", "generation": "The Vision Transformer (ViT) model demonstrates better generalization to the open-set scenario on ImageNet compared to other methods primarily due to its architectural characteristics and training behavior. Despite having a large number of parameters (86 million) and possessing few inductive biases\u2014specifically, it does not use convolutions\u2014ViT does not overfit its representation to the training classes. This lack of overfitting allows ViT to maintain strong performance in identifying semantic novelty in open-set recognition tasks. The superior open-set recognition (OSR) performance of ViT supports findings that purely attention-based vision models benefit from better generalization capabilities. Additionally, ViT's strong closed-set performance correlates positively with its open-set performance, further contributing to its effectiveness in OSR on ImageNet."}
{"question_id": "5420a636705116e4e99d17572011f028d54a72b2", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "Prior work related to this paper has primarily used classical statistical error measures such as the root-mean-squared-error (RMSE) over the whole domain and, in some cases, PDE-motivated variants like the RMSE of the gradient. Measures based on properties of the underlying physical systems have been lacking in these prior benchmarks."}
{"question_id": "0dcfbcf7b77777639a682294aaf99c3fff25cd20", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The authors ensure that the natural language sentences produced from the \"ground truth\" activity graphs accurately describe the scene by converting all levels of the MOMA-LRG activity graph hierarchy into natural language through a graph-to-language module. This module uses structured templates tailored to each level of the hierarchy: \n\n- At the activity level, class names are represented as nouns or prompted phrases (e.g., \"A video of [CLS_NAME]\").\n- At the sub-activity level, class names are expressed as descriptions in the present continuous tense, following a narration style.\n- At the atomic action level, predicates are tagged with [src] (source) and [trg] (target) templates to form grammatically correct present continuous sentences. For example, the predicate \"touching\" is represented as \"[src] touching [trg]\", which, given entities such as [src]=person and [trg]=table, produces the sentence \"A person is touching the table.\"\n\nThis systematic and templated conversion ensures that the generated natural language sentences are precise, grammatically correct, and semantically aligned with the entities and relationships annotated in the activity graphs, thereby accurately describing the scene."}
{"question_id": "4b00407f8b0e58aafdaee12f078de8b313872f04", "paper_id": "openreview/ICLR-2022-conf/8uz0EWPQIMu", "generation": "Yes, the proposed alternate method differs from the original linear model by Dalvi et al. (2019) in how neurons are ranked. The original algorithm distributes neurons equally among labels, assigning the same number of neurons to each label at each portion of the ranking regardless of the actual importance of neurons for that label. This can result in non-important neurons being ranked higher than important ones. In contrast, the alternate method computes, for each neuron, the mean absolute value of the weights associated with it and sorts neurons by this value from highest to lowest. This approach empirically provides better results and is more suitable for large label sets, as it does not enforce equal distribution among labels but ranks neurons purely based on the magnitude of their associated weights."}
{"question_id": "cd38abc68b46b12d953fddc8838eb77978963fca", "paper_id": "openreview/ICLR-2023-conf/kDEL91Dufpa", "generation": "SimCLR and DCL are defined as self-supervised learning criteria that operate on normalized embeddings, using cosine similarity to measure similarity between pairs of samples. Specifically, their loss functions are given as follows:\n\n- **DCL (Decoupled Contrastive Learning) criterion:**\n\n\\[\nL_{\\text{DCL}} = \\sum_{i=1}^N -\\log \\frac{e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau}}{\\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau}} = \\sum_{i=1}^N -\\frac{K_{\\cdot,i}^T K'_{\\cdot,i}}{\\tau} + \\log \\left( \\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau} \\right),\n\\]\n\nwhere \\(K\\) and \\(K'\\) are column-wise \\(\\ell_2\\)-normalized embedding matrices, \\(N\\) is the batch size, and \\(\\tau\\) is a temperature parameter.\n\n- **SimCLR (Simple Framework for Contrastive Learning of Visual Representations) criterion:**\n\n\\[\nL_{\\text{SimCLR}} = \\sum_{i=1}^N -\\log \\frac{e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau}}{e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau} + \\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau}} = \\sum_{i=1}^N -\\frac{K_{\\cdot,i}^T K'_{\\cdot,i}}{\\tau} + \\log \\left( e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau} + \\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau} \\right).\n\\]\n\nBoth criteria rely on maximizing the similarity of positive pairs (indexed by \\(i\\)) while minimizing the similarity of negative pairs (indexed by \\(j \\neq i\\)) using cosine similarity of normalized embeddings. Proposition 3.1 considers these definitions in the limit of an infinite number of negative samples."}
{"question_id": "5c090b48e2d8b39f413f602a716b92676b7e7ba7", "paper_id": "openreview/ICLR-2023-conf/8efJYMBrNb", "generation": "The provided information does not explicitly mention whether the baseline multiple sequence alignment algorithms (ClustalW, DIALIGN, MAFFT, T-Coffee, PRANK, MUSCLE) have adjustable parameters that could be tuned to improve their performance. However, one reference (Rubio-Largo et al., 2018) cited in the references section discusses \"Swarm intelligence for optimizing the parameters of multiple sequence aligners,\" which implies that these aligners do have parameters that can be optimized. This suggests that baseline aligners possess adjustable parameters that can be tuned to enhance alignment accuracy.\n\nIn contrast, BetaAlign is described as a deep-learning-based method trained on simulated data reflecting specific evolutionary dynamics, allowing it to personalize the alignment process without requiring manual parameter tuning at the development stage. The discussion emphasizes that BetaAlign's performance depends on the realism of the simulation used for training rather than on manual parameter adjustments.\n\nTherefore, while the baseline aligners likely have adjustable parameters that can be optimized to improve performance, BetaAlign relies on training data and simulation realism rather than parameter tuning."}
{"question_id": "1eafcdeb90458c749f5b2e6dcdaaa06a4ba58abd", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "The phrase \"but train the detector head with \\( r(\\cdot) \\) online in a single stage\" refers to the training approach used in the F-VLM method for open-vocabulary object detection. Specifically, it means that during training, only the detector head component of the model is trained, while the backbone vision and language model (VLM) remains frozen (i.e., its parameters are not updated). The detector head, which includes components such as the region proposal network (RPN), feature pyramid network (FPN), and Mask R-CNN heads, is randomly initialized and trained directly on the frozen features extracted by the pretrained VLM backbone.\n\nThe term \"online\" indicates that the detector head is trained in a standard end-to-end manner during the training process, without requiring separate pretraining or multi-stage pipelines such as knowledge distillation or detection-tailored pretraining. \"Single stage\" emphasizes that this training occurs in one unified phase, rather than multiple sequential stages.\n\nIn summary, the phrase means that the detector head is trained directly and solely on the frozen backbone features during a single training phase, enabling efficient and simplified training while preserving the open-vocabulary classification ability of the pretrained VLM."}
{"question_id": "dd594c4d0897a3c1259ae3d2d2d23859fbb96f04", "paper_id": "nlpeer/F1000-22/10-637", "generation": "The purpose of the saliva sample was to validate the smoking status of participants. Participants who provided a saliva sample to confirm their smoking status were given an additional \u00a330 voucher as an incentive."}
{"question_id": "127423c37403d9d9d34a21c17bfb33234b5f944a", "paper_id": "openreview/ICLR-2023-conf/tYIMtogyee", "generation": "Yes, the authors used a validation set to tune the hyperparameters on the QM9 dataset. They tuned hyperparameters, including the noise scale for denoising during pre-training and fine-tuning, on the HOMO target using the validation set and then kept these hyperparameters fixed for all other targets."}
{"question_id": "41338acb67bcf333b2817c15b8b74290fb3d9327", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "The provided information does not report any experiments or results involving downstream tasks such as natural language inference, question answering, or semantic role labeling using an encoder transferred from language 1 to language 2 without any parameter updates. The study focuses primarily on dependency parsing and language modeling tasks to evaluate the transferability of pretrained encoders."}
{"question_id": "a8466adf7868015b87e7447c11f576b29d121012", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The phrase \"environment's health\" refers to the overall well-being and sustainability of the natural environment, which is closely related to human health aspects. It encompasses the condition of environmental systems and resources that support life, and recycling is adopted as a measure to preserve and protect these systems from degradation and environmental hazards."}
{"question_id": "46780f3f8ab86a46782f68b7ca66e5e1438afc01", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "An image's adversarial distance radius \\( R' \\) was considered \"too big\" if it was more than 33% larger than the certified radius \\( R \\) of the smoothed classifier \\( g \\). Images with such large \\( R' \\) values were excluded from the analysis to avoid trivial attacks where the image is attackable for most classes on \\( g \\). This exclusion criterion was applied to ensure that the selected adversarial distance \\( R' \\) was neither too small nor too large, maintaining a meaningful balance for the attack problem."}
{"question_id": "d7f32782ecbb103a3971b1e3a918576549b44723", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The motivation for building provably robust adversarial examples arises from the limitations of existing adversarial attack methods, which typically produce only single concrete adversarial examples whose effects can often be mitigated by current defenses. Existing robust adversarial examples are mostly empirical and lack formal guarantees, meaning their robustness is not uniform and can be misleadingly overestimated. Provably robust adversarial examples address this by defining large connected input regions guaranteed to contain only adversarial examples under a set of perturbations, such as pixel intensity changes and geometric transformations. This provability ensures uniform robustness throughout the region, providing a more reliable and effective method for constructing adversarial examples that are invariant to real-world perturbations and more resistant to state-of-the-art defenses."}
{"question_id": "fef7c2c7a69370b666710942e3d931819e7bc02d", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "The accuracy of the surrogate model directly impacts the accuracy of downstream tasks such as inverse problem solving. Specifically, surrogate models like the Fourier Neural Operator (FNO) that provide more accurate forward predictions also yield better performance in inverse inference tasks, where unknown initial conditions are estimated by minimizing the prediction error of the surrogate over a time horizon. For example, FNO outperforms U-Net in predicting initial conditions in inverse problems, indicating that higher surrogate accuracy leads to more precise downstream estimations. Conversely, limitations in surrogate accuracy, such as increased error in FNO with decreasing diffusion coefficients or in high-frequency regimes, degrade the reliability of downstream tasks. Additionally, the inability of current ML models to extrapolate accurately beyond the training time steps further constrains downstream task accuracy. Therefore, the fidelity of the surrogate model's approximation of the forward propagator is critical for achieving accurate and stable results in subsequent scientific machine learning applications."}
{"question_id": "94974352b0e42eb1b459e4b85aea1ca6ddb9b713", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "Yes, the robust regions proposed in this paper explicitly include physical distortions that may be encountered in real-world cases, specifically geometric transformations such as rotations and translations. The method defines adversarial examples robust to geometric perturbations by considering a convex region in the geometric parameter space, which captures a range of such transformations. This approach allows the generation of provably robust adversarial examples that remain adversarial under these real-world geometric changes.\n\nThe paper demonstrates that these provably robust regions are large, containing on the order of 10^599 adversarial examples for geometric perturbations, indicating that robustness to such physical distortions is not only possible but can be guaranteed over extensive input regions. The method relies on certification techniques (e.g., DeepG combined with DeepPoly) to ensure that all images within these regions, corresponding to a continuous range of geometric transformations, are classified as the adversarial target class.\n\nTherefore, the robust regions constructed by the proposed method do contain physical distortions relevant to real-world scenarios, and this robustness is guaranteed over large connected regions in the input space."}
{"question_id": "8397ceb9d91201c9f2eb30de3c87e1e8243a827d", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The conceptual model presented in Figure 2 is an overview of the Edcraft Gamified Learning (EGL) activity flow. This model details the two-level online gamified recycling craft activity conducted during the COVID-19 pandemic lockdown. Participants, youths aged 16\u201323, engaged in crafting recyclable items through game elements such as levels, points, leaderboards, and rewards. The activity involved watching video tutorials, completing crafting tasks at two increasing difficulty levels, and having their crafts evaluated and ranked by art teachers. Certificates and prizes were awarded to top-ranked participants. This gamified learning process is designed to motivate youths intrinsically and extrinsically, fostering engagement and recycling intention through competitive and challenging game elements."}
{"question_id": "f87855d105235224a2584b0e0716b794ef647705", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "Yes, the authors took specific steps to address jittering caused by alignment in the processed videos. They first detected facial landmarks for each frame using a single-frame face alignment method, which inherently introduces temporal inconsistency. To mitigate this, they applied a low-pass Gaussian filter to smooth the estimated keypoints before warping the images. Additionally, after estimating camera poses for each video frame via deep face reconstruction, which also introduces temporal inconsistency, they again applied the low-pass Gaussian filter to smooth the camera pose results. These smoothing steps help reduce jittering and improve temporal consistency in the aligned video frames."}
{"question_id": "ce87b952cfde98f1de69d4c860537a4d3989c67a", "paper_id": "nlpeer/F1000-22/11-9", "generation": "The study analyzed a total of 30,229 SARS-CoV-2 genome sequences with collection dates ranging from 2020-01-01 to 2021-03-21. These sequences were downloaded from the GISAID database and filtered to include only high-quality genomes, defined by criteria such as complete genome coverage, less than 1% unresolved nucleotides (Ns), less than 0.05% unique amino acid mutations not seen in other sequences, and no unverified insertions or deletions. Sequences with base calling errors, unresolved nucleotides, or gaps were omitted.\n\nThe geographical distribution of these SARS-CoV-2 sequences was summarized in a table and deposited as extended data on Figshare (https://doi.org/10.6084/m9.figshare.19721716.v1). The dataset includes multiple sequence alignment files divided into four time periods covering the entire date range, also available on Figshare.\n\nRegarding genetic nomenclature and variants of concern (VOC), the frequency percentages of the top 10 nonsynonymous mutations were obtained from COVID CG, which associates these mutations with primary lineages linked to past and present VOCs. This information is presented in Table 3 of the study. The study also discusses co-mutation analysis and the relevance of some identified mutations to newer variants in the discussion section.\n\nIn summary, the 30,229 sequences represent a globally distributed, high-quality dataset of SARS-CoV-2 genomes collected over more than a year, with detailed mutation frequency data linked to known variants of concern."}
{"question_id": "d3ce17368ff699857360c15972ad48cb235350b8", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "Setting the dimension of the word vectors and discourse vectors to 10 empirically results in the entire token distribution approximating the Zipfian distribution because this dimensionality balances the representation capacity of the vectors such that the probability distribution over tokens, defined by the log-linear model \\( p(w|s) \\propto \\exp(\\vec{c}_s \\cdot \\vec{v}_w) \\), naturally exhibits a frequency-rank relationship similar to Zipf's law. In other words, with 10-dimensional vectors, the interaction between the discourse vector (representing sentence topic) and the word vectors produces non-trivial cooccurrence patterns and token probabilities that align with the inverse proportionality to rank characteristic of Zipfian distributions."}
{"question_id": "77d9dbb3a3af5156b369e66924d4bcf14f794893", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The criteria used to evaluate \"provability\" in the paper is based on the output of a verifier \\( V \\) applied to the input region \\( U \\). Specifically, the verifier \\( V \\) returns a non-negative value \\( e \\) if and only if the region \\( U \\) is provably robust, meaning that all points within \\( U \\) are guaranteed to be adversarial. Algorithm 1 in the paper terminates and returns a provably robust region \\( U \\) only when the verifier certifies this non-negativity condition, ensuring that the region is guaranteed to be adversarial rather than empirically verified. This formal verification distinguishes provably robust adversarial examples from prior empirical approaches that do not offer such guarantees."}
{"question_id": "df9756e054d7db2937ebc51e1ed8477345e57387", "paper_id": "nlpeer/COLING2020/341", "generation": "In the context of the paper, the logical atom \"nsubj:noun-verb-obj:noun\" represents a single-link property extracted from Universal Dependencies treebanks that encodes the relative frequency of a specific syntactic construction in a language. Specifically, it denotes the construction where a verb (head) has two dependents: a nominal subject (nsubj) and a nominal object (obj). The notation indicates the dependency relations and part-of-speech tags involved, with \"nsubj:noun\" referring to a nominal subject dependent and \"obj:noun\" referring to a nominal object dependent of the verb. This atom quantifies how often this particular syntactic pattern occurs in the language's corpus data, serving as a variable in the language\u2013property matrix used for evaluating typological universals."}
{"question_id": "273390ef5c449b8574f4d7b6e46f332e7152a15d", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "The intuition and motivation behind the head-to-tail method in section 3.2.3 stem from the observation that words in natural language sentences often have dependency relations, where the presence of a certain word can predict another word (e.g., the verb \"am\" always cooccurs with \"I\"). The study adopts a dependency-based latent structure to model these relations, hypothesizing that pretraining on data with such dependency relations will induce an inductive bias in language models toward identifying relations between tokens in the input. This inductive bias is considered important for processing natural language.\n\nSpecifically, the head-to-tail method pairs half of the tokens in the vocabulary as \"heads\" with the other half as \"tails,\" representing simple dependency relations. These pairs always appear together in a sentence, expressing dependency arcs. This approach differs from previous Parenthesis languages by representing heads and tails as different tokens, reflecting the natural language property that dependency relations usually hold between different words. The method aims to capture the structural property of natural language dependencies more faithfully, which is expected to benefit the transferability of pretrained encoders to natural language tasks."}
{"question_id": "fc938634e35cc53e7b6cb50564929eabb0fc7afe", "paper_id": "egu/esurf/11-917-2023", "generation": "\"Production versus depth curves\" refer to the non-linear relationship between the growth rate of carbonate producers (such as different coral species) and water depth. These curves represent how carbonate production rates vary as a function of water depth for each carbonate sediment class. In the Dionisos forward stratigraphic model, each carbonate producer is classified under specific sediment classes, and a user-defined production-versus-depth curve is assigned to each class to capture this depth-dependent growth behavior. These curves streamline the modeling process by providing growth rate profiles for frequently used carbonate sediment classes, allowing the model to simulate carbonate production rates that change with depth rather than assuming constant rates."}
{"question_id": "bc6f50621da1a65a6e46211a4f48751a6da35304", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The paper defines the concept of an \"adversarial L2 ball\" in the context of smoothed classifiers as follows: For a given adversarial attack \\(\\tilde{x}\\) on the smoothed classifier \\(g\\), the adversarial L2 ball is the radius \\(R_{\\text{adv}}\\) around \\(\\tilde{x}\\) within which all points are certified by the CERTIFY procedure to have the same adversarial class as \\(\\tilde{x}\\) on \\(g\\). Formally, \\(R_{\\text{adv}}\\) is the radius of an L2 ball centered at \\(\\tilde{x}\\) such that for all points \\(x'\\) within this ball, \\(g(x') = g(\\tilde{x})\\), where \\(g(\\tilde{x}) \\neq g(x)\\).\n\nThis means that the adversarial L2 ball is not defined around the original input \\(x\\), but rather around the adversarial example \\(\\tilde{x}\\). The key point is that within this ball, the smoothed classifier \\(g\\) consistently predicts the adversarial class assigned to \\(\\tilde{x}\\), which differs from the original class \\(g(x)\\). Thus, every sample within the adversarial L2 ball shares the same adversarial classification as \\(\\tilde{x}\\), confirming the robustness of the adversarial example \\(\\tilde{x}\\) to small perturbations within that radius.\n\nIn summary, the adversarial L2 ball is a region around the adversarial example \\(\\tilde{x}\\) where the smoothed classifier's prediction remains the adversarial class, ensuring that the adversarial example is robust to perturbations within that ball. This differs from the expectation that samples within the ball should differ in classification from \\(x\\); rather, the ball is centered at \\(\\tilde{x}\\), and all points inside it share \\(\\tilde{x}\\)'s adversarial classification."}
{"question_id": "4006c4e45f822e15f9ccc27df354f3bcd8298509", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "Using alignment for video preprocessing, as opposed to using a fixed cropping window, can introduce temporal inconsistency in the processed video frames. This is because the alignment process involves detecting landmarks for each frame independently using a single-frame face alignment method, which does not guarantee temporal smoothness across frames. To mitigate this temporal inconsistency, a low-pass Gaussian filter is applied to smooth the estimated keypoints before warping the images. Additionally, the camera pose estimation for each video frame, which is part of the preprocessing, also introduces temporal inconsistency and is similarly smoothed using a low-pass Gaussian filter. Therefore, while alignment enables more precise spatial normalization of facial features across frames, it requires additional smoothing steps to address the temporal inconsistency that arises from frame-wise independent processing."}
{"question_id": "2a76dc6fa246d4afcfe9aea7050a47aa3949d755", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "No, the camera pose is not concatenated with the latent code in the same way as the EG3D method. Instead, PV3D conditions the generator on pre-estimated camera pose sequences separately from the latent codes. The appearance latent code \\( z_a \\) and motion latent code \\( z_m \\) are processed independently, with the appearance code projected into an intermediate appearance code \\( w_a \\) and the motion code combined with timestep information to produce intermediate motion codes \\( w_m \\). The camera pose is used to condition both the mapping network and rendering process, but PV3D employs a specific camera conditioning strategy to alleviate motion ambiguities and improve temporal consistency. This includes sharing the same camera pose in the mapping network during inference to maintain temporal consistency, rather than concatenating camera pose directly with the latent code as in EG3D."}
{"question_id": "9be6e9b8ef3f3db6f29bafd74243978f78f2f657", "paper_id": "egu/esurf/11-917-2023", "generation": "The phrase \"actual exposed MIS 5e facies are lacking\" refers to the observation that, within the synthetic well logs produced by the Dionisos model, the stratigraphic sequences corresponding to the Marine Isotope Stage 5e (MIS 5e) reef facies are sometimes absent or underrepresented despite the presence of preserved exposed fossil MIS 5e reef facies observed in the field at Lembetabe. This discrepancy arises because, although the model deposits MIS 5e reef facies at certain locations (e.g., Well C), the timing, amount, and preservation of these facies vary significantly across different sea level scenarios. Additionally, multi-meter sections of lithology deposited during the MIS 5e are often removed by subaerial erosion during the long exposure times between the MIS 5e highstand and the Holocene, leading to a \"preservation bias\" in the modeled stratigraphic record. Consequently, the synthetic stratigraphic record may lack continuous or well-preserved MIS 5e facies even though such facies are physically exposed and documented in the fossil record."}
{"question_id": "d88d8f911add3ead1f649741f7c22700199f9094", "paper_id": "nlpeer/ARR-22/ed81d66dc55496061fd4c97530be7b966d2e734e8435092b2c0c6764f54ea145e62f67555e480d996aebb93772e0de2877ccaad504e0f2228a1395c68d157eda", "generation": "The provided chunks do not contain any explicit information regarding the public availability of the code and dataset associated with the paper. Therefore, it cannot be determined from the given content whether the code and dataset will be made available to the public."}
{"question_id": "73faf71e5e746272235608cd46a8fda2b309ef70", "paper_id": "openreview/ICLR-2023-conf/tYIMtogyee", "generation": "The authors refer to tuning \"approximately\" 5 values on the QM9 dataset in the context of selecting optimal noise scales for denoising during pre-training and fine-tuning. Specifically, they tuned over a discrete set of noise values {0.005, 0.01, 0.02, 0.05, 0.1} to determine the best noise level for the HOMO energy target. This tuning process involved evaluating these roughly five candidate noise values to identify the most effective one, which was then fixed for all other targets in QM9."}
{"question_id": "9373d254f956bcbffe53a9ba10531f5102ecdb83", "paper_id": "openreview/ICLR-2023-conf/dSYoPjM5J_W", "generation": "The authors justify the claim that gradient-based attacks are responsible for the distribution shift between training and testing data in adversarial attacks by analyzing the location and nature of the perturbations introduced by these attacks. They observe that gradient-based methods predominantly modify the graph structure around training nodes rather than uniformly across the graph, which leads to an effective increase in the distribution shift between training and testing node distributions.\n\nSpecifically, gradient-based attacks treat the adjacency matrix as a parameter and modify it via the gradient of the attack loss. The choice of surrogate loss function and the method of gradient computation critically influence the distribution of adversarial edges. When the attack loss focuses on training nodes (L_train), perturbations concentrate on edges involving training nodes (Train-Train and Train-Test), leaving the Test-Test edges largely unchanged. Conversely, when the loss focuses on testing nodes (L_self), perturbations are mainly around testing nodes.\n\nMetaAttack, a gradient-based method, uniquely adapts its perturbation distribution by employing a meta-gradient computation that accounts for the training process. This adaptivity allows MetaAttack to identify and modify the more easily perturbed distributions in both training and testing sets, thereby significantly increasing the distribution shift. Theoretical analysis (Theorem 4.1) supports that perturbations around training nodes enlarge the distribution shift more effectively, especially when the training set is small.\n\nFurthermore, the authors formalize the distribution shift in graph adversarial attacks as the Kullback-Leibler divergence between the conditional distributions of perturbed features given labels in training and testing sets. They argue that the effectiveness of gradient-based attacks fundamentally arises from their ability to increase this distribution shift, which biases the model and degrades its performance.\n\nIn summary, the justification is grounded in empirical observations of perturbation locations, theoretical formulation of distribution shift, and the unique gradient computation strategy of MetaAttack that collectively demonstrate how gradient-based attacks induce and exploit distribution shifts between training and testing data."}
{"question_id": "1fd3a7fbc973d1042fee519c269028acdbb1ccec", "paper_id": "nlpeer/COLING2020/341", "generation": "The antecedent is included in the sum in #6 because the property values represent relative frequencies of constructions, and the sum accounts for all alternative word orders that are mutually exclusive but collectively exhaustive for the given dependency relations. Specifically, the sum in #6 combines the frequencies of multiple word order variants involving the verb, subject, and object (e.g., verb-nsubj:noun-obj:noun, nsubj:noun-verb-obj:noun, and nsubj:noun-obj:noun-verb) to capture the total presence of these alternative orders in a language. This approach reflects the real-valued nature of the variables, which express degrees of relative frequency rather than binary presence or absence. Using only a single implication such as \"verb-nsubj:noun-obj:noun \u21d2 nsubj:noun-verb-obj:noun\" would ignore the distribution of other alternative orders and fail to represent the full range of word order variation relevant to the universal being tested. Thus, the sum provides a more comprehensive and accurate measure of the linguistic property under investigation."}
{"question_id": "a8d6ed01ff1866040e47a7082ff97ea95a6edd03", "paper_id": "egu/esurf/11-917-2023", "generation": "Yes, there is a scientific reason for the Greenland Ice Sheet (GrIS) to start melting before the Antarctic Ice Sheet (AIS) beyond the convenience of modeling two distinct peaks. The G2A5 scenario described in the study separates the contributions of GrIS and AIS melt, with GrIS melting first and contributing 2 m to sea level rise over 126\u2013124 ka, followed by AIS melting later at 118 ka contributing 5 m. This scenario represents possible discrepancies in GrIS\u2013AIS stability and variable hemisphere-specific climate fluctuations, reflecting differences in the timing and stability of the ice sheets driven by regional climate variations (e.g., Govin et al., 2012; Stone et al., 2016). Thus, the staggered melting sequence is consistent with paleoclimate interpretations and fossil reef evidence, not merely a modeling convenience."}
{"question_id": "174eff40340ae0616a2328f75efd8cf8431b3150", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The conceptual model in Figure 2 was developed through a qualitative study involving an online gamified recycling activity called Edcraft Gamified Learning (EGL) conducted during the COVID-19 pandemic lockdown. The study engaged 100 youths aged 16\u201323 from three institutions in Selangor, Malaysia, who participated in two levels of crafting activities using recyclable materials, with game elements such as levels, points, leaderboards, certificates, and prizes to motivate participation.\n\nFollowing the activity, 29 purposively selected students participated in focus group discussions (FGDs). The qualitative data from these FGDs were analyzed using Braun and Clarke\u2019s six-phase thematic analysis approach. Initially, 37 codes were identified from the interview data, which were then sorted into 13 categories and further refined into four main themes: (1) gameful experience, (2) intrinsic motivation, (3) extrinsic motivation, and (4) social influence. These themes were considered the key factors influencing youths\u2019 recycling intention.\n\nThe themes and participant insights were used to verify, enrich, and create a \"flow\" that formed the conceptual framework, linking the gamified learning experience to recycling intention. This framework conceptualized that youths\u2019 motivation to recycle is driven by both intrinsic factors (e.g., satisfaction, happiness, enjoyment) and extrinsic factors (e.g., monetary rewards, promotion, or punishment) evoked by the gameful experience, which also socially influences their family and friends through competitive and challenging game elements.\n\nThus, the conceptual model in Figure 2 was developed by integrating thematic analysis findings from participant responses to the gamified recycling activity, highlighting the interplay of gameful experience, intrinsic and extrinsic motivation, and social influence as factors leading to recycling intention."}
{"question_id": "ff4b45b2af4e13f58512fc1783fc12dd129feb6f", "paper_id": "egu/esurf/11-917-2023", "generation": "Yes, the model incorporates wave erosion. The hydrodynamic boundary conditions for the model include wave energy and wave height parameters derived from global hindcast data, which account for both normal sea state conditions and storm impacts. Storm wave parameters, such as maximum significant wave height, are included at a 10% yearly occurrence rate to approximate storm impacts on coral reef development and long-term stability. Additionally, sediment weathering in the model accounts for mechanical and biological erosion under marine conditions, with a maximum weathering rate of 100 m Myr\u207b\u00b9 used to quantify marine erosion. The model also considers subaerial weathering and dissolution rates, which are incorporated into the overall erosion framework. Thus, wave-driven erosion processes are represented through these hydrodynamic and weathering parameters within the model."}
{"question_id": "70ee52925bfa4ed21c8798964ec74fcdd0f9845f", "paper_id": "egu/esurf/11-917-2023", "generation": "No, the subaerial erosion rate is not equivalent to the maximum weathering rate. The maximum subaerial weathering rate is a user-defined parameter that approximates the rate of weathering under subaerial exposure conditions, influenced by environmental factors such as precipitation and groundwater chemistry. In the Dionisos model, the dissolution rate is incorporated within the maximum subaerial erosion rate, but the subaerial erosion rate itself is a broader term that includes mechanical and biological erosion processes. For example, a maximum subaerial weathering rate of 100 m Myr\u207b\u00b9 is used for Lembetabe, which is lower than some cited values (e.g., 250 m Myr\u207b\u00b9), to avoid overestimation of erosion. Thus, while related, the subaerial erosion rate encompasses more processes and is not strictly equivalent to the maximum weathering rate."}
{"question_id": "7e53c05206cc77e9d6e3b28338d1c85336543244", "paper_id": "egu/esd/14-81-2023", "generation": "The authors determine the accuracy of the CMIP6 climate models in simulating the processes by including a large number of models and multiple members per model to cover a wide range of physically plausible climate responses. They use at least three members with historical and SSP245 data from 13 CMIP6 models, selecting the first three members when more are available. This approach accounts for both within-model internal variability and across-model variability and uncertainty. Additionally, they utilize long unforced control simulations (450-year piControl simulations for 10 out of the 13 models, totaling 4500 years of unforced data) to sample a representative distribution of unforced trends. This extensive sampling increases confidence in assessing whether forced trends lie outside the likely range of unforced trends, thereby evaluating the models' ability to simulate climate processes accurately."}
{"question_id": "e7b7d480aa1076d06dccb8bcb2b7f2b1fd0f9c87", "paper_id": "egu/esurf/11-917-2023", "generation": "The model presented in the study has spatial limitations and is specifically applied to a stable Pleistocene coastline at Lembetabe, southwestern Madagascar. While it provides valuable insights into the sensitivity of the Last Interglacial (LIG) geological record, its direct application to other geographical areas, especially those with more complex glacial isostatic adjustment (GIA) and tectonic settings, requires caution. The study suggests that further analysis and investigation are warranted for other coral-dominated coastlines with diverse GIA and tectonic influences. Therefore, the model cannot be easily applied to other geographical areas without additional adaptation and testing to account for local geological and geophysical conditions."}
{"question_id": "356d231752c38eb7874ebc87c5847c943c9bdf95", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "The authors did not explicitly report observing jittering in the processed videos. Instead, they noted that their PV3D model produces temporally consistent and motion plausible videos with high-quality geometry. They also applied smoothing techniques during preprocessing, such as a low-pass Gaussian filter on estimated keypoints and camera poses, to reduce temporal inconsistency introduced by single-frame face alignment and deep face reconstruction. These steps suggest efforts to mitigate jittering, but no direct mention of jittering as an observed issue is made."}
{"question_id": "eeba4b725b3f8ad526cddce373ff444a591594c9", "paper_id": "nlpeer/ARR-22/df4051328ee57291cf600f10ef67af6872fcef0deb3ff7323b89142b68beb16ea1fbe09c44be8ccca24fe30d67a1e6dbfb715a77384c30fbeb37c362c25c743c", "generation": "The difference in performance between the DICTA test set and the new test set, particularly the disparity between character accuracy (CHA) and word accuracy (WOR) scores, can be explained by several factors related to dataset composition and system training:\n\n1. **Dataset Diversity and Size**: The DICTA test set is relatively small and non-diverse, consisting of 22 documents all originating from Hebrew Wikipedia articles. In contrast, the new test set is larger (approximately 3.5 times the size of DICTA) and more diverse, including texts from multiple sources such as high-quality Wikipedia articles, edited news stories, and user-generated blog posts. This increased diversity introduces a wider variety of vocabulary, styles, and possibly more out-of-vocabulary (OOV) words, which can challenge word-level accuracy more than character-level accuracy.\n\n2. **Out-of-Vocabulary (OOV) Words**: The new test set contains a higher proportion of OOV words relative to the training data. NAKDIMON\u2019s performance on OOV words is substantially worse compared to other systems, especially on the VOC metric, which affects word-level accuracy. Many OOVs are either known words with unseen clitic or possessive marker combinations or proper names absent from the training set but present in dictionaries. This complexity impacts word accuracy more severely than character accuracy, as character-level models can still partially capture pronunciation patterns even when full words are unseen.\n\n3. **System Training and Lexical Resources**: Systems like DICTA and Morfix utilize word-level dictionaries, which help maintain higher word accuracy on datasets similar to their training or dictionary coverage. NAKDIMON, being an unlexicalized character-level model, performs well on character-level metrics but less so on word-level metrics, especially when encountering OOVs or complex morphological constructions not explicitly handled by morphological analyzers used by other systems.\n\n4. **Error Types and Vocalization Ambiguity**: A significant portion of word-level errors are due to vocalization-agnostic dotting, which affects pronunciation but may not be critical for character-level correctness. The new test set\u2019s diversity likely increases such cases, leading to a larger gap between CHA and WOR scores.\n\nIn summary, the larger size and greater diversity of the new test set, combined with a higher incidence of OOV words and morphological complexity, contribute to a more pronounced difference between character-level and word-level accuracy compared to the DICTA test set. The reliance on character-level modeling without explicit lexical or morphological resources further accentuates this effect."}
{"question_id": "6d0c861407de2db08718ca55a383b59284a8e223", "paper_id": "nlpeer/PeerRead-CONLL2016/129", "generation": "The number of selected in-domain sentences for each machine translation system is empirically determined based on experimental results on a separate validation set."}
{"question_id": "aab380aaa605fffff2d765c9cb058cfc03ee1729", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The rationale for only keeping scenarios that cause collisions with at least two algorithms is to ensure that the selected testing scenarios have high transferability across different autonomous driving (AD) algorithms and represent high risk levels. This selection criterion improves both the effectiveness and efficiency of AD evaluation by focusing on scenarios that are safety-critical and challenging for multiple AD algorithms, rather than scenarios that may only affect a single algorithm. This approach helps to identify more universally challenging scenarios that better assess the robustness and safety of AD systems."}
{"question_id": "69d95e16f2f308754136f8e2245592ca5497ff5f", "paper_id": "openreview/ICLR-2023-conf/xYlJRpzZtsY", "generation": "In the ROSCOE training process, the model is trained on triplets consisting of reference reasoning steps, positive hypothesis reasoning steps, and hard-negative hypothesis reasoning steps. Specifically, for contrastive learning, the pairs used are: the context and reference reasoning steps as a positive sample (s, r), and the context and perturbed reference steps as hard-negative pairs (s, h)."}
{"question_id": "5b14dc7213f8e7181d9bf848cef4fb79a7b1ad10", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The performance of scenario generation algorithms is evaluated using the following metrics:\n\n1. Collision Rate (CR): Measures the frequency of collisions occurring in the generated scenarios.\n2. Overall Score (OS): Represents a composite safety-critical scenario generation capability metric, calculated as a weighted sum of various evaluation metrics.\n3. Selection Rate (SR): Evaluates the transferability of the generated scenarios across different autonomous driving (AD) algorithms, indicating how many scenarios cause collisions in multiple AD agents.\n\nThese metrics are reported both before and after scenario selection to assess the effectiveness of the generation algorithms in producing safety-critical scenarios with high risk levels and transferability."}
{"question_id": "6610ad96e462f49d6d8f20fee0cdc6dd8a70175a", "paper_id": "egu/esd/14-81-2023", "generation": "If ridge regression (RR)-based fingerprint construction is not applied, the detection method would be less effective in optimizing the signal-to-noise ratio (SNR). Without RR regularisation, the regression coefficients may overfit the training data, resulting in higher variance and less generalizable fingerprints. This overfitting leads to increased variance in forced response estimates and consequently a lower SNR. The RR method, through regularisation, produces smoother and more homogeneous fingerprints that emphasize regions with higher SNR by down-weighting areas with high internal variability or model disagreement. This improves the robustness and interpretability of the detection fingerprints and enhances the ability to isolate the forced response signal from internal variability. In contrast, less regularised models (e.g., those with minimal cross-validated mean squared error but no or weak regularisation) show lower SNR and later time of emergence of forced changes due to increased variance caused by overfitting. Therefore, not applying RR-based fingerprint construction reduces the effectiveness of detecting forced changes and diminishes the SNR of the detection metric."}
{"question_id": "2f75586071f2de4ab14810c7f2bd7f7b4e143fb6", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "Popular meta-learning frameworks like MAML and Probabilistic MAML are not considered in the experiments due to challenges related to stability and convergence when applying these optimization-based meta-learning methods to sequential latent variable models (SLVMs). Specifically, issues such as vanishing gradients over the complex computation graph of SLVMs make the extension of MAML to these models non-trivial and difficult to implement effectively."}
{"question_id": "7110b14e5ab532a6273415a059f6808204376ee6", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "The sequential neural process (SNP) setting is not included as a comparison because it is originally designed for supervised learning of a regression function over time rather than for forecasting. The work extends SNP to realize a meta-version of the sequential latent variable model (SLVM) formulation as a counterpart to be compared with the presented meta-SLVM, implying that the original SNP formulation does not directly address the forecasting task targeted in the experiments."}
{"question_id": "7c6f77a64467e8275e381a36386d66650b13e832", "paper_id": "nlpeer/F1000-22/11-404", "generation": "The term \"AES\" (aging effect of smiling) specifically refers to the phenomenon that smiling faces are estimated to be older than neutral faces in direct age estimations. It does not refer to the retrospective estimation where a smiling face group is estimated to be younger; rather, the direction of the effect changes depending on the method of estimation, with AES denoting the direct estimation effect of smiling faces appearing older."}
{"question_id": "d07dca8f8e126c43dacdaf145ec4103ef25400f5", "paper_id": "nlpeer/PeerRead-ACL2017/561", "generation": "Not having access to pre-trained embeddings, specifically the pre-trained context embeddings from bidirectional language models (LM embeddings), would significantly reduce the performance of the sequence tagging method. The LM embeddings, which are learned from large-scale unlabeled data, provide rich semantic and syntactic contextual information that the task-specific RNNs trained only on labeled data cannot fully capture. Experiments show that including LM embeddings improves test set F1 scores by approximately 1.06% in named entity recognition (NER) and 1.37% in chunking tasks compared to baselines without LM embeddings. Moreover, removing the LM embeddings or using only the baseline tagger without these pre-trained embeddings results in lower performance. For example, using only LM embeddings without the task-specific RNN leads to a substantial drop in F1 to 88.17, well below the baseline. Therefore, the absence of pre-trained LM embeddings would lead to a notable decrease in accuracy and overall effectiveness of the sequence tagging system."}
{"question_id": "42b9bcc5c85c3d4087d4f57791f953fa732fc625", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The scenarios used in the paper were selected to provide comprehensive coverage of the most representative and challenging pre-crash traffic situations as summarized by the National Highway Traffic Safety Administration (NHTSA). Specifically, the paper considers 8 safety-critical testing scenarios that reflect real-world pre-crash conditions. For each scenario, 10 diverse driving routes were designed to capture variations in surrounding environments, number of lanes, road signs, and other factors that influence vehicle decision-making. This diversity ensures a more comprehensive safety evaluation by accounting for different environmental and road conditions.\n\nFurthermore, the scenario selection process involved generating a large number of raw testing scenarios using four different safety-critical scenario generation algorithms. These scenarios were then tested on four autonomous driving (AD) algorithms, and only those scenarios that caused collisions in at least two AD algorithms were selected. This selection criterion ensures that the chosen scenarios have high transferability across different AD algorithms and represent high-risk situations, thereby improving the effectiveness and efficiency of AD evaluation.\n\nIn summary, the rationale for selecting the scenarios is to cover a broad and representative set of real-world safety-critical situations with diverse route variants, and to focus on scenarios that are demonstrably challenging and transferable across multiple AD algorithms to enhance the robustness and efficiency of safety evaluation."}
{"question_id": "b16ae6d142599eafe257ac83cdf206be914a7a7e", "paper_id": "openreview/ICLR-2023-conf/tyZ1ChGZIKO", "generation": "The Multi-branch Dynamic Selective Frequency module (MDSF) and the Multi-branch Compact Selective Frequency module (MCSF) are concatenated in Figure 1 to leverage their complementary strengths in frequency feature processing for image restoration. MDSF dynamically decomposes feature maps into different frequency components using learnable, theoretically proven filters and applies channel-wise attention to select the most informative frequency parts for reconstruction. It processes features locally with multiple branches of varied filter sizes to enhance frequency diversity. In contrast, MCSF efficiently enlarges the receptive field by employing global and window-based average pooling to obtain frequency maps at multiple scales, modulating these maps with learnable weights without convolution layers, thus maintaining low complexity and enabling embedding at multiple positions.\n\nConcatenating MDSF and MCSF allows the network to combine the dynamic, fine-grained frequency selection and modulation of MDSF with the large receptive field and lightweight frequency recalibration of MCSF. This integration enriches frequency representations by capturing both local dynamic frequency components and broader contextual frequency information, improving the network\u2019s ability to restore images with various degradation scales and characteristics."}
{"question_id": "83065c1670e7bbe6476efd1959f21480a4f3bf69", "paper_id": "nlpeer/ARR-22/eeda0f4ff6c7aef2647481ec15f8ade5a9b32ec32b643e6918a0b8d0ffa1ba44a3f2b9171ba4ba44d5c0faf3199e3bc5f0f740ee31cf48cb966493a96f35ca71", "generation": "In this work, \"knowledge\" is defined as the outcome of assimilating information through learning, encompassing the body of facts, principles, theories, and practices related to a field of work or study. Knowledge components correspond to hard skills, such as knowing a programming language like Python.\n\n\"Skill\" refers to the ability to apply knowledge and use know-how to complete tasks and solve problems. Skills represent the application of knowledge components to specific tasks and include both hard and soft skills.\n\n\"Attitude\" corresponds to soft skills and is considered part of skill components. Attitudes involve the ability to use knowledge, skills, and personal, social, and/or methodological abilities in work or study situations. In annotation, attitudes are treated as skills unless they contain explicit skill or knowledge components, in which case only the skill or knowledge is tagged.\n\nTo summarize, knowledge represents what one possesses cognitively (hard skills), skill is the ability to apply that knowledge practically (including both hard and soft skills), and attitude refers to personal and social abilities that manifest as soft skills within the skill category."}
{"question_id": "d096c58eea777208cfd4ba272dac018b8a808d6c", "paper_id": "openreview/NeurIPS-2022-dabt/TscdNx8udf5", "generation": "The simulation environments were selected to cover a wide range of manufacturing processes, including beer fermentation, atropine production, penicillin manufacturing, monoclonal antibodies production, and a continuous stirred tank simulation. These environments were chosen to model real-world factories with high fidelity, enabling the testing of advanced reinforcement learning algorithms in controlled settings without safety concerns. The goal was to bridge the gap between deep reinforcement learning research and industrial manufacturing by providing realistic, diverse, and challenging environments that reflect actual industrial processes. This selection facilitates research on improving control algorithms for manufacturing plants, aiming for safer, more stable, efficient, and productive process control."}
{"question_id": "587b947b50e65e3caa8174633245ab39edbdb0f0", "paper_id": "egu/esurf/11-33-2023", "generation": "The authors did not conduct analyses using detrended data to isolate the effect of the temperature increase. Their transient model simulations from 1951 to 2019 analyzed the evolution of ground temperatures and ice tables in response to observed air temperature increases over this period, but there is no mention of detrending the data to separate the warming trend from other variability. Instead, they performed spin-up runs using the coldest 10-year period (1962\u20131971) to initialize stable permafrost conditions and then simulated the transient response to climate warming using the full temperature time series."}
{"question_id": "1818a27b73310c09fb736d5e044187417bda0dbc", "paper_id": "nlpeer/ARR-22/eeda0f4ff6c7aef2647481ec15f8ade5a9b32ec32b643e6918a0b8d0ffa1ba44a3f2b9171ba4ba44d5c0faf3199e3bc5f0f740ee31cf48cb966493a96f35ca71", "generation": "The dataset was annotated by domain experts, including a hired linguist who participated in the annotation process. Initially, three annotators, among whom was the linguist, annotated the data, and subsequently, the linguist annotated job postings in larger batches. Thus, the annotation team included expertise both in linguistics and in the domain of the dataset."}
{"question_id": "f5fe5047a045ce5a97066fd72458d8951c846342", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "A total of 27 3D replicas were used in the survey. These consisted of 3 replicas each for 9 distinct types of surface discontinuities identified around the targeted urban areas."}
{"question_id": "aae1c73c7b0de5fb88e34c245782e2ecb4dcb24d", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "The dataset is not balanced across different classes. The taxonomy of surface discontinuity includes five distinct classes: down-steps, up-steps, uncovered drainage, drop-off without handrail, and mixed gradient. Among these, the up-steps and down-steps classes have slightly higher frequency than the others, indicating a minor class imbalance typical of the urban areas sampled. To address this imbalance during model training, a random under-sampling technique was applied to reduce samples from the majority classes, mainly down-steps and up-steps."}
{"question_id": "836969164a688341782ffa72b87f1348ba1ee4ac", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "A total of 27 3D printed replicas were used in the pre-collection stage, constructed as 3 replicas each for 9 distinct types of surface discontinuities."}
{"question_id": "de2dc4d1f8b898e5b34a256294729fe7b46f6fda", "paper_id": "openreview/ICLR-2023-conf/ZrEbzL9eQ3W", "generation": "The board games studied use the following win conditions:\n\n- Connect Four: Players win by connecting four of their tokens in a line.\n- Pentago: Players win by connecting a line of five tokens, with each turn ending with a rotation of one of the four quadrants of the board.\n- Oware: The text does not explicitly state the win condition, but it is implied that the game involves capturing seeds from pits, as the neural network input counts the number of seeds in each pit."}
{"question_id": "cbb83b653ecc965d0b930f4f016e4ff93c485696", "paper_id": "openreview/ICLR-2023-conf/HnSceSzlfrY", "generation": "RPM-Random works poorly in pure cooperation scenarios because it samples policies without maintaining ranks, leading to unstable performance and large variance in evaluation returns. In pure coordination tasks (PC 1-3), this random sampling fails to consistently produce effective coordinated behaviors, resulting in low and highly variable returns. In contrast, in Prisoners\u2019 Dilemma scenarios (PD 1-3), although RPM-Random still underperforms compared to ranked sampling, the impact is less severe, likely because the strategic dynamics of social dilemmas are less sensitive to the precise coordination that ranking facilitates. Therefore, the absence of rank-based policy selection in RPM-Random critically impairs stable coordination required in pure cooperation tasks, whereas its effect is comparatively mitigated in Prisoners\u2019 Dilemma."}
{"question_id": "911fa2b76fba7e5ae58fb8322c5940c42acbd848", "paper_id": "openreview/ICLR-2023-conf/xYlJRpzZtsY", "generation": "The proposed taxonomy of generic reasoning errors is comprehensive in that it covers a broad spectrum of error types encountered in step-by-step reasoning by large language models (LLMs). It includes nine distinct error categories that address both overall reasoning chain quality and fine-grained step-level inconsistencies. These error types encompass grammar, factuality, hallucination, redundancy, repetition, missing steps, coherency, commonsense, and arithmetic errors. The taxonomy is supported by manual analysis of multiple human-judged datasets spanning diverse reasoning tasks such as logical inference, arithmetic, and commonsense reasoning. It has been used to construct diagnostic datasets with synthetic perturbations and to guide expert human annotations, ensuring coverage of a wide range of reasoning errors. Furthermore, the taxonomy is designed to evaluate natural language rationales with alignment to input context and generated explanations, and it differentiates between chain-level and step-level errors for detailed evaluation. While acknowledging that it may not cover all possible reasoning errors, the taxonomy is described as generic and extensible, providing a foundational framework for further research and metric development in reasoning error evaluation."}
{"question_id": "3546db32608ccae0b45d96e051b10a8967437d6f", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "Only one single person was employed to wear the wearable sensor and capture all the image sequences for the survey."}
{"question_id": "9dbf9a9e3f0cc960065255b48616ad3b79759600", "paper_id": "openreview/ICLR-2023-conf/tyZ1ChGZIKO", "generation": "The MDSF module employs a multi-branch design where features are split along the channel dimension into multiple parts, and each part is processed by a decoupler with a different filter (kernel) size. Specifically, MDSF splits the input feature map into multiple groups (branches), and each group is assigned a distinct filter size to provide various local receptive fields. This multi-branch approach allows the module to dynamically decompose feature maps into separate frequency components using learnable low-pass and high-pass filters of different kernel sizes for each branch. The kernel sizes for these decouplers are thus determined by the number of branches and the design choice to apply different filter sizes to each split feature group, enabling diverse frequency separation and enhanced feature representation."}
{"question_id": "5c790742a803e76547d117cf4a77434d1737b5b1", "paper_id": "egu/esurf/11-849-2023", "generation": "Besides temperature and precipitation, several other factors could influence the distribution of glaciers in a given area:\n\n1. **Precipitation Type:** The form of precipitation (snow versus rain) affects glacier mass balance. Rain does not contribute to glacier accumulation, so areas with a higher proportion of rain relative to snow may have reduced glacier growth.\n\n2. **Cloudiness:** Cloud cover modulates the amount of solar radiation reaching the glacier surface, influencing melt rates. Windward sides of mountain ranges tend to have more cloudiness due to orographic lifting, while leeward sides experience cloud dissipation, potentially increasing solar radiation and melt.\n\n3. **Humidity and Lapse Rate:** Variations in humidity can affect the atmospheric lapse rate and sublimation rates. Descending airflows on the leeward side of mountains can reduce humidity, increase the lapse rate, and enhance glacier melt.\n\n4. **Valley Geometry and Topography:** The shape and narrowing of valleys can induce ice elevation feedbacks, where glacier thickening raises ice surface elevation, increasing accumulation area and promoting further glacier growth. This feedback is sensitive to local topography and can cause significant differences in glacier extent.\n\n5. **Spatial Variability in Ablation Efficiency:** Differences in melting efficiency across regions, potentially due to local climatic or topographic factors, can limit glacier growth in some valleys relative to others.\n\n6. **Topographic Shading and Aspect:** Variability in shading due to valley orientation and surrounding terrain can influence local energy balance and melting patterns.\n\nThese factors, individually or in combination, can significantly affect glacier distribution beyond the direct effects of temperature and precipitation."}
{"question_id": "876d1ffd9379695a117eb81936e7bb2b0ffb1e9d", "paper_id": "openreview/NeurIPS-2022-dabt/TscdNx8udf5", "generation": "The baseline algorithms, including PID, MPC, and EMPC, were tuned by varying either the weights or the horizon, or both, until closed-loop trajectories with little to no overshoot and fast response times were obtained."}
{"question_id": "9505c56639fea265e46601d12575e9d9715b9e7a", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "The variational approximation of the latent variable \\( c \\) given the query and support sets \\( D_s^j \\) and \\( x_q^{0:T} \\) is modeled as the distribution\n\n\\[\nq_\\zeta(c \\mid D_s^j \\cup x_q^{0:T}),\n\\]\n\nwhich shares the same meta set-embedding model as the prior \\( p_\\zeta(c \\mid D_s^j) \\) but is conditioned on the augmented set formed by combining the support set \\( D_s^j \\) with the query series \\( x_q^{0:T} \\). This approximation enables inference by incorporating information from both the support and query data."}
{"question_id": "cfbd6962220a29ddfda999443b628e02ebd2d79b", "paper_id": "egu/esurf/11-849-2023", "generation": "The analysis and modeling presented do not explicitly consider the effect of slope orientation (aspect) on solar radiation absorption and its influence on glacier distribution. The glacier flowline model used is one-dimensional and captures variability primarily along the direction of steepest precipitation gradients, neglecting variability across the valleys such as aspect-related melting. The discussion acknowledges that spatially variable drivers of ablation, including topographic shading and potentially cloudiness, could influence glacier mass balance and extent, but these factors were not incorporated in the current model. Therefore, while aspect-related differences in solar radiation absorption could affect glacier distribution, this effect was not directly evaluated or included in the modeling of the Elwha and Quinault glaciers."}
{"question_id": "916923a8909ab06632f575b7f36db3ac70642419", "paper_id": "egu/esd/14-1261-2023", "generation": "The molecular diffusivity \u03ba_m is given as 1.4 \u00d7 10^(-7) m\u00b2/s. The wave-induced diffusivity \u03ba_v varies depending on wave parameters and can be significantly larger than \u03ba_m. Table 1 shows dimensionless values of wave-induced diffusivity \u03ba_v relative to molecular diffusivity \u03ba_m, with \u03ba_v/\u03ba_m ranging from approximately 0.36 to 128.9 across different wave conditions. This indicates that \u03ba_v can be several orders of magnitude greater than \u03ba_m, demonstrating that wave-induced mixing is much more efficient than molecular diffusion under the studied wave conditions."}
{"question_id": "e232c66d9986ff1ac6f532437bd94f0b71a44ca5", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "The dataset collected from ten locations in urban areas of Klang Valley, Malaysia, provides a focused representation of surface discontinuities relevant to blind and low vision (BLV) navigation within these specific urban environments. The sampling was judgmental, targeting areas with a high frequency of hazardous surface discontinuities as identified through consultations with BLV volunteers and local knowledge. The dataset includes nine distinct types of pathway surfaces identified through 3D printed replicas and field measurements, encompassing smooth walkways, steps, drop-offs, ramps, drainage, and mixed gradients.\n\nHowever, the dataset is limited to Malaysian urban environments and may not capture the full diversity of surface conditions found in other regions or countries. The authors acknowledge that the dataset might lack the rich diversity of surface conditions present in other nations and that further expansion is needed to represent more diverse regions beyond the selected sampling locations.\n\nTherefore, while the ten locations are sufficient to represent a targeted variety of surface discontinuities within the sampled Malaysian urban areas, they are not sufficient to comprehensively represent the variety of surfaces in broader or more diverse urban environments globally."}
{"question_id": "6c9381de277251ad2ce40cd39b94a872cbc4126e", "paper_id": "egu/esd/14-81-2023", "generation": "The authors' conclusion about the accuracy of the CMIP6 climate models in simulating forced changes in mean and extreme precipitation is based on the robust global detection of forced signals despite observational disagreement on the magnitude of change. They use a large ensemble of 13 CMIP6 models with multiple members per model to comprehensively cover physically plausible climate responses and to account for both within-model internal variability and across-model variability and uncertainty. The availability of long unforced control simulations (piControl) allows sampling of a representative distribution of unforced trends, increasing confidence in assessing whether forced trends lie outside the likely range of unforced variability.\n\nWhile the study acknowledges disagreement among observational datasets regarding the strength of the forced signal, the methodology ensures that the combined model results are not unduly influenced by any single model's climate sensitivity. The detection and attribution of anthropogenically forced changes are achieved through optimal fingerprinting and spatial aggregation approaches, which consider residual variability.\n\nTherefore, the authors' conclusion about the models' accuracy is indeed grounded in the agreement between observed data and model predictions in terms of the forced signal being distinguishable from residual (unforced) variability, as sampled and characterized by the models' control simulations. This approach supports robust detection of forced changes despite observational uncertainties, indicating that the CMIP6 models reliably simulate the relevant processes when residual variability is properly accounted for."}
{"question_id": "f2e744ebd60bf15d94cd1b9a5cdc7db9f0c4ad93", "paper_id": "openreview/ICLR-2023-conf/ZrEbzL9eQ3W", "generation": "The playing strength of agents is evaluated by restricting them using a fixed number of Monte Carlo Tree Search (MCTS) simulations per move rather than by time limits. Specifically, the number of MCTS steps per move is fixed and used both during training and testing, with a standard value of 300 MCTS steps per move. In experiments examining inference-time compute constraints, the number of MCTS steps is varied inversely with the forward-pass compute costs to enforce a fixed inference-time compute budget. This approach allows tailoring move-selection inference time by adjusting the number of MCTS steps, which does not have to match the number used during training. Time limits are not explicitly used as a constraint for evaluating playing strength."}
{"question_id": "064bd1dff89282732ddcf6c71a98975792d8b3d4", "paper_id": "openreview/ICLR-2023-conf/6xXtM8bFFJ", "generation": "The two-time-scale algorithm, specifically the simultaneous SGDA-RR (simSGDA-RR) and alternating SGDA-RR (altSGDA-RR), can be extended to the mini-batch setting by grouping the component functions into mini-batches of size \\( b \\geq 1 \\), where \\( b \\) divides the total number of components \\( n \\) (i.e., \\( n = bq \\) for some integer \\( q \\geq 1 \\)).\n\nThe extension is formalized in Algorithm 2, which proceeds as follows:\n\n1. **Mini-batch Construction:**  \n   At each epoch \\( k \\), a random permutation \\( \\sigma_k \\) of the indices \\([n]\\) is sampled uniformly without replacement. The indices are partitioned into \\( q = n/b \\) mutually disjoint mini-batches:\n   \\[\n   B_{k,t} := \\{ \\sigma_k(j) : b(t-1) < j \\leq bt, j \\in [n] \\}, \\quad t \\in [q].\n   \\]\n\n2. **Updates Within an Epoch:**  \n   For each mini-batch \\( B_{k,t} \\), the algorithm performs gradient updates on the variables \\( x \\) and \\( y \\) using the averaged gradients over the mini-batch:\n   - For simSGDA-RR (simultaneous update):\n     \\[\n     x_{k,t} = x_{k,t-1} - \\frac{\\alpha}{b} \\sum_{i \\in B_{k,t}} \\nabla_1 f_i(x_{k,t-1}; y_{k,t-1}),\n     \\]\n     \\[\n     y_{k,t} = y_{k,t-1} + \\frac{\\beta}{b} \\sum_{i \\in B_{k,t}} \\nabla_2 f_i(x_{k,t-1}; y_{k,t-1}).\n     \\]\n   - For altSGDA-RR (alternating update):\n     \\[\n     x_{k,t} = x_{k,t-1} - \\frac{\\alpha}{b} \\sum_{i \\in B_{k,t}} \\nabla_1 f_i(x_{k,t-1}; y_{k,t-1}),\n     \\]\n     \\[\n     y_{k,t} = y_{k,t-1} + \\frac{\\beta}{b} \\sum_{i \\in B_{k,t}} \\nabla_2 f_i(x_{k,t}; y_{k,t-1}).\n     \\]\n\n3. **Epoch-wise Update:**  \n   After processing all mini-batches in epoch \\( k \\), the new epoch initial points are set as:\n   \\[\n   (x_{k+1,0}, y_{k+1,0}) = (x_{k,q}, y_{k,q}).\n   \\]\n\n4. **Step Size Scaling:**  \n   The step sizes \\( \\alpha \\) and \\( \\beta \\) are chosen with respect to the mini-batch size \\( b \\), typically scaled linearly by \\( b \\), and satisfy certain conditions to ensure convergence. For example, \\( \\alpha = \\beta / r \\) for some \\( r \\geq 14 \\kappa_2^2 \\), and\n   \\[\n   \\beta = b \\cdot \\min \\left\\{ \\frac{1}{6 L n q} \\sqrt{1 + \\frac{n-b}{n-1} \\cdot \\frac{A}{n}}, \\quad \\text{other terms depending on problem constants} \\right\\}.\n   \\]\n\n5. **Theoretical Guarantees:**  \n   The mini-batch extension preserves convergence properties under the same assumptions as the single-sample case, with convergence rates and bounds adjusted to account for the mini-batch size \\( b \\) and the without-replacement sampling scheme.\n\n6. **Without-Replacement Sampling:**  \n   The mini-batches are mutually disjoint within each epoch due to the random reshuffling of indices, which differs from other mini-batch sampling methods where mini-batches may overlap.\n\nIn summary, the two-time-scale SGDA-RR algorithms are extended to the mini-batch setting by partitioning the data into disjoint mini-batches per epoch, performing gradient updates averaged over each mini-batch, and scaling the step sizes accordingly. This extension maintains the theoretical convergence guarantees and leverages the variance reduction benefits of without-replacement sampling."}
{"question_id": "8192e96a224224e5fc15e03019d0ac65253d1492", "paper_id": "nlpeer/F1000-22/10-838", "generation": "The provided chunks do not contain any information regarding a \u201crange\u201d parameter or its purpose. Therefore, no answer about the purpose of the \u201crange\u201d parameter can be derived from the given content."}
{"question_id": "cf91a671c7d2248b716cf143ff64e032fed4681e", "paper_id": "nlpeer/ARR-22/27046f6633d116b03e48eb94976921b46a86a976ddd2199ef76c90820a95e0d85406daa5ebc9d7b12359157d20f3bd0025e1cbc2c126bb2506f5e83e93f435b5", "generation": "The layer normalization approximation incurs the most significant performance reduction among the approximation components in THE-X. Specifically, it causes an average performance drop of 1.08% on the GLUE benchmark tasks and leads to a 1.9% reduction in F1 score on the token-level named entity recognition (NER) task. This performance degradation is more pronounced in token-level tasks, which require more detailed attention patterns. Overall, the layer norm approximation is identified as the primary source of performance loss in THE-X, contributing more to the reduction than other approximations such as softmax estimation or activation function replacement."}
{"question_id": "e7cb5933a3df86f543cb36cb77b3f41cd7ad4021", "paper_id": "nlpeer/COLING2020/1367", "generation": "A rule-based approach, such as finite-state transducers (FSTs) used for building verb conjugators in Indigenous polysynthetic languages, has the advantage of not requiring large corpora to produce accurate models. This is particularly important because most Indigenous languages lack sufficiently large text corpora to train reliable statistical or neural models. Thus, rule-based methods can effectively model complex morphological phenomena in these languages despite limited data availability, complementing language learning without replacing traditional study methods."}
{"question_id": "27642536b6affc6438fc0e5a5b2ce6b2d5208309", "paper_id": "egu/esd/14-1261-2023", "generation": "The study applies to both nearshore and offshore environments. The numerical model is designed to cover a range of water depths, including shallow-water and deep-water conditions, as indicated by the range of dimensionless wave number parameters kh from 0.5 to 2.0 used in the test cases. This range allows direct comparison with previous work and includes conditions relevant to nearshore (shallow water) and offshore (deeper water) settings. Furthermore, the modelling framework can be modified to simulate pseudo-random ocean waves in large periodic domains, which is characteristic of offshore open-ocean conditions. Thus, while the parameters and wave flume experiments are particularly suited for surfzone or nearshore dynamics, the approach and model are applicable to offshore environments as well."}
{"question_id": "****************************************", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "Yes, the proposed algorithms can be applied to other classes of probability distributions beyond the Gaussian distribution and the banana-shaped source. Experiments were conducted on real-world data from particle physics (Z-boson decay dataset with 16-dimensional vectors) and speech data, where the algorithms obtained tighter sandwich bounds on the rate-distortion (R-D) function compared to similar dimensional Gaussians. Additionally, the algorithms were applied to high-dimensional GAN-generated images with low intrinsic dimension and natural images, demonstrating applicability to complex, structured data sources beyond the Gaussian and banana-shaped distributions."}
{"question_id": "130985a6f0c94e81204c5a5014faa6017dc2a328", "paper_id": "egu/esd/14-1261-2023", "generation": "The problem under investigation is genuinely nonlinear rather than merely weakly nonlinear. The study extends previous analyses based on weakly nonlinear theory by employing a fully nonlinear wavemaker model that admits higher-order wave components and strong nonlinearities. This fully nonlinear approach captures amplitude dispersion, nonlinear wave\u2013wave interactions, and solitary wave propagation, which go beyond the applicability of weakly nonlinear methods. The fully nonlinear model provides a more accurate estimation of the phase-averaged wave velocity field and time-mean velocity, leading to improved predictions of wave-induced mass transport and vertical mixing. The differences between weakly nonlinear and fully nonlinear solutions become significant, especially for waves with higher steepness and larger Ursell numbers, with velocity differences reaching up to 40% in the subsurface layer. These findings demonstrate that the problem involves strong nonlinearity and that higher-order nonlinear terms are essential for reliable modeling of wave-induced vertical mixing."}
{"question_id": "ebd7cf5f3adbc674bd5ce88a563f3fea990873a9", "paper_id": "openreview/NeurIPS-2022-dabt/UrAYT2QwOX8", "generation": "The rationale for training on the first six months of data and validating the models on the last two months is to leverage a temporal splitting strategy that reflects common practice in the fraud detection domain. This approach is used because more recent data tends to be more faithful to the data\u2019s distribution when models are deployed in production. By training on earlier months and testing on later months, the evaluation better simulates real-world conditions where models are trained on past data and applied to future, potentially evolving data distributions."}
{"question_id": "8f0ed4f134911b593527e3793459b6d55faf5923", "paper_id": "openreview/NeurIPS-2022-dabt/-VyJim9UBxQ", "generation": "The copyright for the images used in the paper is held by the individual Reddit users who posted the photos. The dataset complies with the Reddit User Agreement and Reddit API terms of use, which state that \"User Content\" such as photos are owned by the users and not by Reddit. Users posting images are assumed to be the owners of the photography and thus have the right to distribute it. The dataset does not modify the original content and provides tools to access the data under these terms."}
{"question_id": "33ac8263606098fca8bcdc4746cd3f4235387b26", "paper_id": "openreview/ICLR-2022-conf/0jP2n0YFmKG", "generation": "Yes, the new method, Graph Parallelism, demonstrates superior performance compared to existing distributed training methods such as data parallelism and pipeline (model) parallelism. Specifically, Graph Parallelism achieves a weak scaling efficiency of approximately 79% with 8 GPUs for nearly billion-parameter models, and when combined with 32-way data parallelism on 256 GPUs, it maintains a scaling efficiency of 47% relative to a single GPU. In contrast, pure data parallel training with 32 GPUs attains a scaling efficiency of 75% for smaller models, indicating that Graph Parallelism scales better for very large models.\n\nFurthermore, Graph Parallelism outperforms pipeline parallelism for the considered models. This is attributed to better load balancing across GPUs since Graph Parallelism evenly distributes nodes, edges, and triplets of a batch, whereas pipeline parallelism suffers from load imbalance due to varying graph sizes. Additionally, combining Graph Parallelism with pipeline parallelism yields complementary benefits, further enhancing training performance.\n\nIn terms of raw computational throughput, the largest model using Graph Parallelism sustains 3.5 PetaFLOPs on 256 GPUs, and on a single GPU, a 120M parameter model sustains 32 TeraFLOPs, which is about 25% of the theoretical peak FLOPS, indicating efficient utilization.\n\nOverall, Graph Parallelism provides faster and more scalable training for large GNNs modeling atomic simulations compared to existing distributed training methods."}
{"question_id": "3b1176248b0cfc5fb6e1786bb4007f98aa2ac210", "paper_id": "openreview/NeurIPS-2022-dabt/UrAYT2QwOX8", "generation": "The first and second disparities are induced by controlling the generative model sampling process, depending on the group and label, respectively."}
{"question_id": "53c8c64ae66712791ba2a355e4cc97a262c61acc", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "The provided text does not explicitly define the rate-distortion (R-D) function for probability distributions where the expectation is undefined, such as the Cauchy distribution. The standard definition of the R-D function \\( R(D) \\) assumes the existence of expectations with respect to the source distribution \\( P_X \\), specifically the expected distortion \\( \\mathbb{E}[\\rho(X,Y)] \\) and the mutual information \\( I(X;Y) \\), both defined under the joint distribution \\( P_X Q_{Y|X} \\).\n\nFormally, the R-D function is defined as the infimum of the mutual information over all conditional distributions \\( Q_{Y|X} \\) such that the expected distortion constraint is satisfied:\n\n\\[\nR(D) = \\inf_{Q_{Y|X} : \\mathbb{E}[\\rho(X,Y)] \\leq D} I(X;Y),\n\\]\n\nwhere the expectation \\( \\mathbb{E}[\\rho(X,Y)] \\) is taken with respect to the joint distribution \\( P_X Q_{Y|X} \\).\n\nFor distributions like the Cauchy distribution, where the expectation of the distortion may be undefined or infinite, the standard formulation of the R-D function does not directly apply because the distortion constraint \\( \\mathbb{E}[\\rho(X,Y)] \\leq D \\) cannot be evaluated. The text does not provide an alternative or extended definition of the R-D function for such cases.\n\nTherefore, within the scope of the provided information, the R-D function is defined only for sources and distortion metrics where the relevant expectations exist and are finite. No explicit definition or treatment is given for probability distributions with undefined expectations such as the Cauchy distribution."}
{"question_id": "95d0f3eec5444caddab3df7e45aa31db81cabef8", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The operator \\( M_i,CM_i \\) represents the adjacency matrix under Markovian constraints for step \\( i \\) in a constrained random walk on a temporal knowledge graph. Specifically, for a given step \\( i \\) and corresponding Markovian constraints \\( CM_i = \\{ P_i, TR_{i,l+1} \\} \\), the matrix \\( M_i,CM_i \\in \\{0,1\\}^{|E| \\times |E|} \\) is defined such that its \\((x,y)\\)-th entry is\n\n\\[\n(M_i,CM_i)_{x,y} = \\max_{F \\in F_{y,x}} f_{CM_i}(F) = \\max_{F \\in F_{y,x}} f_{P_i}(F) f_{TR_{i,l+1}}(F, (e_s, r, ?, I))\n\\]\n\nwhere \\( F_{y,x} \\) is the set of facts from entity \\( e_y \\) to entity \\( e_x \\), and \\( f_{P_i} \\) and \\( f_{TR_{i,l+1}} \\) are filtering operators for the predicate and temporal relation constraints, respectively. The entries are set to 1 if there exists a fact satisfying these constraints, and 0 otherwise.\n\nThis operator encodes the allowed transitions between entities at step \\( i \\) under the Markovian constraints, enabling the identification of all paths between entity pairs that satisfy these constraints during the random walk process."}
{"question_id": "09c36735b520089d4936e6966157cb10f8f1ed0e", "paper_id": "openreview/ICLR-2022-conf/iulEMLYh1uR", "generation": "Yes, when scaling the depth (number of layers) or width (hidden dimension) of the Vision Transformer (ViT) models, all other hyperparameters are kept fixed based on the default values given by the referenced papers. This is explicitly stated in the experiments where depth and width are scaled while maintaining other hyperparameters constant."}
{"question_id": "1be49494b9a1c964df99b3dabe0af0bfdc970713", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The methodology presented, Meta-EGN, applies meta learning to unsupervised learning for combinatorial optimization (CO) by viewing each training instance as a separate task. Specifically, it treats each instance as a pseudo-new instance and learns a good initialization that can be quickly fine-tuned to achieve instance-wise good solutions. This approach aligns with the meta learning framework, particularly model-agnostic meta learning (MAML), which is designed to learn parameter initializations that facilitate fast adaptation to new tasks.\n\nSince Meta-EGN regards each training instance as an individual task and optimizes for good initialization across these tasks, it inherently fits the multi-task learning paradigm where multiple related tasks are learned jointly to improve generalization and adaptation. The training procedure involves fine-tuning on each task (instance) starting from a shared initialization, which is a core concept in multi-task and meta learning.\n\nTherefore, the methodology can be applied to multi-task learning by treating each task as a separate instance and learning a shared initialization that enables fast adaptation to each task, consistent with the principles of meta learning."}
{"question_id": "d5b246ec5a8edcc34e88a24fb9bfd7d313572647", "paper_id": "openreview/ICLR-2022-conf/pjqqxepwoMy", "generation": "The target return \\( v^{tar}_t \\) in equation (1) is estimated by any value learning algorithm such as TD(0), Peng\u2019s Q(\u03bb), or other methods based on Bellman equations. In the implementation described, double Q-learning with dueling architecture is employed to compute \\( v^{tar}_t \\) due to its effectiveness and simplicity. Specifically, for the Maze and MinAtar tasks, the target Q value is computed as\n\n\\[\nQ^{target}_t = r_t + \\gamma Q(z_{t+1}, \\arg\\max_{a'} Q(z_{t+1}, a'; \\theta); \\theta^-),\n\\]\n\nwhere \\( r_t \\) is the reward at step \\( t \\), \\( \\gamma \\) is the discount factor, \\( \\theta \\) denotes the parameters of the Q network (MLP decoder), and \\( \\theta^- \\) denotes the parameters of the target Q network. The latent state \\( z \\) used here is obtained from the posterior encoder with oracle observation as input. The first term of the VLOG loss function is then given by the mean square error between this target \\( Q^{target}_t \\) and the output of the Q network."}
{"question_id": "a9dce4895de5aac10ec1c0b1da92de90c47582f9", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The EGN and Meta-EGN models were pretrained until convergence, with the wall clock training times to convergence for different datasets reported in Table 9. The training times (in minutes:seconds) for EGN and Meta-EGN are as follows:\n\n- MC: EGN 46:50, Meta-EGN 101:55\n- MVC: EGN 104:37, Meta-EGN 210:04\n- MIS: EGN 282:57, Meta-EGN 609:47\n- Twitter: EGN 100:58, Meta-EGN 276:38\n- RB200: EGN 83:27, Meta-EGN 168:25\n- RB500: EGN 128:39, Meta-EGN 282:15\n- RRGs: EGN 733:02, Meta-EGN 1088:55\n\nMeta-EGN generally takes about two to three times longer to converge compared to EGN, but both remain on the same order of magnitude in training time."}
{"question_id": "317ee5566b85c3b36699add3f268020579e8b718", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The drawbacks of using statistical learning methods such as StreamLearner and TLogic are as follows:\n\n1. **Inflexibility in Temporal Constraint Learning:** TLogic builds temporal constraints on timestamps rather than intervals, and these constraints are fixed during learning. This inflexibility impairs the ability to learn temporal constraints effectively, especially for interval-based temporal knowledge graphs where temporal relations are more complex.\n\n2. **Restricted Random Walks:** Both StreamLearner and TLogic perform random walks in a very restricted manner. For example, StreamLearner extends static random walks separately into the time domain, resulting in all body atoms in the extended rules having the same timestamp. This restriction impairs the quality of the learned temporal logical rules.\n\n3. **Statistical Confidence Estimation:** These methods estimate rule confidence by counting the number of rule groundings and body groundings independently. This independent rule learning ignores interactions between different rules from the same positive example, which can lead to inaccurate confidence estimation. For instance, the confidence of some rules might be enhanced or diminished due to interactions, but these methods do not account for such effects.\n\n4. **Lack of Handling Rule Similarity:** Statistical methods cannot effectively deal with the similarity between different rules. Even if a reliable rule exists, a similar rule with slightly different temporal patterns may have low estimated confidence if it is infrequent in the dataset.\n\n5. **Limited Performance on Interval-Based tKGs:** The performance of timestamp-based methods like TLogic on interval-based temporal knowledge graphs is not demonstrated and is limited due to the complexity of temporal relations between intervals.\n\n6. **Sampling Strategy Limitations:** TLogic uses a sampling strategy to control the total number of temporal walks for efficiency, but this can impair model performance by not guaranteeing successful long-distance random walks, which are necessary for learning longer temporal logical rules.\n\n7. **No Interaction Between Rules:** The statistical counting approach ignores the interactions between different rules derived from the same positive example, which can affect the overall confidence and quality of rule learning.\n\nIn summary, statistical methods such as StreamLearner and TLogic suffer from inflexible temporal constraint modeling, restricted random walk strategies, independent and potentially inaccurate confidence estimation, inability to handle rule similarity, limited applicability to interval-based temporal data, and inefficiencies due to sampling strategies."}
{"question_id": "85afe8245083d99893657bc1eeadbcefa12dbf59", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "\"1 dB in PSNR\" refers to an improvement of one decibel in Peak Signal-to-Noise Ratio, which is a common metric used to measure the quality of lossy compression methods, particularly in image compression. An increase of 1 dB in PSNR indicates a higher fidelity or lower distortion of the compressed image relative to the original, implying better compression performance."}
{"question_id": "7603a58573eeaabda0e22ca42e407dd44c83bd3e", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "Action frames are syntactic and semantic structures associated with verbs that represent the contexts in which actions occur, capturing the relationships between the verb and its core arguments. Specifically, action frames are defined by extracting core components of an action\u2014such as the subject, direct object, and prepositional object\u2014using dependency parses, and mapping these components to semantic roles: the subject to an agent, the direct object to a theme, and the prepositional object to a goal. For frames involving prepositional phrases, separate frames are created for each preposition based on corpus statistics. These frames correspond to different frame relations associated with a verb, reflecting the various ways a verb can be used in different syntactic constructions. Each frame thus encodes a particular pattern of argument structure and is used to model the physical implications and relative knowledge of actions applied over objects."}
{"question_id": "d250649b5c73368021f92321b3d59f4c1d3c762f", "paper_id": "openreview/ICLR-2022-conf/iulEMLYh1uR", "generation": "The msec/example values in Figures 1 and 2 were computed using 64 TPU-V3 hardware."}
{"question_id": "b4885c9ebb178f8daa578cef0c857a1de41d8d54", "paper_id": "nlpeer/ARR-22/43e8458544e18304f6aa2c1aededf974877f088dd2ccd7d53ef71eb59e54567c0572f715ecdcbaf04c39206950c3f5628198e61ebc5ec09fa1ba0a559102f923", "generation": "The location of NLP conferences has a significant impact on the diversity of participation. Conferences held in different geographic locations encourage attendance from local and nearby researchers, thereby increasing participation diversity. This effect is particularly pronounced in lower-income countries, where researchers tend to prefer attending local events to reduce travel distance. Hosting conferences across various global regions allows researchers from diverse locations to participate without the need for long-distance travel, which also reduces the overall carbon emissions associated with conference travel.\n\nEmpirical analysis shows that the number of short-distance trips to conferences is higher than expected under a random venue choice scenario, confirming a preference for local or regional events. This local attendance contributes to higher diversity indices for conferences held in diverse locations. For example, European conferences have historically exhibited high diversity, with Asian conferences increasingly catching up. Certain conferences, such as LREC, achieve high diversity with relatively low travel distances, indicating effective local participation.\n\nHowever, the relationship is nuanced: holding conferences in distant locations from the typical attendance base can increase travel distances and carbon emissions, as seen in 2018 when ACL was held in Australia and LREC in Japan, causing longer travel for researchers from Europe and North America. Therefore, increased diversity through geographically varied conference locations depends on the events being well advertised and attended by local researchers.\n\nOverall, the diversity index of in-person NLP conferences tends to increase when events are distributed across different continents and regions, promoting local participation and reducing travel distances, which enhances the inclusivity and geographic diversity of the NLP research community."}
{"question_id": "413aa7a24c99874e0aae31b569348cc6c4e39b14", "paper_id": "nlpeer/F1000-22/10-170", "generation": "The recommendations based on the views expressed on coordination, preparation, and decision-makers in the management of the COVID-19 pandemic in Bangladesh are as follows:\n\n1. **Engagement of Relevant Experts:** A science-based professional response should be implemented by involving appropriate experts such as public health professionals, infectious disease epidemiologists, health policy and systems experts, medical anthropologists, health economists, health communication experts, laboratory scientists, and clinicians. Public health professionals must be engaged in scientific decision-making to ensure adequate pandemic preparedness and management.\n\n2. **Decentralization and Multisectoral Collaboration:** Coordination should be improved through tactful decentralization involving different relevant ministries and government departments at district and sub-district levels. Multisectoral collaboration should extend beyond government actors to include religious leaders, cultural activists, the private sector, non-governmental organizations, political parties, community groups, and individuals to address vertical and horizontal incoordination.\n\n3. **Transparency and Communication:** Data and decision transparency must be ensured, with correct and contextually appropriate information disseminated by trusted messengers within communities. Social and behavior change communication experts should tailor messages and deliver them through appropriate channels to prevent miscommunication.\n\n4. **Regulation and Accountability:** The private health sector should be regulated for cost and quality. Allegations of poor regulation and corruption must be addressed by enforcing punitive actions against wrongdoers, dissolving corrupt syndicates, and ensuring accountability in health system governance.\n\n5. **Resource Allocation and Service Quality:** Increased budgetary allocation and efficiency are necessary to overcome preexisting constraints such as budget shortages, low-quality services, and high out-of-pocket payments. Service providers should be trained and directed to deliver high-quality, efficient, and responsive care.\n\n6. **Support for Service Providers:** Service providers\u2019 legitimate demands, including engagement in decision-making, provision of training, personal protective equipment (PPE), adequate medical equipment, and workplace security, should be met to enhance their motivation and performance.\n\n7. **Establishment of a Public Health Career Track:** A dedicated public health career track should be developed within the health sector to ensure sustained expertise and leadership in public health emergencies.\n\n8. **Evidence-Based Policy and Political Will:** Pandemic management requires political will, good governance, and adherence to evidence-based scientific approaches, learning from successful models in other countries.\n\nThese recommendations aim to address the identified failures in coordination, preparation, and decision-making to improve pandemic response and health system resilience in Bangladesh."}
{"question_id": "bc784ef2841eb98841522b97ab75da7a7106b99c", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The term \"fine-tuning timing for classical solver\" refers to the additional computational time required to perform one-step fine-tuning of the Meta-EGN model parameters on a given test instance before applying the solver. This fine-tuning step involves updating the model parameters by taking a gradient step to optimize the loss function specific to the test instance, as described in Algorithm 3 and Algorithm 1. The timing measures the overhead introduced by this fine-tuning process compared to running the solver without fine-tuning.\n\nIn the experiments, fine-tuning timing is reported alongside approximation rates and solution quality metrics to quantify the trade-off between improved solution quality and extra computational cost. For example, Table 4 presents results with and without one-step fine-tuning (\"f-t\"), showing the time per graph (in seconds) required for fine-tuning. The fine-tuning timing is thus the time cost incurred by the classical solver when enhanced by the Meta-EGN model's one-step fine-tuning procedure on each test instance."}
{"question_id": "92b04f60c27edb89dcdc8dcd575bcb9872f0e307", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "Yes, the incorrectly-classified actions and objects exhibit ambiguity for humans. The error analysis highlights cases where the model fails due to underspecified frames or polysemy, which also cause disagreement among human annotators. For example, in one case, crowd workers provided conflicting examples regarding the relative weight of objects involved in the action \"stopped with,\" illustrating complex underlying physics not modeled by the system. Another example involves the phrase \"She caught the runner in first,\" where the correct physical relation depends on the sense of \"caught\" chosen; crowd workers selected a different sense than the model, leading to opposite physical implications. These instances demonstrate that the ambiguity in verb senses and frame specifications contributes to classification errors and reflects genuine uncertainty even among human annotators."}
{"question_id": "08ee038d964c18feafe50974403477b69a786d82", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The dimensions of the variables and functions in equations (8)\u2013(13) are as follows:\n\n- \\( h_l^i \\in \\mathbb{R}^d \\): the \\(i\\)-th step state in the \\(l\\)-length rule with feature dimension \\(d\\).\n- \\( X_l \\in \\mathbb{R}^{|R| \\times d} \\): the embedding matrix for the target predicate \\(P_h\\), where \\(|R|\\) is the number of predicates.\n- \\( f_{T\\text{embdd}}(X_l, P_h) \\in \\mathbb{R}^d \\): the embedding lookup function output.\n- \\( w_{\\text{Len}} \\in \\mathbb{R}^L \\): the attention vector of rule length, where \\(L\\) is the maximum rule length.\n- \\( (w_P)_l^i \\in \\mathbb{R}^{|R|} \\): the attention vector of predicate at step \\(i\\) in the \\(l\\)-length rule.\n- \\( (w_{TR})_l^{i,l+1} \\in \\mathbb{R}^{|TR|} \\): the attention vector of temporal relation (TR) between query intervals at step \\(i\\) and \\(l+1\\).\n- \\( (w_{TR})_l^{j,k} \\in \\mathbb{R}^{|TR|} \\): the attention vector of pairwise temporal relations between body intervals at steps \\(j\\) and \\(k\\), for \\(j \\in [1, l-1]\\) and \\(k \\in [j+1, l]\\).\n- \\( W_P \\in \\mathbb{R}^{|R| \\times d} \\), \\( W_{TR} \\in \\mathbb{R}^{|TR| \\times d} \\), \\( W'_{TR} \\in \\mathbb{R}^{|TR| \\times 2d} \\), \\( W_{\\text{Len}} \\in \\mathbb{R}^{L \\times d} \\): learnable parameter matrices.\n- \\( b_P \\in \\mathbb{R}^{|R|} \\), \\( b_{TR}, b'_{TR} \\in \\mathbb{R}^{|TR|} \\), \\( b_{\\text{Len}} \\in \\mathbb{R}^L \\): learnable bias vectors.\n\nThe confidence score function in equation (13) involves embedding lookups \\( f_{\\text{embdd}}(\\cdot, \\cdot) \\) that map attention vectors and predicates or temporal relations to scalar confidence values in \\(\\mathbb{R}\\). Specifically:\n\n- \\( f_{\\text{embdd}}(w_{\\text{Len}}, l) \\in \\mathbb{R} \\): confidence of rule length \\(l\\).\n- \\( f_{\\text{embdd}}((w_P)_l^i, P_i) \\in \\mathbb{R} \\): confidence of predicate \\(P_i\\).\n- \\( f_{\\text{embdd}}((w_{TR})_l^{i,l+1}, TR_{i,l+1}) \\in \\mathbb{R} \\): confidence of temporal relation \\(TR_{i,l+1}\\).\n- \\( f_{\\text{embdd}}((w_{TR})_l^{j,k}, TR_{j,k}) \\in \\mathbb{R} \\): confidence of temporal relation \\(TR_{j,k}\\).\n\nIn summary, the key dimensions are:\n\n- \\(d\\): feature dimension of hidden states and embeddings.\n- \\(|R|\\): number of predicates.\n- \\(|TR|\\): number of temporal relations.\n- \\(L\\): maximum rule length.\n- Attention vectors for predicates and temporal relations have dimensions \\(\\mathbb{R}^{|R|}\\) and \\(\\mathbb{R}^{|TR|}\\), respectively.\n- States \\(h_l^i\\) and embedding lookups are in \\(\\mathbb{R}^d\\).\n- Learnable parameter matrices and biases conform to these dimensions accordingly."}
{"question_id": "0544f3fb3619a15d1f6076086707d91cea93b334", "paper_id": "openreview/NeurIPS-2022-dabt/wPEXGTzZJt", "generation": "The authors selected the four datasets because, to their knowledge, these are the only publicly available datasets specifically designed for the task of visual search in natural scenes. Each dataset comprises curated search images, target objects, and anonymized participants\u2019 scanpaths, allowing for evaluation of visual search models. The datasets differ in characteristics such as number of participants, number of images, target size, image content, and task design, which introduces variability but also represents the current scope of available data for this task. To enable a fair comparison across these heterogeneous datasets, the authors performed preprocessing steps to bring them into a common format and criteria, including standardizing fixation criteria, scanpath length, and target representation. This approach facilitates benchmarking despite the limited number of datasets, and the authors suggest that future datasets could be added under similar preprocessing and evaluation conditions."}
{"question_id": "748b3e8ce8fe5bb6fa899f962211e41d18c30cae", "paper_id": "openreview/NeurIPS-2022-dabt/wPEXGTzZJt", "generation": "The bounds presented in Table 1 correspond to baseline models that provide lower and upper performance limits for the evaluated visual search models. The lower bound is established by the uniform and center bias models: the uniform model assumes fixations are uniformly and independently distributed over the image, while the center bias model reflects the human tendency to fixate near the center of images, modeled using a Gaussian Kernel Density Estimate (GKDE) over fixations from a training dataset. The upper bound is given by the Gold Standard model, which predicts fixations of each participant based on a GKDE over all fixations from other participants on the same image. These bounds serve as reference points to contextualize the performance of computational models in predicting human visual search behavior."}
{"question_id": "add8f70fda4a981fbac7e3f41f938eeecd3ccd4d", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The first kind of rule in Section 4 is called \"Markovian\" because the calculation of the next state probability in the random walk depends only on the current state probability, without considering the previously visited edges. This means that the random walk satisfies the Markov property, where the next step is determined solely by the present state, making the process memoryless with respect to the path history."}
{"question_id": "6de0c620431f72ce5a6331d7dde1b8df91e24936", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "The criteria for determining which knowledge dimensions to annotate for each frame involve collecting human judgments through crowdsourcing. Crowd workers are presented with a frame template (e.g., \"x threw y\") and asked to list plausible objects for the missing slots. They then rate the general relationship between the arguments of the frame with respect to all considered knowledge dimensions (attributes), such as size, weight, strength, rigidness, and speed. For each attribute, workers select one of four options indicating the relative relation between the arguments: (1) x > a y, (2) x < a y, (3) x \u2243 a y, or (4) no general relation. Only frame/attribute combinations that achieve at least two-thirds agreement among workers and are not predominantly rated as having no relation are retained for annotation. This process ensures that annotations are made only for knowledge dimensions that exhibit a clear and agreed-upon relative relation between the frame arguments."}
{"question_id": "207945518935728931a4b020daa416e8fc8f1cda", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The relaxations in Table 2 follow the design and relaxation approach established in prior works, specifically referencing (Karalias & Loukas, 2020) and (Wang et al., 2022). The detailed derivation of the loss function relaxation for the three problems (MC, MVC, and MIS) is based on these existing studies. Thus, the relaxations are not newly derived in this work but are based on previously established formulations."}
{"question_id": "9776571072ba250ab654c2a326bd48a527e61213", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The proposed framework, Meta-EGN, is designed as a general unsupervised learning framework for combinatorial optimization (CO) problems by leveraging meta learning to achieve instance-wise good solutions. It views each training instance as a separate but related task and learns a good initialization for fast adaptation to new instances. This approach has demonstrated good generalization across different problem scales and distributions, and it can improve greedy heuristics with minimal extra time cost, as shown in the maximum independent set problem on large-scale random regular graphs.\n\nRegarding extension to other applications or tasks, the framework is inherently general for CO problems and is not limited to specific problem types. The meta learning approach is suitable for problems where each instance can be treated as a separate task with related optimization landscapes. The paper notes that meta learning has shown good generalization when data distributions shift across tasks in other domains such as computer vision and natural language processing, suggesting potential applicability beyond the studied CO problems.\n\nHowever, the discussion on limitations indicates that while Meta-EGN performs well on problems with more local structures (e.g., maximum clique), it may face challenges on CO problems requiring more global assignments (e.g., minimum vertex cover). The authors also mention the possibility of extending Meta-EGN to improve other advanced Monte Carlo algorithms beyond the ones tested, indicating future research directions for broader applicability.\n\nIn summary, the framework can be extended to other CO problems and potentially to other tasks that can be formulated as related optimization tasks suitable for meta learning. Further research is needed to adapt and optimize the framework for CO problems requiring global assignments and to integrate it with more advanced optimization algorithms."}
{"question_id": "006b4d78ff2835159d4e1f745a3f9c4f41fe8351", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The Markovian assumption in equation (6) is not fully valid in the context of temporal logical rules on temporal knowledge graphs (tKGs) because the temporal constraints introduce dependencies beyond the current state. Specifically, equation (6) describes the random walk under Markovian constraints as:\n\nv_{i+1} = M_{i,CM_i} v_i  for 1 \u2264 i \u2264 l,\n\nwhere M_{i,CM_i} is an adjacency matrix operator encoding Markovian constraints (body predicates and temporal relations between the query interval and each body interval). This formulation assumes that the next state probability depends only on the current state.\n\nHowever, the temporal logical rules include two classes of constraints: Markovian and non-Markovian. Markovian constraints relate to the current step's predicate and temporal relation with the query interval, which can be encoded in the matrix operators. Non-Markovian constraints involve pairwise temporal relations between body intervals at different steps, requiring knowledge of previously visited edges to ensure satisfaction.\n\nSince non-Markovian constraints depend on the history of the walk (previous edges and their temporal intervals), the random walk process cannot be fully Markovian. The current step's transition depends not only on the current state but also on the temporal relations among multiple intervals visited in the walk.\n\nMoreover, the starting time and relation type of the query remain fixed during the random walk, but the temporal relations between intervals at different steps must be checked to satisfy non-Markovian constraints. This breaks the Markovian property because the transition probabilities depend on the entire path history, not just the current state.\n\nTherefore, while equation (6) models the random walk under Markovian constraints, the full temporal logical rule learning framework requires filtering these walks with non-Markovian constraints, which invalidates the pure Markovian assumption. The framework first performs random walks under Markovian constraints and then filters the results to enforce non-Markovian constraints, acknowledging that the Markovian assumption alone is insufficient."}
{"question_id": "26c5a6b5fdd3b1d0b2d97c1550126b503f144eed", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The chance performance reported in Table 4 is 20%."}
{"question_id": "c5bafca1e41f1bfed6bf6063b321e6bb5102b171", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The poorer performance of the CLIP-ViL model compared to other models is attributed to its use of the image-text matching (ITM) pre-training objective, whereas models like X-VLM and ViLT are fine-tuned specifically for image-text retrieval tasks. CLIP-ViL's ITM objective may be less effective for the association task in WinoGAViL, while CLIP uses a different contrastive pre-training objective that appears more suitable for this task."}
{"question_id": "3684e20e39c14e7d52c12084515e178aa0789584", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "Associations that are solvable by AI should be kept in the framework to reduce dependence on a specific AI model and to maintain the naturalness and quality of the data. The selection of test instances is based solely on the \"solvable-by-humans\" score, without excluding instances that AI models can solve. This approach prevents the dataset from becoming biased or unnatural by focusing only on examples that fool the AI model. It ensures that the collected associations remain representative of human reasoning and are not artificially constrained to be adversarial only to the current AI models, thereby preserving the dataset's relevance and generality as the field advances."}
{"question_id": "530a4fe0ab0d3f801dab10132e0e5791d29d2051", "paper_id": "nlpeer/ARR-22/12ea2851de6445bf343d642c6ccc8640bf7a6c28b5f20ba123d6d58f02790c7c7f8ab696e6a6b4e45bfad28bd4bc0419bdcf6358693404dd0c14d202b291503b", "generation": "The results in Table 1 with decoder downsampling are obtained without the two-step decoding method. This is indicated by the fact that the two-step decoder is explicitly mentioned as a separate condition in the experiments, and the table shows results for models with decoder downsampling but does not specify the use of the two-step decoder in those rows. Additionally, the text states that employing the two-step decoder matches the decoding speed of subword models but results in much worse overall translation quality, implying that the main reported results with decoder downsampling do not use the two-step decoding method."}
{"question_id": "caa94f24704e76df0ceac395ee650a45f7a174e1", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The most popular and advanced methods in cross-task learning are pre-training and joint training paradigms. Pre-training involves first training the representation part on source data and then fine-tuning the entire target model on the target data. Joint training simultaneously uses both source and target data to train a shared representation model along with task-specific functions for both source and target tasks. These paradigms are commonly employed with models such as BERT, where the representation model is shared and the task-specific function is typically a last-layer linear classifier. Additionally, multi-task learning algorithms like MT-DNN are adopted within these paradigms to facilitate learning across multiple tasks."}
{"question_id": "a7ad87d54def516b43292de78c758cf6107320f7", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "Yes, the proposed method, Target-Aware Weighted Training (TAWT), can be implemented using projected gradient methods instead of mirror descent. The update rule for the weights \u03c9 in TAWT is given by a mirror descent step on the probability simplex, which is a canonical generalization of Euclidean gradient descent. However, it is explicitly stated that other optimization methods, such as projected gradient descent, can also be used for updating the weights."}
{"question_id": "04e9ce2f786d7603574645eafe3bfe6e1a603190", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The choice of mirror descent in the proposed Target-Aware Weighted Training (TAWT) method is not strictly necessary but is motivated by its suitability for optimization on the probability simplex. Specifically, mirror descent is used in the update rule for the weights \u03c9 (Equation 2.8) because it is a canonical generalization of Euclidean gradient descent to gradient descent on the probability simplex (Beck & Teboulle, 2003). This allows the weights to be updated in a manner that respects their probabilistic constraints.\n\nHowever, the text explicitly states that other optimization methods, such as projected gradient descent, can also be used in place of mirror descent. Thus, while mirror descent provides an intuitive and theoretically principled approach for updating the weights in the simplex domain, it is not the only possible choice for the optimization step in the algorithm."}
{"question_id": "fe2486dcff37fd819cb07981aa6a1e026c1f52e5", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The proposed method, Target-Aware Weighted Training (TAWT), demonstrates clear improvements over standard cross-task learning paradigms such as pre-training and joint training. Empirical results show that TAWT consistently enhances performance across multiple sequence tagging tasks in natural language processing, including part-of-speech tagging, chunking, predicate detection, and named entity recognition. For example, in joint training settings, weighted joint training with TAWT achieves statistically significant improvements in accuracy and F1 scores compared to unweighted joint training, with average absolute improvements around 3.1%. \n\nTAWT is computationally efficient, easy to implement, requires minimal hyperparameter tuning, and can be applied as a plugin to existing cross-task learning frameworks without substantial overhead. It also enjoys non-asymptotic learning-theoretic guarantees, which is a novel contribution compared to prior heuristic weighting strategies in multi-task learning. The method introduces a representation-based task distance metric that provides a theoretically principled way to assign adaptive weights to source tasks or samples, optimizing the proximity between source and target task representations. This approach contrasts with previous methods that rely on assumptions of a shared representation or heuristic weighting schemes.\n\nOverall, TAWT advances the state of the art in cross-task learning by providing both theoretical guarantees and empirical performance gains, making it the first weighted algorithm for cross-task learning with such theoretical underpinnings and practical effectiveness."}
{"question_id": "9baa6e51bac226403b63b8bef97ea58737e9f14c", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The primary factor that makes the 10 & 12 candidate task more difficult than the 5 & 6 candidate task is the increased number of candidates, which raises the difficulty level for models but does not significantly affect human performance. Models experience a significant performance drop with the increase in candidates, indicating that they struggle to handle the higher complexity and larger candidate set. In contrast, humans remain robust to this increased difficulty, maintaining similar high accuracy across both tasks.\n\nAdditionally, training models improves performance notably only in the harder 10 & 12 candidate cases, suggesting that models require more supervised data to exploit the information necessary to solve these challenging associations. The difficulty for models is also linked to the nature of the associations, which often require reasoning beyond direct visual detection, such as concept-related, activity-based, or non-salient visual cues, where models perform worse.\n\nIn summary, the increased number of candidates in the 10 & 12 task raises the complexity and challenge for models, leading to lower model accuracy, while humans maintain stable performance due to their superior reasoning and associative capabilities."}
{"question_id": "ddbf5cd6168ece49006281d9def64503e3610f0f", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The provided excerpts do not contain any explicit statement or assumption labeled as \"assumption b\" regarding the nature of \\(\\phi\\), nor do they discuss whether \\(\\phi\\) must be a vector. The proofs and theoretical guarantees focus on properties of learned functions \\(\\phi\\) and associated predictors \\(f_t\\), their empirical and population risks, and the behavior of weighted training for cross-task learning. The notation \\(\\phi \\in \\Phi\\) suggests \\(\\phi\\) is an element of a function class \\(\\Phi\\), but there is no indication that \\(\\phi\\) is necessarily a vector or must be represented as such.\n\nTherefore, based on the given information, there is no requirement or necessity stated that \\(\\phi\\) must be a vector under any assumption labeled \"b\" or otherwise. The analysis treats \\(\\phi\\) as a function or mapping within a function class, without specifying its parametric form as a vector."}
{"question_id": "8f304b893f44b97b98ba8df1ea43ce0fcb657b87", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The expression in question involves sums of weighted losses and differences of losses, which are bounded and non-negative due to the properties of the loss function and the weighting scheme.\n\nSpecifically, the loss function \u2113 is bounded in the interval [0, 1], ensuring that all individual loss terms are non-negative and at most 1. The weights \u03c9_t are non-negative as they represent importance weights in the weighted training framework. Consequently, sums of the form\n\n\\[\n\\sum_{t=1}^T \\omega_t [L_t(\\phi, f_t) - \\ell(f_t \\circ \\phi(x_{ti}), y_{ti}) + \\ell(\\bar{f}^\\omega_t \\circ \\bar{\\phi}^\\omega(x_{ti}), y_{ti}) - L_t(\\bar{\\phi}^\\omega, \\bar{f}^\\omega_t)]\n\\]\n\nare composed of non-negative weighted terms or differences bounded by the loss function's range.\n\nMoreover, the proofs invoke norms and covering numbers defined with respect to \\(L_2\\) metrics and empirical measures, which are inherently non-negative. For example, the \\(L_2\\) norms and covering numbers used in the entropy integral bounds are defined via sums of squared differences, which are always non-negative.\n\nBecause these sums and norms are non-negative, it is mathematically valid to take powers such as the \\(1/\\rho\\) power (for some \\(\\rho > 0\\)) without ambiguity or issues related to negativity. This is explicitly used in the transferability assumption and the bounding steps in the proofs, where expressions raised to the power \\(1/\\rho\\) appear.\n\nIn summary, the positivity (or at least non-negativity) of the sums arises from the boundedness and non-negativity of the loss function and weights, as well as the use of norms and empirical measures defined via squared differences. This justifies taking powers such as \\(1/p\\) or \\(1/\\rho\\) on these expressions."}
{"question_id": "da72c5aada0a4b421a7919d5ef2739ddb658ae5e", "paper_id": "nlpeer/ARR-22/12ea2851de6445bf343d642c6ccc8640bf7a6c28b5f20ba123d6d58f02790c7c7f8ab696e6a6b4e45bfad28bd4bc0419bdcf6358693404dd0c14d202b291503b", "generation": "The results in Table 2 were generated from models trained on English-German (en-de) systems, but the exact number of samples used to generate the results in Table 2 is not explicitly stated in the provided chunks. However, Table 2 presents chrF and COMET scores for different decoding methods evaluated on validation data, and Figure 4 mentions that the translation quality was evaluated for selected models over different beam sizes, but no specific sample size for Table 2 is given.\n\nTherefore, the number of samples used to generate the results in Table 2 is not specified in the provided information."}
{"question_id": "b79e294382978a8a9cebe595211529bcb653e0f2", "paper_id": "nlpeer/COLING2020/1886", "generation": "The topics for n-grams are chosen by assigning each n-gram to a predefined list of topics using a lexicon such as the Empath lexicon. For example, n-grams like \"missile\" and \"army\" are assigned to the topic \"War.\" The assignment involves categorizing n-grams into content-related topics, proper names, hashtags, and style-related categories such as punctuation marks, all-caps, function words (including parts of speech like pronouns), and emojis for user-generated texts. The importance of each topic for the two target classes (fake and real news) is then defined by summing the relevance values (R_v and F_v) of the n-grams included in that topic. To focus on n-grams relevant to one of the target classes, the difference between R_v and F_v for each n-gram is computed, and only those n-grams whose difference exceeds the mean plus one standard deviation (\u03bc + \u03c3) are retained."}
{"question_id": "21f851f7058a46d8f9904d493b82811edf3aa8f3", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "There is no mention or discussion of connections between this work and existing research on learning data weighting using multi-arm bandits in the provided content. The paper focuses on a weighted training algorithm called Target-Aware Weighted Training (TAWT) for cross-task learning, which adaptively assigns weights on source tasks or samples by minimizing a representation-based task distance. The approach is contrasted with other weighting schemes such as importance sampling in domain adaptation, but no reference or comparison to multi-arm bandit methods for learning data weighting is made."}
{"question_id": "1874144ac78d10d99982bc7f6446545ac56a4805", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "A fine-grained model analysis was conducted on different association types to investigate the observation that models struggle with associations that are not visually salient. Approximately 1,000 cue-image pairs with 10-12 candidates were sampled and annotated by three authors into six categories: (a) Visually salient, (b) Visually non-salient, (c) Concept related, (d) Activity, (e) Counting, and (f) Colors. The final category for each instance was determined by majority vote with 98% agreement.\n\nThe model evaluated was CLIP ViT-B/32, and accuracy per category was reported as the proportion of successful model predictions. Results showed that model performance was highest in the visually salient and colors categories, degraded in concept related and activity categories, and was much worse in visually non-salient and counting categories. This pattern suggests that models have difficulty with associations requiring common sense reasoning or less direct visual detection, particularly in visually non-salient cases.\n\nThese findings highlight a lack of common sense reasoning capabilities in the models and indicate that models perform better on association instances that require direct visual detection, consistent with their training objectives. The annotated data and detailed analysis, including examples and annotation guidelines, were released for future research."}
{"question_id": "c44a73f20a295acb499abf61c0f0b96e4080d9ba", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "No, there is no explicit \\(\\forall\\) quantifier before equation (3.5) in Definition 3.2. The definition states that a representation \\(\\phi \\in \\Phi\\) is \\((\\rho, C_{\\rho})\\)-transferable from \\(\\omega\\)-weighted source tasks to the target task if there exist \\(\\rho > 0\\) and \\(C_{\\rho} > 0\\) such that for any \\(\\bar{\\phi}_{\\omega} \\in \\bar{\\Phi}_{\\omega}\\), the inequality\n\n\\[\nL^{\\star}_0(\\phi) - L^{\\star}_0(\\bar{\\phi}_{\\omega}) \\leq C_{\\rho} \\left( \\sum_{t=1}^T \\omega_t [L^{\\star}_t(\\phi) - L^{\\star}_t(\\bar{\\phi}_{\\omega})] \\right)^{1/\\rho}\n\\]\n\nholds. The quantification is over \\(\\bar{\\phi}_{\\omega} \\in \\bar{\\Phi}_{\\omega}\\), but not universally over all \\(\\phi \\in \\Phi\\). The definition applies to a given \\(\\phi \\in \\Phi\\), not to all \\(\\phi\\) simultaneously."}
