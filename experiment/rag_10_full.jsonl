{"question_id": "7ed6482c76ae428708b4675495718652adfed68d", "paper_id": "nlpeer/COLING2020/679", "generation": "Yes, six of the nine annotators who participated in the evaluation were native English speakers. All annotators had prior training in AMR annotation and were either current or former PhD students or professors at the university conducting the study."}
{"question_id": "e76615760f7625a5b984cb961f5d948ab2407178", "paper_id": "nlpeer/PeerRead-ACL2017/193", "generation": "In the UCCA scheme, a node (or unit) corresponds either to a terminal (a single token) or to a non-terminal, which is a unit comprising one or more terminals that may not be contiguous. Thus, a node can be either a terminal or a non-terminal, but not both simultaneously. Non-terminal nodes represent complex semantic units that include multiple tokens, while terminal nodes correspond directly to individual tokens of the text."}
{"question_id": "cdb59f3c15dd8d951c169a9f2fae03e000215bae", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "Yes, the cross-linguistic analysis includes both French and Spanish. The zero-shot cross-lingual evaluation was conducted using models trained on English data and tested on Spanish (Newsela-Es) and French (Vikidia-Fr) datasets. The experiments demonstrated that the neural pairwise ranking model achieved over 80% ranking accuracy for both French and Spanish in this zero-shot cross-lingual transfer setting."}
{"question_id": "465b94530bc99598bbefb742da70cf99236b86d9", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "Classification-based models were not used for the zero-shot experiments because there is no way of resolving ties with classi\ufb01er predictions. This limitation makes classification approaches unsuitable for ranking tasks in cross-corpus and cross-lingual settings, where distinguishing between ties is necessary. Consequently, only regression and pairwise ranking models were employed in these experiments."}
{"question_id": "0081780bb3b2f8ca303dd426ce4ddfd89c13e80d", "paper_id": "nlpeer/ARR-22/0f595b4bd968ef5daacc88c4edb581bf7f683af30b6874ba157c6d2282aabead577041409b949924e4bc5ef54c543d85a10e962be8e70304dea65e1b18441bdb", "generation": "The average duration reported in Table 1 represents the average time taken by workers to complete a Human Intelligence Task (HIT) and the average time taken to assess dialogues. This information is provided to characterize the time investment required from workers during the human evaluation process."}
{"question_id": "5f4c9dea82aa176c2e42cd2c59ff3da0fce4a367", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "Yes, the pairwise Euclidean distances between the latent representations of the sequences can be quantitatively compared to the corresponding branch lengths in the phylogenetic tree. This comparison was performed for both a standard variational autoencoder (VAE) and the Draupnir model, which incorporates a tree-structured Ornstein-Uhlenbeck (TOU) process as a prior on the latent space. The results show a strong correlation between the latent space distances and the phylogenetic branch lengths, with Draupnir achieving higher correlation coefficients (Pearson correlation of 0.91 and Spearman correlation of 0.94) compared to the standard VAE (Pearson correlation of 0.79 and Spearman correlation of 0.85). This indicates that the latent representations learned by Draupnir effectively capture the evolutionary relationships encoded in the phylogenetic tree."}
{"question_id": "a3bcdc5b71130202e27b2a0b4a8485392efedec5", "paper_id": "openreview/ICLR-2023-conf/rLguqxYvYHB", "generation": "When treatments are discrete, estimating the function \\(\\phi_A\\) does not pose significant challenges related to downplaying treatment information. Specifically, the back-door adjustment algorithm models the conditional expectation \\(g(a,x)\\) as a linear function of the tensor product \\(\\phi_A(a) \\otimes \\phi_X(x)\\), where \\(\\phi_A\\) and \\(\\phi_X\\) are feature maps represented by neural networks. This tensor form explicitly separates the treatment features from the covariate features, which is particularly advantageous when the covariate dimension is much higher than that of the treatment. In the discrete treatment case, the probability of observing all elements in the treatment-covariate space \\(A \\times X\\) is non-zero, satisfying the assumptions required for uniform convergence of estimators. Thus, the estimation of \\(\\phi_A\\) is well-posed and consistent under these conditions. There is no indication of specific difficulties in estimating \\(\\phi_A\\) when treatments are discrete, unlike the challenges noted for continuous or high-dimensional treatments."}
{"question_id": "cf26cc0cd1000ad63bfba19b5159a20efba34b18", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "No, the parameters of the BLOSUM matrix are not estimated by the model but are pre-computed prior to training. The model uses a pre-computed BLOSUM substitution matrix (e.g., BLOSUM62 or PAM70) to represent each position in the multiple sequence alignment as a weighted average of the BLOSUM vectors of the characters in that column. These averaged BLOSUM vectors are then processed by a neural network to produce BLOSUM embeddings that provide position-specific information. The BLOSUM vectors themselves are fixed and only need to be precomputed once; the model does not learn or update the substitution matrix parameters during training."}
{"question_id": "****************************************", "paper_id": "openreview/ICLR-2023-conf/-CoNloheTs", "generation": "Yes, the proposed algorithm can be used to recover real neural networks, specifically two-layer and three-layer ReLU networks, under mild general position assumptions. It provides a polynomial-time and polynomial-query complexity method for exact reconstruction of these networks from membership queries, without restrictive assumptions such as Gaussian input distributions or linear independence of weight matrix rows. For three-layer networks, the algorithm requires that the number of first-layer neurons is smaller than the input dimension and that the second layer has non-zero partial derivatives, conditions that hold for most practical networks with more second-layer neurons than first-layer neurons. The algorithm reconstructs the network parameters up to an affine transformation and can identify the correct signs of neurons, enabling exact recovery of the network architecture and weights. However, the approach is currently limited to relatively shallow networks (two or three layers) and assumes exact access to the network outputs, which may pose challenges in practical scenarios due to numerical inaccuracies."}
{"question_id": "7088d27d671a415164b81660a60173ea7602d968", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "A bidirectional gated recurrent unit (GRU) decoder is used for sequences of the same length to model the sequential dependencies along the aligned positions of the multiple sequence alignment (MSA). Although all sequences have the same length due to alignment, the GRU processes each position by taking as input a concatenated vector consisting of the sequence-specific latent vector and a position-specific BLOSUM embedding. This allows the GRU to capture complex dependencies and coevolutionary patterns across sequence positions while incorporating both sequence-level and position-level information. The GRU states at each position are then mapped to logit vectors specifying the probabilities of the possible characters, enabling probabilistic modeling of the sequence characters at each aligned position. Thus, the GRU decoder effectively models the conditional distribution of characters along the fixed-length sequences, leveraging both latent sequence representations and position-specific embeddings."}
{"question_id": "d256c384aa446ef6ba7d69269df08e3dbbdb2db9", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "The construction of the phylogenetic tree required as input for the algorithm is a non-trivial task that involves inferring the tree topology, the labels of the tree\u2019s edges, and the composition of the ancestral sequences. This inference is typically performed using methods based on heuristics, such as maximum parsimony, or probabilistic evolutionary models. The tree is represented as a binary tree with leaf nodes corresponding to known extant sequences and internal nodes representing unknown ancestral sequences. Edges in the tree are labeled by positive real numbers that represent the time difference or amount of evolutionary change between nodes, forming a patristic distance matrix used as input for the model. Thus, constructing the phylogenetic tree requires careful evolutionary analysis and computational methods to accurately represent evolutionary relationships and distances among sequences."}
{"question_id": "b8c9effb94fbab2db127d7262041fecdb031c1dd", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The preprocessing step of converting numerical features to categorical ones through binning is performed primarily to make the dataset suitable for BERT-based models, whose vocabularies would otherwise become too large. For a fair comparison, the same preprocessing is applied to all methods in the experiments. However, experiments are also conducted without preprocessing the numerical features, and the results show that performance varies depending on the method and split. It is not clear that one feature set (binarized or original numerical) is consistently better across all methods. Thus, while the categorical conversion is used for all methods to ensure fairness, alternative experiments without this preprocessing are also evaluated."}
{"question_id": "6ceebb857d2674bd38452e8bc30a3d280f2ce647", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "The lower performance on Vikidia-Fr compared to Newsela-Es can be attributed to several factors: \n\n1. Language difference: Vikidia-Fr is in French, whereas Newsela-Es is in Spanish, which may inherently affect model transferability.\n\n2. Domain difference: Newsela-Es shares the same domain as Newsela-En (the training dataset), while Vikidia-Fr contains more diverse content, making it harder for the model trained on Newsela-En to generalize.\n\n3. Number of reading levels: Vikidia-Fr has only two reading levels, whereas Newsela-Es has ten. Ranking metrics such as SRC and KTCC are more forgiving when there are more reading levels. In Vikidia-Fr, an incorrect ranking of levels can result in a completely reversed list, which is heavily penalized by these metrics. Thus, a small number of completely incorrectly ranked slugs can cause low SRC and KTCC scores for Vikidia-Fr, while Newsela-Es can still achieve high scores despite some ranking errors."}
{"question_id": "6a2461eebef2649b0832b8d91cb8ed490edbf2d3", "paper_id": "nlpeer/ARR-22/ab82036287f47b63ae70bdae7c1ed5900395bfe4538cc14db1a17281e2b45434b348472df2024151e188fe3890b88a0990bf1a36540eb39ddcc015012567bdd8", "generation": "The provided information does not explicitly state the reason for choosing Ordinary Least Squares (OLS) regression over Support Vector Regression (SVR) for the regression-based systems. However, it is noted that non-contextual word-level embeddings were used as input to an OLS model for regression baselines, and results were obtained through 5-Fold cross validation. The choice of OLS may be due to its straightforward implementation and compatibility with non-contextual embeddings, serving as a simple baseline for regression performance comparison against more complex models such as the fine-tuned BERT regressor (regBERT). There is no mention of SVR being used or compared in the experiments."}
{"question_id": "264d5ee47c97b02b5a597533254b78c97cb41f8b", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "There is no information in the provided content regarding the Darpa dataset or any limitations related to it that would prevent it from being a graph dataset. The discussed datasets focus primarily on Kyoto-2006+, network traffic datasets, system logs, and multivariate time series, with no mention of the Darpa dataset or its characteristics."}
{"question_id": "815b242c673ecb7aea2ccec4f6c83ab1191a9124", "paper_id": "openreview/ICLR-2022-conf/FZoZ7a31GCW", "generation": "A Multi-Layer Perceptron (MLP) cannot be used to decode the aligned sequences instead of a Gated Recurrent Unit (GRU) decoder because the decoding task requires modeling sequential dependencies along the alignment length. The GRU, a type of recurrent neural network, processes input sequences position by position and captures contextual information across sequence positions, which is essential for accurately modeling the probabilities of amino acid characters at each position in the multiple sequence alignment (MSA). In contrast, an MLP lacks the recurrent structure necessary to capture such sequential dependencies and positional context. Therefore, the Draupnir model employs a bidirectional GRU decoder that takes as input concatenated vectors of sequence-specific latent representations and position-specific BLOSUM embeddings, enabling it to produce likelihood parameters (logits) for each position in the sequence while accounting for the sequential nature of the data. This architecture allows the model to better capture coevolution and dependencies between sequence positions, which are critical for ancestral sequence reconstruction."}
{"question_id": "e2ee4a1059cbb3c736b7b00cd902bbbd428423e8", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The authors have not considered formulating the Darpa dataset as a dynamic graph for network intrusion detection. Their work focuses on the Kyoto-2006+ dataset, which they analyze for natural distribution shifts over a 10-year period. While they mention that some additional features in the Kyoto-2006+ dataset, such as source and destination IP addresses and port numbers, might be useful for designing models like graphs focusing on connections between nodes, there is no indication that they have applied or considered a dynamic graph formulation specifically for the Darpa dataset or any other dataset."}
{"question_id": "de0f53c58cedd98fd958715bb1a2f5a3d24e829d", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The source and destination IP addresses and port numbers were discarded because the preprocessing focused on the 14 conventional features of the Kyoto-2006+ dataset, which include 2 categorical and 12 numerical features, and applied discretization and binning to make the data suitable for language-modeling approaches such as BERT. Although the original dataset contains these additional features (source and destination IP addresses and ports), they were not included in the main preprocessing pipeline. The reason for discarding these features is implied by the focus on fixed vocabulary size and tokenization suitable for language models, as well as the potential complexity and large vocabulary that including IP addresses and ports would introduce. Additionally, the authors note that some of these additional features might be useful for models designed to focus on connections between nodes (e.g., graph-based models), but they were not used in the current benchmark setup. Thus, the source and destination IP addresses and port numbers were excluded to maintain a manageable and consistent feature set for the anomaly detection benchmark."}
{"question_id": "f3d892c65c8fc7edb3624cad07d6ef39115402eb", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "A new set of error categories was designed to achieve a compromise between informativeness and practicality, addressing limitations in existing tagsets such as Nicholls (2003) and the CoNLL-2014 tagset. The new framework allows for flexible evaluation at different levels of granularity by prefixing error types with indicators of Missing, Replacement, or Unnecessary edits, enabling more detailed and nuanced observations of system performance. Additionally, the new scheme avoids overly narrow distinctions that would require impractical expansion of categories and complex rules\u2014for example, it classifies certain modal and tense errors under a single category rather than splitting them into narrowly defined modal or tense errors. This approach facilitates dataset independence, transparency, and consistency in error classification, which were not fully addressed by previous tagsets."}
{"question_id": "058da91fd7bfe9ecd3163d9d8877e5116cf5cdf6", "paper_id": "openreview/ICLR-2022-conf/C03Ajc-NS5W", "generation": "The evidence that the G-SphereNet generative model is successful in synthesizing new molecules includes the following points:\n\n1. **High Validity of Generated Molecules:** G-SphereNet achieves a high validity rate of 88.18% in generating chemically valid molecular geometries, which is significantly higher than competing methods such as E-NFs (39.77%) and G-SchNet (76.39%). This indicates that the model effectively captures chemical rules and dependencies between atoms during generation.\n\n2. **Accurate Modeling of 3D Structural Distribution:** G-SphereNet attains lower Maximum Mean Discrepancy (MMD) distances for four types of chemical bonds compared to G-SchNet, demonstrating a more accurate modeling of the 3D structural distribution of molecular geometries.\n\n3. **Superior Performance in Targeted Molecule Discovery:** In tasks aimed at optimizing quantum properties (minimizing HOMO-LUMO gap and maximizing isotropic polarizability), G-SphereNet outperforms G-SchNet across all evaluation metrics, including mean property scores, optimal values, and the percentage of generated molecules falling into the defined \"good\" property regions. This shows the model's ability to generate molecules with desirable and targeted chemical properties.\n\n4. **Generation of Diverse Molecular Geometries:** Visualizations of sample molecular geometries generated by G-SphereNet illustrate its capability to produce diverse molecules with specific optimized properties, further supporting its effectiveness in novel molecule synthesis.\n\n5. **Theoretical and Practical Advantages:** The model generates 3D positions by sequentially producing distances, angles, and torsion angles, ensuring invariance and equivariance, which contributes to the generation of valid and physically plausible molecular geometries. The sequential autoregressive approach also helps capture dependencies between atoms more effectively than methods generating all atom coordinates simultaneously.\n\nCollectively, these results demonstrate that G-SphereNet is a successful generative model for synthesizing new, chemically valid, and property-optimized molecular geometries."}
{"question_id": "96a32bff80b5928198a99a4fc2c2e24cd1a982dd", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The magnitude of the noise in the data, denoted as \u03c3, is set to 2 in the toy Gaussian dataset experiments described. Specifically, the input signal for each class is sampled as \\( x | y = k \\sim \\mathcal{N}(\\mu_k, \\sigma^2 I) \\) with \u03c3 = 2, representing the noise level for all samples."}
{"question_id": "a36f298f8941bf93ad6bdc2ef8db6471e6ca4156", "paper_id": "nlpeer/PeerRead-ACL2017/193", "generation": "The UCCA transition scheme, as implemented in the TUPA parser, supports general directed acyclic graph (DAG) parsing, including multiple parents (reentrancy), discontinuous units, and non-terminal nodes. This is in contrast to existing transition-based parsers for Abstract Meaning Representation (AMR), which are limited to a subset of reentrancies and discontinuities and cannot produce arbitrary DAG parses due to their removal of nodes before all parents are predicted. Similarly, Semantic Dependency Parsing (SDP) uses bilexical dependencies and requires head selection for every relation, which can be problematic for constructions without clear heads; UCCA\u2019s use of non-terminal nodes avoids this issue.\n\nThe TUPA transition set\u2019s ability to handle more general graph structures and its novel transitions for remote edges and discontinuities suggest that applying the UCCA transition scheme to AMR and SDP could extend their parsing capabilities beyond current limitations. The authors explicitly state future work includes applying the TUPA transition scheme to different target representations, including AMR and SDP, to explore the limits of its generality.\n\nTherefore, the use of the UCCA transition scheme has the potential to improve the performance of existing algorithms for AMR and SDP by enabling more comprehensive handling of reentrancies, discontinuities, and non-terminal structures, which are not fully supported by current parsers for these representations."}
{"question_id": "962be5303d07e7707c9750241730986cfeb7d754", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "The significance test was performed using the overall scores for each team in the CoNLL-2014 shared task, comparing gold references and automatic references with both the M\u00b2 scorer and the authors' simpler edit comparison approach. The test involved 1,000 iterations of bootstrap significance testing to evaluate precision, recall, and F\u2080.\u2085 scores."}
{"question_id": "49887aceab5099bc8a45f1f01aa437f760c289a5", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "The 90% Normalized Information Gain (NInGa) achieved by the zero-inflated mixture model on the real neural data example indicates a high level of performance in predicting full response distributions for that dataset. Additional analyses were performed on multiple datasets to demonstrate that NInGa facilitates model comparison across different datasets, suggesting that the metric is consistent and comparable across varying data conditions. However, while the mixture model performs remarkably well at 90% NInGa on the presented dataset, the text does not explicitly state that the exact 90% performance level generalizes to other datasets. Instead, it implies that NInGa provides a robust and interpretable scale for evaluating and comparing model performance across datasets, but actual performance values may vary depending on dataset characteristics."}
{"question_id": "ee956c6a1b9b9808fc77d7d1f8f82237123f5000", "paper_id": "nlpeer/PeerRead-ACL2017/169", "generation": "\"Edit boundaries might be unusual\" refers to the fact that when extracting edits from parallel original and corrected sentence pairs, the start and end points of an edit\u2014i.e., the boundaries delimiting the span of text that has been changed\u2014may not align with conventional or expected token boundaries. This occurs because edits can involve multi-token changes or complex substitutions that do not correspond to simple one-to-one token alignments. Consequently, determining these boundaries is fundamentally an alignment problem, requiring sophisticated methods that go beyond aligning individual tokens, such as linguistically-enhanced alignment algorithms that incorporate part-of-speech and lemma information to better approximate human edits."}
{"question_id": "8f2072e6213f44471d3294973c9cfdd790bc7259", "paper_id": "openreview/ICLR-2022-conf/_X90SIKbHa", "generation": "The proposed short-term recurrence Anderson mixing methods (ST-AM and MST-AM) differ from Newton and standard quasi-Newton methods such as BFGS, L-BFGS, Chord, and Levenberg\u2013Marquardt in several key aspects:\n\n1. **Memory Efficiency and Historical Information Storage**  \n   - Standard quasi-Newton methods like BFGS and L-BFGS require storing multiple historical iterations to form secant equations, leading to additional memory overhead. Limited-memory methods such as L-BFGS reduce this by discarding the oldest iterations, which can result in loss of local superlinear convergence properties and sensitivity to the choice of historical length.  \n   - MST-AM only needs to store two corrected historical iterations and incorporates historical information through orthogonalization. In the ideal case (strongly convex quadratic optimization or SPD linear systems), MST-AM is equivalent to full-memory Anderson mixing, thus preserving historical information without the memory overhead of full-memory quasi-Newton methods.\n\n2. **Computational Cost and Flexibility**  \n   - Newton-like methods often have heavy memory and computational costs, especially in large-scale or high-dimensional problems, and can be less appealing in nonconvex stochastic optimization where only sublinear convergence is expected.  \n   - ST-AM and MST-AM have a memory footprint close to first-order methods and do not require direct access to the matrix, only the residual. This makes them computationally efficient and flexible, as they can accelerate any black-box iterative solver without relying on line search or trust-region techniques.\n\n3. **Convergence and Performance**  \n   - MST-AM has the nature of quasi-Newton methods but can outperform BFGS in certain nonlinear problems such as cubic-regularized quadratic minimization.  \n   - ST-AM is equivalent to full-memory Anderson mixing and GMRES in strongly convex quadratic optimization and can have linear convergence for general nonsymmetric linear systems.  \n   - MST-AM shows competitive or superior convergence compared to full-memory methods and Broyden\u2019s method in nonlinear root-finding problems, such as those arising in multiscale deep equilibrium models.\n\n4. **Applicability**  \n   - Unlike nonlinear conjugate gradient methods that rely on line search, ST-AM can be directly extended to unconstrained optimization where gradients are available but Hessians are costly to obtain.  \n   - MST-AM is suitable for solving nonlinear equations and can be integrated into complex frameworks like multiscale deep equilibrium models, demonstrating comparable or better accuracy and convergence speed than classical quasi-Newton methods.\n\nIn summary, the proposed ST-AM and MST-AM methods offer a memory-efficient, flexible, and computationally attractive alternative to Newton and standard quasi-Newton methods, with competitive or superior convergence properties in both linear and nonlinear problems."}
{"question_id": "1d2837da9c9a557b18715b0482bea0532e3ac176", "paper_id": "openreview/NeurIPS-2022-dabt/rbrouCKPiej", "generation": "The Transformer model (BERT for Anomalies) exhibits lower performance in the IID split compared to the NEAR split because, during the IID period, the anomalies are modeled quite poorly by the language model. This results in slightly lower anomaly detection performance on IID data relative to NEAR data. In contrast, the model effectively captures the outliers in the NEAR split, as indicated by higher PR-AUC for outliers in NEAR. Additionally, the inliers\u2019 performance gradually degrades over time within the IID and NEAR splits, but the outliers are better modeled in NEAR, leading to overall better performance there. This suggests that the model's ability to recognize anomalies is initially limited in the IID split but improves in the NEAR split before both degrade in the FAR split due to distribution shifts."}
{"question_id": "92394e14628bdc9941b0581b43b20ab42dbdd3fd", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "No, the expected label does not always match the most probable label given the noisy data. The paper explains that if a data point x is ambiguous or its true conditional distribution p*(y|x) is flat or multimodal, the one-hot label used in training may not reflect the true underlying distribution. In such cases, the one-hot label (most probable label) can be a \"bad label\" that differs from the expected label distribution p*(y|x). The model's learning path often first moves toward the unknown true label distribution p* before eventually converging to the provided one-hot label, exhibiting a \"zig-zag\" pattern. This indicates that the expected label (true distribution) can differ from the noisy or corrupted label used in training."}
{"question_id": "7869279cfc2dc07fcd82704dc07789afa6de5c82", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "The issue with the point estimate (PE) oracle itself is fundamental and not simply resolved by regularization. The PE approach suffers from overconfident estimation of parameters, especially higher moments beyond the mean, when dealing with sparse neural data and few repeats per stimulus. This leads to poor robustness and, in many cases, the Null model outperforming the PE-based Gold Standard (GS) model. Attempts to improve the GS model by methods such as maximum a posteriori (MAP) estimation or other approaches did not outperform the fully Bayesian posterior predictive GS model. The Bayesian approach, which incorporates uncertainty in parameter estimation via posterior predictive distributions, consistently yields better likelihood performance and robustness across different numbers of repeats and signal-to-noise ratios. Therefore, the problem lies in the PE oracle's lack of robustness and overconfidence rather than a lack of regularization, and a full Bayesian treatment is necessary to address these issues effectively."}
{"question_id": "834016a31e50565175511dcdf3d75a1be44b532c", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The paper defines the difficulty of a sample, termed \"base difficulty,\" as the Euclidean distance between the one-hot label vector \\( e_y \\) and the true underlying label distribution \\( p^*(x) \\), specifically \\(\\| e_y - p^*(x) \\|_2\\). A sample has high base difficulty if:\n\n- It is ambiguous, meaning \\( p^*(x) \\) has several large components and no one-hot label is close to \\( p^*(x) \\).\n- It is not very ambiguous (there exists a one-hot label near \\( p^*(x) \\)), but the observed label \\( y \\) was drawn from a low-probability class, making \\( e_y \\) far from \\( p^*(x) \\).\n\nThis measure serves as a proxy to capture how challenging a sample is to learn, influencing the evolution of the network\u2019s predicted distribution \\( q(x) \\) during training."}
{"question_id": "4d8419e9aeb2f3d606bca8774d3618d08b70c41f", "paper_id": "openreview/ICLR-2023-conf/iYC5hOMqUg", "generation": "Improving the upper bound for the Information Gain evaluation metric, specifically Normalized Information Gain (NInGa), provides several practical benefits:\n\n1. **Robust and Interpretable Performance Assessment:** A well-estimated upper bound allows NInGa to place model likelihoods on a bounded and interpretable scale between meaningful lower and upper limits. This facilitates assessing whether a neural encoding model has achieved its \"best possible\" performance given the dataset, accounting for neural variability and noise.\n\n2. **Reliable Model Comparison Across Datasets:** By providing consistent and robust upper bound estimates, NInGa enables fair comparison of models trained on different datasets that may vary in the number of stimulus repeats, signal-to-noise ratios, and response sparsity. This data-efficiency and robustness ensure that performance metrics are comparable despite dataset heterogeneity.\n\n3. **Mitigation of Overconfidence and Estimation Failures:** The Bayesian approach to estimating the upper bound, as opposed to point estimate methods, addresses the overconfident parameter estimation problems that arise with sparse data and few repeats. This leads to more reliable upper bound estimates that do not underestimate the achievable likelihood, avoiding cases where naive upper bound estimates perform worse than null models.\n\n4. **Enhanced Evaluation of Full Response Distributions:** Improving the upper bound supports the evaluation of models that predict full neural response distributions rather than just mean responses. This is critical for understanding neural variability and testing theories that rely on response variability, such as the Bayesian brain hypothesis and probabilistic population codes.\n\n5. **Guidance for Model Improvement:** Accurate upper bound estimates help identify which parameters of the response distribution are not well predicted by current models, guiding future model development to improve prediction of specific distributional parameters and thus overall model performance.\n\nIn summary, improving the upper bound for NInGa enhances the interpretability, robustness, and comparability of neural encoding model evaluations, enabling more precise quantification of model performance relative to the theoretical maximum achievable given the data."}
{"question_id": "fffbbdd88b4cdc0b98de790921df08f7be1eed7d", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The zig-zag learning path behavior is representative and prevalent across the dataset, particularly for samples with higher base difficulty. A quantitative metric called the zig-zag score was defined to measure how much the learning path deviates towards classes other than the training label. Analysis on toy datasets under different settings shows a strong correlation between base difficulty and the zig-zag score, indicating that harder samples exhibit more pronounced zig-zagging during training. In CIFAR10, samples with flipped (incorrect) labels, which are known to have high base difficulty, display significantly higher zig-zag scores than average samples. Additionally, random samples from CIFAR10 with noisy labels also show clear zig-zag learning paths, while easy samples with correct labels converge quickly without zig-zagging. Even in clean training sets, some ambiguous samples exhibit zig-zag paths, likely due to flat true label distributions. Thus, the zig-zag pattern is a common and meaningful phenomenon across datasets, especially associated with difficult or mislabeled samples."}
{"question_id": "a48eb6eab4e9448324227205ae04b8d47a5b181e", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "In addition to accuracy (ACC) and expected calibration error (ECE), the paper considers the L2 distance between the target supervision distribution \\( p_{\\text{tar}} \\) and the ground truth distribution \\( p^* \\), denoted as \\(\\| p_{\\text{tar}} - p^* \\|_2\\), as a criterion related to generalization. Smaller average L2 distance between \\( p_{\\text{tar}} \\) and \\( p^* \\) is hypothesized and empirically shown to lead to better generalization performance. This distance serves as a measure of the quality of the supervision signal and its closeness to the true label distribution, which influences the learned model's generalization ability."}
{"question_id": "67b6a78d6cea6ff4cd6a6cdd262aaf4e4bfea275", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "For the hard sample in Figure 3, the relationship between the ground truth label distribution p*() and the one-hot encoding e_y is characterized by a notable discrepancy. During training with one-hot supervision, the model's predicted output q initially moves toward p*(), reflecting the ground truth distribution, but subsequently veers away from p*() and moves toward the one-hot label e_y. This results in a \"zig-zag\" learning path where the prediction is first attracted by the more accurate soft label p*(), but eventually is pulled toward the hard one-hot label e_y. The forces driving q are twofold: updates from similar samples tend to pull q toward p*(), while updates from the hard sample itself push q toward e_y. For the hard sample, these forces conflict, causing the zig-zag trajectory. Thus, p*() and e_y differ substantially for the hard sample, with p*() representing a softer, more accurate label distribution, and e_y being a hard, one-hot label that the prediction ultimately converges to under standard training."}
{"question_id": "e7e5b24e35bd512176a8587170677228842e2e24", "paper_id": "egu/esd/14-185-2023", "generation": "No, coastal seaweeds with a very low surface-to-volume ratio are unlikely to be competitive in iron uptake against the mostly small and specialized open ocean phytoplankton that have a high surface-to-volume ratio, especially in iron-limited areas. The macroalgae growth model used in the study does not consider iron limitation because iron is a micronutrient required in low quantities and iron limitation on macroalgae is not widely discussed, particularly for genera such as Saccharina. Additionally, the MOS platform could be designed to supply iron to macroalgae, effectively circumventing natural iron limitation. In contrast, open ocean phytoplankton are adapted to efficiently uptake iron due to their small size and high surface-to-volume ratio, which enhances nutrient absorption in iron-limited environments."}
{"question_id": "06dc05622c2bee5d4a078d2ef7542457179ac90b", "paper_id": "nlpeer/ARR-22/6d1b7e47f044e29d515db7e4762ace8e3f089fe88c2a9de074e8d71f0f78da6169be5756ec9320b5354d2f0ebef3352fedbf5dd82012a7eafde0b0dfbfc311df", "generation": "The baseline model achieves significantly better performance on ACE 2004 and ACE 2005 compared to Yu et al. (2020) likely because it employs a stronger pretrained language model (RoBERTa-base) combined with a BiLSTM layer and a biaffine decoder, which together provide more effective contextualized representations and span classification. In contrast, on OntoNotes 5 and CoNLL 2003, the baseline's performance is on par or slightly inferior to Yu et al. (2020), possibly due to differences in dataset characteristics such as entity types and annotation schemes (flat versus nested NER), as well as the baseline's relatively simple model configuration without additional auxiliary embeddings. The baseline's advantage on ACE datasets, which contain a high proportion of nested entities, suggests that its architecture and pretrained model are particularly effective for nested NER tasks, whereas on flat NER datasets like OntoNotes 5 and CoNLL 2003, the performance gains are less pronounced."}
{"question_id": "fcf91acb3ff79184eb4af002b876fec65732620c", "paper_id": "egu/esd/14-185-2023", "generation": "The partial pressure of carbon dioxide (pCO2) related to the dissolved inorganic carbon from remineralized macroalgal biomass (MOS_DIC) was calculated within the UVic Earth System Climate Model (UVic ESCM) by treating MOS_DIC as a tracer participating in the inorganic ocean carbon cycle. The air\u2013sea exchange flux of MOS-C, which includes MOS_DIC, follows the air\u2013sea gas exchange process as described in Weaver et al. (2001, Sect. 3e). This process calculates the location and quantity of outgassing of MOS-C, thereby determining the pCO2 at the ocean surface for MOS_DIC."}
{"question_id": "924a054e5ec561c4d58306dfd312782d7b4f70ca", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "Students are supervised using refined supervisory signals represented as target distributions \\( p_{\\text{tar}}(y|x) \\) for each training sample \\( x \\). These target distributions can differ from the traditional one-hot labels and include:\n\n1. **One-hot labels**: The standard approach where the label vector \\( e_y \\) has a 1 for the correct class and 0 elsewhere.\n\n2. **Distributions from multiple human annotators**: For example, the CIFAR10H dataset uses a distribution of labels obtained from multiple annotators to estimate \\( p_{\\text{tar}} \\), providing a more nuanced supervision signal.\n\n3. **Label smoothing**: A convex combination of the one-hot label and a uniform distribution over classes, which smooths the target distribution.\n\n4. **Knowledge distillation (KD)**: The teacher network\u2019s output probabilities serve as the target distribution \\( p_{\\text{tar}} \\) for the student. This provides sample-specific soft labels rather than hard one-hot labels.\n\n5. **Self-distillation**: A special case of KD where the teacher and student have the same architecture and are trained on the same data.\n\n6. **Filter-KD**: A novel method where the student is supervised by a smoothed (moving average) version of the teacher\u2019s predictions over training iterations, which stabilizes the supervisory signal and better approximates the true label distribution \\( p^*(y|x) \\).\n\nThus, students are supervised by target label distributions that are often soft, sample-specific, and closer to the true underlying label distribution than one-hot vectors, improving generalization and robustness."}
{"question_id": "27a8c35fcd38d0a141fb5248ad93038196553dfb", "paper_id": "egu/esd/14-185-2023", "generation": "The authors do not explicitly discuss coastal seaweeds competing for iron against open ocean phytoplankton. However, in their macroalgae growth model, iron limitation is not considered. They note that although iron is utilized during macroalgae growth, iron limitation on macroalgae is not widely discussed, especially for the genus Saccharina. They further state that because iron is a micronutrient needed in low quantities, the MOS platform could be designed with an iron supply for the macroalgae. Thus, the model effectively assumes that iron can be sourced from the platform to support macroalgae growth, circumventing potential iron limitation issues."}
{"question_id": "3493acb3c91a1415959829136fe3e250966fc8f0", "paper_id": "openreview/NeurIPS-2022-dabt/8lQDn9zTQlW", "generation": "The prompt design for biomedical natural language processing (BioNLP) tasks differs from general NLP tasks primarily in the following ways:\n\n1. **Use of General Domain Templates Adapted for Biomedical Text:**  \n   Biomedical prompts are largely based on general domain templates originally used to tune models like T0. These templates are populated with biomedical text, making the prompts more \"in-distribution\" rather than completely novel. This adaptation likely provides an advantage to models like T0 in biomedical evaluations.\n\n2. **Formulation of Named Entity Recognition (NER) Tasks as Text Translation:**  \n   Unlike typical classification or generation tasks in general NLP, NER tasks in BioNLP are formulated as text translation tasks. Specifically, the output is a list of all entities found in the prompted text, similar to the TANL approach. This contrasts with standard NER evaluation methods and requires different prompt engineering strategies.\n\n3. **Limited Iterative Prompt Tuning:**  \n   The biomedical prompt templates were developed using PromptSource and did not undergo iterative prompt tuning to improve performance. This contrasts with some general NLP prompt engineering practices where iterative tuning is common to optimize task performance.\n\n4. **Incorporation of Structural Markers for Task Differentiation:**  \n   In multi-task learning settings, BioNLP prompts often include non-linguistic markers (e.g., prefix/suffix tokens) to differentiate structural elements required by a task, such as denoting relation entities. This unifies inputs across tasks but may affect transfer learning differently than in general NLP tasks.\n\n5. **Challenges in Transfer Learning Across Tasks:**  \n   The biomedical task mixture is skewed towards information extraction-style tasks, unlike general NLP which often includes more reasoning-type tasks. This difference influences prompt design and the effectiveness of unified input formats and prompt-based multi-task learning.\n\nIn summary, BioNLP prompt design adapts general NLP templates to biomedical contexts, reformulates NER as text translation tasks, employs structural markers for task-specific input formatting, and generally avoids iterative prompt tuning, reflecting the unique characteristics and challenges of biomedical data and tasks."}
{"question_id": "0c4afb8ced370f2f67477fe4617ff846513cfb6d", "paper_id": "openreview/NeurIPS-2022-dabt/8lQDn9zTQlW", "generation": "Table 2 presents summary statistics for the B I G B I O dataset, including counts of datasets by schema and key attributes. Specifically, it enumerates the number of datasets categorized by schema types such as KB (knowledge base), TEXT, PAIRS, QA (question answering), ENTAIL (entailment), T2T (text-to-text), and ALL. It further distinguishes between public and private datasets, datasets sourced from PubMed, the number of languages represented, and the number of task categories covered."}
{"question_id": "36e40e97993a08a2c5e50bfc69c991334be39e6e", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The noise heads are discarded at test time because their purpose is to handle noisy data during training by absorbing query-irrelevant sounds and background noise. During inference, only the query heads are used to specify and separate the target sound based on the input query. This approach avoids the need for any post-selection process, as the noise heads are only necessary for training to improve noise robustness, and are not required for the actual sound separation task at test time."}
{"question_id": "01163085d0c4776005e14d8621ce2bbdd3cc1c13", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The Filter-KD model outperforms both label smoothing (LS) and knowledge distillation (KD) with optimized temperature in terms of accuracy and calibration. Quantitative results show that Filter-KD achieves higher accuracy and lower expected calibration error (ECE) compared to other methods. For example, in self-distillation on CIFAR10 and CIFAR100 datasets, Filter-KD attains accuracies of 95.63% and 80.09%, respectively, which are higher than those of KD and LS. Similarly, on the TinyImageNet dataset with noisy labels, Filter-KD consistently achieves better accuracy than other baselines, including KD and ESKD. Furthermore, in teacher-to-student distillation scenarios on CIFAR100, Filter-KD surpasses ESKD and KD with optimized temperature, demonstrating superior generalization performance. These improvements are attributed to the refined supervisory signals obtained by Filter-KD, which better approximate the ground truth distribution and mitigate issues such as label noise and high variance in teacher predictions."}
{"question_id": "3b4dcf624027feff21ac63b6e451169e1ca6bf2a", "paper_id": "openreview/ICLR-2022-conf/Iog0djAdbHj", "generation": "The proposed criterion for quantifying generalization performance is the average L\u00b2 distance between the target supervision distribution \\( p_{\\text{tar}} \\) and the ground truth distribution \\( p^* \\) over the input samples, expressed as \\( \\mathbb{E}_x \\left[ \\| p_{\\text{tar}}(x) - p^*(x) \\|_2 \\right] \\). Smaller values of this average L\u00b2 distance indicate better quality of the supervision signal, which leads to a better approximation of the true risk and consequently improved generalization performance of the learned model. This criterion is supported by theoretical bounds on the variance of the risk estimate and empirical results demonstrating a strong negative correlation between the L\u00b2 distance and generalization metrics such as accuracy and expected calibration error."}
{"question_id": "0c64726cf3b593196fd8f350d0f2c2d4aba98d1e", "paper_id": "nlpeer/ARR-22/7e701552b7cfaad6239d3d5dfcd9da1852a03a66907c789fe65d368f1c2845e723adcecd7f7008849eef13c7ca467be73de53747c3376a6bb46d1911ed1b6409", "generation": "ELECTRA's binary classification head (RTE) labels each token in the input sequence as either a \"good\" gap or not. Specifically, all the gaps in the training data are replaced with the first intended target answer and labeled as positive (\"good\"), while the remaining tokens are labeled as negative (\"not good\")."}
{"question_id": "c67443bf273772ac2d4297564f839c0a0229e6eb", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "The greyed out portion of Figure 3 represents the noise heads in the CLIPSep-NIT model. These noise heads are used during training to model query-irrelevant noise in the audio mixture. They allow the model to separate and assign noisy or irrelevant sounds away from the target sound sources specified by the query heads. During inference, the noise heads are discarded, and only the query heads are used for sound separation. This design helps the model handle noisy unlabeled data by explicitly modeling noise components, improving separation performance and zero-shot modality transferability."}
{"question_id": "a81ef48de406906c5a847928da2bc47079136f55", "paper_id": "nlpeer/COLING2020/1681", "generation": "The number of distinct phrases used for phrase addition in the adversarial attack generation is not explicitly specified. The text states that a range of simple phrases were used for phrase addition (e.g., appending \u201c\u2026 but he wasn\u2019t sure\u201d and prepending \u201cit is true: ...\u201d), but for simplicity, only the phrase \u201cit is true\u201d is presented in the main results, with other phrases mentioned as being in the Appendix. Therefore, the exact number of distinct phrases used is not provided."}
{"question_id": "c5b7931f3e58dd10d67e388fcd5680c37e267022", "paper_id": "nlpeer/ARR-22/0f8bc751a0472564732a976cd152643d92d5393c2e77c8234f386a801883d09ff9540378b3dab5834544a246c22f292d5b1c4d370219943c9a19a1626a8f8781", "generation": "The threshold values are selected to ensure an appropriate balance between the classes of constituents and distituents, reflecting their natural distribution in sentences. Specifically, the lower and upper bounds of the threshold are chosen so that the distribution of class labels approximates a ratio of about 1:10, with the distituent class being the majority. This ratio is a crude estimate that accounts for the typically larger sentence lengths in the dataset (WSJ-Full section). Linguistically, it is certain that distituents must necessarily outnumber constituents, which justifies setting the thresholds to reflect this skewness. For the self-training experiments, the thresholds are set as \u03c4_min = 0.0005 and \u03c4_max = 0.995, treating outside strings satisfying these bounds as gold-standard examples of constituents and distituents, respectively. This approach aligns with prior work (e.g., the Constituent-Context Model) that uses smoothing ratios to account for the skewness of random spans more likely representing distituents."}
{"question_id": "5678b6bf40f5958402473fd66a08dc836eaa98a7", "paper_id": "nlpeer/COLING2020/1681", "generation": "The coreference resolution pipeline depicted in Table 1 is an idealized and hypothetical model rather than a universally accepted standard in the field. It sequentially involves markable detection, coreference resolution in the source language, and pronoun translation into the target language, aiming to produce the correct pronoun in the target language. While the coreference steps resemble the rule-based approach implemented in Stanford CoreNLP\u2019s Coref-Annotator, current neural machine translation (NMT) models are unable to decouple these individual steps effectively. The pipeline serves as a conceptual framework to isolate and evaluate specific steps through targeted examples, rather than representing a consensus or universally adopted methodology in coreference resolution research."}
{"question_id": "9824d5fa73a188c99c7d977a3dda3d2b24856f9d", "paper_id": "openreview/ICLR-2023-conf/_01dDd3f78", "generation": "Yes, the results of the experiments on the myocardial infarction complications dataset regarding mortality risk attribution have been reviewed and compared with existing medical literature. The case study demonstrates that the concept gradient (CG) scores align well with the severity of mortality risk described in the medical literature for various complications. High-risk complications such as relapse of myocardial infarction are assigned high relevance by CG, while lower-risk complications such as post-infarction angina are assigned lower relevance. This alignment supports the validity of the interpretations and indicates that the results have been evaluated in the context of expert medical knowledge."}
{"question_id": "587b8f363bb9be4e82b38b70f74608f844559b6f", "paper_id": "nlpeer/COLING2020/1681", "generation": "The data augmentation strategy, termed Antecedent-free augmentation (afa), involves identifying sentences in the training data where a coreferential pronoun \"it\" refers to an antecedent that is not present in the current or previous sentence. These sentences are selected as candidates for augmentation. For each candidate, two new training examples are created by modifying the gender of the German translation of \"it\" to masculine (\"er\") and feminine (\"sie\"), while keeping the source English sentence unchanged. For example, the original sentence pair \"I told you before. <SEP> It is red.\" translated as \"Ich habe dir schonmal gesagt. <SEP> Es ist rot.\" is augmented with two additional target sentences: \"Ich habe dir schonmal gesagt. <SEP> Er ist rot.\" and \"Ich habe dir schonmal gesagt. <SEP> Sie ist rot.\" Coreferential pronouns and antecedents are identified using a coreference resolution tool. The model is then fine-tuned on a dataset consisting of both the original candidates and the augmented samples. This approach aims to break the strong prior bias towards the neuter pronoun \"es\" in German and improve coreference resolution capabilities in pronoun translation. The augmentation uses dependency parsing to determine pronoun case, which is necessary due to gender and case distinctions in German pronouns. Fine-tuning is performed with careful selection of learning rates to balance performance across masculine, feminine, and neuter pronouns."}
{"question_id": "31314c6ad7630579c350af928493caac9c563dbb", "paper_id": "nlpeer/ARR-22/0f8bc751a0472564732a976cd152643d92d5393c2e77c8234f386a801883d09ff9540378b3dab5834544a246c22f292d5b1c4d370219943c9a19a1626a8f8781", "generation": "The minimum and maximum threshold values are set as follows:  \n\u03c4_min = 0.0005  \n\u03c4_max = 0.995"}
{"question_id": "0258b0f39ec3f7316f9d299a25a7cd36274e9631", "paper_id": "openreview/ICLR-2023-conf/_01dDd3f78", "generation": "No, the concepts were not ranked according to the TCAV_Q score. Instead, the study used Concept Gradients (CG) as the recommended method for assessing the relative importance of multiple concepts. CG extends beyond the linear assumption of TCAV by modeling concepts with potentially non-linear functions and computing concept relevance via gradients combined through the chain rule. Empirical results showed that CG consistently outperforms TCAV in concept importance attribution both locally and globally across different model architectures and layers. The evaluation involved finetuning the target model for concept prediction and comparing local and global recall metrics, rather than using the TCAV_Q score for ranking concepts."}
{"question_id": "1aa55ba3f2f47947ad4df928f35cfc4c7d7035ec", "paper_id": "nlpeer/ARR-22/21dd1d5e232c7abf421302a95ef2c9ef44c7bad069da7ca4129a8cf1bfbd6f660afee123144ebcbd8da698aed212e2ca1151a13bca7aaef50ce59d27d23aa83a", "generation": "The focus of this paper is on task-oriented cross-lingual natural language understanding (XNLU), specifically addressing two correlated objectives: intent classification, which identifies the type of user command, and entity/slot recognition, which identifies relevant entities in the utterance including their types. The paper introduces and evaluates zero-shot transfer methods to improve these tasks across multiple languages."}
{"question_id": "cd020940c9b12a598dae5fc4fde1d63c2d88d88d", "paper_id": "egu/esd/14-185-2023", "generation": "The study does not explicitly consider the potential for longer permanence of CO2 storage through sediment carbonate dissolution induced by high respiratory dissolved inorganic carbon (DIC) at the ocean bottom. The modeling focuses on the fate of macroalgae-sunk carbon (MOS-C) and its remineralization, oxygen impacts, and carbon leakage primarily via upwelling and outgassing processes, especially in the Southern Ocean. It highlights that when sunken macroalgal biomass is free from remineralization, the contained carbon is permanently isolated from the atmosphere and stored in the ocean, increasing carbon dioxide removal (CDR) potential. However, the role of sediment carbonate dissolution as a mechanism for enhanced permanence of CO2 storage is not addressed or parameterized in the simulations."}
{"question_id": "8fc13b01107e614b030a2c7dbc65aa19d0363778", "paper_id": "nlpeer/ARR-22/21dd1d5e232c7abf421302a95ef2c9ef44c7bad069da7ca4129a8cf1bfbd6f660afee123144ebcbd8da698aed212e2ca1151a13bca7aaef50ce59d27d23aa83a", "generation": "The authors did not report running their experiments multiple times with different random initializations to confirm the results. The experimental setup mentions using fixed hyperparameters between experiments and a minimalist setup focusing on methods rather than hyperparameter tuning or custom architecture design, but there is no indication of multiple runs with different random seeds for statistical robustness. Statistical significance testing was conducted using a two-tailed z-test for the difference of proportions to compare with previous state-of-the-art results, but this does not imply multiple experimental runs with different initializations."}
{"question_id": "1a42a5af41f66bb6428c643d96fd05eba81ce635", "paper_id": "nlpeer/PeerRead-CONLL2016/166", "generation": "Yes, the proposed features and experiments account for named entities that are not listed on Wikipedia. The cross-lingual wiki\ufb01er grounds words and phrases from any language to English Wikipedia entries, providing language-independent features through Wikipedia categories and FreeBase types. Even when a named entity is not an exact entry in Wikipedia, the wiki\ufb01er can link parts of the entity (e.g., individual words) to related Wikipedia entries, thereby providing useful typing information. For example, although \u201cAlbrecht Lehmann\u201d is not an entry in Wikipedia, the wiki\ufb01er links \u201cAlbrecht\u201d and \u201cLehmann\u201d to people, which still yields valuable signals for NER. This approach allows the model to leverage partial or approximate grounding to Wikipedia to improve recognition and typing of named entities beyond those explicitly listed in Wikipedia."}
{"question_id": "9c04f85fb5baad69d0ae21c1c2c07abc0422bd55", "paper_id": "nlpeer/ARR-22/013b9bf63a6f68fd0c3ecc36f8cbe2ad5bc92ea3bfe5a9f6c15eb056ecc4f858718410182c3765b2dc2695ae29ba08fb5dea5fc495faf2bbb77205bc3f765fcd", "generation": "To ensure a more diverse cross-cultural representation when building an NLP model, several concrete steps can be taken across data collection, annotation, and model training:\n\n1. **Data Collection and Curation:**\n   - Use data sources that are appropriate and representative of the target cultures for downstream NLP applications, avoiding culturally biased sources such as certain web corpora or Wikipedia without careful curation.\n   - Recruit geographically and culturally diverse annotators, ideally native speakers, to provide both data and annotations, as exemplified by initiatives like the Masakhane community and MasakhaNER dataset for African languages.\n   - Collect and release all annotations, including disagreeing ones, to capture cultural nuances and reduce hegemonic bias.\n   - Carefully document the annotation process to ensure transparency and reproducibility.\n   - Strive for large-scale, diverse datasets that cover a wide range of cultures, recognizing the challenge of cost and effort but leveraging open and diverse communities to facilitate scalability.\n\n2. **Annotation Practices:**\n   - Employ a diverse pool of annotators to reduce cultural bias and capture multiple perspectives, especially for subjective tasks such as detecting affect, aggression, or hate speech.\n   - Release all annotation variants rather than aggregating to a single ground truth to allow models to generalize better across cultural differences.\n\n3. **Model Training:**\n   - Apply data sampling techniques such as exponential smoothing of language sampling rates to reduce skew towards high-resource languages and improve performance on low-resource languages.\n   - Use group-aware optimization methods like Group Distributionally Robust Optimization (group DRO) or Worst-Case-Aware Curriculum Learning to optimize worst-case performance across cultural or language groups, thereby reducing representation disparity.\n   - Consider cultural factors explicitly when selecting source languages for transfer learning to improve pragmatic task performance.\n   - Balance model training objectives to avoid overemphasizing majority cultures, which can lead to cultural homogenization, and instead ensure minority cultures receive adequate representation.\n   - Recognize that cross-lingual countermeasures are culture-agnostic and thus apply similar principles at the cultural level rather than only at the language level to address cultural disparities.\n\n4. **Translation and Evaluation:**\n   - Use culturally sensitive human translation or original data from the target culture for evaluation to avoid cultural bias introduced by literal or automatic translation.\n   - Allow translators to paraphrase or substitute culture-specific concepts with culturally appropriate equivalents.\n\nThese steps collectively aim to increase the representation of diverse cultures in both the data and the models, mitigate cultural biases, and improve the fairness and applicability of NLP systems across cultural contexts."}
{"question_id": "44279226e8c3ea5497ac4c43754e3e890e0183fb", "paper_id": "openreview/ICLR-2023-conf/XrMWUuEevr", "generation": "The best way to compare the performance of different models or architectures (e.g., x, x', x'') is to perform multiple training reruns with different random seeds and evaluate each model multiple times by drawing different support sets. Performance metrics such as area under the precision-recall curve (AUC-PR) or the difference to a random classifier (\u2206 AUC-PR) should be reported as averages across these reruns and support set samplings, including standard errors or standard deviations to capture variability across tasks, training reruns, and support sets.\n\nStatistical significance of performance differences between models should be assessed using paired statistical tests such as the paired Wilcoxon rank sum test on the metrics (e.g., AUC and \u2206 AUC-PR). Additionally, task-wise model comparisons can be visualized using scatterplots where each point corresponds to a task, and the number of tasks on which one model outperforms another can be tested for significance using binomial tests.\n\nThis approach ensures a robust and fair comparison by accounting for variability due to random initialization, data sampling, and task heterogeneity."}
{"question_id": "ba25580bbc4ec4f20348cefaf968e1cdea408642", "paper_id": "openreview/ICLR-2023-conf/XrMWUuEevr", "generation": "The 8 positive and 8 negative examples used in the domain shift experiment in Section 5.3 refer to molecules selected per task from the Tox21 training set to build the support set. The positive examples are active molecules for the specific toxic effect task, while the negative examples are inactive molecules for that task. These support sets, consisting of 8 active (positive) and 8 inactive (negative) molecules per task, were randomly selected to enable few-shot learning evaluation under domain shift conditions."}
{"question_id": "b97a690598219404cca037101ff05ec348b5e525", "paper_id": "openreview/NeurIPS-2022-dabt/heBKnuV42O", "generation": "There is no indication of a plan to open-source the proprietary medical knowledge base or the telemedicine software. The dataset DDXPlus is synthetic and generated using a proprietary medical knowledge base and a commercial rule-based automatic diagnosis (AD) system provided by Dialogue Health Technologies Inc. While the dataset and code for reproducing experimental results are made publicly available, the proprietary knowledge base and the rule-based AD system themselves remain proprietary and are not released as open-source assets."}
{"question_id": "b9d3ed7981d9f1e47fea48aaf2b4037fe7b25658", "paper_id": "openreview/ICLR-2023-conf/_01dDd3f78", "generation": "The purpose of finetuning the same network for the concept model \\( g \\) is to ensure that \\( g \\) is similar to the target model \\( f \\) in terms of both architecture and weight initialization, which leads to better alignment in the utilization of the input representation. This similarity improves the accuracy and meaningfulness of concept gradient attributions because the gradients propagated through the shared input representation are more aligned between \\( f \\) and \\( g \\). Finetuning \\( g \\) from the pretrained weights of \\( f \\) allows the concept model to converge to a solution closer to \\( f \\), thereby capturing the concept effectively while maintaining similar input feature usage. Additionally, freezing certain layers during finetuning can further constrain \\( g \\) to remain similar to \\( f \\), balancing concept prediction accuracy and gradient alignment for improved importance attribution."}
{"question_id": "70418ac3cb9f40b039a74031b89324e2b891ccf5", "paper_id": "nlpeer/PeerRead-ACL2017/699", "generation": "The word embeddings are randomly initialized with a uniform distribution in the range [-0.1, 0.1]."}
{"question_id": "65df6e41f1c8c77eec8b264ef0a3dcd104abb9dc", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The higher average out-degree of normal users compared to fraudsters is because fraudsters tend to fill fewer emergency contacts in general. This behavior aligns with their primary purpose of defrauding the platform, as providing more emergency contacts is not helpful for committing fraud. Normal users, on the other hand, typically have more emergency contacts, resulting in a higher average out-degree."}
{"question_id": "92c772c75354552e709f16f3e3b15a31e395f1cf", "paper_id": "openreview/NeurIPS-2022-dabt/heBKnuV42O", "generation": "The rationale for altering the original disease incidence and prevalence rates from the knowledge base, despite it being compiled from extensive medical literature, is to address limitations in the data generation process and to produce a more balanced dataset. Specifically, some diseases have incidence rates exceeding 100%, which would lead to a highly imbalanced dataset dominated by a few pathologies (e.g., URTI, Viral pharyngitis, and Anemia). To prevent this, incidence rates are capped at 100%. Conversely, some diseases have extremely low incidence rates, resulting in their underrepresentation in the synthesized patient population. To ensure these pathologies are sufficiently represented, a minimum incidence rate of 10% is imposed. These adjustments help create a dataset with a more balanced distribution of pathologies, facilitating better training and evaluation of automatic diagnosis systems."}
{"question_id": "bf41e9f2b170cb8e1801812167b945e8f56aa8cb", "paper_id": "openreview/ICLR-2022-conf/hcQHRHKfN_", "generation": "Table 1 demonstrates the necessity of both reward switching and intrinsic rewards in the RSPO algorithm for discovering optimal cooperative Nash Equilibria (NE) in multi-agent reinforcement learning tasks with many local optima. Specifically, when intrinsic rewards are turned off, the learning policy fails to escape infeasible subspaces, resulting in a low acceptance rate of trajectories and failure to discover optimal cooperative strategies. Behavior-driven exploration alone is insufficient to find the optimal NE, highlighting the importance of reward-driven exploration to maximize divergence in states and actions. Thus, the results in Table 1 underscore that the combination of reward switching and intrinsic rewards is crucial for effectively exploring the policy space and achieving optimal cooperation in complex multi-agent environments."}
{"question_id": "96525f380b4694ec387b707fa87e78c972a12b4c", "paper_id": "openreview/NeurIPS-2022-dabt/FPgCB_Z_0O", "generation": "Training on the DARTset dataset demonstrates significant effectiveness in improving hand pose estimation and mesh reconstruction performance, as evidenced by multiple evaluations and ablation studies:\n\n1. **Cross-Dataset Evaluation:**  \n   - When training with a mixture of DARTset and FreiHAND datasets (\"Mixed\" training), the CMR model improved by 8.9% on PA-MPVPE on the FreiHAND testing set, indicating that DARTset complements challenging real-world datasets and enhances generalization.  \n   - Mixed training also benefits the METRO model, showing reduced errors compared to training on FreiHAND alone, although a domain gap exists due to differences in textures, accessories, and pose distributions.\n\n2. **Benchmark Results on DARTset:**  \n   - Four mainstream hand reconstruction methods benchmarked on DARTset show competitive performance, with Integral Pose achieving a PA-MPJPE of 3.52 cm and CMR achieving a PA-MPVPE of 3.46 cm, indicating that DARTset supports effective training of both keypoint-based and mesh-based models.\n\n3. **Ablation Study on Accessories:**  \n   - Incorporating accessories in training data from DARTset improves performance: Integral Pose shows a 7.8% improvement in PA-MPJPE, and CMR shows improvements of 5.9% in PA-MPJPE and 7.2% in PA-MPVPE when trained with accessories versus without.\n\n4. **Pose Diversity and Realism:**  \n   - DARTset features a wider and more continuous pose distribution than FreiHAND, achieved by interpolating between synthetic and real poses, which enhances the dataset's generalizability and reduces domain gaps.\n\n5. **Utility in Training Pipelines:**  \n   - DARTset can be mixed with real-world data during training to boost performance and generalization in hand pose estimation and mesh recovery tasks.\n\nIn summary, training on DARTset, especially when combined with real-world datasets, effectively improves the accuracy and generalizability of hand pose estimation and mesh reconstruction models, benefiting from its rich textures, diverse accessories, and wide pose distribution."}
{"question_id": "89fb9729921ad950b90987550b32f9ede60c8a8c", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "Yes, during the interpolation experiment on the Mauna Loa CO2 dataset, the Deep Gaussian Process (DGP) with RBF kernels failed to capture the data trend. This failure led to mean reversion and an over-estimation of the prediction uncertainty. In contrast, the Deep Variational Implicit Process (DVIP) was able to capture the data trend effectively in the missing gaps, producing smoother prior samples. The issue with DGPs could be addressed by using compositional kernels, but this requires kernel search algorithms."}
{"question_id": "b879c4d1344942fd8fab9fbe8fc495f4ae67c0b0", "paper_id": "openreview/ICLR-2023-conf/VA1YpcNr7ul", "generation": "The provided chunks do not contain any explicit definition or statement regarding the nature or domain of the parameter \\(\\omega\\) in Definition 1.1. There is no direct information specifying whether \\(\\omega\\) is defined as an element of \\(\\mathbb{R}\\) or any other set in Definition 1.1."}
{"question_id": "1cd2ab406e0d21ae6597092cde6d7a2fca652f82", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The provided information does not explicitly address whether dynamic nodes need to be included in the data for the dynamic graph approach to be effective. However, the dataset DGraph is described as a real-world dynamic graph in the finance domain, containing about 3 million nodes and 4 million dynamic edges, with over 1 million ground-truth nodes. The dynamic nature of the graph, including temporal dynamics, is emphasized as a key property that is preserved and utilized for accurate fraudster identification and further exploration of graph anomaly detection (GAD) research.\n\nThe importance of temporal dynamics is highlighted as a limitation in most current GAD datasets, which often ignore temporal information despite its prevalence in real-world scenarios. DGraph overcomes this limitation by including dynamic information, which is implied to be crucial for effective modeling and detection in dynamic graph settings.\n\nWhile the text does not explicitly state that dynamic nodes must be included, it strongly suggests that preserving and utilizing dynamic information (which inherently involves dynamic nodes and edges) is essential for advancing GAD methods and achieving effective detection performance in dynamic graph contexts.\n\nTherefore, for a dynamic graph approach to be effective, it is necessary to include dynamic nodes and their temporal information as part of the data, as exemplified by the construction and use of DGraph."}
{"question_id": "5227809e5dbd6a7ef588b7a84fc243e6cd0eed8d", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "The provided evidence chunks do not contain any information regarding the compensation rate for the annotators hired for the Mechanical Turk crowdsourcing work."}
{"question_id": "60c2bf23190bf1120b8652501ff951bae6f3e046", "paper_id": "openreview/NeurIPS-2022-dabt/2rQPxsmjKF", "generation": "The rationale for comparing 2-hop homophily (with background nodes as intermediate nodes, denoted as \"xBx\" or target nodes as intermediate nodes \"xTx\") with 1-hop homophily (direct connections, denoted as \"xx\") in Figure 3(d) is to investigate the role of background nodes in the connectivity and semantic relationships within the graph. The comparison reveals that 2-hop connections involving background nodes as intermediates have a higher homophily ratio than direct 1-hop connections. Moreover, the homophily ratios of 2-hop connections are greater than those of directly connected nodes. This indicates that background nodes serve as important semantic bridges that enhance the similarity (homophily) between nodes beyond direct edges. Therefore, analyzing 2-hop homophily helps to demonstrate the significance of background nodes in maintaining network connectivity and providing abundant semantic information, which is valuable for tasks such as fraud detection."}
{"question_id": "c3790b0e0925f080c8d91160ae348f35ac367993", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "Word overlap was used instead of an exact match when comparing F1(s\u2217_{j,1}, s_{j,1}) because the prediction may mention the same entity as the gold answer but with slightly different names. Using the word-level F1 score allows detection of entities as the same as long as their first mentions have some word overlap, thereby avoiding false positives in detecting invalid questions due to minor naming differences."}
{"question_id": "721388955b97db097e963c4b221fe9af9e2da4ae", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "When the absolute accuracy numbers are higher than the F1 scores, it indicates that human evaluation reports higher model performance compared to automatic evaluation. This discrepancy arises because automatic evaluations rely on gold answers that cannot capture all possible correct answers to open-ended or multi-answer questions, leading to lower F1 scores. In contrast, human evaluators can judge the correctness of answers more flexibly, resulting in higher accuracy measurements. Thus, the higher absolute accuracy reflects the broader recognition of correct answers by humans, whereas the lower F1 scores reflect the limitations of fixed gold-standard answers in automatic evaluation."}
{"question_id": "672a51e77d6a34b48ad5a93aa1a37139d71e3c72", "paper_id": "nlpeer/ARR-22/1370310ca3e0e0d092a2e8b58f90b0d692331caa6e3ce5ca6dacbef926f2b01ff49c81282f32c554fa66c04979a125b7548c9692c2091bb2673642102d85412d", "generation": "The rationale for analyzing the self-attention functions in the last layer of the BERT model is that selecting the highest correlating head from the last attention layer generally produces weaker correlation with human attention than using attention flow, which captures the layered propagation structure across all layers. Although attention flow propagated back from early, medium, and final layers does not considerably change correlations to human attention, focusing on the last layer's self-attention functions provides a consistent reference point for comparison. This approach highlights that raw attention weights from a single layer, including the last, do not reliably correspond to token relevance, emphasizing the importance of considering attention flow rather than isolated layer attention weights."}
{"question_id": "abf4bcae7809ff5b01e8cf7fdb201caa7b8421ac", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The computation time referred to is the process time per Q-function update during the overall training loop (e.g., lines 4\u20139 in Algorithm 2). Specifically, REDQ requires process times in the range of approximately 2200\u20132300 milliseconds per update, whereas SAC requires process times in the 800\u2013900 millisecond range. This indicates that REDQ runs more than twice as slow as SAC in terms of the time needed to update Q-functions within each training iteration."}
{"question_id": "4bb993f44c76628b67f41da43c78aa82b50cbc19", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The full name of the algorithm DUVN is \"Double Uncertainty Value Network.\""}
{"question_id": "f567015ed8777554298ac8d5b511b255c317d3da", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "Dropout on its own does not work effectively for the proposed method because it destabilizes the learning of Q-functions. Specifically, using dropout alone causes significant oscillations in the Q-function loss and in the variance of the gradient of the loss with respect to the Q-function parameters. This instability in learning impairs the propagation of Q-values and consequently degrades overall performance. The introduction of layer normalization alongside dropout suppresses these oscillations and stabilizes the learning process, enabling better Q-value propagation and improved performance. Thus, the combination of dropout and layer normalization is crucial, as layer normalization mitigates the learning instability caused by dropout, which dropout alone cannot achieve."}
{"question_id": "9dfb86a40b292918a304254d03b72b6fee37e740", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The target KB size used for MedMentions in the experiments is 2.3 million entities."}
{"question_id": "9ee8e92c0faa3a3d17cff68d75d28be3dca8e8c4", "paper_id": "openreview/ICLR-2022-conf/hcQHRHKfN_", "generation": "The parameter \\(\\delta\\) in the Reward-Switching Policy Optimization (RSPO) framework serves as a threshold in the trajectory filtering function \\(\\phi(\\tau)\\), which determines whether a sampled trajectory \\(\\tau\\) is accepted or rejected based on its novelty relative to previously discovered policies. Adjusting \\(\\delta\\) directly affects the acceptance or rejection of trajectories during training, thereby influencing the balance between exploitation of extrinsic rewards and exploration via intrinsic rewards.\n\nSpecifically, a smaller \\(\\delta\\) leads to more trajectories being accepted early in training, resulting in policies that converge similarly to the policy gradient baseline with limited diversity. Conversely, a larger \\(\\delta\\) causes more trajectories to be rejected initially, promoting extensive exploration but at the cost of slower convergence and increased training time.\n\nIn the context of Table 1, which reports the discovery of non-trivial local optima and policy diversity, improper tuning of \\(\\delta\\) can negatively impact performance. If \\(\\delta\\) is too small, RSPO may fail to discover diverse or non-trivial strategies, yielding results comparable to standard policy gradient methods. If \\(\\delta\\) is too large, the method may spend excessive time exploring, delaying convergence and potentially reducing data efficiency.\n\nTo mitigate sensitivity to \\(\\delta\\), the authors propose an automatic threshold selection method where \\(\\delta_j = \\alpha \\cdot D(\\pi_{\\text{rnd}}, \\pi_j)\\), with \\(\\alpha\\) as a task-specific hyperparameter and \\(\\pi_{\\text{rnd}}\\) a fully random policy. This approach simplifies tuning and stabilizes training, ensuring that the acceptance rate of trajectories starts lower and smoothly converges to 1, which empirically supports effective discovery of diverse and high-quality policies as reflected in Table 1.\n\nIn summary, adjusting \\(\\delta\\) impacts the trade-off between exploration and exploitation in RSPO, affecting the diversity and quality of discovered policies. Proper tuning or automatic selection of \\(\\delta\\) is crucial for achieving the improved results shown in Table 1, including the discovery of non-trivial local optima and enhanced policy diversity."}
{"question_id": "7b375e548c69cd6c0b0d75953da0021adb9e2a7e", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The computation time per update for SAC is in the range of approximately 835 to 870 milliseconds across different environments (e.g., 870 msec for Hopper-v2, 835 msec for Walker2d-v2, 848 msec for Ant-v2, and 854 msec for Humanoid-v2). In contrast, REDQ requires significantly more time per update, approximately 2269 to 2340 milliseconds (e.g., 2269 msec for Hopper-v2, 2283 msec for Walker2d-v2, 2277 msec for Ant-v2, and 2340 msec for Humanoid-v2).\n\nThus, REDQ takes roughly 2.5 to 2.7 times longer than SAC per update."}
{"question_id": "d49df57b22ec381fed263033d6a02678f16a18c1", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "The width of the model, defined as the number of units per layer, does not significantly affect the performance when the prior functions are the same for all units. This is because each unit in a layer shares the same prior implicit process (IP), and thus increasing the number of units (width) does not introduce additional diversity in the prior functions. Instead, the model's flexibility and performance improvements arise primarily from the depth (number of layers) and the adaptation of the prior IP parameters to the observed data, rather than from increasing the width."}
{"question_id": "4a12daa058e224f39629de8997d5de7c8b0c2d3c", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "Using the complete graph for training is avoided because it introduces noise and undesirable connections that cross cluster boundaries. Specifically, constructing the graph by connecting all pairs of mentions (e.g., via a minimum spanning tree) ignores the directionality of nearest neighbor relationships, leading to noisy edges. This results in edges that connect mentions across different clusters, which is undesirable for modeling coreference. The pruning procedure removes edges with weights above a dissimilarity threshold and those that violate constraints ensuring each cluster contains at most one entity and maintains valid connectivity. This pruning yields a directed minimum spanning arborescence structure rooted at entity nodes, which better reflects the coreference relationships and provides a useful inductive bias for training. Thus, pruning the graph minimizes dissimilarity between coreferent mentions and entities, reduces noise, and preserves cluster validity, improving the quality of positive training examples and overall model performance."}
{"question_id": "2074c1cd08c7d4b134ac01c5ee57f13765a4cc47", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "Layer normalization offers distinct advantages over other regularization schemes for improving the performance of dropout in deep reinforcement learning, particularly in high update-to-data (UTD) ratio settings. Specifically:\n\n1. **Stabilization of Learning Instability Caused by Dropout:** Dropout alone destabilizes the learning of Q-functions, leading to significant oscillations in Q-function loss and its gradient variance. Layer normalization effectively suppresses these oscillations, thereby stabilizing the learning process. This stabilization enables better Q-value propagation and consequently improves overall performance, especially in complex environments such as Ant and Humanoid.\n\n2. **Synergistic Effect with Dropout:** The combination of dropout and layer normalization produces a synergistic effect that significantly enhances sample efficiency and reduces estimation bias compared to using either technique alone or neither. This synergy is particularly pronounced in complex tasks and when using small ensemble sizes of Q-functions.\n\n3. **Superiority Over Other Normalization Methods:** Compared to batch normalization and group normalization, layer normalization is more effective in improving average return and reducing estimation bias when combined with dropout. Batch normalization does not significantly improve performance and leads to unstable Q-function learning in high UTD settings. Group normalization, while somewhat effective when combined with dropout, does not match the stability and performance gains achieved by layer normalization.\n\n4. **Importance of Variance Re-scaling:** The variance re-scaling component of layer normalization is critical for its effectiveness. Variants of layer normalization without variance re-scaling fail to improve performance and result in unstable Q-function learning, indicating that this feature is a key factor in the synergistic effect with dropout.\n\nIn summary, layer normalization uniquely stabilizes the learning dynamics disrupted by dropout, enhances the synergy with dropout for bias reduction and sample efficiency, outperforms other normalization methods in high UTD reinforcement learning settings, and relies on variance re-scaling to achieve these benefits."}
{"question_id": "9073f9407118eaf0b34170000a57846d672b4a5d", "paper_id": "nlpeer/ARR-22/25eb5aa88234e90e4d4729fea900619c7d42062b6c8c967d02567ea0548fbc60fc4cacfd968a7f10d96f9cd92302173b1db10f7767e3b7e379ff1260c12db74d", "generation": "The four conversational question answering (CQA) models were selected based on their different model architectures and training strategies. Specifically, the chosen models include:\n\n1. BERT: A simple baseline model that concatenates the previous two turns of question-answer pairs, the current question, and the passage as input to predict the answer span.\n\n2. GraphFlow: A model employing a recurrent graph neural network on top of BERT embeddings to model dependencies between the question, conversation history, and passage.\n\n3. HAM (History Attention Mechanism): A model that uses a history attention mechanism to softly select the most relevant previous turns in the conversation.\n\n4. ExCorD: A model that incorporates a question rewriting module trained on CANARD to generate context-independent questions, using both original and rewritten questions to train the QA model; it achieves state-of-the-art performance on QuAC.\n\nThese models were chosen to represent a range of architectures and training approaches for direct comparison in human evaluation and analysis."}
{"question_id": "8d69a05246c31778897996bc35b60061f15554f3", "paper_id": "nlpeer/COLING2020/1550", "generation": "The results presented in the paper are primarily task- and dataset-specific. The study focuses on the task of stance detection related to Fake News using two specific datasets (FNC-1 and FNC-1 ARC), which involve multi-class classification of stances between headlines and associated news articles. The authors explicitly caution against generalizing their findings without further validation, stating that while their findings can serve as starting points for related experiments, they do not conclude that these results are directly transferable to other Fake News related datasets with different tasks or label sets. The performance differences observed, such as RoBERTa outperforming XLNet due to the segment-level nature of the stance detection task, further emphasize the specificity of the results to the examined task and datasets."}
{"question_id": "52e2f447dc9bcf3dc208f99a89d4b59da4004923", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The embedding matrix for relative positions (W_p) is randomly initialized."}
{"question_id": "c3b651600b60b22f2a4c805aeb87745aff3c0c84", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The full name of the proposed method \"REDQ\" is Randomized Ensembled Double Q-learning."}
{"question_id": "45a145511dd96e98d18e5ac09f454b95ceee5a38", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The left and right neighbors of the trigger word are used to create the representation by concatenating their word embeddings with the sentential features derived from the convolutional neural network. Specifically, after the convolution and max-pooling layers generate the sentential feature vector \\( v_{sent} \\), this vector is concatenated with the word embeddings of the trigger word and its immediate left and right neighbors, resulting in a combined vector \\( v_{sent+lex} \\in \\mathbb{R}^{2 \\cdot d_c + 3 \\cdot d_w} \\). This concatenation encourages the model to incorporate the lexical semantics of the trigger and its surrounding context, which can be strong indicators for coreference. The combined vector \\( v_{sent+lex} \\) is then processed by an additional hidden layer to generate the final event-mention representation \\( v_e \\)."}
{"question_id": "428b48f2d5cfea8890c3fe80599575b25565a976", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "Using only one mixture component (R = 1) during training and 100 components (R = 100) during testing is a strategy employed in Deep Variational Implicit Processes (DVIP). During training, setting R = 1 reduces computational complexity by propagating a single Monte Carlo sample through the network, which simplifies optimization. In contrast, during testing, using R = 100 Monte Carlo samples allows the approximation of the predictive distribution as a Gaussian mixture with 100 components, resulting in a more flexible and accurate predictive distribution than the Gaussian predictive distribution of VIP. This approach enables tractable computation of the predictive distribution and improves the flexibility and calibration of uncertainty estimates at test time without incurring the high computational cost during training."}
{"question_id": "8b0ad0aeb866b9064a6bd71b1559fe85d81a612b", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "The event-mention representation \\( v_e \\) serves as a latent feature vector that encodes the semantics of an individual event mention, combining sentential features derived from convolutional neural networks with lexical-level features of the trigger word and its immediate context. This representation is generated by processing the concatenated sentential and lexical features through a hidden layer, resulting in a fixed-size vector \\( v_e \\) that captures relevant event aspects. The purpose of \\( v_e \\) is to provide a compact, informative embedding of an event mention that can be used as input for subsequent event-linking decisions, specifically for determining coreference between event mentions."}
{"question_id": "10dbff5874380289cdab832a0eecab1cc3c34117", "paper_id": "openreview/ICLR-2023-conf/VA1YpcNr7ul", "generation": "The proposed algorithm DASHA is related to the family of variance reduction methods, including momentum variance reduction (MVR) techniques. Specifically, DASHA is inspired by MARINA and momentum variance reduction methods (MVR) as indicated in the algorithm description. DASHA incorporates a variance reduction strategy borrowed from MVR, which is a type of variance reduction method related to the Stochastic Variance Reduced Gradient (SVRG) family. \n\nDASHA uses different variance reduction strategies depending on the oracle setting: GD-like for gradient, PAGE-like for finite-sum, and MVR-like for stochastic settings. This shows that DASHA generalizes and extends variance reduction techniques, including those related to SVRG, by integrating them with communication compression and distributed optimization.\n\nTherefore, DASHA is related to SVRG and other variance reduction algorithms through its use of momentum variance reduction strategies and PAGE-like methods, which are part of the broader family of variance reduced gradient methods."}
{"question_id": "c322ea8d24002e229a330243b37f745a157a764e", "paper_id": "egu/esd/14-185-2023", "generation": "The reference numbers for the delta to the DIC pool, as mentioned in Table 1, can be verified in Weaver et al. (2001, Sect. 3e), which describes the inorganic ocean carbon cycle and the air\u2013sea gas exchange process in the UVic Earth System Climate Model (UVic ESCM)."}
{"question_id": "81b292ac9640d75024251269649ea71272b1710c", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "When both Deep Gaussian Processes (DGP) and Deep Variational Implicit Processes (DVIP) use the same Gaussian Process (GP) priors, the tradeoff between runtime and performance is as follows:\n\nDVIP generalizes DGPs and can achieve similar predictive performance when using GP priors. However, the computational cost of generating continuous samples from a GP prior in DVIP is significantly higher, making it too expensive in practice. Consequently, if a GP prior is used, it is computationally cheaper to employ a DGP rather than DVIP.\n\nFurthermore, differences in performance between DVIP and DGP on datasets such as the Year dataset are not solely due to the choice of prior (BNN vs. GP) but also arise from differences in the posterior approximation methods (linear model in DVIP vs. inducing points in DGP). VIP using inducing points and a GP prior yields results similar to sparse GPs, but the cost of approximately sampling from the GP prior in VIP (and by extension in DVIP) is too high to consider adding extra layers.\n\nIn summary, while DVIP with GP priors can match DGP performance, DGPs are more computationally efficient in this setting. DVIP offers improved flexibility and potentially better performance when using other implicit process priors (e.g., Bayesian neural networks), but this advantage diminishes when restricted to GP priors due to the high sampling cost in DVIP."}
{"question_id": "720c06898bbd974bce657e8eefac71ea8641b762", "paper_id": "nlpeer/PeerRead-CONLL2016/13", "generation": "Position embeddings are generated by table lookup from a matrix of trainable parameters, similar to word embeddings. Specifically, for relative positions of words with respect to the event mention, embeddings \\( v_i^p \\) of size \\( d_p \\) are obtained by looking up a matrix \\( W_p \\in \\mathbb{R}^{d_p \\times (s_{\\max} \\times 2 - 1)} \\), where \\( s_{\\max} \\) is the maximum sentence length in the dataset. This process parallels the generation of word embeddings, which are also obtained by lookup from a trainable embedding matrix."}
{"question_id": "8a7925cf9978728b68e7bc89204643a94468964a", "paper_id": "openreview/ICLR-2022-conf/xCVJMsPv3RT", "generation": "The combination of layer normalization with dropout significantly improves overall performance, particularly in complex reinforcement learning environments such as Ant and Humanoid. Layer normalization suppresses the learning instability caused by dropout, which otherwise destabilizes the Q-function learning. Specifically, dropout alone leads to more significant oscillations in the Q-function loss and the variance of the gradient loss with respect to Q-function parameters. Layer normalization effectively reduces these oscillations, stabilizing the learning process. This stabilization enables better Q-value propagation, resulting in improved sample efficiency and bias reduction. The synergistic effect of using both dropout and layer normalization is especially pronounced when the ensemble size of dropout Q-functions is small, where their combined use markedly enhances sample efficiency compared to using dropout alone. For larger ensemble sizes, layer normalization alone remains beneficial. Thus, layer normalization plays a critical role in mitigating the adverse effects of dropout-induced instability, thereby enhancing the effectiveness of dropout in reinforcement learning."}
{"question_id": "3356f072c76c12c8ebc215b5bd495d5ccbea0126", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "The training batches are constructed by sequentially building mini-batches of mentions \\( B \\subset M \\) over the training data, where each mention \\( m_i \\in B \\) has its gold entity \\( e_i^\\star \\). The graph \\( G_B \\) for each mini-batch includes nodes consisting of (a) each mention \\( m_i \\in B \\), (b) each mention coreferent to \\( m_i \\in B \\), and (c) the set of gold entities for each \\( m_i \\in B \\). This means that the batch graph includes not only the mentions in the batch but also their coreferent mentions, which may come from other documents. The edges are constructed to ensure that each connected component contains exactly one entity, linking mentions coreferent to the same entity.\n\nThus, the batches are designed to include mentions that are coreferent, potentially across documents, to explicitly model mention coreference relationships. This approach leverages coreferent mentions as much as possible during training.\n\nRegarding inference, the context does not explicitly state that inference batches are constructed similarly or restricted to mentions from the same document. However, the method is described as capable of performing cross-document coreference, indicating that mentions from multiple documents can be considered together.\n\nIn summary, the training batches contain mentions along with their coreferent mentions, which may be from the same or different documents, to leverage coreference relationships extensively. The approach is not limited to mentions from the same document."}
{"question_id": "b509625d37c9da4c7585e00370dc166be8e9be88", "paper_id": "nlpeer/ARR-22/c53c0e92d95b1e222ebbecee84efd55c426d3f9916ac9f50280eaebe6640d35f9d395744c228f1f352eb4954a72a42b3886d581396fa3202056879d792fb62b7", "generation": "No, the model does not need to compute \\(\\psi(m_i, e)\\) for all entities in the target knowledge base during inference. Instead, the inference procedure constructs a graph \\(G\\) for each mention \\(m\\) by adding edges from the mention's \\(k\\)-nearest neighbor mentions and from the mention's nearest entity to \\(m\\), based on the edge weights \\(w_{e,m} = -\\psi(e, m)\\). This means that only the nearest entity (or a limited set of candidate entities) is considered for each mention, rather than computing \\(\\psi(m_i, e)\\) exhaustively over all entities. The final linking decision assigns the entity ID present in the mention's cluster, which is formed through this graph-based clustering approach."}
{"question_id": "c83f53bbc1390bf3f6a15aa58e1c559cf391a507", "paper_id": "openreview/ICLR-2023-conf/H-T3F0dMbyj", "generation": "Section 4.1 is not explicitly included in the provided chunks. However, the context and content of the paper indicate that the overall task described involves learning text-queried sound separation without labeled data, specifically using the CLIPSep model and its noise-invariant training variant (CLIPSep-NIT). The task entails separating target sounds from audio mixtures given either image or text queries, with a focus on handling noisy, unlabeled video data from the wild. During training, the model uses paired audio mixtures and corresponding video frames to learn to separate individual sound sources, and at test time, it uses text queries to specify the target sound for separation. The noise-invariant training method further addresses the challenge of noisy data by introducing noise heads that capture query-irrelevant sounds, allowing the model to focus on query-relevant sound separation.\n\nTherefore, the task described in Section 4.1 is the evaluation of the CLIPSep and CLIPSep-NIT models on sound separation performance, particularly examining their ability to separate target sounds from noisy audio mixtures using text queries, without relying on labeled data."}
{"question_id": "e0385760ba4f37eeba3376cf4dd4ed0727cedb81", "paper_id": "openreview/ICLR-2023-conf/8aeSJNbmbQq", "generation": "The rationale for choosing the prior in the model is to use an implicit process (IP) as a flexible and expressive prior distribution over the target function. IPs generalize Gaussian processes (GPs) and can represent complex function distributions that are easy to sample from but generally lack closed-form density expressions. Specifically, Bayesian neural networks (BNNs) are a particular case of IPs, providing a rich family of priors that can capture complex dependencies and uncertainties.\n\nHowever, the posterior distribution under an IP prior is typically intractable, except in the GP case. To address this, the Variational Implicit Process (VIP) method approximates the posterior by a GP with mean and covariance functions matched to those of the prior IP, enabling tractable inference. This GP approximation allows the model to retain the dependence on the IP prior parameters, which can be adapted to observed data, improving predictive accuracy.\n\nFurthermore, to reduce the computational cost associated with GPs (which scale cubically with the number of data points), the GP prior is approximated by a linear model with Gaussian coefficients. This approximation facilitates mini-batch training and scalable inference.\n\nIn summary, the prior is chosen as an IP to leverage its flexibility and expressiveness in modeling functions, while the GP approximation and linear model enable tractable and scalable Bayesian inference with the ability to adapt prior parameters to the data. This approach balances model flexibility, computational feasibility, and the capacity for prior adaptation, which is key for accurate predictions."}
{"question_id": "a74c71ff53a5ff84cacb938350996a66ceb0ae12", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "In Phase-I training, the proposal network is trained on geometric cues, specifically depth or normal input, using the base class bounding box annotations. The training loss used is the same as in Equation (2), which includes the bounding box regression loss and the objectness score prediction loss calculated only on anchors associated with known base class objects. The architecture employed is OLN, built on top of Faster RCNN with classification heads replaced by objectness score prediction heads. The training uses the SGD optimizer with an initial learning rate of 0.01, a batch size of 16, and runs for 8 epochs. The proposal network is trained to generate object proposals from the geometric cue inputs to discover novel unannotated objects in the training set."}
{"question_id": "dfa7d8d8808a8928555e5e665068db90d3261334", "paper_id": "openreview/ICLR-2022-conf/fQTlgI2qZqE", "generation": "Yes, it is valid to assume that finite evaluations of one arm are sufficient to obtain an accurate reward. This is explicitly stated as Assumption 1 in the proposed interaction detection method, which assumes that a finite number \\( m \\) of evaluations (or pulls) of one arm are sufficient to obtain an accurate estimate of its reward. This assumption underpins the design and analysis of the UCB algorithm used for detecting the \\( k \\)-strongest pairwise interactions."}
{"question_id": "c8e25c77b2ec42c4f94bc044959aa372dd3f9638", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The design of the proposed method, EViT, is not arbitrary for all layers of a given ViT model. The token reorganization modules are placed at specific layers determined by a simple strategy that evenly divides the ViT into blocks. For a ViT with L layers and t token reorganization layers, the reorganization layers are located at indices \\([s + 1, 2s + 1, ..., ts + 1]\\), where \\(s = L / (t + 1)\\). This approach ensures that the reorganization locations cut the ViT evenly rather than being arbitrarily assigned. Additionally, placing the reorganization modules in shallow layers is avoided because attention maps at shallow layers are unreliable for token removal, and reorganization in deeper layers has only marginal influence on accuracy. Thus, the reorganization locations are fixed according to this strategy and are not arbitrary across all layers."}
{"question_id": "65ca807b7bfc58200ae0e5c46fcec1e31096cbf5", "paper_id": "openreview/ICLR-2022-conf/fQTlgI2qZqE", "generation": "Yes, the boost in performance of the ParaACE model observed in the selected datasets is expected to be seen in other datasets as well. The experiments demonstrate that the detected interactions help improve predictive performance and significantly reduce model size across multiple real-world datasets such as Elevators, Parkinsons, Skillcraft, Bike sharing, and Cal housing. Additionally, the ParaACE model shows sample efficiency, performing well even with smaller training samples, and surpassing over-parameterized neural networks trained with substantially more data. The ablation studies and comparisons with various baselines on both synthetic and real datasets further support the generalizability of the performance gains induced by interaction detection."}
{"question_id": "5af1f02badf7c044e04f7544f4881486216b9f42", "paper_id": "openreview/ICLR-2023-conf/OTbRTIY4YS", "generation": "The new metric, Concept Purity, is introduced because the previously used metric based on graph edit distance, as proposed in Magister et al. (2021), is computationally expensive. Concept Purity is adapted to exploit the annotation of local explanations, specifically the typology of the motif represented by the local explanation, which provides a more efficient and practical evaluation of how well the embedding clusters local explanations. This metric measures the homogeneity of clusters by counting the most frequent label within each cluster relative to its size, thus assessing the quality of concept representations in a way that is computationally feasible and aligned with the domain-specific annotations. This approach contrasts with the metrics used in Yuan et al. (2020), which do not address the clustering quality of learned concepts and rely on different evaluation criteria that are not directly applicable to the concept-based explanations generated by GLGExplainer."}
{"question_id": "986801b0e009ad637f5bc4d62c8af27a2580f7b9", "paper_id": "nlpeer/ARR-22/3282bdf968d7f87ae69c4ae7fdd35d4e9a46fa83f56d8bee0296c16dcf23b23cb1e4f20f510c7d01fb7be2309c08dd29cc240b54bdcbc3d66969041b5c84077a", "generation": "The degree of formality in Hindi formality transfer is controlled by the parameter \u03bb, with higher \u03bb values corresponding to more formal sentences. As sentences become more formal, the English loanword \"job\" (\u091c\u0949\u092c) is replaced by more formal equivalents from Persian (\u0928\u094c\u0915\u0930\u0940) and high Sanskrit (\u01d3\u0928\u092f\u0941\u093f\u00c8\u0924), and honorific forms such as \u0906\u092a\u0915\u0940 and verb forms like \u092c\u0924\u093e\u090f\u0902 are used. This indicates that the transfer process effectively maintains and increases the use of Persian and Sanskrit lexical forms as formality increases. The model DIFFUR and its variants, especially DIFFUR-INDIC and DIFFUR-MLT, demonstrate strong control over the magnitude of style transfer, achieving high calibration scores (CALIB around 69.0 to 69.6) and significant style score increases at maximum \u03bb values, while preserving semantic similarity. Thus, the degree of formality and the corresponding use of Persian and Sanskrit forms are well maintained and can be finely controlled in Hindi formality transfer."}
{"question_id": "dc3fd256c5702edb18e7a21a01836945f7bc0b17", "paper_id": "openreview/ICLR-2023-conf/WlbG820mRH-", "generation": "The limitations on the aggregation, combination, and readout components significantly affect the applicability of the results to other GNN models. The paper restricts its analysis to Message Passing Neural Networks (MPNNs) where the aggregation function is a simple sum of input vectors, and the combination and readout functions are represented by neural networks with ReLU activation only. Theorem 1 and Corollary 1 extend only to GNN models that are at least as expressive as these MPNNs. Minor modifications, such as using other piecewise-linear activation functions, do not invalidate the results, which still hold under such changes.\n\nHowever, when moving beyond the MPNN or spatial-based GNN model, the question of formal verifiability must be reconsidered, as the results do not directly transfer. For example, models like DropGNN, which are more expressive than MPNN, cannot have these results directly applied. The expressiveness of the GNN model relative to the MPNN model is thus a critical factor in determining whether the impossibility or possibility results for formal verification carry over. Consequently, the applicability of the results depends on the expressiveness of the aggregation, combination, and readout functions in the target GNN model compared to those in the studied MPNN framework."}
{"question_id": "2e474adb8f0c5bd3285e43db4bfb774e7cd5b7a5", "paper_id": "nlpeer/ARR-22/78d7b31f3d19cf16bcc71aa109ca56b923d9082a2919fd8008060641daf4d5ae37d70daea5bb36f4aa83c9c49ad8b44e597aa41960348f3a0abe0abb529f9aea", "generation": "The proposed knowledge prompting method requires the knowledge generation model to be relatively large in order to generate useful and reliable knowledge. Smaller knowledge generation models, such as those with 1.3 billion or 0.4 billion parameters, do not provide significant improvement in performance. In contrast, larger models like the 6.7 billion parameter GPT-3 knowledge model yield moderate improvement (5.0%), and the largest 175 billion parameter GPT-3 model produces the greatest improvement (10.5%). Therefore, while the method can still function with smaller models, its effectiveness is substantially reduced unless the knowledge source model is sufficiently large."}
{"question_id": "5ab449ca87a0bb86111a5e4e594ac959ec7d0e86", "paper_id": "nlpeer/PeerRead-CONLL2016/142", "generation": "Post-editing rates were chosen over prediction Human Translation Error Rate (HTER) for intrinsic uncertainty evaluation because post-editing time is a more comprehensive measure of post-editing effort. It captures not only the technical effort measured by HTER but also the cognitive effort involved in post-editing. Additionally, post-editing time is more directly applicable in real translation environments, as it relates directly to productivity measures, making uncertainty estimates based on it more useful in practical scenarios."}
{"question_id": "5739894b5714e42337f53319a265bb28e2f6e18d", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Yes, using RGB images in addition to depth and normal images in the first step (Phase-I) leads to worse performance. When RGB is stacked with geometric cues to train the proposal network, the model tends to rely more heavily on RGB inputs because RGB provides a stronger signal in the closed-world setup. This reliance causes the model to underutilize geometric cues, which are crucial for discovering novel objects in open-world detection. Empirical results show that stacking RGB with geometric cues results in inferior performance across many benchmarks. Furthermore, training a separate RGB-based proposal network and merging its pseudo boxes with those from geometric cues (the \"GOOD-All\" method) either yields no performance gains or even degrades performance on benchmarks such as VOC to ADE20K. This degradation is attributed to RGB-based models favoring smaller detection boxes that often correspond to small objects, textures, or parts of larger objects, which can harm overall detection quality."}
{"question_id": "3f32444ad6371e1401f9047615faeed1a6572e73", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The difference between the results in Table 1 and Table 2(a) lies in the datasets and models evaluated as well as the specific metrics reported:\n\n- Table 1 presents results of EViT on the LV-ViT-S model, showing performance metrics such as Top-1 accuracy, Top-5 accuracy, throughput (images per second), and MACs (Giga multiply-accumulate operations) for various keep rates and image sizes. It includes comparisons between the baseline LV-ViT-S and EViT variants trained or finetuned on higher resolution images, demonstrating improvements in accuracy and efficiency.\n\n- Table 2(a) reports results of EViT on the DeiT-S model, focusing on the comparison between two variants of EViT: one with vanilla inattentive token removal and one with inattentive token fusion. The table provides Top-1 and Top-5 accuracy averaged over three independent trials, throughput, and MACs for different keep rates. It highlights the accuracy differences relative to the baseline DeiT-S and the computational gains achieved by token reorganization strategies.\n\nIn summary, Table 1 evaluates EViT on LV-ViT-S with emphasis on training/finetuning at different resolutions, while Table 2(a) evaluates EViT on DeiT-S comparing two inattentive token handling methods with detailed accuracy statistics over multiple trials."}
{"question_id": "888ba5daeae0d5b3d5120c824c8f61abd5b77ee3", "paper_id": "openreview/ICLR-2022-conf/IfNu7Dr-3fQ", "generation": "The difference in error between the KT+ and standard thinning (ST) methods using the Hinch posterior with the inverse multiquadric (IMQ) kernel arises because KT+ simultaneously inherits improved maximum mean discrepancy (MMD) guarantees and dimension-free single-function integration error guarantees, even for kernels without fast-decaying square-roots like IMQ. Specifically, KT+ applies generalized kernel thinning to a sum of the original kernel and a fractional power kernel, which yields better-than-i.i.d. MMD error decay rates and significantly smaller integration errors for functions both inside and outside the reproducing kernel Hilbert space (RKHS). In contrast, ST does not leverage these kernel-based improvements and thus exhibits higher MMD and integration errors for the same coreset size.\n\nEmpirically, KT+ uniformly improves upon the MMD error of ST in the Hinch IMQ kernel setting, as shown by the observed MMD values (e.g., KT+ MMD around 0.50\u20130.70 versus ST MMD around 0.44\u20130.46 for various coreset sizes). KT+ also provides significantly smaller integration errors for RKHS functions (like k(X\u2032, \u00b7)) and non-RKHS functions (such as moments and benchmark CIF functions) across nearly all tested coreset sizes. This improvement is consistent with the theoretical guarantees that KT+ achieves better MMD decay rates (close to n^(-1/2)) compared to the slower decay rates of ST and i.i.d. sampling (around n^(-1/4)).\n\nTherefore, the explanation for the difference in error is that KT+ leverages a generalized kernel thinning approach that improves both MMD and integration error rates for kernels like IMQ, while ST lacks these enhancements, resulting in higher errors for the same coreset size."}
{"question_id": "87b3ef59cb4832acb7306d8368503784307b1adc", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The provided information does not explicitly discuss or compare the advantages of proposal-based object detectors relative to DETR or similar object detectors. The text focuses on the use of geometric cues in open-world class-agnostic object detection, the architecture choices between proposal-based detectors (such as OLN, a modified Faster R-CNN) and proposal-free detectors (such as FCOS), and the benefits of incorporating geometric cues for detecting novel objects. However, no direct comparison or stated advantages of proposal-based detectors over DETR or related models are presented in the given content."}
{"question_id": "fced44b64830046f3b8766306510296277091b7a", "paper_id": "nlpeer/ARR-22/3282bdf968d7f87ae69c4ae7fdd35d4e9a46fa83f56d8bee0296c16dcf23b23cb1e4f20f510c7d01fb7be2309c08dd29cc240b54bdcbc3d66969041b5c84077a", "generation": "The provided evidence specifically details the variation in formality and lexical choices for Hindi, illustrating that as sentences become more formal, the English loanword \"job\" (\u091c\u0949\u092c) is replaced by Persian-derived (\u0928\u094c\u0915\u0930\u0940) or high Sanskrit-derived (\u01d3\u0928\u092f\u0941\u093f\u00c8\u0924) terms, accompanied by the use of honorifics (e.g., \u0906\u092a\u0915\u0940, \u092c\u0924\u093e\u090f\u0902). However, for the other languages studied\u2014Bengali, Kannada, Telugu, Swahili, Spanish, and Gujarati\u2014there is no explicit information or examples given regarding the degree of formality or the use of Persian or Sanskrit lexical forms in their formality transfer.\n\nThe analysis and evaluation results for these languages focus on automatic and human evaluation metrics of formality transfer performance (e.g., r-AGG, a-AGG, ACC, SIM) and lexical diversity of paraphrases, but do not provide qualitative or lexical details about how formality manifests in terms of specific lexical substitutions or honorific usage analogous to the Hindi example.\n\nTherefore, no direct evidence is available on the degree of formality or the use of Persian/Sanskrit forms for the other languages studied in this work."}
{"question_id": "a4439f559ec40c32bb7edf1ee7fa3a854ed2b883", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Combining RGB, depth, and normals does not improve the performance of the model for smaller classes. The object proposal networks trained on RGB inputs tend to favor smaller detection boxes, which can correspond to small objects or textures and parts of larger objects. However, incorporating RGB with geometric cues (depth and normals) by stacking inputs during Phase-I training leads to inferior performance, as the model tends to rely more on RGB and may ignore geometric cues, which are crucial for discovering novel objects. Additionally, merging pseudo boxes from RGB with those from geometric cues (referred to as \"GOOD-All\") either results in no performance gains or even worsens performance on benchmarks such as VOC to ADE20K. Geometric cues alone (depth and normals) are stronger in discovering novel objects, including those of different sizes, and provide complementary benefits to RGB-based detection, but adding RGB does not enhance detection for smaller classes."}
{"question_id": "1b3c40fd196db55e9ffea18c2b7d9ffe988c5ad2", "paper_id": "openreview/ICLR-2022-conf/IfNu7Dr-3fQ", "generation": "The kernel \\( k_{\\text{split}} \\) serves as an auxiliary kernel in the generalized kernel thinning algorithm. Its primary role is in the KT-SPLIT step, where it is used to divide the input point sequence \\( S_{\\text{in}} = (x_i)_{i=1}^n \\) into \\( 2^m \\) candidate coresets of size approximately \\( n / 2^m \\) using non-uniform randomness. The kernel \\( k_{\\text{split}} \\) defines the reproducing kernel Hilbert space (RKHS) \\( \\mathcal{H}_{\\text{split}} \\) in which the integration error of the KT-SPLIT coresets is analyzed and bounded.\n\nSpecifically, \\( k_{\\text{split}} \\) is used to compute swapping parameters and thresholds during the splitting process, guiding the probabilistic swapping of points between candidate coresets to ensure low integration error. Theoretical guarantees (Theorem 1) establish that for any fixed function \\( f \\in \\mathcal{H}_{\\text{split}} \\), the integration error between the original distribution and the coreset constructed by KT-SPLIT is bounded in terms of the norm \\( \\| f \\|_{k_{\\text{split}}} \\) and the kernel's supremum norm \\( \\| k_{\\text{split}} \\|_{\\infty, \\text{in}} \\).\n\nMoreover, when \\( k_{\\text{split}} \\) is chosen as the square-root kernel \\( k_{\\text{rt}} \\) of the target kernel \\( k \\), the generalized kernel thinning recovers the original ROOT KT algorithm. However, using \\( k_{\\text{split}} = k \\) is a practical choice that yields dimension-free error bounds and better integration error guarantees compared to the square-root kernel choice.\n\nIn summary, \\( k_{\\text{split}} \\) functions as a critical kernel that governs the splitting and initial coreset construction phase, enabling theoretical control over the integration error and facilitating the subsequent KT-SWAP refinement step."}
{"question_id": "4191cd3e5bd482f3d097b689c9857bf3d727f98b", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Depth and normal maps are combined for training the proposal network in Phase-I by training two separate object proposal networks independently on each geometric cue (depth and normal maps). The pseudo boxes generated by these two networks are then merged into a single pseudo box pool for Phase-II training. The merging process involves filtering out overlapping boxes: if the Intersection over Union (IoU) of two pseudo boxes from the different sources is greater than 0.5, they are considered overlapping, and the box with the lower objectness score is removed. This approach of ensembling pseudo labels from separately trained networks is empirically shown to be slightly better than stacking the two geometric cues together as input for a single proposal network in Phase-I."}
{"question_id": "c12d6fb12967631fd985c4b437ee77cd1d2a8e2e", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "Yes, when combining multiple modalities for the model, filtering is applied to merge pseudo boxes generated by object proposal networks trained separately on different modalities such as depth and normal maps. Specifically, if the Intersection over Union (IoU) of two pseudo boxes is larger than 0.5, they are considered overlapping, and the box with the lower objectness score is filtered out. This filtering process ensures that overlapping boxes from different modalities do not redundantly contribute to the pseudo box pool used for training."}
{"question_id": "ecbcef296be692e081bf8e60e3d63dcbf2090dbf", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "No, a language expert was not explicitly consulted to verify the accuracy of the translations of English time expressions into other languages. The translations were performed automatically, and the paper acknowledges that automatic translation of time expressions and templates from English to other languages may introduce some errors. There is no mention of expert verification of these translations."}
{"question_id": "68cdad6fbee4b667c3a6b10e5761a4ccf660e32b", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "The data includes an analysis comparing the average night start and end times reported by workers from different countries to the average October sunrise and sunset times in the capital cities of those countries. This comparison shows some alignment between the average sunset time and the average night start time for the US, India, and Italy, but no such alignment between sunrise time and the end of night or beginning of morning. However, the exact locations of the workers within each country are unknown, and the study notes that it would be interesting, given larger scale data collection, to perform a finer-grained analysis of the correlation between sunrise and sunset times in specific locations within each country and the times indicated by workers in these locations. Thus, while there is some preliminary data relating country-level time zone location to time expression grounding, detailed data on how intra-country location within a time zone affects the results is not currently available."}
{"question_id": "d753561800b2ad04d3d8262519328d014142d717", "paper_id": "openreview/ICLR-2022-conf/BjyvwnXXVn_", "generation": "The rationale for choosing to remove tokens at the 4th, 7th, and 10th layers in Vision Transformer (ViT) models is based on a strategy that evenly divides the ViT into blocks with the same number of layers for token reorganization. Specifically, for a ViT with L layers and t token reorganization layers, the separating length is calculated as s = L / (t + 1). The token reorganization layers are then placed at the layer indices [s + 1, 2s + 1, ..., ts + 1], which evenly cuts the ViT into segments. For example, in a ViT with 12 layers and 3 token reorganization layers, the reorganization layers are placed at the 4th, 7th, and 10th layers. This approach ensures that token removal occurs at intermediate depths rather than shallow layers, as placing reorganization modules in shallow layers leads to unreliable attention maps and significant accuracy reduction. Placing the reorganization modules in these deeper layers maintains stable performance and accuracy while effectively reducing computational cost."}
{"question_id": "888f26be8b81ccb011c79b043940eec47d9b414e", "paper_id": "openreview/ICLR-2023-conf/W-nZDQyuy8D", "generation": "The same network architecture, specifically the Object Localization Network (OLN) built on top of Faster RCNN, is used separately for both depth and normal inputs by training distinct object proposal networks on each geometric cue. Initially, off-the-shelf pretrained models (DPT-Hybrid models from the Omnidata repository) are used to infer geometric cues\u2014depth and surface normals\u2014from the RGB images. These depth and normal maps serve as inputs to train separate object proposal networks in Phase-I, each using the same OLN architecture and training loss but applied to their respective geometric modality. The pseudo boxes generated by these two separately trained networks are then merged by filtering overlapping boxes based on their Intersection over Union (IoU) and objectness scores to form an ensemble (GOOD-Both). Thus, the same network architecture is reused for both depth and normal by training it independently on each geometric cue modality."}
{"question_id": "f21290e4ef8f0ead710c484502ad8e103217da11", "paper_id": "openreview/NeurIPS-2022-dabt/76w7bsdViZf", "generation": "Yes, the authors have evaluated additional pretrained models beyond ResNet50 and DeiT, including DenseNet161 and VGG16, on ImageNet. These models were assessed without finetuning on the Hard ImageNet and RIVAL20 datasets. The evaluation showed that all models, including DenseNet161 and VGG16, exhibited higher accuracies under ablation, lower Relative Foreground Sensitivity (RFS) scores, and lower saliency alignment on Hard ImageNet compared to RIVAL20. This indicates that the authors have indeed tested versions of VGG and DenseNet on ImageNet-derived datasets to analyze spurious feature reliance."}
{"question_id": "e7ac4283196f09643f681e08d6093e9313983cf1", "paper_id": "nlpeer/ARR-22/4b7c5fafdc37dc7cb22b9868c3dc5a3df61127f67fe8b48aaebe06624f82096f9a2fd1d81f57f86ecd7bfaae463ae744196e2c0e5f132cbd9ccff1065f620afe", "generation": "Yes, the translation of the template has been adapted to account for languages with grammatical gender. For example, in Italian, \"morning\" (mattina) is feminine, whereas \"afternoon\" (pomeriggio) is masculine, which results in variation in the determiner used\u2014\u201cla <time_exp>\u201d for feminine and \u201cil <time_exp>\u201d for masculine expressions."}
{"question_id": "496d254bdc722d815fb98ad3903cdc34df700fce", "paper_id": "openreview/ICLR-2023-conf/KDhFkA6MQsW", "generation": "The complexity stated as \\( t = \\frac{\\delta_f}{\\epsilon^{1.75}} \\log d \\) does not match the complexity in Theorem 2 because Theorem 2 establishes a more precise iteration complexity that depends on additional problem parameters and has a different dependence on \\(\\epsilon\\), \\(\\rho\\), \\(\\ell\\), and dimension \\(d\\).\n\nSpecifically, Theorem 2 sets the total number of iterations as\n\\[\nT = O\\left( \\frac{\\Delta_f \\ell^{1/2} \\rho^{1/4}}{\\epsilon^{7/4}} \\log\\left(\\frac{\\ell \\sqrt{d} \\Delta_f}{\\delta \\epsilon^2}\\right) \\right),\n\\]\nwhere \\(\\Delta_f = f(x_0) - f^*\\), \\(\\ell\\) is the smoothness parameter, \\(\\rho\\) is the Hessian Lipschitz constant, and \\(\\delta\\) is a failure probability parameter.\n\nThis complexity differs from the simpler form \\( t = \\frac{\\delta_f}{\\epsilon^{1.75}} \\log d \\) in several ways:\n\n1. **Dependence on \\(\\ell\\) and \\(\\rho\\):** Theorem 2 includes explicit fractional powers of \\(\\ell\\) and \\(\\rho\\) (\\(\\ell^{1/2}\\) and \\(\\rho^{1/4}\\)), reflecting the influence of smoothness and Hessian Lipschitz constants on the iteration complexity.\n\n2. **Exponent on \\(\\epsilon\\):** The exponent on \\(\\epsilon\\) in Theorem 2 is \\(7/4 = 1.75\\), matching the power in the denominator, but the presence of \\(\\ell^{1/2}\\) and \\(\\rho^{1/4}\\) modifies the scaling, making it more nuanced than a simple \\(\\epsilon^{-1.75}\\) dependence.\n\n3. **Logarithmic term:** The logarithmic factor in Theorem 2 depends on \\(\\ell\\), \\(\\sqrt{d}\\), \\(\\Delta_f\\), \\(\\delta\\), and \\(\\epsilon\\) in a more complex manner than just \\(\\log d\\).\n\n4. **Constants and parameters:** Theorem 2's complexity arises from a detailed probabilistic analysis involving perturbation steps, negative curvature detection, and Hamiltonian decrease arguments, which introduce constants and parameters (such as \\(c_A\\), \\(T'\\), \\(s\\), and \\(\\delta_0\\)) that affect the iteration count.\n\nTherefore, the simplified form \\( t = \\frac{\\delta_f}{\\epsilon^{1.75}} \\log d \\) omits critical dependencies and constants that are essential in the rigorous complexity bound of Theorem 2, leading to a mismatch."}
{"question_id": "cf66689ffef1b230b7bab23901a8bf528a8e97f8", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "The paper incorporates section titles into the BOS (Beginning of Sentence) representation by generating section title embeddings (STE) or classified section title embeddings (classified STE) and adding them to the sentence representations. Specifically, the section title embedding is generated by feeding the tokenized section title into the pre-trained Transformer language model (TLM) and summing the last hidden states of each token position to form a single embedding. For classified STE, typical section title classes are manually predefined, and all intra-class section titles are replaced with the embedding of their corresponding class. If a section title does not belong to any class or belongs to multiple classes, the original STE is used. This STE or classified STE is then added to the initial sentence representation at the BOS token position, enriching the BOS representation with section title information before it is fed into the hierarchical contextual learning layers."}
{"question_id": "b248a530072224a71459f4ce7aa708f9990067c2", "paper_id": "openreview/ICLR-2023-conf/KDhFkA6MQsW", "generation": "The parameter \\(\\mu\\) used in the experiments is set to values on the order of \\(0.001\\) or \\(0.01\\), depending on the specific algorithm and experiment. Specifically:\n\n- For the cubic regularization problem experiment with dimensions \\(d = 20, 100, 200, 1000\\), \\(\\mu\\) is set to \\(0.001\\) for ZO-Perturbed-AGD and \\(0.001\\) or \\(0.01\\) for ZO-Perturbed-AGD-ANCF (e.g., \\(\\mu = 0.001\\) or \\(\\mu = 0.01\\) depending on dimension).\n\n- For the quartic function experiment, \\(\\mu\\) is set to \\(0.001\\) for ZO-Perturbed-AGD and \\(0.01\\) for ZO-Perturbed-AGD-ANCF.\n\n- In the cubic regularization problem, the perturbation radius \\(r\\) and \\(\\mu\\) are both set to \\(0.001\\).\n\n- In the quartic function experiment, the perturbation radius \\(r\\) and \\(\\mu\\) are set to \\(0.01\\).\n\nThus, the typical values of \\(\\mu\\) used in the numerical experiments are \\(0.001\\) or \\(0.01\\), chosen based on the problem dimension and algorithm variant."}
{"question_id": "a7d741be648d514c67c1a0468a78782b19c6d11c", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Label smoothing does not always improve performance; there are cases where it can degrade performance. Specifically, label smoothing during head probing (lsHP) is beneficial when the pretrained features are good and the standard head probing training accuracy converges very fast to above 90%, as it reserves energy for feature adaptation and leads to better generalization. However, when the head probing training accuracy is too low, the assumption that the model\u2019s prediction converges to the labels no longer holds, and lsHP may not bring enhancement or can even degrade performance. Additionally, using smoothed labels in both head probing and finetuning phases with improper settings (e.g., \u03b7_HP > \u03b7_FT) can cause features to adapt in opposite directions, resulting in larger adaptation but worse generalization. Therefore, careful tuning of label smoothing parameters is necessary, and lsHP is not universally beneficial."}
{"question_id": "28241e220ebb3458ef6d5eaf6829f8563161a7c1", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Mild adaptation of the pretrained features results in the features being stretched (i.e., an increase in the norm \u2225z_T\u2225_2) while maintaining a similar direction (cosine similarity changes little) compared to the original features z_0. This stretching makes originally overlapped features more separable without significantly altering the manifold of the features. Such an adaptation is desirable in most transfer learning scenarios because it improves feature separability while preserving the overall structure of the feature space."}
{"question_id": "e111e75817d67b6fbeec06d5ba117b3419bf2f0f", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The initial value being discussed in Section 3.2 is the initialized task head parameter denoted as \\( v_0 \\). This parameter \\( v_0 \\) is the focus because it influences the adaptation of features learned from the pretraining stage to the downstream task, which in turn affects the model's potential to generalize better. The analysis considers how \\( v_0 \\) affects certain distance metrics related to feature changes during fine-tuning, particularly through its relationship with quantities \\( B_0 \\) and \\( B_t \\), where \\( B_0 \\) is fixed and \\( B_t \\) depends on \\( v_0 \\). The goal is to find a good initialization \\( v_0 \\) that leads to better downstream performance."}
{"question_id": "691280cb66aae7b9fee2d8ac0937e5f970437b43", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "Human evaluation results for the extractive summarization task are presented through case analyses of extracted summaries from the HiStruct+ model and a baseline model, compared against the gold summaries (abstracts of the papers). Two samples are showcased: one from the arXiv dataset and one from PubMed. In these evaluations, the HiStruct+ model selects sentences from later positions in the documents, providing summaries that better cover the main content, conclusions, and future work, as indicated by highlighted phrases. In contrast, the baseline model tends to select the first sentences, resulting in summaries that focus more on background information and lack an overall perspective or key conclusions. The HiStruct+ model\u2019s summaries align more closely with the gold summaries in terms of informativeness and coverage of key points, demonstrating its superiority in producing more informative and comprehensive extractive summaries."}
{"question_id": "8e2cb1c95dffd133cc91ab3123074a0853c829fb", "paper_id": "nlpeer/ARR-22/db3d0c16f2a8a3baf7fe008d20bfe3fd82dedab22900659a9d8352b2ecce076fa9a82869602cc78d2c3b732f645c25eacb9b0b6fc4d3b6441bfb1825109e85ea", "generation": "The authors were motivated to experiment with their model on CNN/DailyMail (CNN/DM) because it serves as an exemplary dataset with less conspicuous hierarchical structure compared to PubMed and arXiv. CNN/DM contains shorter news articles with a smaller average hierarchical width (hi-width = 1.33), which contrasts with the longer scientific papers in PubMed and arXiv that have larger hi-width values and deeper hierarchical structures. By including CNN/DM, the authors aimed to evaluate the effectiveness of their HiStruct+ model across datasets with varying hierarchical characteristics, particularly to test the model's performance on documents with less obvious hierarchical structure."}
{"question_id": "ca87a914265cffe46bfb63e2e24a3568efbc7888", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Yes, there are cases where the 'end' histogram does not match the label vector \\( e_y \\) when using different head types for hyperparameter tuning. Specifically, when the task head capacity is increased, such as using a two-layer MLP head instead of a linear head, the head converges slower and the feature adaptation becomes more unpredictable at the beginning of finetuning due to the inconsistent direction term \\(\\nabla_z q_t\\). This can prolong a chaotic phase where the features change in inconsistent directions. Consequently, the features' manifold and the resulting representations after finetuning can differ significantly depending on the head type and the hyperparameter \\(\\tau\\) controlling the number of head probing epochs. Experiments involving head-exchange between models trained with different \\(\\tau\\) values show incompatibility for strongly adapted backbones (small \\(\\tau\\)), indicating that the features' manifold has changed substantially and the final features do not align well with the original label directions \\( e_y \\). Thus, the 'end' histogram, representing the final feature distribution, may not match \\( e_y \\) under these conditions."}
{"question_id": "f29ff7d6be64035f374fe6b3fc470453591154e9", "paper_id": "nlpeer/COLING2020/1570", "generation": "Yes, the annotated mistakes will be released upon the release of ManyNames v2. The dataset release will include the consistent response sets as well as the raw verification annotations, which contain information on different inadequacy types and errors. This facilitates the use of different thresholds for adequacy and allows analysis of naming errors and confounders in object naming data."}
{"question_id": "953feae01ae0b8d2066fd035c079f0a5dd581aaf", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Label smoothing during the head probing (HP) stage, referred to as lsHP, can improve performance in scenarios where the pretrained features are already good and the HP training accuracy converges very quickly to a high value (e.g., 90%+). In such cases, lsHP reserves some \"energy\" for feature adaptation during finetuning, leading to better downstream performance without changing the HP-train-accuracy. However, lsHP does not always improve performance. When the HP-train-accuracy is low, indicating that the pretrained features are not well suited for the downstream task, the assumption that the model's prediction converges to the labels no longer holds, and lsHP may fail to bring enhancement or even degrade performance. Additionally, if label smoothing is applied in both HP and finetuning stages with equal smoothing factors (\u03b7_HP = \u03b7_FT), the reserved energy disappears, and performance is similar to the baseline without smoothing. Using label smoothing with \u03b7_HP > \u03b7_FT can cause feature adaptation in opposite directions, leading to worse generalization. Therefore, label smoothing is beneficial only under certain conditions and does not universally improve the hyperparameter-fine tuning procedure."}
{"question_id": "ffe260fb92f4c53395118a567f59d32fd365c351", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The degree of separability when adapting a model to a task is influenced primarily by the structure and capacity of the task head relative to the backbone, as well as the initial training accuracy and loss at the beginning of finetuning, which determine the \"energy\" available for feature adaptation. Specifically:\n\n1. **Task Head Capacity**:  \n   - A low-capacity head (e.g., a linear head) may struggle to achieve high training accuracy during head probing, limiting the reduction of adaptation-induced error (AIE) and thus hindering the preservation of pretrained features.  \n   - Conversely, a task head with much larger capacity than the backbone can distort the pretrained information, as the features may be rewritten during a prolonged random changing phase before the gradient direction stabilizes.\n\n2. **Backbone Depth and Task Head Composition**:  \n   - Treating later layers of the pretrained backbone as part of the task head increases the head's capacity but reduces the amount of pretrained information preserved.  \n   - Preserving earlier layers of the pretrained backbone tends to retain more pretrained features, which can be beneficial when pretrained features are of good quality.  \n   - When pretrained features are poor, discarding later layers and enlarging the task head can improve adaptation.\n\n3. **Initial Training Accuracy and Energy**:  \n   - The training accuracy and loss at the start of finetuning (after head probing) determine the energy available for feature adaptation.  \n   - Higher initial energy leads to greater Euclidean and cosine distances between adapted and original features, affecting separability.  \n   - Mild adaptation is generally beneficial, especially when pretrained features are reasonably good but require some domain adaptation.\n\n4. **Interaction Between Backbone and Head**:  \n   - The learning dynamics involve both energy and direction terms, where the direction term influences the feature changes especially at the beginning of finetuning.  \n   - Controlling the number of head probing epochs or manipulating the head's capacity can regulate the degree of feature adaptation and thus separability.\n\nIn summary, the degree of separability during adaptation is controlled by the balance between the task head's capacity and the backbone's pretrained features, the initial training conditions that set the adaptation energy, and the strategic design of the finetuning process to preserve or adapt features as needed for the downstream task."}
{"question_id": "99d5ca18b41cac7092cd7ca7cf0888b8a29a3018", "paper_id": "openreview/ICLR-2023-conf/OM7doLjQbOQ", "generation": "Yes, the new automated image transformation strategy, referred to as Automated Data Augmentation (AutoDA), can be applied to other transfer-based methods such as MI-FGSM. Experimental results show a significant improvement in attack transferability when AutoDA is applied to attacks that originally have no data augmentation, including I-FGSM and MI-FGSM. However, for some attacks that already employ fixed augmentation schemes, like the CTM family, AutoDA does not improve performance and may even lower the attack success rate due to excessive distortions. The text does not explicitly mention DIM in the context of AutoDA application, but since DIM already uses random resizing and zero-padding as augmentations, it is likely similar to the CTM family in already employing fixed augmentations. Therefore, while AutoDA is effective for MI-FGSM, its benefit for DIM is not explicitly confirmed and may be limited."}
{"question_id": "08c2ff08d58f88bfead47fc3783d34333d02f023", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The rationale behind choosing a label-smoothing coefficient of 0.9 (denoted as \u03b7_HP = 0.9) is to reserve a certain amount of \"energy\" during the head probing (HP) stage, which facilitates a mild but effective feature adaptation in subsequent finetuning. Specifically, by setting the labels during HP as a mixture \u03b7_HP * e_y + (1 - \u03b7_HP) * u, where u is a uniform K-class categorical distribution, the HP stage retains at least (1 - \u03b7_HP) * \u2225e_y - u\u2225\u00b2 energy for feature adaptation even as the number of HP epochs \u03c4 approaches infinity. This reserved energy prevents the model's prediction p_0 from fully converging to the hard labels e_y, thus allowing the features to adapt more during finetuning without changing the HP training accuracy significantly.\n\nThis approach is particularly useful when the HP training accuracy converges very quickly to above 90%, indicating that the pretrained features are already strong and the standard HP would otherwise leave little room for further adaptation. The label smoothing coefficient of 0.9 strikes a balance by maintaining high HP accuracy while ensuring sufficient reserved energy for feature adaptation.\n\nMoreover, it is important to set \u03b7_HP \u2264 \u03b7_FT (the label smoothing coefficient during finetuning) to ensure that the feature adaptation proceeds in the correct direction. If both coefficients are equal (e.g., \u03b7_HP = \u03b7_FT = 0.9), the reserved energy disappears, and the adaptation behaves similarly to the baseline without label smoothing. If \u03b7_HP > \u03b7_FT, the features may adapt in opposite directions, leading to worse generalization.\n\nIn summary, choosing \u03b7_HP = 0.9 enables mild feature adaptation by reserving energy during HP, which is beneficial when pretrained features are strong and HP accuracy is high, thereby improving downstream finetuning performance without sacrificing HP accuracy."}
{"question_id": "6ed4842f06973b3b3b83a068d590e3a5421678f8", "paper_id": "openreview/ICLR-2023-conf/T2Ncx_PN2K", "generation": "Using a transformer-based acoustic model, specifically a Conformer encoder, in the RNN-T architecture is effective and yields significant reductions in word error rate (WER) on target domains while maintaining minimal catastrophic forgetting compared to shallow fusion. The FixedGram-based TOLSTOI adaptation method works well with the Conformer-based RNN-T model, demonstrating its applicability beyond bidirectional LSTM encoders. However, attempts to replace the simple feedforward imputation model with a Transformer-based encoder-decoder imputation model performed worse than the simpler design choices of TOLSTOI. Thus, while the Conformer (transformer-based) encoder improves acoustic modeling and adaptation effectiveness, more complex transformer-based imputation models do not necessarily improve the overall adaptation architecture."}
{"question_id": "36fdc759d8b028d2f3c0c5cb9e8c26b5744962d0", "paper_id": "openreview/ICLR-2023-conf/OM7doLjQbOQ", "generation": "ILA-DA consistently outperforms ILA across all tested intermediate layers in terms of attack success rate, exhibiting an inverted U-curve pattern that indicates robustness to the choice of intermediate layer near the default layer proposed by Huang et al. (2019). When compared to other state-of-the-art transferable attack methods such as LinBP and the CTM family, ILA-DA achieves significantly higher attack success rates on both undefended and defended models. Specifically, on undefended models, ILA-DA attains an average attack success rate of 84.5%, which is 19.5% better than ILA and 4.7% better than the previous state-of-the-art method VNI-CT-FGSM, a member of the CTM family. Against defended models, ILA-DA also surpasses the strongest CTM-based attacks, achieving an average success rate of 75.9% with a weak I-FGSM reference attack and further improvements when combined with stronger attacks. The superior performance of ILA-DA is attributed to its novel augmentation techniques that enhance transferability by exploiting gradients with better resemblance to different architectures."}
{"question_id": "789b3799040a63d59e93a029bab4459c7ff3aa2c", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "The phrase \"information exchanges between the two parts of the network\" in Fig. 1 refers to the interaction and mutual influence between the backbone (feature extractor) and the task head during finetuning. Specifically, it describes how the feature representations \\( z = f(x; B) \\) produced by the backbone change in response to updates in the task head parameters \\( v \\), and conversely, how the task head gradients affect the backbone features. This interaction is mathematically characterized by the decomposition in Equation (2), where the change in features \\( z \\) depends on the empirical neural tangent kernel (NTK) of the backbone, the gradient of the task head prediction with respect to the features, and the difference between the one-hot label vector and the predicted probability vector. The \"information exchange\" thus encapsulates the dynamic feedback loop during finetuning, where the task head's parameter updates influence the backbone's feature adaptation, and the backbone's features in turn affect the task head's learning process. This concept highlights the importance of considering both parts jointly when designing finetuning strategies."}
{"question_id": "9ff146fb1145a7e6cd038252a41b96f5c6ac0494", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "Yes, there are examples of synthetic and ground-truth data used to compare model performance. In the zero-shot image recognition setting, synthetic data generated by the GLIDE text-to-image model were used alongside real data to evaluate classification accuracy on 17 diverse datasets. For instance, on the EuroSAT dataset, synthetic data improved top-1 accuracy by as much as 17.86% for the CLIP-RN50 backbone compared to the baseline without synthetic data. Similarly, improvements were observed on CIFAR-10, CIFAR-100, and other datasets, with synthetic data boosting performance by several percentage points.\n\nIn the few-shot image recognition setting, synthetic data were generated and combined with a small number of real images per class (e.g., 1, 2, 4, 8, 16 shots) to assess performance gains. Strategies such as Real Filtering (RF) and Real Guidance (RG) were employed to leverage few-shot real samples to improve the quality and relevance of synthetic data. Experiments on 8 datasets including ImageNet, Caltech101, Pets, and EuroSAT demonstrated that synthetic data could enhance few-shot learning performance, although the positive impact diminished as the number of real shots increased.\n\nThese examples illustrate direct comparisons between models trained or evaluated with synthetic data versus ground-truth real data, showing that synthetic data can significantly improve classification accuracy in both zero-shot and few-shot scenarios."}
{"question_id": "9f2536e9ca279c79b121794af57c2ad02d6b13c8", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "The use of synthetic data does not significantly improve model performance when a large amount of ground-truth (real) data is available. Synthetic data is beneficial primarily in data-scarce scenarios such as zero-shot or few-shot learning, where it helps address insufficient data and boosts performance. However, as the number of real data samples increases, the positive impact of synthetic data gradually diminishes. This is because real data samples are more effective and efficient due to the absence of domain gaps, whereas synthetic data suffers from domain gaps and lower data efficiency. Additionally, the benefits of synthetic data and real data tend to overlap, reducing the incremental gains from synthetic data as more real data becomes available."}
{"question_id": "e0bf6addede2ca2d1e76eac67027e72e3ff385f5", "paper_id": "nlpeer/ARR-22/3cfcbbad78a71ca6cf4a5b2bbaee2a7ad8a1de295cf9f4103408dfeaf38a0de01b2b8d23cb94e0ef0b5f76a1947815f0335a7077bdea03ea4a29f6a2bff432cc", "generation": "Hanja is considered an extinct language rather than merely a script because it represents a distinct linguistic system used in ancient Korea, characterized by its own lexical, semantic, and syntactic features that differ significantly from modern Korean and modern Chinese. Although Hanja is based on Chinese characters, it evolved to incorporate the basic syntax of classical Chinese mixed with colloquial Korean elements, resulting in a language that is no longer understood or used in contemporary communication. This linguistic distinctiveness and the cessation of its use as a living language justify referring to Hanja as an extinct language rather than simply a writing system or script."}
{"question_id": "cbd5e6e55ec199de5569c76823febc8a19d28e5e", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "Yes, the \u039b-DARTS NAS method can be trained on a large-scale dataset such as ImageNet. The search and evaluation were performed on ImageNet using a search model with 8 layers (6 normal and 2 reduction cells) and 16 initial channels, following the PC-DARTS setting. The evaluation used a model with 14 layers and 48 initial channels for 250 epochs. \u039b-DARTS improved upon all baselines in terms of top-1 and top-5 accuracy on ImageNet, surpassing DARTS by 2.5% in top-1 accuracy and 1.7% in top-5 accuracy, and outperforming the best-performing baseline \u03b2-DARTS and DrNAS by small margins. The discovered architectures are efficient and well within the mobile setting boundaries introduced in the original DARTS ImageNet search space."}
{"question_id": "e57f2a5a860c3aa8c1e0f8ca5a3375dd735d463c", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "The performance drop after 100 epochs in Figure 4 (c) is caused by the performance collapse phenomenon in DARTS, which is primarily due to low layer alignment (\u039b) among the gradients of different layers. This low layer alignment results in vastly different optimal architectures for each layer, with deeper layers (closer to the output) strongly favoring skip-connection operations because these have larger gradient magnitudes due to reduced gradient vanishing. As the search progresses, the architecture parameters corresponding to skip-connections dominate, leading to saturation of the softmax function and an architecture overwhelmingly composed of skip-connections. This convergence point is independent of the loss function and is exacerbated by increasing the number of search epochs, which intensifies gradient vanishing effects and further promotes the dominance of skip-connections, thereby causing the observed performance degradation after 100 epochs."}
{"question_id": "9246fb2439ec9512f9298b927660f030736765c0", "paper_id": "openreview/ICLR-2023-conf/T2Ncx_PN2K", "generation": "The training process for the proposed model TOLSTOI is not truly end-to-end. While the base RNN-Transducer (RNN-T) ASR model itself is trained end-to-end by jointly optimizing the speech module, language module, and joint network parameters over paired speech-text data, the adaptation approach involves a separate imputation model that is trained independently. \n\nSpecifically, the imputation model f_IMP is a simple feedforward neural network trained to generate speech representations (h vectors) from text tokens using alignments derived from the pretrained RNN-T model. This imputation model is trained by minimizing a reconstruction loss on the speech representations extracted from the original training data, using fixed alignments and outputs from the pretrained RNN-T. \n\nAfter training the imputation model separately, it is used to generate pseudo speech representations for target-domain text-only data, which are then used to fine-tune only the language module and joint network parameters of the RNN-T model, while keeping the speech module fixed. This fine-tuning step adapts the model to the target domain but does not involve retraining the entire network jointly from raw speech and text.\n\nThus, the overall adaptation procedure involves a two-stage process: first training the imputation model separately, then fine-tuning parts of the RNN-T model using imputed speech representations. This modular approach contrasts with a fully end-to-end training paradigm where all components are trained simultaneously from raw inputs to outputs."}
{"question_id": "726e56359927a7fc14be9019ea69e50929fa9e67", "paper_id": "nlpeer/ARR-22/dd0a83852cb35c8d0ac0c40da6b7ebeb385f9c7e4816ef51f43abd6910a9adacc0daf460dfda4e56f37fa542a749961a78ddbbe74af0326f41e5cd38e867b121", "generation": "The provided evidence chunks do not contain explicit information or experimental results comparing the effects of using predicted negation cues versus gold (true) cue information for scope resolution. The discussion and results focus on improvements in cue detection and scope resolution performance through negation-focused pre-training strategies, such as targeted data augmentation and explicit cue masking, but do not specifically address the impact of using predicted cues instead of gold cues on scope resolution accuracy. Therefore, no direct conclusion can be drawn from the given text regarding the effects of using predicted cues versus gold cue information for scope resolution."}
{"question_id": "9b256b585691520864c3cf7d1b8cfb8f863d6663", "paper_id": "openreview/ICLR-2023-conf/gVOXZproe-e", "generation": "Yes, the results of the study suggest several implications beyond the general empirical impression. The study provides a formal decomposition of the learning dynamics of feature adaptation during finetuning, identifying key factors such as the \"energy\" and \"direction\" terms that govern how pretrained features change. It reveals a non-trivial trend in feature adaptation: as the initial energy (related to training accuracy and loss at the beginning of finetuning) increases, the Euclidean and cosine distances between the adapted and original features increase, while their dot product and norm exhibit a quadratic pattern. This nuanced understanding goes beyond simple distance metrics commonly used.\n\nFurthermore, the study demonstrates that mild feature adaptation is generally beneficial when pretrained features are not optimal for downstream tasks, and that controlling the degree of adaptation through early stopping in head probing, label smoothing, or task head design can improve downstream performance. It also shows that the relationship between feature adaptation and downstream performance is complex and depends on the quality of pretrained features and the nature of the downstream task.\n\nAnalytically, these trends are rigorously proved in an overparameterized linear model and experimentally verified across various settings, providing a theoretical foundation for empirical observations. The study also highlights open questions, such as the need for quantitative or adaptive methods to analyze the discrepancy between pretraining and downstream tasks to better guide head probing strategies.\n\nIn summary, the study offers a principled framework and practical guidelines for understanding and controlling feature adaptation in transfer learning, advancing beyond the general empirical impression to a more comprehensive and theoretically grounded perspective."}
{"question_id": "f18d6c2ac81007f7b42cab31234075307c85ee0a", "paper_id": "nlpeer/ARR-22/6262c349a3980c6850e252b3a44391b1b1318c26fbf29be55c61c0a32b8b626e5581c98234d1ecb410674c00ebcf3ba2adfdce41c2b2d32211721372a8ed1bc0", "generation": "The training languages used for the non-baseline models in the cross-lingual experiments do not include German. Specifically, the models were trained on speech in 8 languages with one speaker per language, drawn from the Nancy dataset and the CSS10 dataset, excluding German. German was only used during fine-tuning on a randomly chosen 30-minute subset from the Karlsson dataset, which is separate from the training data. This setup was designed to evaluate cross-lingual fine-tuning performance on low-resource data without overlap in training languages."}
{"question_id": "14f24eacc79985de8d643389b87e35ceb5209775", "paper_id": "openreview/ICLR-2023-conf/oztkQizr3kk", "generation": "The search cost of the proposed method, \u039b-DARTS, on the DARTS search space and CIFAR-10 dataset is 0.8 GPU days on a GTX 1080 Ti GPU when using the central finite difference approximation. This cost is twice that of the original first-order DARTS (0.4 GPU days) but is 20% less than the second-order DARTS (1.0 GPU days) and about 40% less than SDARTS-ADV (approximately 1.3 GPU days).\n\nTo reduce the search cost, \u039b-DARTS can employ forward or backward finite difference approximations instead of the central difference approximation. Using these biased estimations in an interleaved fashion (forward estimation for one mini-batch and backward estimation for the next) reduces the cost by 25%, bringing it down to approximately 0.6 GPU days. This reduced cost is only 50% more than the original first-order DARTS and comparable to DrNAS with progressive search.\n\nThus, the search cost of \u039b-DARTS ranges from about 0.6 GPU days (with forward/backward finite difference approximation) to 0.8 GPU days (with central finite difference approximation) on a 1080 Ti GPU."}
{"question_id": "b9dc0dacfa9d5676e09c6d6308a65cb2885392cd", "paper_id": "nlpeer/ARR-22/24714d27941075cbad95c02db3ac730df71d355f85f5d247a62c9453ea29004b15cfb386663c82ce55ba17d652253064f4ff70f21dd5c08d8e39986ab22c45ce", "generation": "The results of the Montreal Forced Aligner, when applied to singing recordings for obtaining alignments to lyrics, have not been explicitly stated as manually checked for accuracy. The alignments produced by the Montreal Forced Aligner are used to derive ground-truth pitch alignment based on shared lyrics in the PopBuTFy dataset, but there is no mention of manual verification of these alignments in the provided information."}
{"question_id": "ff310f12cf0c134c9763ec3389c106e6c16dc65c", "paper_id": "openreview/ICLR-2022-conf/ZTsoE8G3GG", "generation": "The metric \"score\" is a task-specific real number in the range [0, 1], where higher values indicate better performance, reflecting how well the generated molecules match a target molecular profile. The \"quality\" metric is defined as the absence of undesirable substructures in the optimized molecules, evaluated post-hoc using the same quality filters as in Brown et al. (2019). Quality can only be computed if at least 100 molecules per benchmark are found."}
{"question_id": "d539111f12ea7af828c1637c34c8e6fcb06f589a", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "The framework described uses an ensemble of models to estimate epistemic uncertainty by measuring the variance between model outputs, specifically applied to the predicted probability distributions over semantic classes in the map space. The uncertainty is computed as the variance of the ensemble predictions for each map location and semantic class, which directly reflects uncertainty in the semantic segmentation logits or probabilities.\n\nThe method leverages this variance in the output space (probability distributions over semantic classes) both for active training\u2014selecting informative samples with high epistemic uncertainty\u2014and for goal selection during navigation, balancing exploration and exploitation based on uncertainty in predicted semantic maps.\n\nThere is no explicit discussion or evaluation of applying the variance-based uncertainty estimation directly in the semantic feature space (i.e., intermediate feature representations before the final classification logits). The approach focuses on variance in the output probability space, which is interpretable as uncertainty over semantic class predictions.\n\nGiven that the framework relies on variance between ensemble outputs as a proxy for epistemic uncertainty and that these outputs correspond to semantic class probabilities (logits transformed by softmax), it is not directly established that the same variance-based approach would be equally effective or meaningful in the semantic feature space. The semantic feature space is typically high-dimensional and less directly interpretable in terms of class uncertainty, whereas the output logits or probabilities provide a natural probabilistic interpretation suitable for uncertainty estimation.\n\nTherefore, while the variance of ensembles is effective and validated in the space of semantic class predictions (logits/probabilities), the framework does not provide evidence that it would work equally well in the semantic feature space. The probabilistic interpretation and direct connection to class uncertainty make the output space more appropriate for this method."}
{"question_id": "94d3d227741b9bf8258649cb3567dc790b9dca07", "paper_id": "nlpeer/ARR-22/91d9e763eb1470028002e5c97689ada4b74f30be17291a14458f7542c387a9f2a7516e60f4022d887bdbd2165aa8cce6d566db380e5a82af677917c48efc2446", "generation": "The authors use regularization specifically for label embeddings to address the anisotropy problem and prevent model degeneration by promoting uniformity and dispersion of label representations on the unit hypersphere. The label embedding regularizer (LER) encourages the label representations to be uniformly dispersed, keeping the inter-label similarity as low as possible so that the feature space of each class is more dispersed and avoids representation collapse. This is important because label embeddings serve as anchors in the contrastive learning framework, and their uniform distribution ensures better semantic separation and representation capacity.\n\nIn contrast, instance embeddings are aligned to their corresponding label embeddings through the instance-centered contrastive loss (ICL) and label-centered contrastive loss (LCL), which already encourage alignment and uniformity from the perspective of instances and their labels. The ICL aligns each instance to its label representation, while the LCL makes instances of different labels far apart and instances of the same label more compact. Together, these losses effectively handle the representation learning for instances without requiring an additional explicit regularization term. Thus, the regularization is applied to label embeddings to maintain their uniformity and dispersion, which complements the contrastive losses that govern instance embeddings."}
{"question_id": "c0701214e646badb3bbb44e4985982c8e9e65fc7", "paper_id": "nlpeer/ARR-22/83dc885bef279cbb7a3b5b173494428b6cd043d34527016519250477d274fd1793fa0bddd2bd4bbb3edfa9709ddc85e90825a0554bfa8eceb8cb34d813c06c53", "generation": "To ensure the accuracy of the confidence network in neural machine translation (NMT), the following measures have been taken:\n\n1. **Joint Learning with Training Process:** The confidence estimate is learned jointly with the NMT model training in an unsupervised manner, allowing the confidence network to adapt alongside the translation model without degrading translation performance.\n\n2. **Confidence Defined as Number of Hints:** Confidence is conceptualized as the number of \"hints\" (ground-truth information) the model requires to make a correct prediction. More hints indicate lower confidence. This approach encourages the model to translate independently when confident and to request hints only when uncertain, thereby providing a meaningful measure of confidence.\n\n3. **Confidence Network Architecture:** A dedicated confidence estimation network (ConNet) is added in parallel to the original prediction branch. It takes the multi-layer hidden states of the decoder at each decoding step as input and outputs a scalar confidence value between 0 and 1 via a sigmoid function. This design allows the network to capture nuanced information from the decoder's internal states.\n\n4. **Interpolation of Prediction Probabilities:** During training, the model's softmax prediction probabilities are adjusted by interpolating with the ground truth distribution, weighted by the confidence estimate. This interpolation penalizes the model for requesting hints, encouraging accurate confidence estimation.\n\n5. **Implementation Details to Avoid Performance Degradation:** Careful design choices, such as not using the highest hidden state to predict confidence and translation simultaneously, prevent degradation of translation quality. For example, using the highest hidden state for both tasks led to a significant BLEU score drop with larger beam sizes, which was avoided by the implemented approach.\n\n6. **Empirical Validation:** The confidence network's accuracy is validated through extensive sentence- and word-level quality estimation tasks, demonstrating that the learned confidence better indicates translation accuracy than model probabilities. It also effectively detects risks from noisy samples and out-of-domain data.\n\nThese measures collectively ensure that the confidence network provides a well-calibrated, accurate estimate of the model's predictive uncertainty without compromising translation performance."}
{"question_id": "f8b91940d2ce9e4e0df966f18d724e12b5aac0e5", "paper_id": "openreview/ICLR-2023-conf/nUmCcZ5RKF", "generation": "In a fully-supervised setting for image recognition, synthetic data exhibit distinct effects depending on the training scenario:\n\n1. **Training from Scratch:**  \n   Synthetic data deliver inferior performance compared to real data and are significantly less data-efficient. For example, training a ResNet-50 model from random initialization on a synthetic CIFAR-100 dataset (50k images) achieves only 28.74% top-1 accuracy, which is substantially lower than models pre-trained on real data. To match this performance, approximately five times fewer real images (9.5k real images) are required, indicating that synthetic data require a much larger volume to achieve comparable results. Increasing the amount of synthetic data beyond a certain point does not yield further performance gains, likely due to limitations in data quality and diversity.\n\n2. **Large-Scale Pre-Training for Transfer Learning:**  \n   Synthetic data are effective and suitable for large-scale supervised pre-training, showing promising transfer learning performance that can approach or even surpass standard ImageNet pre-training. When pre-training on synthetic data generated according to the downstream task label space (downstream-aware setting), increasing the amount of synthetic data (e.g., 1.2M to 3.6M images) improves performance, eventually outperforming ImageNet-1K pre-training. Initializing models with ImageNet-1K pre-trained weights before synthetic data pre-training further boosts performance. Synthetic data pre-training benefits from larger data amounts and increased label space diversity.\n\n3. **Downstream-Agnostic Supervised Pre-Training:**  \n   When synthetic data are generated from a general label space such as ImageNet-1K without access to downstream task labels, supervised pre-training on synthetic data achieves performance close to that of real ImageNet pre-training. Increasing synthetic data size and label diversity further narrows the performance gap.\n\nIn summary, synthetic data in fully-supervised image recognition are currently less effective and less data-efficient than real data when training from scratch. However, synthetic data are highly effective for large-scale supervised pre-training, where they can achieve comparable or superior transfer learning performance relative to real data pre-training, especially when combined with real data initialization and increased data scale and diversity."}
{"question_id": "6f797e6284c2b0ebd83dc98348c33626ac517dbb", "paper_id": "openreview/NeurIPS-2022-dabt/Zx5qJzNesn0", "generation": "The benchmark differences between the two locations, Rock Springs and New Holland in Pennsylvania, primarily concern weather data, as CyclesGym ships with weather data corresponding to these two locations. The environments are provided for both locations, allowing for testing generalization across location. However, there is no explicit mention of other differences such as soil characteristics or management practices between the two locations in the benchmark setup. The soil files can be custom specified or generated, but the provided environments focus on weather differences. Therefore, besides weather, no other location-specific differences are explicitly indicated in the benchmark."}
{"question_id": "0eb6095e3dbae2dd6e1abc90265e56378f49fa1a", "paper_id": "openreview/ICLR-2022-conf/ZTsoE8G3GG", "generation": "Yes, individual atoms can be added to the partial graph when using Algorithm 2 with larger vocabulary sizes. Algorithm 2 specifies that during the generation order determination, if the currently selected atom is part of a motif, the entire motif is added to the partial graph at once. Otherwise, a single atom is added. This means that even with a larger motif vocabulary, the model retains the capability to add atoms individually when the selected atom is not covered by any motif in the vocabulary."}
{"question_id": "030389c23b9697a71ca59dec3bcab088d7f20ced", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "No, equation (1) is a summation from j = 1 to k, not from j = 0 to k. The equation is given as:\n\n\\[ M^l_i = \\sum_{j=1}^k \\text{Softmax}(-d_j) m^l_j, \\]\n\nwhere the summation index j starts at 1 and goes up to k."}
{"question_id": "7a4e6842b9fed6c17b9fc508c5e7f7bdc1614d7c", "paper_id": "openreview/ICLR-2023-conf/ySCL-NG_I3", "generation": "The design of the network architecture in the Harmonic Molecular Representations (HMR) framework follows several specific strategies:\n\n1. **Multi-Resolution Representation via Laplace-Beltrami Eigenfunctions:**  \n   The molecular surface is represented as a Riemannian manifold, and its geometry is encoded using the truncated Laplace-Beltrami (LB) eigenfunctions. The number of eigenfunctions retained determines the resolution of the representation, allowing tuning of spatial resolution for different molecular systems and tasks.\n\n2. **Harmonic Message Passing Using Spectral Filters:**  \n   The network performs message passing by propagating surface functions through learned spectral-space frequency filters applied to the LB eigenvalues. Each feature channel has a unique Gaussian frequency filter parameterized by mean (\u00b5) and width (\u03c3), combined with a heat operator term governed by propagation time (t). This design enables multi-range communication across the molecular surface at different spatial scales and resolutions.\n\n3. **Learnable Spectral Filters:**  \n   The parameters of the Gaussian frequency filter and the heat operator are learned through backpropagation, allowing the network to adaptively focus on specific frequency components of the surface functions, capturing both local and global geometric and chemical features.\n\n4. **Stacking Multiple Message Passing Blocks:**  \n   Multiple harmonic message passing blocks can be stacked to enhance the representation power and capture complex interactions on the molecular surface.\n\n5. **Integration of Geometric and Chemical Features:**  \n   Initial surface features, encoding both geometric and chemical properties, are extracted via multilayer perceptrons (MLPs) and then propagated on the surface manifold, ensuring that the network jointly encodes shape and chemistry.\n\n6. **Global Pooling for Molecule-Level Representations:**  \n   After message passing, global pooling operations are applied to obtain holistic molecule-level representations suitable for downstream prediction tasks.\n\n7. **Use of Functional Maps and Cross-Attention for Protein Docking:**  \n   For rigid protein docking, the architecture incorporates cross-attention layers and functional maps to establish correspondence between receptor and ligand surfaces, enabling alignment of binding interfaces and subsequent rigid docking via the Kabsch algorithm.\n\nThese strategies collectively enable the network to efficiently encode molecular surfaces with multi-scale geometric and chemical information, bypassing the need for equivariant networks and allowing robust representation learning on continuous manifolds."}
{"question_id": "fd9af00fe3f20196d71e9e364f55c157d4cd2cd3", "paper_id": "nlpeer/COLING2020/939", "generation": "To ensure that paths were not shared between the training and test sets, the validation and test sets were filtered to include only hyponym queries that were unseen anywhere in the full taxonomy paths of the training data. Additionally, hyponyms appearing as hyponyms in the training set were excluded from the test and validation sets to prevent models from merely copying. This filtering created a subset denoted as WN18RR-hp, which guarantees that all evaluated queries are equally new to both path-based and non-path models."}
{"question_id": "9d285bc752521120d3b45a5b35069f1365c8f603", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "Set expansion approaches are used to construct a dictionary from customer reviews because these reviews are typically unlabeled and contain a small initial seed set of entities related to specific concepts of interest. The goal is to expand this seed set to achieve high coverage of relevant entities within the reviews. This is particularly important in user-generated text domains, such as hotel reviews, where concepts often include multifaceted, vague, and non-named entities that are not well represented in existing benchmarks focused on well-formed text. Since training data in new domains is scarce, set expansion methods enable the discovery of a more complete set of entities by leveraging contextual features and limited supervision, thereby facilitating applications like semantic search that require comprehensive entity dictionaries to highlight relevant information in user queries."}
{"question_id": "d59bc31fea9ec1c2594f0ed7813ed2d9348abc75", "paper_id": "nlpeer/COLING2020/939", "generation": "The task described in the paper focuses specifically on hypernym prediction, which involves identifying the correct direct hypernym (target synset) for a given source synset (hyponym) within an existing taxonomy such as WordNet. This is formulated as a relation prediction problem in a semantic graph, concentrating solely on the hypernymy relation and aiming to predict direct hypernyms rather than indirect ones.\n\nIn contrast, taxonomy induction refers to the broader task of constructing or extending an entire taxonomy. This involves not only predicting individual hypernym relations but also organizing terms into a hierarchical structure, potentially creating new nodes and relations to build or expand the taxonomy as a whole.\n\nThus, the key difference is that hypernym prediction is a focused task of attaching a new node to the correct position in an existing taxonomy by predicting its direct hypernym, whereas taxonomy induction encompasses the more general problem of building or extending the taxonomy structure itself."}
{"question_id": "3611098cfd2590d775531ef564d87617713fe8bf", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The rationale for using entity set expansion (ESE) methods on user-generated text is that user-generated text is abundant and largely unlabeled, making it a valuable resource for mining entities needed in various downstream natural language processing (NLP) applications such as semantic search, question answering, and conversational AI. User-generated text contains distinctive characteristics\u2014such as a higher prevalence of multifaceted entities (entities belonging to multiple concepts), non-named entities (noun phrases that are not proper names), and vague entities (with subjective or unclear concept labels)\u2014which are not captured in existing well-curated benchmarks. These characteristics make ESE on user-generated text challenging but essential for creating more complete and domain-specific knowledge bases that reflect the diversity and complexity of real-world data. Consequently, ESE methods are employed to expand small seed sets of entities in these domains to achieve high coverage of relevant entities, enabling effective NLP applications over user-generated content."}
{"question_id": "05fe45ed14b202a953eb1a25a0c4552d1d2ff42d", "paper_id": "openreview/ICLR-2022-conf/k7efTb0un9z", "generation": "Yes, the proposed Graph Network-based Scheduler (GNS) can be applied to simpler models such as multilayer perceptrons (MLPs). An evaluation was conducted on a 3-layer MLP with a hidden size of 128 trained on the Fashion MNIST dataset, where GNS achieved the best test accuracy of 89.8%, demonstrating its applicability to such simple scenarios."}
{"question_id": "3f6d76b052a19c42fdc0f3fa7a03e99d76e053d5", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "The navigation tasks require the semantic layout of unseen regions in order to be solved effectively. The proposed framework actively learns to generate semantic maps beyond the agent\u2019s field of view by predicting semantics in unobserved areas, leveraging spatial associations and contextual semantic priors. This prediction of unseen semantic regions, combined with uncertainty estimation over these predictions, is used to select informative long-term goals and guide navigation. Traditional methods that rely solely on observed areas lack this capability and tend to have limited generalization. Therefore, incorporating the semantic layout of unseen regions is essential for improved performance in object-goal navigation tasks in novel environments."}
{"question_id": "ae75b890d30e2879b6a6571bbc634ee4e6157e30", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "Measuring the performance of a method using Mean Average Precision (MAP) at gold-k (k_g), where k_g equals the actual concept size (number of entities in the concept), provides a more comprehensive and realistic evaluation of entity set expansion (ESE) methods. This metric adapts to varying concept sizes and offers an estimate of recall, which is crucial for assessing effectiveness in real-world scenarios where concept sizes are commonly large. Unlike smaller fixed-k evaluations (e.g., MAP@20), MAP@k_g includes more instances of multifaceted, vague, and non-named entities that are typically present in user-generated text but often ignored in limited top-k evaluations. Consequently, MAP@k_g helps to stress test ESE methods for coverage, making it particularly useful for applications such as knowledge base population and domain-specific ESE where completeness and robustness in capturing diverse entity types are important. It complements existing metrics by providing a more accurate characterization of method performance in practical settings involving user-generated text."}
{"question_id": "be0cd13d8445fb87a73943d5acf2e5089a02876c", "paper_id": "openreview/ICLR-2022-conf/swrMQttr6wN", "generation": "No, the optimization described in equation (1) and the related goal selection strategies do not happen only over the geocentric map locations observed so far. Instead, locations are selected from the accumulated uncertainty estimates in the global map, which includes both observed and unobserved areas. The approach explicitly targets unobserved map locations with high epistemic uncertainty to maximize information gain during active training and goal selection. This is done by selecting grid locations \\( l_j \\) from the entire map region over which the model estimates semantic predictions, including unobserved areas, to maximize the variance of the ensemble predictions as a proxy for information gain. Thus, the optimization considers all candidate locations in the global map, not just those already observed."}
{"question_id": "06b380902968cd38bbb66c2a75d9372c2f039f2f", "paper_id": "openreview/NeurIPS-2022-dabt/in7XC5RcjEn", "generation": "A graph with an average diameter in the range of 10-50 is indicative of the presence of long-range relationships because such a diameter implies that nodes can be separated by many hops, requiring information to propagate over a large receptive field. This is supported by the characterization of long-range interactions (LRI) in graph datasets, where the problem radius \\( r \\) (the range of interaction) is related to the graph diameter and the number of layers \\( L \\) in a GNN. When the diameter is large, local message passing GNNs (MP-GNNs) face information oversquashing due to exponential growth of the receptive field with \\( L \\), limiting their ability to capture distant node interactions. Consequently, datasets with larger graph diameters necessitate models capable of long-range signal propagation, such as fully-connected Graph Transformers, which have been empirically shown to outperform local MP-GNNs on such tasks.\n\nSpecifically, the Peptides-func and Peptides-struct datasets, which have substantial graph statistics including average diameters in this range, demonstrate that long-range dependencies are essential for good performance. The superior performance of Transformer-based models on these datasets further confirms that the graph diameter range of 10-50 corresponds to tasks requiring LRI. Additionally, the contribution of global graph structure to the task, which is more pronounced in graphs with larger diameters, supports the need for models that can integrate distant node information effectively. Thus, an average diameter of 10-50 serves as a practical indicator that the graph learning task involves long-range relationships."}
{"question_id": "d8ac040e919b01e19818a6416896dd66bd58e69d", "paper_id": "nlpeer/COLING2020/939", "generation": "The success of the path-based model, particularly the hypo2path rev variant, can be attributed to several factors:\n\n1. **Sequence Generation of Taxonomy Paths:** The model formulates hypernym prediction as a sequence generation task, generating the entire hypernym path in the WordNet taxonomy from the root node to the direct hypernym. This more difficult objective encourages the model to learn richer representations compared to predicting only the direct hypernym.\n\n2. **Reversed Path Generation:** The hypo2path rev model generates the hypernym path in reverse order, starting from the direct hypernym and moving upward. This framing allows each generation step to be a direct hypernym prediction, which the decoder can learn more easily, improving performance.\n\n3. **Use of Encoder-Decoder Architecture with Attention:** The model employs a standard LSTM-based sequence-to-sequence architecture with Luong-style attention. The attention mechanism helps the decoder maintain focus on the source hyponym during the generation of long hypernym paths, preventing forgetting.\n\n4. **Training on Full Taxonomy Paths:** Incorporating the full taxonomy path as an auxiliary task provides additional structural information, which enhances the model's ability to accurately predict direct hypernyms.\n\n5. **Model Simplicity and Effective Training:** Despite its simplicity, the model uses effective training strategies such as teacher forcing, Adam optimization, and freezing the embedding layer, which contribute to stable and efficient learning.\n\n6. **Empirical Evidence of Improved Performance:** The hypo2path rev model achieves state-of-the-art performance on the WN18RR-hp dataset, outperforming more complex benchmarks by notable margins in hit-at-one (H@1) scores, especially for nouns and instance nouns.\n\nIn summary, the combination of generating entire hypernym paths, reversing the generation order to simplify prediction steps, leveraging attention mechanisms to maintain source information, and training on structured taxonomy paths collectively contributed to the superior performance of the path-based model."}
{"question_id": "fd627c54f792bc91ab0db4a51e8311a23d8af60d", "paper_id": "nlpeer/ARR-22/82520f1d31572d488cfeae02712db642750fda4063e5eab8a771a000f6e17e1397ab18af73e5362e4d660b3dcc78b2260e121fdefc4c1664448e1c30066d5cb9", "generation": "For prompts 7 and 8, which did not have trait-specific scores in the original ASAP AEG dataset, the trait-specific scores were obtained using the trait-specific scores provided by Mathias and Bhattacharyya (2018a)."}
{"question_id": "c0f788c455af0bee35fb5b9cc36af6a5ec6aaf3f", "paper_id": "nlpeer/ARR-22/212dff6380418cd7c050ec757b31e6b5b65bbe922aa30ffbd5950fe7a04ca737b7c7b3d706f1cd1502d7932b61d2b7c079924793e45e437526230c1e9c0626ed", "generation": "Additional fine-tuning on CLIP was not included in the experiments because fine-tuning the last few layers of CLIP with a classification head consistently resulted in worse performance compared to using extracted features for classification with SVM. This outcome is attributed to the relatively small size of the labeled dataset, which is insufficient for effective fine-tuning of CLIP for the claim detection task."}
{"question_id": "280960bc073f24e47cd5b63da7388c21eb12d9be", "paper_id": "openreview/ICLR-2023-conf/pWVASryOyFw", "generation": "The justification for selecting the CNN classifier for the sentiment analysis experiments is not explicitly detailed in the provided text. However, it is mentioned that the sentiment analysis task uses the Amazon product review dataset and employs a CNN classifier (Kim, 2014) as the task model. This choice aligns with common practice in NLP experiments where CNNs are used for text classification tasks due to their effectiveness in capturing local features in text data."}
{"question_id": "46357f5d8816d410e6100ea03a5fde2f576ae270", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "The extracted patterns are representative traffic speed patterns derived from historical traffic data, characterized by repeating and highly correlated leading and trailing segments even over short-term periods. Each daily pattern consists of 288 speed data points corresponding to 24 hours at 5-minute intervals for each road vertex. These daily patterns are sliced into smaller segments using a given window size to form the pattern set. The pattern set initially exhibits a biased distribution with many similar patterns, causing class imbalance. To address this, clustering-based undersampling with cosine similarity is applied, grouping patterns with similarity above a threshold into clusters and using the cluster centers as representative patterns. This results in a balanced and representative set of traffic patterns that serve as keys for memory access in the forecasting model. The patterns capture intrinsic spatio-temporal features common among roads and enable the transformation of traffic forecasting into a pattern-matching task."}
{"question_id": "0d68ad6ddb3ddfccd1c2d71ae7fc8a724843e891", "paper_id": "nlpeer/F1000-22/10-72", "generation": "The econometric models and economic theories used to write equations (2) through (10) are as follows:\n\n1. **Econometric Model:**\n   - The theoretical foundation for the equations is based on the **neoclassical investment model with partial adjustment of capital stock and adaptive expectations of economic growth**, as developed by Agosin & Machado (2005). This model has been adapted to include Foreign Direct Investment (FDI) and its relationship with Domestic Investment (DI).\n   - The estimation procedure employs the **system Generalized Method of Moments (system-GMM)** estimator. This approach addresses issues of endogeneity, particularly the two-way relationship between DI and FDI, and controls for unobserved country-specific effects and serial correlation in the error terms. The system-GMM combines regressions in first differences and levels using internal instruments derived from lagged values of dependent and independent variables.\n\n2. **Economic Theories:**\n   - The **neoclassical investment theory** underpins the model, emphasizing the role of capital adjustment and cost of capital in investment decisions.\n   - The **Q theory of investment** is referenced, which relates investment decisions to the market value of capital relative to its replacement cost.\n   - The **accelerator theory** and **cash flow theory** are also mentioned as part of the broader theoretical context for investment behavior.\n   - The interaction between FDI and DI is theoretically explained by the **competition effect** and **linkage effect** as theorized by Markusen & Venables (1999), where the entry of multinational enterprises affects domestic firms through increased competition and demand for intermediate inputs.\n   - The **Dunning\u2019s eclectic paradigm (OLI framework)** is used to explain the determinants of FDI, focusing on ownership, location, and internalization advantages of multinational enterprises.\n\nEquations (2) through (10) specifically incorporate these theoretical perspectives by modeling the dynamic relationship between FDI and DI across different country development levels (developing, transition economies, developed), including interaction terms to capture heterogeneity in effects. The short-run and long-run effects of FDI on DI are tested through hypotheses involving the coefficients of lagged variables and interaction terms, consistent with the partial adjustment and adaptive expectations framework."}
{"question_id": "9d42f16a53845fd4e37c5676307657425faf1538", "paper_id": "nlpeer/ARR-22/841d64f4eb3d5e3d6ea89751ea19897bb8424acf856c9dd1574ef7d2803ff7d155df1b9cc201df8b4fc8c1984f6ca8bdfaf94036960348047e8da35b0003ae34", "generation": "Yes, it is feasible to generate a lattice graph for a large dataset. The proposed decoding framework constructs lattices that compactly encode a massive number of generation options, enabling efficient exploration of diverse hypotheses. The number of nodes in the lattice is bounded by the number of node expansions during beam search, ensuring that the lattice size remains manageable and can be stored compactly in memory. Computationally, the wall clock time for the BFS-Recombination strategy is on the order of 1 to 10 seconds for summarization tasks, and the method can be parallelized across examples to improve efficiency. Experiments were conducted on substantial datasets, sampling 100 data instances with 1,000 samples each, resulting in evaluations on 100,000 generated summaries or translations per dataset, demonstrating the method's scalability to large datasets."}
{"question_id": "c0ddf26bae180b57c24cd4b90e7a0da4a0676425", "paper_id": "openreview/ICLR-2022-conf/KTPuIsx4pmo", "generation": "A real-world pushing task involves using a physical robotic arm (specifically a UR5 robot) equipped with an RGB camera to push a designated target object into a specified target area on a table. The task requires the robot to identify and push the correct object within a pink rectangular target region measuring 12 cm by 12 cm. The environment includes distractor objects that differ from those in the demonstration videos, adding complexity to the task. Success is defined by the robot pushing the target object so that its center lands within the target area. The input to the robot's policy consists solely of sequences of 128 \u00d7 128 RGB pixel images captured from a fixed camera perspective, without access to robot joint states or end-effector positions. The robot outputs incremental 3D movements of its end-effector to accomplish the pushing. The task is evaluated by testing the robot on novel objects not seen during training, with multiple trials per object to assess success rates. The real-world pushing task is more challenging than simulated tasks due to observation and action noise, environmental complexity, and misaligned paired data between human and robot demonstrations."}
{"question_id": "8952c8598f43e3e36131d56d62db44fded0352d3", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The improved performance of the LM-Base model compared to the CGExpan model on user-generated text datasets can be attributed to several factors related to the characteristics of user-generated text and the design of the models:\n\n1. **Handling Multifaceted and Vague Entities:** CGExpan penalizes entities that belong to multiple concepts (multifaceted entities) or are mentioned in diverse contexts (vague entities) because it scores candidates by selecting one positive concept and multiple negative concepts. This design avoids ambiguous contexts but leads to suboptimal ranking of multifaceted and vague entities, which are prevalent in user-generated text. In contrast, LM-Base, which uses language model-based contextual representations, is better able to accommodate such entities.\n\n2. **Context Diversity:** CGExpan tends to retrieve entities that co-occur frequently with the seed concept but may fail when entities appear in diverse or ambiguous contexts, a common characteristic of user-generated text. LM-Base leverages pre-trained language models that capture richer contextual information, enabling it to better handle entities with diverse contexts.\n\n3. **Non-named Entities:** User-generated text contains a higher proportion of non-named entities (e.g., noun phrases rather than proper names), which CGExpan's lexical and co-occurrence based methods may not effectively capture. LM-Base's language model probing approach is more flexible in recognizing such entities.\n\n4. **Robustness to User-generated Text Characteristics:** User-generated text exhibits more multifacetedness, vagueness, and non-named entities compared to well-curated benchmarks. LM-Base's reliance on language model knowledge and contextual embeddings allows it to generalize better to these challenging characteristics, whereas CGExpan's carefully designed feature selection and concept scoring mechanisms, optimized for well-curated data, do not generalize well.\n\n5. **Empirical Evidence:** Empirical results show that LM-Base significantly outperforms CGExpan on user-generated text benchmarks, while CGExpan performs better on well-curated datasets. Ensemble methods combining LM-Base with other approaches further improve performance, indicating LM-Base's complementary strengths.\n\nIn summary, LM-Base's use of language model-based contextual representations enables it to better handle the multifaceted, vague, and non-named entities prevalent in user-generated text, as well as the diverse contexts in which these entities appear, leading to improved performance over CGExpan on user-generated datasets."}
{"question_id": "e26fc7a2455a2acb2de4d608a7ca7bf1c8fb62a1", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "For a neural module to be \"in charge of solving one specific subproblem\" means that each module m_i in the architecture is responsible for addressing a distinct subproblem F_i within the overall reinforcement learning task. There is a one-to-one and onto mapping between subproblems and modules, such that all tasks requiring the solution to subproblem F_i share the same module m_i. Each module processes only the subset of the state components relevant to its subproblem and outputs the solution to that subproblem. This design allows the modular policy to be constructed by chaining these modules in sequence, where each module focuses solely on solving its designated subproblem without needing to handle the entire task or pass through extraneous information."}
{"question_id": "1740b93cc1257022895a050e975d38feebe0f904", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "Yes, the activity graph is an abstraction introduced in the MOMA-LRG dataset that serves as a single universal representation of human activities encompassing multiple levels of granularity. It captures hierarchical and compositional aspects of activities across three levels: activity, sub-activity, and atomic action. This abstraction enables a unified and overarching task called activity parsing, which involves generating the activity graph from a video and thereby allows evaluation of video-language models on complex human activities at different granularities. The activity graph improves and extends the original MOMA framework by providing this multi-level hierarchical representation, facilitating comprehensive evaluation and training of models on fine-grained, actor-centric activity recognition."}
{"question_id": "e5d8459c3ebc7cdeb1a56ddced28a7467921a917", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The videos in the MOMA-LRG dataset were collected from YouTube."}
{"question_id": "a3566edd083568caf0264186c9b8e1658c31e561", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The rationale behind the design of the different modules is to assign each neural module \\( m_i \\) the responsibility of solving one specific subproblem \\( F_i \\), establishing a one-to-one and onto mapping from subproblems to modules. This modular decomposition allows all tasks requiring the same subproblem \\( F_i \\) to share the corresponding module \\( m_i \\). The architecture assumes that the overall state can be factorized into module-specific components, such that each module receives only the subset of state variables necessary to solve its assigned subproblem and outputs only the solution to that subproblem. This design avoids brittle dependencies caused by chaining entire inputs through all modules, which can lead to cascading effects when early modules change. Instead, by restricting each module's input to relevant state components, the modules become more robust and generalize better to unseen combinations with other modules after training on a few tasks. This modular structure facilitates compositional reinforcement learning by enabling the agent to construct task-specific policies through the sequential composition of specialized modules, each focused on a distinct functional aspect of the task."}
{"question_id": "e774f0bb72932f381463769b74f98a8f360db732", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "GraphVLM is a framework designed to evaluate video-language models (VLMs) on the task of activity parsing, which is defined as the generation of activity graphs. Activity graphs serve as a single universal representation of human activities encompassing three hierarchical levels: activity, sub-activity, and atomic action. These graphs capture temporal changes, compositionality, and detailed interactions among multiple actors and objects.\n\nGraphVLM incorporates the structured knowledge encoded in activity graphs into VLMs through a model-agnostic and lightweight adaptation approach. It features a video stream, a text stream, and shared video tokenizers that operate across all three levels of the activity hierarchy. The framework uses two different tokenizers for entities and contexts and employs task-specific heads for each hierarchical level of the MOMA activity graph.\n\nBy leveraging the activity graph abstraction introduced in MOMA-LRG, GraphVLM enables the evaluation and fine-tuning of VLMs on complex, hierarchical human activity recognition tasks. It facilitates few-shot learning by converting structured graph annotations into natural language sentences, thus bridging graphical and language representations. This approach addresses limitations of existing VLMs by providing a unified, hierarchical, and interpretable representation of activities for improved parsing and classification performance."}
{"question_id": "a37fa0862f28bd4005c14092d19b86f84c30c983", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The selection criteria for the videos and classes in the study were designed to mitigate potential ethical issues and reduce bias. Specifically, the activity classes in the taxonomy were carefully chosen to be gender-neutral, culturally inclusive, and friendly toward people from different socioeconomic backgrounds. The video selection was conducted by a diverse team of researchers from different ethnicities and genders who examined and discussed each video to ensure diversity and the absence of offensive content. Additionally, keywords from multiple languages, including but not limited to English, Chinese, French, and Japanese, were used to search for videos, thereby enhancing the robustness and generalization of the models and significantly reducing potential dataset bias."}
{"question_id": "7f3ceaefa9722ecb3ce14f4c48d0191a6893f607", "paper_id": "nlpeer/F1000-22/11-222", "generation": "The lupeol and pristimerin used in this study were obtained from Cayman Chemicals (MI, USA)."}
{"question_id": "be6ee11df60dadea667438571e3ed15560c3cb04", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The MOMA-LRG dataset improves and extends the original MOMA dataset by introducing a new abstraction of human activity through activity graphs as a single universal representation encompassing all three hierarchical levels: activity, sub-activity, and atomic action. MOMA-LRG contains an order of magnitude more annotations and longer videos from a greater variety of scenes. It provides richer and more detailed annotations, including grounding of all associated entities, classification of each actor\u2019s role, and differentiation between static and dynamic predicates to capture state transitions, which encourages learning perceptual causality. Additionally, MOMA-LRG introduces a new annotation schema that is easily convertible from natural language to graphical annotations, enabling few-shot learning capabilities. This dataset also supports the evaluation and fine-tuning of video-language models (VLMs) on complex activity parsing tasks, which was not addressed by MOMA. In summary, MOMA-LRG offers a more comprehensive, hierarchical, and language-refined framework for multi-object multi-actor activity recognition compared to MOMA."}
{"question_id": "b6cb81cf492f5369fa4051c1d2e90b05b0aa9247", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The modules were partitioned by associating each neural module \\( m_i \\) with a specific subproblem \\( F_i \\), establishing a one-to-one and onto mapping from subproblems to modules. Each module receives only the subset of the state components necessary to solve its corresponding subproblem, effectively decomposing the overall state into module-specific components. This design ensures that each module processes only the relevant information required for its subproblem and outputs the solution to that subproblem. For example, in the robotics domain, modules related to the robot receive only the robot state components. This partitioning avoids passing the entire input through a chained sequence of modules and instead feeds decomposed task components separately to distinct modules, thereby reducing brittle dependencies and improving generalization to unseen module combinations."}
{"question_id": "507cdeff564fe9a3d5fe258fe00eef340d135d9b", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The ethical considerations taken into account when selecting the data for the dataset included the following protocols: (1) Taxonomy selection was carefully conducted to ensure that each activity class is gender-neutral, culturally inclusive, and respectful toward people from different socioeconomic backgrounds. (2) Video selection was performed by a diverse team of researchers from different ethnicities and genders who examined and discussed each video to ensure diversity and the absence of offensive content. Additionally, keywords from multiple languages (including English, Chinese, French, and Japanese) were used to search for videos to enhance diversification, robustness, and generalization of the models while significantly reducing potential bias in the dataset."}
{"question_id": "f2564b011de1ce166a9e4410c3857b5a9c2496ce", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The significant performance difference between CGExpan and LM-Base on user-generated text can be explained by the characteristics of entities in such text and the design choices of the methods. CGExpan scores each candidate entity by selecting one positive concept and multiple negative concepts, which tends to penalize entities that are multifaceted (belonging to multiple concepts) or vague (mentioned in diverse contexts). This design avoids selecting ambiguous contexts but consequently underperforms on entities with diverse or ambiguous contexts, which are prevalent in user-generated text. In contrast, LM-Base, a simpler language model-based baseline, better handles entities with diverse contexts and outperforms CGExpan on concepts involving user-generated text characteristics, such as \"seating arrangement.\" Thus, CGExpan's approach is less effective for user-generated text due to its penalization of multifaceted and vague entities, while LM-Base's contextual representations allow it to better capture such entities, leading to its superior performance."}
{"question_id": "62968052606a9490b4add4170997a2c69f19d4ec", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "Yes, the MOMA-LRG dataset is licensed under a Creative Commons license, specifically the Attribution 4.0 International (CC BY 4.0) license."}
{"question_id": "23c1d98a22e68ab8a92b7b1cd2fee83fa79e9a86", "paper_id": "nlpeer/ARR-22/488687633dbb7f9cbac3b0164d08e1cef083aa01ab86fad82e7c5b199bda3c1213b66dfcf3627a183c977d0aee40f46f169cdd51a5efe539ec4fc02981a383f7", "generation": "The optimal depth of a ranked list of entities returned by an entity search engine approach corresponds to the actual size of the entity set for the target concept, denoted as \\( k_g \\). Evaluating at \\( k_g \\) adapts to different concept sizes and provides an estimate of recall, which is crucial for assessing effectiveness in real-world settings where concept sizes are commonly large. Using a fixed small \\( k \\) (e.g., top 10-50) is inadequate because it may not represent multifaceted, vague, and non-named entities prevalent in user-generated text, and thus may overestimate performance. Therefore, the ranked list should ideally include all entities up to \\( k_g \\), the full concept size, to ensure comprehensive retrieval of relevant entities."}
{"question_id": "67314f99bdc98da9611efbf0de1f4660e36f629c", "paper_id": "openreview/ICLR-2022-conf/wwDg3bbYBIq", "generation": "The sampling method used to extract patterns with a time window involves first computing an average daily traffic speed pattern consisting of 288 data points (corresponding to 24 hours with 5-minute intervals) for each vertex. Then, the daily patterns are sliced into segments using a given window size \\( T' \\), resulting in multiple traffic patterns. This slicing process creates the initial pattern set \\( P \\), where the number of patterns is \\( |P| = N \\times \\lfloor 288 / T' \\rfloor \\), with \\( N \\) being the number of vertices. To address class imbalance caused by many similar patterns, clustering-based undersampling is applied using cosine similarity. Patterns with cosine similarity larger than a threshold \\( \\delta \\) are grouped into the same cluster, and the cluster centers are used as representative patterns. This clustered and undersampled set of representative patterns serves as the keys for memory access in the model."}
{"question_id": "bf76bbb77fabe1a9105b55efcd591d179958b2c6", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "Optimizing the reward with respect to the possible combinations of modules means finding the selection and arrangement of neural modules from a shared set that, when composed into a policy, yields the highest expected return for a given reinforcement learning task. Formally, each task corresponds to a pair of state and action nodes in a compositional graph, and the goal is to identify a path through this graph\u2014i.e., a sequence of modules\u2014that defines a policy maximizing the task-specific reward. This involves performing a discrete search over all feasible module combinations, evaluating each resulting policy by rolling out episodes in the environment, and selecting the combination that achieves the highest average return. This process ensures that the agent reuses and composes previously learned subproblem solutions optimally to solve new tasks efficiently."}
{"question_id": "0dcfbcf7b77777639a682294aaf99c3fff25cd20", "paper_id": "openreview/NeurIPS-2022-dabt/eJhc_CPXQIT", "generation": "The authors ensure that the natural language sentences produced from the \"ground truth\" activity graphs accurately describe the scene by employing a graph-to-language module that converts all levels of the MOMA-LRG activity graph hierarchy into natural language using specific conventions. At the atomic action level, predicates are tagged with [src] and [trg] templates, where [src] denotes the source entity and [trg] the target entity, enabling the construction of grammatically correct present continuous tense sentences. For example, a predicate such as \"touching\" is represented as \"[src] touching [trg]\", which, given entities like \"person\" and \"table,\" produces the sentence \"A person is touching the table.\" All annotations are provided in the present continuous tense, resembling live narration, which facilitates accurate and natural descriptions of the scene. This structured approach to converting graphical annotations into natural language ensures that the sentences faithfully represent the interactions and activities depicted in the video."}
{"question_id": "5219ee2947eb66850d9df883d32c6549b914d086", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The system contains a set of neural modules \\( M = \\{ m_1, \\ldots, m_k \\} \\), where each module \\( m_i \\) corresponds to a specific subproblem \\( F_i \\). The number of modules \\( k \\) corresponds to the number of distinct subproblems that must be solved in the compositional RL tasks. For example, in the robotics domain, the number of modular layers \\( d_{\\max} \\) equals the number of subproblems, such as \\( d_{\\max} = 3 \\) for grasping an object, avoiding an obstacle, and actuating a robot arm.\n\nIn the discrete 2-D tasks experimental setting, the architecture is constructed with three types of modules: static object modules, target object modules, and agent modules. The learner constructs a separate architecture for each task by combining one module of each type, resulting in a total of three modules per task.\n\nAdditional experiments tested varying the number of modules from 3 to 6, showing that the method is robust to the number of modules, with minor effects on performance. The original correct number of modules for the 2-D tasks was 4, indicating that the system can flexibly handle different numbers of modules depending on the task domain.\n\nIn summary, the system contains a modular set of neural networks where the number of modules \\( k \\) corresponds to the number of subproblems in the task domain, typically ranging from 3 to 6 modules in the evaluated experiments."}
{"question_id": "5c090b48e2d8b39f413f602a716b92676b7e7ba7", "paper_id": "openreview/ICLR-2023-conf/8efJYMBrNb", "generation": "The baseline alignment programs compared against BetaAlign\u2014MUSCLE, MAFFT, PRANK, T-Coffee, ClustalW, and DIALIGN\u2014were used with their default parameters in the evaluation. There is no explicit mention in the provided information that these baseline programs had their adjustable parameters tuned or optimized for improved performance in the experiments. The discussion highlights that traditional alignment algorithms rely on fixed scoring schemes and assumptions about evolutionary models, which may not fit all datasets well. It is noted that fitting scoring schemes to specific datasets can improve performance in dynamic programming-based aligners, implying that these baseline methods do have adjustable parameters (such as scoring schemes) that could potentially be tuned. However, in the reported comparisons, the baselines were used with default settings without parameter adjustment."}
{"question_id": "6159c6e153be58a55c17f3cda104c7ebdd581acc", "paper_id": "openreview/ICLR-2023-conf/8efJYMBrNb", "generation": "The rationale for using the CS-error metric instead of SPS metrics lies in the strictness and precision of the column score (CS) measure. The CS metric quantifies alignment accuracy by counting the number of alignment columns in the inferred alignment that are identical to the \u201ctrue\u201d alignment, requiring both the coordinates and the characters of each column to match exactly. This means that even if the characters match but their positions differ, it is not considered a correct match. The CS score is normalized by the total number of columns in the true multiple sequence alignment (MSA), and the CS-error is defined as 1.0 minus the CS score, reflecting the level of disagreement between the inferred and true MSAs. This approach provides a more stringent and precise evaluation of alignment accuracy compared to SPS metrics, which may not require exact coordinate matching."}
{"question_id": "1b206d1d36f66f3d336a33e34858567e8a593ab0", "paper_id": "openreview/ICLR-2022-conf/5XmLzdslFNN", "generation": "The system employs a neural modular policy architecture designed for functionally compositional reinforcement learning. The architecture consists of distinct neural modules, each responsible for solving a specific subproblem \\( F_i \\) within a task, with a one-to-one mapping between subproblems and modules. Tasks are constructed by chaining these modules in a fixed sequential graph structure, where each module processes only the subset of the state relevant to its subproblem and receives as input both its module-specific state component and the output of the preceding module in the chain.\n\nFor discrete 2-D tasks, the architecture processes the static object first, followed by the target object, and finally the agent dynamics. The static object module consumes multiple input channels corresponding to static objects and applies convolutional blocks with ReLU activations and max pooling. The target object module preprocesses the target object channel similarly, concatenates its output with that of the static object module, and applies further convolutional processing. The agent module preprocesses the agent channel, concatenates it with the target object module output, and passes this through separate multi-layer perceptrons for the actor and critic, each with a single hidden layer of 64 units and tanh activation.\n\nIn the robotic manipulation domain, the architecture mirrors the 2-D task structure but adapts to continuous control. The obstacle module processes obstacle states through a single hidden tanh layer of 32 units. The object module preprocesses the object state with a tanh layer, concatenates it with the obstacle module output, and applies another tanh layer. The robot module processes robot and goal states through two tanh hidden layers of 64 units each, concatenates this with the object module output, and applies a linear output layer. Separate networks are used for the actor and critic, with the critic additionally receiving the action as input to the robot module.\n\nThis modular design decomposes the input state into task components, feeding each component to its corresponding module, which facilitates zero-shot generalization to unseen task combinations by restricting each module's input to relevant information only. The architecture avoids brittle dependencies typical of pure chaining by ensuring module outputs are compatible and focused on their subproblems. This design has been empirically shown to outperform simpler chained modular architectures and monolithic baselines in compositional lifelong reinforcement learning tasks."}
{"question_id": "86b7bff4eb8f5701bb87715221c22a2db29eaae1", "paper_id": "openreview/ICLR-2022-conf/sPIFuucA3F", "generation": "In Algorithm 1, line 4, the data tuple \\((x_t, a_t, r_t)\\) is retrieved from the offline data set \\(D_n\\)."}
{"question_id": "c68719740a590d79c10dfd14e5cb4e1f51938871", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "The low performance of the \"DESCRIPTION\" answer type in Table 5 is attributed to the inherent difficulty in predicting the structure of these answers. The \"DESCRIPTION\" class was also identified by the annotators as the most challenging to annotate, indicating that its answer spans have less predictable syntactic patterns and more complex semantics compared to other answer types. This complexity leads to a significant drop in model performance for this category."}
{"question_id": "4b00407f8b0e58aafdaee12f078de8b313872f04", "paper_id": "openreview/ICLR-2022-conf/8uz0EWPQIMu", "generation": "Yes, the proposed alternate way to use the linear method differs from the original linear model by Dalvi et al. (2019). In the original algorithm, after training the probe, the weights are fed into a neuron ranking algorithm that distributes neurons equally among labels, regardless of the actual importance of neurons for each label. This can result in non-important neurons being ranked higher than important ones. The alternate method computes, for each neuron, the mean absolute value of the weights associated with it and sorts neurons by this value from highest to lowest. This modification empirically provides better results and is more suitable for large label sets, addressing the imbalance issue present in the original method."}
{"question_id": "19e742075a73b79f8d1593d22060ea47535a485a", "paper_id": "openreview/ICLR-2022-conf/8uz0EWPQIMu", "generation": "Figure 3a illustrates the Standard pattern observed in the accuracy results across different configurations when evaluating neuron rankings with classifiers. In this pattern, all top-to-bottom rankings consistently yield higher accuracy than the random ranking, which in turn outperforms all bottom-to-top rankings. This pattern demonstrates the expected behavior where better-ranked neurons lead to improved classification accuracy. The figure serves to exemplify one of the three distinct accuracy patterns identified in the study, highlighting the relationship between ranking direction and classifier performance."}
{"question_id": "5ac9e91c8d313af9a0771c5e3e46e95d2a7c2315", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "The paper proposes a new evaluation metric for multi-span question answering called the partial match metric. This metric measures the overlap between predicted answer spans and ground truth answer spans by computing partial retrieved and partial relevant scores based on the longest common substring (LCS) between each predicted span and each ground truth span. Specifically, for each predicted span \\( p_i \\) and ground truth span \\( t_j \\), the partial retrieved score \\( s^{ret}_{ij} \\) is defined as the length of the LCS divided by the length of \\( p_i \\), and the partial relevant score \\( s^{rel}_{ij} \\) is the length of the LCS divided by the length of \\( t_j \\). Precision and recall are then calculated by taking the maximum partial scores across all pairs of predictions and ground truths, followed by micro-averaged F1 computation. This metric complements the exact match metric by capturing partial overlaps between multi-span answers, providing a more nuanced evaluation of multi-span QA systems."}
{"question_id": "42aec356c91c672d813f540951f0b79d9f57705f", "paper_id": "openreview/ICLR-2022-conf/oiZJwC_fyS", "generation": "DeepNN refers to a ReLU activated deep neural network that can be mathematically represented as a tropical rational mapping. Specifically, a DeepNN with input dimension \\( d \\) and output dimension \\( m \\) consists of multiple layers including an input layer, one or more hidden layers with ReLU activations, and an output layer. Each hidden layer node computes a tropical polynomial with two terms, and the output nodes can be expressed as differences of tropical polynomials, i.e., tropical rational functions. This tropical geometric interpretation allows the network's function to be analyzed and approximated via the geometry of associated tropical zonotopes, which are Minkowski sums of line segments representing the network's tropical polynomials."}
{"question_id": "b49d17a4084f479d1c1f3e920a863be370968f66", "paper_id": "nlpeer/ARR-22/3cbf91f8e2f7e62be51109688c84a2b424e6018ee3c976fbe55e5331bc9ab2f4c46491d006087aed3be35a3370c945c100bd718e2c1131de19eac3dbe93326bc", "generation": "The per-triple templates were developed as simple, single-sentence, mostly clear-cut verbalizations of the predicates. They were manually created by one of the authors using only the input data, without relying on human reference texts. The templates were hand-crafted for each predicate to introduce in-domain knowledge efficiently while minimizing manual workload. Although the authors considered extracting templates from training data by delexicalizing single-triple examples, this option was rejected due to noise in the data and the unavailability of such data in a zero-shot setup. The templates were designed to be general and sometimes resulted in minor disfluencies, which could be addressed in later pipeline stages. Examples of these templates include phrases like \"<s> plays <o>\" for the predicate \"instrument\" and \"<s> is from <o>\" for \"countryOrigin.\""}
{"question_id": "8c6b063b9a5318af6557db02c0c7dbc93f8939be", "paper_id": "openreview/NeurIPS-2022-dabt/HYELrdRdJI", "generation": "The 16k real captured views are individual images of objects collected from multiple cameras and trials, while the 9k multi-view sets are constructed by combining these captured views into groups for classification tasks. Specifically, multi-view sets are formed by sampling and combining two to six views per set, ensuring each set contains at least one informative view. For training, 20 informative and 20 uninformative views per object are manually sampled from Collection A, and ten multi-view sets are constructed for each object. For validation and testing, 40 informative and 120 uninformative views per object are randomly sampled from Collections A and B, and 100 multi-view sets are constructed for each object. Thus, the 9k multi-view sets represent curated combinations of the 16k individual views designed to provide diverse and informative input for multi-view object classification."}
{"question_id": "f5c277093cecafb8c4d4588dfbae6b5279b14a79", "paper_id": "nlpeer/ARR-22/e4742f934fad6dc1f3389981d0d0fc6b8b48638f4c5c0a74b9dc11bdd026489bb2d3e78a6a395e82124e87c7409ff370cdff0edf375958bcf909372da1859815", "generation": "The re-annotation process was performed on the original MultiSpanQA dataset, which contains only multiple-span answers. This involved three trained annotators who re-annotated all data using the Brat annotation tool, following a detailed annotation guide and categorizing answer structures into five types. The re-annotation included rejecting some instances, rewriting questions, and modifying answer spans.\n\nFor the expanded dataset, which includes single-span question\u2013answer pairs and unanswerable questions added by randomly sampling from Natural Questions (NQ) and applying the same preprocessing, there is no explicit mention of a separate re-annotation process. The expansion was created by augmenting the original dataset with additional instances rather than re-annotating them.\n\nTherefore, re-annotation was conducted for the original multi-span dataset but not explicitly for the expanded dataset."}
{"question_id": "7cd7d4d944b16a2603aaaf3ebb5628edd813a346", "paper_id": "nlpeer/ARR-22/3cbf91f8e2f7e62be51109688c84a2b424e6018ee3c976fbe55e5331bc9ab2f4c46491d006087aed3be35a3370c945c100bd718e2c1131de19eac3dbe93326bc", "generation": "The sentence aggregation model places delimiters between ordered facts based on a binary classification of whether neighboring facts should be mentioned separately or aggregated into a single sentence. Specifically, for each pair of neighboring facts in the ordered sequence, the model outputs a delimiter \u03b4_i \u2208 {0, 1}, where \u03b4_i = 1 indicates that the neighboring facts should be separated into distinct sentences (no fusion), and \u03b4_i = 0 indicates that the facts should be aggregated and their corresponding sentences fused into one. The model is trained on synthesized sentences from the WIKI FLUENT corpus, where \u03b4_i = 0 is assigned to pairs of sentences originally aggregated (resulting from splitting a single sentence), and \u03b4_i = 1 otherwise."}
{"question_id": "61588ca196125738d21c2d191ecac13249af297d", "paper_id": "openreview/ICLR-2022-conf/oiZJwC_fyS", "generation": "Yes, the Neural Path K-means algorithm can be tested on larger networks. Experiments conducted on CIFAR datasets using CIFAR-VGG and an altered version of AlexNet adapted for CIFAR demonstrate that Neural Path K-means retains good performance on larger datasets and deeper architectures. It achieves slightly better accuracy and lower deviation than baseline pruning methods in most cases, although its performance degrades when keeping very few weights. Additionally, experiments on a deep neural network with layers of size 28*28, 512, 256, 128, and 10 (referred to as deepNN) show that the method performs well in compressing deeper architectures."}
{"question_id": "b0767779541047ab4deb8c71f900288615ddd5a7", "paper_id": "openreview/ICLR-2022-conf/5hLP5JY9S2d", "generation": "The Vision Transformer (ViT) model demonstrates better generalization to the open-set scenario on ImageNet compared to other methods primarily due to its architectural characteristics and training behavior. Despite having a large number of parameters (86 million) and lacking convolutional inductive biases, ViT does not overfit its representation to the training classes. This suggests that the purely attention-based architecture of ViT enables it to learn more generalizable features that are less tied to the closed-set categories. Consequently, ViT outperforms the typical trend observed in open-set recognition (OSR) performance relative to closed-set accuracy. This superior generalization aligns with recent findings on the benefits of attention-based vision models, which support both improved closed-set performance and enhanced ability to detect semantic novelty in open-set conditions."}
{"question_id": "e1369f11b53bb858522bacf4bf2e9d8448dc1ef5", "paper_id": "openreview/ICLR-2022-conf/EnwCZixjSh", "generation": "It is important for the metric to be insensitive to the number of layers when evaluating rank correlations because the choice of GIN architecture, including the number of layers, has been found to be less critical than the choice of metric itself for strong evaluation performance. Metrics computed using different GIN configurations, including varying numbers of graph propagation rounds (layers), show low variance in rank correlation results across experiments. This insensitivity ensures that the evaluation metric reliably measures the dissimilarity between generated and reference graph sets regardless of the specific GIN architecture used, thereby providing stable and consistent rankings of graph generative models without dependence on architectural hyperparameters."}
{"question_id": "3318142bc7bd1401191fcc4a9712243c0df0f1df", "paper_id": "openreview/NeurIPS-2022-dabt/HYELrdRdJI", "generation": "High inter-class view similarity refers to the phenomenon where different objects appear similar or nearly identical in specific views. This similarity makes it difficult for humans to accurately classify an object from those particular views, especially in fine-grained (instance-level) object categorization tasks. Views that exhibit this uncertainty in human class labeling are termed uninformative views. Because the class labels are typically assigned in a one-hot manner (i.e., a single definitive label per view), this uncertainty leads to inconsistencies between the assigned labels and human judgment. Such inconsistencies across multiple views of the same object result in multi-view label noise, where the labels do not reliably represent the true class information due to ambiguous or misleading visual cues in uninformative views."}
{"question_id": "f117dce3beae4a1fc909bbadebcc235634d017c0", "paper_id": "openreview/NeurIPS-2022-dabt/7w-a8PYPlP", "generation": "The seismic forward modeling applied absorbing boundary conditions."}
{"question_id": "1eafcdeb90458c749f5b2e6dcdaaa06a4ba58abd", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "The phrase \"but train the detector head with \\( r(\\cdot) \\) online in a single stage\" refers to the training approach used in the F-VLM framework for open-vocabulary object detection. Specifically, the detector head, which includes components such as the region proposal network (RPN), feature pyramid network (FPN), and Mask R-CNN heads, is the only part of the system that is trainable and is trained directly (\"online\") during the detection training process. The function \\( r(\\cdot) \\) denotes the region embedding function that extracts region features from the frozen backbone features \\( F(I) \\) given region proposals. Training the detector head \"online in a single stage\" means that the detector head is trained end-to-end in one training phase using the frozen backbone features without requiring multiple training stages or additional pretraining steps. This approach contrasts with multi-stage pipelines or knowledge distillation methods, simplifying the training process by only updating the detector head parameters while keeping the vision and language model backbone frozen."}
{"question_id": "ec3f80fbff718abc4b5fae665cfdc994570329fb", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "No, a quantitative evaluation of the experiment on Ego4D cannot be performed based on the provided information. The application of F-VLM to Ego4D is described as a transfer detection task without any finetuning, where categories are provided by the user based on visual inspection of the video. The results are presented qualitatively through visualization of detected novel categories in indoor and grocery store scenes. There are no reported quantitative metrics, such as average precision or detection accuracy, for the Ego4D experiment in the provided content."}
{"question_id": "3e6d53b8861714d6727e6f1a924eb2046baac6a7", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "The accuracy of the model on novel categories improves significantly when larger vocabularies are incorporated. Specifically, on the LVIS dataset, which contains a large and diverse set of 1203 object categories, the F-VLM model achieves a novel category mask AP (AP_r) of 32.8, representing a substantial improvement of +14.2 AP_r over the ResNet50 baseline and outperforming the best existing approach by 6.5 mask AP on novel categories. This indicates that scaling up the vocabulary size to include many novel categories enhances the model's open-vocabulary detection performance.\n\nFurthermore, the model demonstrates strong scaling properties with increased backbone capacity, which correlates with improved accuracy on novel categories. For example, in transfer detection benchmarks where the model trained on LVIS base categories is tested on datasets with smaller vocabularies such as COCO (80 categories) and Objects365, the model achieves gains of +7.3 AP on COCO and +5.8 AP on Objects365 by increasing backbone capacity. This suggests that the model effectively leverages larger vocabularies during training to improve detection accuracy on novel categories.\n\nIn summary, adding novel categories with large vocabularies to the candidate set leads to marked improvements in detection accuracy for those novel categories, as evidenced by the substantial AP gains on LVIS and transfer benchmarks."}
{"question_id": "a8466adf7868015b87e7447c11f576b29d121012", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The phrase \"environment's health\" refers to the overall well-being and sustainability of the natural environment, which is closely linked to human health aspects. It implies maintaining the environment in a condition that supports ecological balance and reduces environmental hazards, thereby preserving resources and preventing environmental destruction. Recycling is adopted as a measure to promote positive environmental attitudes and behaviors that contribute to sustaining the environment's health."}
{"question_id": "601d6dade2b1d6724ae69aafc64a71bafd79062e", "paper_id": "openreview/NeurIPS-2022-dabt/7w-a8PYPlP", "generation": "Yes, the published code includes the algorithms used to generate the data. The data generation pipelines involve synthesizing velocity maps from three different priors (mathematical representations, natural images, and geological reservoir) and generating seismic data via forward modeling. The forward modeling algorithm is based on finite difference methods with absorbing boundary conditions and a Ricker wavelet source function, originally implemented in MATLAB and rewritten in Python for efficiency and neural network compatibility. The code and related information are released on Github under OSS and BSD-3 licenses, ensuring reproducibility and accessibility of the data generation methods."}
{"question_id": "c908b12fc3ea26161680a836fc0ee29b02fd4e96", "paper_id": "openreview/ICLR-2023-conf/MIMwy4kh9lf", "generation": "Region classification for novel categories with a large vocabulary of class names not present in the training images can be performed by leveraging the open-vocabulary recognition capability of frozen vision and language models (VLMs). Specifically, the approach involves the following steps:\n\n1. Use a pretrained VLM with frozen backbone features that have learned from large-scale image-text pairs, enabling rich, locality-sensitive, and discriminative features transferable to object detection.\n\n2. At test time, crop and resize region features from the frozen backbone using ROI-Align to obtain fixed-size inputs, then apply a VLM pooling layer to generate region embeddings.\n\n3. Compute cosine similarity scores between the region embeddings and text embeddings of both base categories and novel categories, where the text embeddings are generated by the matching pretrained text encoder of the VLM.\n\n4. Combine the detection scores from the detector head (trained only on base categories) with the VLM similarity scores for both base and novel categories using a geometric mean weighted by parameters controlling the influence of VLM scores.\n\n5. This fusion of detection and VLM scores yields final open-vocabulary detection scores that enable classification of novel categories without requiring their presence in the training images.\n\nThis method allows direct open-vocabulary classification at the region level by expanding the text embeddings at inference time to include novel categories, thus supporting large vocabularies of class names unseen during training."}
{"question_id": "127423c37403d9d9d34a21c17bfb33234b5f944a", "paper_id": "openreview/ICLR-2023-conf/tYIMtogyee", "generation": "Yes, the authors used a validation set to tune the hyperparameters on the QM9 dataset. They performed hyperparameter optimization by sweeping noise values and loss coefficients for denoising and atom type recovery, tuning over a set of noise values for PCQM4Mv2 and QM9 (on the HOMO energy target). The QM9 dataset was split into training, validation, and testing sets, with 10,000 examples reserved for validation, which was used for tuning hyperparameters."}
{"question_id": "fcd1a1d599ae695d923bfabe4f62e5e457ca2de1", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "The training time for machine learning (ML) surrogate models, such as the Fourier Neural Operator (FNO), is significantly higher than the runtime required for classical numerical PDE solvers to generate a single data sample. For example, training times reported are on the order of tens to hundreds of thousands of seconds (e.g., 48,760 seconds for the Diffusion-sorption case, 107,567 seconds for 2D CFD, and 12,387 seconds for 3D CFD), whereas the simulation times for these cases range from about 1.59 seconds to 2,582.61 seconds. However, once trained, the ML models can perform inference\u2014i.e., predict solutions\u2014multiple orders of magnitude faster than numerical solvers, with inference times on the order of 0.14 to 0.32 seconds per sample.\n\nTherefore, although the initial computational demand for training the surrogate models is very high, the substantial speedup in inference time allows for efficient repeated evaluations of the dynamic system. This efficiency gain is particularly valuable in scenarios requiring many evaluations, such as parameter sweeps or real-time predictions. Hence, the time investment in training the surrogate is outweighed by the benefits of accelerated evaluation during inference."}
{"question_id": "174eff40340ae0616a2328f75efd8cf8431b3150", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The conceptual model in Figure 2 was developed through a six-phase thematic analysis of focus group discussion (FGD) data collected from 29 student participants who experienced the Edcraft Gamified Learning (EGL) activity. Initially, audio recordings of five online FGDs were transcribed and familiarised with annotations. In the second phase, initial coding was performed and compiled into a codebook, which underwent inter-rater reliability assessment yielding a substantial kappa agreement of 0.786. Subsequently, 37 codes were sorted into 13 categories and further grouped into four main themes based on repeated patterns and similarities: gameful experience, intrinsic motivation, extrinsic motivation, and social influence. These themes were reviewed and refined to ensure alignment with the research questions. The final themes were then used to verify, enrich, and create the conceptual framework depicted in Figure 2, illustrating the factors influencing youths\u2019 recycling intention following the gamified recycling activity."}
{"question_id": "5420a636705116e4e99d17572011f028d54a72b2", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "Prior work related to this paper has primarily used classical statistical error measures such as the root-mean-squared-error (RMSE) over the whole domain and, at most, PDE-motivated variants such as the RMSE of the gradient. Measures based on properties of the underlying physical systems have been lacking in these previous benchmarks."}
{"question_id": "8397ceb9d91201c9f2eb30de3c87e1e8243a827d", "paper_id": "nlpeer/F1000-22/10-890", "generation": "The conceptual model presented in Figure 2 is an overview of the Edcraft Gamified Learning (EGL) activity flow. This model outlines the two-level online gamified recycling crafting activity conducted during the COVID-19 pandemic lockdown. Participants first watch a video tutorial to learn the basic recycling craft for level 1, then have a day to complete the craft creatively. The following day, they proceed to level 2, which involves a more difficult recycling craft following the same procedure. Their crafts are then evaluated by art teachers and ranked on a leaderboard. Participants ranked on the leaderboard receive certificates and prizes. This flow integrates game elements such as levels, points, leaderboard, and rewards to engage youths in recycling through gamified learning."}
{"question_id": "73faf71e5e746272235608cd46a8fda2b309ef70", "paper_id": "openreview/ICLR-2023-conf/tYIMtogyee", "generation": "The authors refer to tuning \"approximately\" 5 values on the QM9 dataset in the context of selecting noise scales for denoising during pre-training and fine-tuning. Specifically, they tuned over a discrete set of noise values {0.005, 0.01, 0.02, 0.05, 0.1} to determine the optimal noise level for the denoising objective on the HOMO energy target of QM9. This tuning process involved evaluating these roughly five candidate noise values to identify the best performing noise scale, which was then fixed for all other targets."}
{"question_id": "273390ef5c449b8574f4d7b6e46f332e7152a15d", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "The intuition and motivation behind the head-to-tail method in section 3.2.3 stem from the observation that words in natural language sentences often have dependency relations, where the presence of one word can be predictive of another (e.g., the verb \"am\" always co-occurs with \"I\"). This dependency-based latent structure is adopted to model such relations explicitly in artificial languages. The method pairs half of the tokens as \"heads\" with the other half as \"tails,\" representing simple dependency relations that always appear together in a sentence. This design aims to endow the sampled tokens with structural properties similar to natural language dependencies, hypothesizing that pretraining on such data will induce an inductive bias in language models toward finding relations between tokens. This inductive bias is considered important for processing natural language effectively."}
{"question_id": "41338acb67bcf333b2817c15b8b74290fb3d9327", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "The provided information does not mention any downstream tasks such as natural language inference, question answering, or semantic role labeling being tested using an encoder transferred from language 1 (L1) to language 2 (L2) without any parameter updates. The study primarily focuses on evaluating transferred encoders on language modeling and dependency parsing tasks. Specifically, the downstream tasks discussed include natural language modeling on the Penn Treebank corpus and dependency parsing on the English EWT dataset. There is no reference to experiments involving natural language inference, question answering, or semantic role labeling in the context of encoder transfer without parameter updates."}
{"question_id": "fef7c2c7a69370b666710942e3d931819e7bc02d", "paper_id": "openreview/NeurIPS-2022-dabt/dh_MkX0QfrK", "generation": "The accuracy of the surrogate model directly impacts the accuracy of downstream tasks such as inverse problem solving. Specifically, the surrogate's prediction error influences the estimation of unknown initial conditions or parameters by minimizing the prediction loss over a time horizon. For example, in an inverse problem setup where the initial condition is identified to minimize the surrogate's prediction error over 15 time steps, the Fourier Neural Operator (FNO) surrogate outperforms U-Net, indicating that a more accurate surrogate leads to better inverse problem solutions. However, limitations in surrogate accuracy, such as increased error in regimes with strong discontinuities or low diffusion coefficients, degrade the quality of downstream inference. Thus, the surrogate's ability to accurately learn the forward propagator and capture temporal dependencies is critical for reliable downstream task performance."}
{"question_id": "cd38abc68b46b12d953fddc8838eb77978963fca", "paper_id": "openreview/ICLR-2023-conf/kDEL91Dufpa", "generation": "SimCLR and DCL are defined as self-supervised learning methods that use normalized embeddings (l2 normalized column-wise). Their loss functions involve a contrastive criterion based on cosine similarities between embeddings. Specifically:\n\n- DCL's loss function is given by:\n\\[\nL_{\\text{DCL}} = \\sum_{i=1}^N -\\log \\frac{e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau}}{\\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau}}\n= \\sum_{i=1}^N - \\frac{K_{\\cdot,i}^T K'_{\\cdot,i}}{\\tau} + \\log \\left( \\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau} \\right),\n\\]\nwhere \\(K\\) and \\(K'\\) are matrices of normalized embeddings, \\(\\tau\\) is a temperature parameter, and the sums run over batch indices.\n\n- SimCLR's loss function is given by:\n\\[\nL_{\\text{SimCLR}} = \\sum_{i=1}^N -\\log \\frac{e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau}}{e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau} + \\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau}}\n= \\sum_{i=1}^N - \\frac{K_{\\cdot,i}^T K'_{\\cdot,i}}{\\tau} + \\log \\left( e^{K_{\\cdot,i}^T K'_{\\cdot,i} / \\tau} + \\sum_{j \\neq i} e^{K_{\\cdot,i}^T K_{\\cdot,j} / \\tau} \\right).\n\\]\n\nBoth methods rely on normalized embeddings and use cosine similarity as the similarity measure between embeddings. Proposition 3.1 states that, considering an infinite number of negative samples, these criteria lead to embeddings where the expected dot product of negative pairs is zero and the variance of the dot product is \\(1/M\\), where \\(M\\) is the embedding dimension."}
{"question_id": "d7f32782ecbb103a3971b1e3a918576549b44723", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The motivation for building provably robust adversarial examples arises from the limitations of existing adversarial attack methods, which typically produce only single concrete adversarial examples whose effects can often be mitigated by current defenses. Existing robust adversarial examples are empirically verified but lack formal guarantees, and their robustness can be non-uniform across the input region, with significant subregions exhibiting much lower robustness. This non-uniformity can create a misleading impression of the strength of empirical robustness. Provably robust adversarial examples address these issues by constructing large connected input regions that are guaranteed, through formal certification, to contain only adversarial examples. This guarantees uniform robustness across the entire region, providing a more reliable and stronger adversarial attack that is invariant to real-world perturbations such as pixel intensity changes and geometric transformations. Additionally, provably robust examples are more effective against state-of-the-art defenses like randomized smoothing and avoid the pitfalls of empirical methods that rely on sampling and expectation calculations, which may not scale well or provide uniform guarantees."}
{"question_id": "46780f3f8ab86a46782f68b7ca66e5e1438afc01", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "An image's adversarial distance radius \\( R' \\) was considered \"too big\" if it was more than 33% larger than the certified radius \\( R \\) of the smoothed classifier \\( g \\). Images with such large \\( R' \\) values were excluded from the experiments to avoid trivial attacks where the image is attackable for most classes on \\( g \\). This criterion was part of a heuristic procedure to select \\( R' \\) by searching for the smallest adversarial distance on the base classifier \\( f \\) where at least 10% of 500 attacks succeed, while excluding images with \\( R' \\) exceeding this threshold to ensure the problem remained non-trivial and the smoothed classifier was likely to be attackable."}
{"question_id": "d3ce17368ff699857360c15972ad48cb235350b8", "paper_id": "nlpeer/ARR-22/eb931c7319dc1b08fb3c97fb77b60b7eac9a41d699b58e0139d655f4d1cbc5c61db8d9e9dd42e3515a6cd839518933bd7d033289be73a8111b2170e8b2bf983f", "generation": "Setting the dimension of the word and discourse vectors to 10 empirically makes the entire token distribution close to the Zipfian distribution because this dimensionality balances the representation capacity of the vectors such that the log-linear model, which samples tokens according to the probability \\( p(w|s) \\propto \\exp(\\vec{c}_s \\cdot \\vec{v}_w) \\), naturally produces non-trivial cooccurrence patterns within sentences. The discourse vector \\(\\vec{c}_s\\) represents the topic of the sentence and determines the unigram distribution over the vocabulary, while the word vector \\(\\vec{v}_w\\) encodes token properties. When both vectors are drawn from normal distributions with dimension 10, the resulting interaction between discourse and word vectors yields a token distribution that approximates the empirical Zipfian distribution observed in natural language."}
{"question_id": "2a76dc6fa246d4afcfe9aea7050a47aa3949d755", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "No, the camera pose is not concatenated with the latent code in the same way as the EG3D method. Instead, PV3D conditions the generator on pre-estimated camera pose sequences separately from the latent codes. The appearance and motion latent codes are independently encoded into intermediate codes, and the camera poses are used to condition the generator at each timestep to model head rotation by rotating the camera rather than deforming the 3D scene. This approach alleviates motion ambiguities and facilitates convergence. Additionally, the mapping network takes a camera pose when computing the appearance code, and different camera conditioning strategies are explored to balance temporal consistency and multi-view consistency, with the preferred strategy being to share the same camera pose in the mapping network during inference only. This differs from EG3D, which does not incorporate temporal dynamics or separate camera conditioning in this manner."}
{"question_id": "d88d8f911add3ead1f649741f7c22700199f9094", "paper_id": "nlpeer/ARR-22/ed81d66dc55496061fd4c97530be7b966d2e734e8435092b2c0c6764f54ea145e62f67555e480d996aebb93772e0de2877ccaad504e0f2228a1395c68d157eda", "generation": "The provided chunks do not contain any explicit information regarding the public availability of the code and dataset associated with the paper. Therefore, it cannot be determined from the given content whether the code and dataset will be made publicly available."}
{"question_id": "****************************************", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "The authors did not explicitly report observing jittering in the processed videos. Instead, they emphasize that their PV3D model produces temporally consistent and motion plausible videos with high-quality geometry, outperforming baseline models that suffer from implausible motions due to non-end-to-end trainable designs. They also address temporal consistency issues by carefully selecting synthesis layers for motion feature incorporation and by sharing camera poses in the mapping network during inference to improve temporal consistency. These design choices and evaluations suggest that jittering, which would indicate temporal inconsistency, was effectively mitigated in their generated videos."}
{"question_id": "bc6f50621da1a65a6e46211a4f48751a6da35304", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The paper defines an \"adversarial attack on smoothed classifiers\" in terms of an adversarial distance \\( R' \\) and a perturbed point \\(\\tilde{x}\\) such that \\(\\|\\tilde{x} - x\\|_2 < R'\\) and the smoothed classifier \\(g\\) predicts a different class at \\(\\tilde{x}\\) than at \\(x\\), i.e., \\(g(x) \\neq g(\\tilde{x})\\). The adversarial \\(L_2\\) ball around \\(\\tilde{x}\\) is then defined as the radius \\(R_{\\text{adv}}\\) within which the smoothed classifier \\(g\\) is certified to predict the same adversarial class as at \\(\\tilde{x}\\). Formally, for all points \\(x + \\delta\\) with \\(\\|\\delta\\|_2 < R_{\\text{adv}}\\), the classifier satisfies \\(g(x + \\delta) = g(\\tilde{x})\\).\n\nThus, the adversarial \\(L_2\\) ball is centered at the adversarial example \\(\\tilde{x}\\), and every sample within this ball shares the same adversarial classification as \\(\\tilde{x}\\). This does not contradict the expectation that \\(\\tilde{x}\\) differs in classification from the original point \\(x\\); rather, it ensures that the entire \\(L_2\\) ball around \\(\\tilde{x}\\) is consistently classified as the adversarial class, confirming the robustness of the adversarial example within that region."}
{"question_id": "94974352b0e42eb1b459e4b85aea1ca6ddb9b713", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "Yes, the robust regions proposed in this paper explicitly include geometric perturbations that correspond to physical distortions commonly encountered in real-world scenarios, such as rotations and translations. These geometric perturbations are modeled as transformations parameterized by vectors within bounded hyperboxes, allowing the generation of adversarial examples that are provably robust to a range of such geometric changes. The method constructs connected input regions in the low-dimensional geometric parameter space, ensuring that for all parameter values within these regions, the transformed images are classified consistently as adversarial.\n\nThe paper demonstrates the effectiveness of this approach by generating large robust regions containing a vast number of concrete adversarial examples (e.g., approximately 10^599 for geometric perturbations), indicating that these regions are non-trivial and encompass a wide variety of physically plausible distortions. The robustness certification leverages DeepG, which overapproximates the set of images resulting from geometric transformations, and the method uses input space splitting to increase precision.\n\nFurthermore, the experimental evaluation shows that the method successfully finds robust adversarial regions for most tested cases, with the regions containing more than 10^64 concrete adversarial examples, and the approach outperforms baseline methods in generating such robust regions efficiently. This indicates that the robust regions are not only theoretically sound but also practically significant in covering realistic physical distortions encountered in real-world applications."}
{"question_id": "eb715a337474694a5b2fa3212f3936f2979ff998", "paper_id": "openreview/ICLR-2023-conf/kDEL91Dufpa", "generation": "Yes, different contrastive learning methods can be represented using Definition 3.2, which classifies methods into sample-contrastive and dimension-contrastive based on the criteria they minimize.\n\nDefinition 3.2 states:\n- A method is sample-contrastive if it minimizes the contrastive criterion \\( L_c = \\|K^T K - \\text{diag}(K^T K)\\|_F^2 \\).\n- A method is dimension-contrastive if it minimizes the non-contrastive criterion \\( L_{nc} = \\|K K^T - \\text{diag}(K K^T)\\|_F^2 \\).\n\nEvidence supporting this representation includes:\n\n1. **Sample-contrastive methods:**\n   - SimCLR-abs/sq, DCL-sq/abs, and Spectral Contrastive Loss are explicitly identified as sample-contrastive methods (Proposition 3.2).\n   - SimCLR and DCL, although originally formulated with cosine similarities, lead to embeddings where negative pairs have zero mean dot product, aligning with the goal of \\( L_c \\) (Proposition 3.1).\n   - The Spectral Contrastive Loss criterion can be written as:\n     \\[\n     L_{SCL} = -2 \\sum_{i=1}^N K_{\\cdot,i}^T K'_{\\cdot,i} + \\sum_{i \\neq j} (K_{\\cdot,i}^T K_{\\cdot,j})^2 = -2 \\sum_{i=1}^N K_{\\cdot,i}^T K'_{\\cdot,i} + \\|K^T K - \\text{diag}(K^T K)\\|_F^2,\n     \\]\n     which matches the sample-contrastive criterion \\( L_c \\) (Proofs section).\n\n2. **Dimension-contrastive methods:**\n   - Barlow Twins, VICReg, and TCR are identified as dimension-contrastive methods (Proposition 3.2).\n   - VICReg\u2019s covariance criterion \\( c(K) = \\|K K^T - \\text{diag}(K K^T)\\|_F^2 = L_{nc} \\) directly corresponds to the dimension-contrastive criterion (Proofs section).\n   - Barlow Twins\u2019 loss can be rewritten to include the term \\( \\|K K^T - \\text{diag}(K K^T)\\|_F^2 \\), confirming it as dimension-contrastive (Proofs section).\n\n3. **Equivalence and relationship:**\n   - Theorem 3.3 establishes that the sample-contrastive criterion \\( L_c \\) and the dimension-contrastive criterion \\( L_{nc} \\) are equivalent up to row and column normalization of the embedding matrix \\( K \\):\n     \\[\n     L_{nc} + \\sum_{j=1}^M \\|K_{j,\\cdot}\\|_4^4 = L_c + \\sum_{i=1}^N \\|K_{\\cdot,i}\\|_4^4,\n     \\]\n     where \\( N \\) is batch size and \\( M \\) is embedding dimension.\n   - Corollary 3.4.1 further bounds the difference between \\( L_c \\) and \\( L_{nc} \\) when embeddings or dimensions are \\( L_2 \\)-normalized, showing that these criteria cannot be arbitrarily far apart in practice.\n\n4. **Rewriting VICReg as sample-contrastive:**\n   - VICReg\u2019s loss can be rewritten to explicitly include \\( L_c \\) plus a regularization term on norms, further illustrating the close relationship between sample-contrastive and dimension-contrastive methods (Equivalence section).\n\nIn summary, the classification of contrastive learning methods into sample-contrastive and dimension-contrastive categories using Definition 3.2 is supported by explicit formulations and proofs showing that well-known methods such as SimCLR, DCL, Spectral Contrastive Loss, Barlow Twins, and VICReg can be expressed in terms of these criteria. This unifying framework highlights the underlying equivalence and close relationship between different contrastive learning approaches."}
{"question_id": "4006c4e45f822e15f9ccc27df354f3bcd8298509", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "Using alignment for video preprocessing instead of a fixed cropping window introduces temporal inconsistency due to the single-frame face alignment technique. To mitigate this, a low-pass Gaussian filter is applied to smooth the estimated facial keypoints before image warping. Additionally, the alignment process involves realigning images in the depth direction to force all nose keypoints to the same point in the world coordinate system defined by a parametric face model (3DMM), which further processes the video clips. Camera poses for each video frame are estimated using deep face reconstruction, and smoothing is again applied to reduce temporal inconsistency. Despite these measures, the automated preprocessing pipeline can still produce noise and failure cases, necessitating an extra verification step to remove noisy video clips. This verification uses facial feature similarity thresholds to discard frames or clips with low-quality alignment. Thus, alignment improves the spatial consistency of facial features across frames but requires additional smoothing and verification to address temporal inconsistencies and noise, which would not be issues with a fixed cropping window."}
{"question_id": "f87855d105235224a2584b0e0716b794ef647705", "paper_id": "openreview/ICLR-2023-conf/o3yygm3lnzS", "generation": "Yes, the authors took specific steps to address jittering caused by alignment in the processed videos. They first detected landmarks for each frame using a single-frame face alignment method, which inherently introduces temporal inconsistency. To mitigate this, they applied a low-pass Gaussian filter to smooth the estimated keypoints before warping the images. Additionally, after estimating camera poses for each video frame using deep face reconstruction, they again applied the low-pass Gaussian filter to smooth the camera pose results. These smoothing steps were designed to reduce temporal inconsistency and thus alleviate jittering in the aligned video frames."}
{"question_id": "df9756e054d7db2937ebc51e1ed8477345e57387", "paper_id": "nlpeer/COLING2020/341", "generation": "The logical atom \"nsubj:noun-verb-obj:noun\" represents a double-link property extracted from Universal Dependencies treebanks, specifically the relative frequency of a syntactic construction involving one head and two dependents. In this construction, the head is a verb (VERB), and it has two dependents: a nominal subject (nsubj) and a nominal object (obj), both of which are nouns (NOUN). Formally, it corresponds to the relative frequency of occurrences of the pattern where a VERB head governs a NOUN dependent with the relation nsubj and another NOUN dependent with the relation obj, among all possible permutations of these three elements in the language \u2113."}
{"question_id": "ff4b45b2af4e13f58512fc1783fc12dd129feb6f", "paper_id": "egu/esurf/11-917-2023", "generation": "Yes, the model incorporates wave erosion through the characterization of the nearshore wave environment, which allows for a reasonable approximation of hydrodynamics at the model grid resolution. The hydrodynamic boundary conditions include mean significant wave height and direction extracted from a global wave hindcast, as well as storm wave parameters added at a 10% yearly occurrence rate. Additionally, sediment weathering in the model accounts for mechanical and biological erosion under marine conditions with a maximum weathering rate of 100 m Myr\u207b\u00b9. This marine weathering rate implicitly includes wave erosion effects within the model framework."}
{"question_id": "70ee52925bfa4ed21c8798964ec74fcdd0f9845f", "paper_id": "egu/esurf/11-917-2023", "generation": "No, the subaerial erosion rate is not equivalent to the maximum weathering rate. The maximum subaerial weathering rate is a user-defined parameter that approximates the rate of weathering under subaerial exposure, influenced by environmental factors such as precipitation and groundwater chemistry. In the Dionisos model, the dissolution rate is incorporated within the maximum subaerial erosion rate, but the overall subaerial erosion process also depends on other factors and is subject to model caveats. For example, in the Lembetabe case, a lower maximum subaerial weathering rate of 100 m Myr\u207b\u00b9 is used instead of the commonly cited 250 m Myr\u207b\u00b9 to avoid overestimation of erosion. Thus, while related, the subaerial erosion rate is a modeled outcome influenced by the maximum weathering rate and other processes, and they are not strictly equivalent."}
{"question_id": "1fd3a7fbc973d1042fee519c269028acdbb1ccec", "paper_id": "nlpeer/COLING2020/341", "generation": "The antecedent in the double-link property definition (#6) is included in the sum because the property value represents the relative frequency of a construction involving one head and two dependents, considering all possible linear orders of these elements. Specifically, the sum accounts for all six possible permutations of the head and its two dependents (e.g., VERB NOUN NOUN with nsubj and obj relations in any order). This comprehensive summation ensures that the relative frequency reflects the total occurrence of the construction regardless of word order variations.\n\nUsing a single fixed order such as \"verb-nsubj:noun-obj:noun\" alone would not capture the full distribution of the construction's occurrences across different linearizations. Therefore, the valuation function sums over all permutations to accurately represent the empirical frequency of the construction in the language, rather than restricting to a single order or implication form like \"verb-nsubj:noun-obj:noun \u21d2 nsubj:noun-verb-obj:noun.\""}
{"question_id": "ce87b952cfde98f1de69d4c860537a4d3989c67a", "paper_id": "nlpeer/F1000-22/11-9", "generation": "The study analyzed a total of 30,229 high-quality SARS-CoV-2 genome sequences obtained from the GISAID database, with collection dates ranging from January 1, 2020, to March 21, 2021. The sequences were filtered to include only complete genomes with high coverage (<1% Ns and <0.05% unique amino acid mutations) and excluded low coverage sequences. The geographic distribution of these sequences along with the date range is summarized in an extended data table.\n\nGenetic nomenclature was based on the reference strain NC_045512.2 from the NCBI database. Eleven different coding sequences were extracted according to their genomic positions in this reference strain. The study identified 231 nonsynonymous mutations across these sequences, with the top 10 highest frequency nonsynonymous mutations affecting nine amino acid residues in various proteins, including ORF1a nsp5 P108S, ORF1b nsp12 P323L and A423V, S protein N501Y and D614G, ORF3a Q57H, and N protein P151L, R203K, and G204R.\n\nRegarding variants of concern (VOCs), the frequency percentages of the top 10 nonsynonymous mutations were obtained from COVID CG and are associated with primary lineages of past and present VOCs. Notably, five nonsynonymous mutations\u2014ORF1b nsp12 P323L, S protein N501Y, S protein D614G, N protein R203K, and N protein G204R\u2014are part of the defining mutations in the alpha variant and are also found in the omicron variant. The mutational profile is dynamic, reflecting rapid changes in the SARS-CoV-2 genome over time.\n\nThus, the dataset encompasses a broad temporal and geographic range, with detailed genetic characterization linked to known VOCs, facilitating the study of mutation effects on viral proteins."}
{"question_id": "77d9dbb3a3af5156b369e66924d4bcf14f794893", "paper_id": "openreview/ICLR-2022-conf/UMfhoMtIaP5", "generation": "The criteria used to evaluate \"provability\" in the paper is based on neural network certification methods that employ convex relaxations to prove that every point within a convex input region \\( I \\subset \\mathbb{R}^{n_0} \\) is classified as the target label \\( y_t \\) by the network \\( f \\). Specifically, for each label \\( y \\neq y_t \\), a linear certification function \\( L_y : \\mathbb{R}^{n_0} \\to \\mathbb{R} \\) is computed such that for all \\( x \\in I \\), \\( L_y(x) \\leq [f(x)]_{y_t} - [f(x)]_y \\). The global lower bound \\( \\min_{x \\in I} L_y(x) \\) is then obtained. If this lower bound is positive for all \\( y \\neq y_t \\), robustness is proven, meaning the entire region \\( I \\) is guaranteed to be adversarial with respect to the target label.\n\nThe certification error is defined as the minimum value of the certification objective \\( L(x) \\), which is the function \\( L_y(x) \\) with the smallest minimum over \\( I \\). A non-negative certification error indicates that the region is provably robust.\n\nAlgorithm 1 in the paper uses a verifier \\( V \\) that returns the certification error \\( e_{i-1} \\) for a candidate region \\( U_{i-1} \\). The algorithm returns a region \\( U \\) as provably robust only if the verifier returns a non-negative certification error for that region. Thus, the provability criterion is that the certification error computed by the verifier is non-negative, certifying that all points in the region are adversarial with respect to the target label."}
{"question_id": "fc938634e35cc53e7b6cb50564929eabb0fc7afe", "paper_id": "egu/esurf/11-917-2023", "generation": "\"Production versus depth curves\" refer to the non-linear relationship between the growth rate of carbonate producers (such as different coral species) and water depth. These curves represent how carbonate production rates vary as a function of depth, reflecting environmental controls like water temperature, turbidity, wave energy, and water depth itself. In the Dionisos forward stratigraphic model, each carbonate sediment class is assigned a user-defined production-versus-depth curve that quantifies the rate of carbonate sediment production (measured in meters per million years) at different water depths. These curves are used to simulate carbonate growth patterns realistically, with specific growth rate values derived from literature for various coral facies and carbonate sediments."}
{"question_id": "e7b7d480aa1076d06dccb8bcb2b7f2b1fd0f9c87", "paper_id": "egu/esurf/11-917-2023", "generation": "The model presented in the study is spatially limited and was specifically applied to a fringing reef system off the southwestern coast of Madagascar. While it provides valuable insights into the sensitivity of the Last Interglacial (LIG) geological record and ice sheet melt scenarios, its direct application to other geographical areas, especially those with more complex glacial isostatic adjustment (GIA) and tectonic settings, requires caution. The study suggests that further investigation is warranted for more GIA- and tectonically diverse LIG coral-dominated coastlines, indicating that the model may need adaptation or additional testing in different regions. Therefore, the model cannot be considered easily or universally applicable without modification or further validation in other geographic contexts."}
{"question_id": "42b9bcc5c85c3d4087d4f57791f953fa732fc625", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The scenarios used in the paper were selected based on their ability to induce safety-critical conditions that cause collisions across multiple autonomous driving (AD) algorithms, thereby ensuring high transferability and risk levels. Specifically, after generating a large set of testing scenarios using four different safety-critical scenario generation algorithms applied to eight template scenarios with diverse driving routes, the authors tested all scenarios on four AD algorithms. They then selected only those scenarios that caused collisions in at least two of the AD algorithms. This selection process improved the collision rate of the testing scenarios by focusing on those that are more challenging and effective for safety evaluation. The selected scenarios thus have high transferability across different AD algorithms and represent high-risk situations, which enhances both the effectiveness and efficiency of AD evaluation."}
{"question_id": "aab380aaa605fffff2d765c9cb058cfc03ee1729", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The rationale for only keeping scenarios that cause collisions with at least two algorithms is to ensure that the selected testing scenarios have high transferability across different autonomous driving (AD) algorithms and represent high risk levels. This selection criterion improves both the effectiveness and efficiency of AD evaluation by focusing on scenarios that are consistently safety-critical for multiple algorithms, thereby providing a more robust and generalizable assessment of AD safety performance."}
{"question_id": "eeba4b725b3f8ad526cddce373ff444a591594c9", "paper_id": "nlpeer/ARR-22/df4051328ee57291cf600f10ef67af6872fcef0deb3ff7323b89142b68beb16ea1fbe09c44be8ccca24fe30d67a1e6dbfb715a77384c30fbeb37c362c25c743c", "generation": "The difference in performance between the DICTA test set and the new test set, particularly the discrepancy between character accuracy (CHA) and word accuracy (WOR) scores, can be explained by several factors:\n\n1. **Dataset Diversity and Size**: The DICTA test set is relatively small and non-diverse, consisting of 22 documents all originating from Hebrew Wikipedia articles. In contrast, the new test set is larger (approximately 3.5 times the size of DICTA) and more diverse, including high-quality Wikipedia articles, edited news stories, and user-generated blog posts from eleven different sources. This increased diversity likely introduces more linguistic variability and complexity, which can affect word-level accuracy more than character-level accuracy.\n\n2. **Handling of Out-of-Vocabulary (OOV) Words**: The new test set contains a higher proportion of OOV words, including foreign names and terms not present in training dictionaries or corpora. While character-level models like NAKDIMON can capture pronunciation patterns from similar words, the presence of unseen clitic combinations and proper names can reduce word-level accuracy. Other systems use morphological analyzers to segment and handle such cases, which may not be as effective in the new test set.\n\n3. **Model Architecture and Decoding Strategy**: NAKDIMON uses a greedy decoding approach without lexical or morphological validation, which can lead to errors in word-level dotting, especially for fused preposition+determiner characters and clitics. This limitation is more pronounced in the new test set due to its linguistic variety, impacting WOR scores more than CHA scores.\n\n4. **Error Types and Vocalization-Agnostic Dotting**: A significant portion of word-level errors in the new test set are attributable to vocalization-agnostic dotting, where errors do not affect pronunciation. This phenomenon leads to a larger gap between CHA and WOR metrics, as character-level accuracy remains high while word-level accuracy suffers from subtle dotting inconsistencies.\n\nIn summary, the larger size, greater linguistic diversity, higher incidence of OOV words, and the model's decoding approach contribute to the observed differences in CHA and WOR scores between the DICTA test set and the new test set."}
{"question_id": "cbb83b653ecc965d0b930f4f016e4ff93c485696", "paper_id": "openreview/ICLR-2023-conf/HnSceSzlfrY", "generation": "RPM-Random performs poorly in pure cooperation scenarios because it samples policies without using ranks, leading to unstable performance and large variance, as shown in Pure Coordination (PC) 1-3 results. The absence of ranks causes frequent sampling of policies with high count values in substrates with skewed return-checkpoint count distributions, reducing the diversity of training data. Pure Coordination scenarios typically have such skewed distributions with many suboptimal policies, so random sampling without ranks fails to maintain stable and effective training. In contrast, in Prisoners\u2019 Dilemma (PD) scenarios, RPM-Random achieves relatively better performance because the return-checkpoint count distribution is more uniform, allowing random sampling to maintain more diverse and effective policies. Thus, the key reason for RPM-Random's poor performance in pure cooperation compared to Prisoners\u2019 Dilemma is the lack of rank-based sampling, which is critical for maintaining policy diversity and stable training in environments with skewed policy performance distributions."}
{"question_id": "7c6f77a64467e8275e381a36386d66650b13e832", "paper_id": "nlpeer/F1000-22/11-404", "generation": "The term \"AES\" (aging effect of smiling) specifically refers to the phenomenon that smiling faces are estimated to be older than neutral faces in direct age estimations. It does not refer to the retrospective estimation where a smiling face group is estimated to be younger; rather, these two effects depend on the method of age estimation and are distinct."}
{"question_id": "9dbf9a9e3f0cc960065255b48616ad3b79759600", "paper_id": "openreview/ICLR-2023-conf/tyZ1ChGZIKO", "generation": "The MDSF module employs multiple branches with varied filter sizes to provide different local receptive fields for frequency decoupling. Specifically, the input feature map is split along the channel dimension into multiple groups, and each group is processed by a decoupler with a distinct kernel size. This multi-branch design allows each branch to generate frequency components using filters of different kernel sizes, thereby capturing diverse frequency information. The kernel sizes of these decouplers correspond to the filter sizes assigned to each branch, and the features are equally split among the branches. The number of groups (and thus branches) determines the diversity of filters, with each group having a filter of size \\(k \\times k\\). The exact kernel sizes for the different decouplers are chosen to vary across branches to enrich frequency representations, as expressed in the formulation:\n\n\\[\n\\hat{X} = [ M_1(D_1(X_1)), ..., M_m(D_m(X_m)) ]\n\\]\n\nwhere \\(D_m\\) denotes the decoupler with a specific kernel size applied to the split feature \\(X_m\\). The selection of the number of groups (and implicitly the kernel sizes) balances complexity and feature diversity, with eight groups chosen as optimal in experiments."}
{"question_id": "5b14dc7213f8e7181d9bf848cef4fb79a7b1ad10", "paper_id": "openreview/NeurIPS-2022-dabt/dwi57JI_-K", "generation": "The performance of scenario generation algorithms is evaluated using the following metrics:\n\n1. Collision Rate (CR): Measures the frequency of collisions occurring in the generated scenarios.\n\n2. Overall Score (OS): Reflects the safety-critical scenario generation capability, with lower OS indicating more safety-critical scenarios.\n\n3. Selection Rate (SR): Evaluates the transferability of generated scenarios across different autonomous driving (AD) algorithms, indicating the proportion of scenarios that cause collisions in multiple AD agents.\n\nThese metrics are calculated both before and after scenario selection to assess the effectiveness of the generation algorithms in producing safety-critical and transferable testing scenarios."}
{"question_id": "7e53c05206cc77e9d6e3b28338d1c85336543244", "paper_id": "egu/esd/14-81-2023", "generation": "The authors determine the accuracy of the CMIP6 climate models in simulating precipitation processes by including a large ensemble of 13 CMIP6 models with multiple members per model to capture a wide range of physically plausible climate responses and internal variability. They use long preindustrial control (piControl) simulations, totaling 4500 years of unforced data from 10 models, to sample a representative distribution of unforced trends. This approach increases confidence in assessing whether observed forced trends lie outside the likely range of unforced variability. Additionally, they compare model outputs with multiple observational datasets (HadEX3, GHCNDEX, and GPCC) derived from station observations, applying coverage masks to ensure comparable spatial coverage. The authors acknowledge structural differences between model grid cell averages and gridded observational data, particularly for extreme precipitation indices, but note that trend biases due to these differences have been shown to be negligible. This justifies the comparison between models and observations for evaluating model accuracy in simulating precipitation changes."}
{"question_id": "6d0c861407de2db08718ca55a383b59284a8e223", "paper_id": "nlpeer/PeerRead-CONLL2016/129", "generation": "The number of selected in-domain sentences for each machine translation system is empirically determined based on experimental results on a separate validation set. Specifically, the development set (dev) is used as the in-domain data for training the CNN models and for tuning meta-parameters, and the selection of the number of in-domain sentences is based on performance on held-out data, which serves as the validation set."}
{"question_id": "9373d254f956bcbffe53a9ba10531f5102ecdb83", "paper_id": "openreview/ICLR-2023-conf/dSYoPjM5J_W", "generation": "The authors justify the claim that gradient-based attacks are responsible for the distribution shift between training and testing data in adversarial attacks by analyzing the location and impact of perturbations generated by these methods. They observe that gradient-based attacks predominantly modify the local structure around training nodes rather than uniformly perturbing the entire graph. This selective perturbation increases the distribution shift between the training distribution \\( p_{\\text{train}}(\\tilde{x} | y) \\) and the testing distribution \\( p_{\\text{test}}(\\tilde{x} | y) \\), as formalized by the Kullback-Leibler divergence between these conditional distributions.\n\nTheoretical support is provided by Theorem 4.1, which states that attacking the smaller training set by inserting heterophilous edges causes a larger distribution shift than attacking the testing set. Empirically, methods like MetaAttack adaptively focus perturbations on the training nodes when the training set is small, thereby maximizing distribution shift and attack effectiveness. This adaptivity arises from the meta-gradient computation, which incorporates the training process and allows the attack to identify structural modifications that significantly increase distribution shift.\n\nFurthermore, the authors contrast gradient-based methods with heuristic attacks like DICE, which perturb training and testing nodes synchronously and uniformly, resulting in a smaller distribution shift and weaker attack performance. Experiments show that perturbing only the training nodes (the smaller subset) is more destructive than perturbing testing nodes or both sets simultaneously, confirming that the gradient-based attack's focus on training nodes drives the observed distribution shift.\n\nIn summary, the gradient-based attack's destructive power stems from its ability to selectively perturb the training node subgraph, thereby increasing the distribution shift between training and testing data distributions, which biases the learned classifier and degrades performance."}
{"question_id": "6610ad96e462f49d6d8f20fee0cdc6dd8a70175a", "paper_id": "egu/esd/14-81-2023", "generation": "If ridge regression (RR)-based fingerprint construction is not applied, the detection method is more prone to overfitting and results in unphysical coefficient fingerprints characterized by high positive coefficients offset by adjacent negative coefficients. This leads to increased variance in forced response estimates and a lower signal-to-noise ratio (SNR). Specifically, less regularised RR models with minimal cross-validated mean squared error (\u03bb_min) exhibit lower SNR and later time of emergence of forced changes due to overfitting on training data, despite slightly larger forced response estimate trends. In contrast, the regularisation in RR optimises the regression coefficient pattern for a high SNR across models, improving generalisability and robustness of detection. Therefore, without RR-based fingerprint construction and its regularisation, the detection performance deteriorates, resulting in weaker and less reliable forced response signals."}
{"question_id": "9be6e9b8ef3f3db6f29bafd74243978f78f2f657", "paper_id": "egu/esurf/11-917-2023", "generation": "The phrase \"actual exposed MIS 5e facies are lacking\" refers to the scarcity or absence of preserved reef facies from the Last Interglacial (LIG, Marine Isotope Stage 5e) period that remain exposed above modern sea level in the geological record. This scarcity is particularly evident under scenarios involving two-peak sea level fluctuations during the LIG, where intermittent subaerial exposure of freshly deposited reef material leads to significant erosion and reduces the robustness of the LIG reef record. Consequently, such relative sea level fluctuations are difficult to observe or confirm in the fossil record because the reef facies deposited during the LIG are either eroded away or not preserved in a continuous, exposed form."}
{"question_id": "d07dca8f8e126c43dacdaf145ec4103ef25400f5", "paper_id": "nlpeer/PeerRead-ACL2017/561", "generation": "Not having access to pre-trained embeddings would significantly reduce the performance of the method. The method relies on pre-trained neural language models (LMs) trained on large unlabeled corpora to generate context-sensitive LM embeddings, which are then incorporated into the supervised sequence tagging model. These LM embeddings encode semantic and syntactic roles of words in context, providing substantial improvements over baseline models that only use labeled data.\n\nExperiments show that including LM embeddings increases F1 scores by more than 1% absolute in named entity recognition (NER) and chunking tasks compared to baselines without LM embeddings. Without pre-trained embeddings, the model would lack this rich contextual information learned from large unlabeled data, resulting in lower performance. For example, removing the task-specific sequence RNN and relying solely on LM embeddings yields a much lower F1 score (88.17), well below the baseline, indicating that the RNN trained on labeled data encodes essential information not captured by LM embeddings alone.\n\nFurthermore, the LM embeddings are crucial for improving performance even when the labeled training data is large, and their benefit is especially pronounced when labeled data is scarce. Therefore, without pre-trained embeddings, the method would lose its key advantage of leveraging large-scale unlabeled data to enhance sequence tagging, leading to substantially degraded performance."}
{"question_id": "2f75586071f2de4ab14810c7f2bd7f7b4e143fb6", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "Popular meta-learning frameworks like MAML and Probabilistic MAML are not considered in the experiments because applying optimization-based meta-learning methods such as MAML to sequential latent variable models (SLVMs) like DKF and GRU-res encountered challenges related to stability and convergence. These difficulties, including vanishing gradient issues over the complex computation graph inherent in SLVMs, make extensions of MAML to these models non-trivial and problematic."}
{"question_id": "f2e744ebd60bf15d94cd1b9a5cdc7db9f0c4ad93", "paper_id": "openreview/ICLR-2023-conf/ZrEbzL9eQ3W", "generation": "The playing strength of agents is evaluated by fixing the number of Monte Carlo Tree Search (MCTS) simulations per move, i.e., the number of MCTS steps, rather than by imposing time limits. Specifically, all agents use the same fixed number of MCTS steps per move at test time, equal to the number used during training (300 MCTS steps). This approach allows tailoring inference-time compute by scaling the MCTS search size inversely with forward-pass compute costs, but the evaluation itself is based on a fixed MCTS iteration count rather than a time constraint."}
{"question_id": "a8d6ed01ff1866040e47a7082ff97ea95a6edd03", "paper_id": "egu/esurf/11-917-2023", "generation": "Yes, there is a scientific rationale for the Greenland Ice Sheet (GrIS) to begin melting before the Antarctic Ice Sheet (AIS) beyond merely creating two distinct peaks in a model. This timing difference reflects possible discrepancies in the stability of the GrIS and AIS and variable hemisphere-specific climate fluctuations. The scenario termed \"G2A5\" in the study separates the contributions of GrIS and AIS melt, with GrIS melting first and contributing 2 m to sea level rise over 126\u2013124 ka, followed by AIS melting later at around 118 ka, contributing 5 m. This staggered melting pattern is consistent with interpretations of fossil reef sequences from Western Australia and is supported by the understanding that climate fluctuations can differ between hemispheres, affecting the timing and magnitude of ice sheet responses."}
{"question_id": "83065c1670e7bbe6476efd1959f21480a4f3bf69", "paper_id": "nlpeer/ARR-22/eeda0f4ff6c7aef2647481ec15f8ade5a9b32ec32b643e6918a0b8d0ffa1ba44a3f2b9171ba4ba44d5c0faf3199e3bc5f0f740ee31cf48cb966493a96f35ca71", "generation": "In this work, the distinction between \"skill,\" \"knowledge,\" and \"attitude\" is defined according to the ESCO taxonomy as follows:\n\n- **Knowledge** is the outcome of assimilating information through learning and constitutes the body of facts, principles, theories, and practices related to a field of work or study. It is considered a knowledge component and typically corresponds to hard skills (e.g., knowing the Python programming language).\n\n- **Skill** refers to the ability to apply knowledge and use know-how to complete tasks and solve problems. It is the application of knowledge components to specific tasks and is considered a skill component.\n\n- **Attitude** corresponds to soft skills and is regarded as part of skill components. It involves the ability to use knowledge, skills, and personal, social, and/or methodological abilities in work or study situations. Attitudes are annotated as skills in this work.\n\nThus, hard skills are usually equated with knowledge components, while soft skills are equated with attitudes, which are a subset of skill components. The work prioritizes annotating skills over knowledge and attitudes, with attitudes being treated as skills when they do not contain embedded skill or knowledge components."}
{"question_id": "de2dc4d1f8b898e5b34a256294729fe7b46f6fda", "paper_id": "openreview/ICLR-2023-conf/ZrEbzL9eQ3W", "generation": "The board games studied use the following win conditions:\n\n- Connect Four: Players win by connecting four of their tokens in a line on a vertical board.\n- Pentago: Players win by connecting a line of five tokens, with each turn ending with a rotation of one of the four quadrants of the board."}
{"question_id": "1818a27b73310c09fb736d5e044187417bda0dbc", "paper_id": "nlpeer/ARR-22/eeda0f4ff6c7aef2647481ec15f8ade5a9b32ec32b643e6918a0b8d0ffa1ba44a3f2b9171ba4ba44d5c0faf3199e3bc5f0f740ee31cf48cb966493a96f35ca71", "generation": "The dataset annotations were performed by domain experts, including a hired linguist. Specifically, the annotation process involved three annotators for the initial rounds, and subsequently, one of the annotators, identified as a hired linguist, annotated job postings in larger batches. This indicates that the annotation team combined expertise in linguistics with domain knowledge relevant to the job postings."}
{"question_id": "d096c58eea777208cfd4ba272dac018b8a808d6c", "paper_id": "openreview/NeurIPS-2022-dabt/TscdNx8udf5", "generation": "The simulation environments were selected to bridge the gap between deep reinforcement learning research and industrial manufacturing by modeling real-world factories with high fidelity. The paper introduces five manufacturing simulation environments\u2014beer fermentation, atropine production, penicillin manufacturing, monoclonal antibodies production, and a continuous stirred tank simulation\u2014that cover a wide range of manufacturing processes. These environments allow testing of the latest reinforcement learning advances in controlled settings without safety concerns. They are built on published dynamic models validated and parameterized with industrial data to align with real-world factories. The environments are designed to facilitate research by providing standardized, open-source biochemical process control environments that are otherwise difficult to develop due to the complexity of underlying mathematical models. The selection aims to enable researchers to develop and benchmark reinforcement learning algorithms for manufacturing process control, addressing challenges such as partial observability, stochasticity, and safety constraints inherent in real industrial processes."}
{"question_id": "b16ae6d142599eafe257ac83cdf206be914a7a7e", "paper_id": "openreview/ICLR-2023-conf/tyZ1ChGZIKO", "generation": "The Multi-branch Dynamic Selective Frequency module (MDSF) and the Multi-branch Compact Selective Frequency module (MCSF) are concatenated in Figure 1 to integrate their complementary functionalities within the SFNet architecture. MDSF dynamically decomposes feature maps into different frequency components using learnable filters and applies channel-wise attention to select and emphasize the most informative frequency parts locally. In contrast, MCSF enlarges the receptive field efficiently by employing global and window-based average pooling to perform frequency selection over larger spatial contexts without convolution layers, thus maintaining low complexity. Concatenating these modules allows SFNet to leverage both dynamic, local frequency decomposition and recalibration (MDSF) and efficient, large receptive field frequency selection (MCSF) simultaneously, enhancing the network\u2019s ability to recover image details across multiple scales and frequency bands. This combined approach improves image restoration performance by effectively selecting and recalibrating frequency components at different receptive fields."}
{"question_id": "836969164a688341782ffa72b87f1348ba1ee4ac", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "A total of 27 3D models were used in the pre-collection stage of the data collection process. This comprised 3 replicas each for 9 distinct types of pathway surfaces identified as relevant for the study."}
{"question_id": "aae1c73c7b0de5fb88e34c245782e2ecb4dcb24d", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "The dataset is not balanced across different classes. Specifically, the classes of up-steps and down-steps have slightly higher frequency than the other classes, which is a typical scenario in most of the urban areas sampled. This minor class imbalance was addressed during model training by applying a random under-sampling technique to remove some samples from the majority classes, mainly from the up-steps and down-steps classes."}
{"question_id": "f5fe5047a045ce5a97066fd72458d8951c846342", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "A total of 27 3D printed replicas were used in the survey. These consisted of 3 replicas each for 9 distinct types of surface discontinuities identified around the targeted urban areas."}
{"question_id": "3546db32608ccae0b45d96e051b10a8967437d6f", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "Only one single person was employed to wear the sensor and capture all the image samples for the survey."}
{"question_id": "e232c66d9986ff1ac6f532437bd94f0b71a44ca5", "paper_id": "openreview/NeurIPS-2022-dabt/kQUOIyPg-ux", "generation": "The dataset was collected from ten different locations within urban areas of Klang Valley, Malaysia, selected based on judgmental sampling to target areas with a high frequency of surface discontinuities relevant to blind and low vision (BLV) navigation. These locations include Damansara Perdana, Kota Damansara, Bandar Utama, Up-Town, Damansara Jaya, Bandar Sunway (PJS 7, PJS 9, PJS 10), Brickfields, and Pudu. While these sites were chosen to capture a range of surface discontinuities informed by consultations with BLV volunteers and local urban conditions, the dataset is limited to Malaysian urban environments and may not encompass the full diversity of surface conditions found in other regions or countries. The authors acknowledge that expanding the dataset to include more diverse regions beyond these ten locations is necessary to better represent a wider variety of urban surface conditions. Therefore, ten locations provide a focused but not fully comprehensive representation of urban surface variety."}
{"question_id": "587b947b50e65e3caa8174633245ab39edbdb0f0", "paper_id": "egu/esurf/11-33-2023", "generation": "The authors did not conduct analyses using detrended data to isolate the effect of the temperature increase. Their transient model simulations analyzed the response of ground temperatures and ice tables to climate warming from 1951 to 2019, incorporating observed increases in air temperature over this period. The simulations evaluated the effects of different ground stratigraphies and drainage conditions on permafrost degradation and warming rates but did not mention the use of detrended data or any method to remove the temperature trend to isolate warming effects separately."}
{"question_id": "6c9381de277251ad2ce40cd39b94a872cbc4126e", "paper_id": "egu/esd/14-81-2023", "generation": "The authors do not base their conclusion about the accuracy of CMIP6 climate models in simulating precipitation-relevant processes on the agreement between observed data and model predictions in terms of residual variability. Instead, they focus on the detection of forced changes in mean and extreme precipitation by constructing high signal-to-noise ratio fingerprints using regularised linear regression, which reduces the influence of internal variability and structural model error. They find that forced changes in precipitation are unequivocally detected in observations, but the magnitude of these forced changes differs among observational datasets. The relative trend fluctuations over time are consistent across datasets, increasing confidence in the robustness of the detection method. However, internal variability and short observational record lengths limit the ability to conclusively determine whether observed changes are weaker or stronger than model simulations suggest. The authors highlight that differences in spatial coverage and data processing among observational datasets are the main sources of uncertainty in estimating observed forced trends, rather than residual variability agreement per se."}
{"question_id": "e7cb5933a3df86f543cb36cb77b3f41cd7ad4021", "paper_id": "nlpeer/COLING2020/1367", "generation": "A rule-based approach, such as the use of finite-state transducers (FSTs) for verb conjugation in Indigenous languages, has the advantage of not requiring large corpora to produce accurate models. This is particularly important because most Indigenous languages lack sufficiently large textual or speech data to train statistical or neural methods effectively. Consequently, rule-based methods can reliably model complex morphological phenomena in these languages despite limited data availability, complementing rather than replacing traditional language learning experiences within communities."}
{"question_id": "09c36735b520089d4936e6966157cb10f8f1ed0e", "paper_id": "openreview/ICLR-2022-conf/iulEMLYh1uR", "generation": "Yes, when scaling the depth or width of the Vision Transformer (ViT) model, all other hyperparameters are kept fixed based on the default values given by the referenced papers."}
{"question_id": "064bd1dff89282732ddcf6c71a98975792d8b3d4", "paper_id": "openreview/ICLR-2023-conf/6xXtM8bFFJ", "generation": "The two-time-scale algorithm, characterized by separate step sizes \u03b1 and \u03b2 for the variables x and y respectively, can be extended to the mini-batch setting by incorporating mini-batches of size \\( b \\geq 1 \\) into the update rules. This extension is formalized in Algorithm 2, which generalizes the simultaneous SGDA-RR (simSGDA-RR) and alternating SGDA-RR (altSGDA-RR) methods.\n\nIn this mini-batch setting, the total number of components \\( n \\) is assumed to be an integer multiple of the mini-batch size \\( b \\), i.e., \\( n = bq \\) for some integer \\( q \\geq 1 \\). The algorithm proceeds in epochs, where each epoch consists of \\( q \\) iterations corresponding to the mini-batches.\n\nAt each iteration \\( t \\) within epoch \\( k \\), a mini-batch \\( B_k^t \\) of size \\( b \\) is formed by uniformly shuffling the indices without replacement. The updates for \\( x \\) and \\( y \\) are then performed using the averaged gradients over the mini-batch:\n\n- For simSGDA-RR (simultaneous update):\n\\[\nx_k^t = x_k^{t-1} - \\frac{\\alpha}{b} \\sum_{i \\in B_k^t} \\nabla_1 f_i(x_k^{t-1}; y_k^{t-1}),\n\\]\n\\[\ny_k^t = y_k^{t-1} + \\frac{\\beta}{b} \\sum_{i \\in B_k^t} \\nabla_2 f_i(x_k^{t-1}; y_k^{t-1}).\n\\]\n\n- For altSGDA-RR (alternating update):\n\\[\nx_k^t = x_k^{t-1} - \\frac{\\alpha}{b} \\sum_{i \\in B_k^t} \\nabla_1 f_i(x_k^{t-1}; y_k^{t-1}),\n\\]\n\\[\ny_k^t = y_k^{t-1} + \\frac{\\beta}{b} \\sum_{i \\in B_k^t} \\nabla_2 f_i(x_k^{t}; y_k^{t-1}).\n\\]\n\nAfter completing all \\( q \\) mini-batch updates in epoch \\( k \\), the algorithm sets the initial point for the next epoch as:\n\\[\n(x_{k+1}^0, y_{k+1}^0) = (x_k^q, y_k^q).\n\\]\n\nThis mini-batch extension preserves the two-time-scale nature by maintaining distinct step sizes \\( \\alpha \\) and \\( \\beta \\) for \\( x \\) and \\( y \\), respectively, and applies them to the averaged gradients over each mini-batch. The mini-batch sampling is performed without replacement within each epoch, ensuring that all mini-batches are mutually disjoint in that epoch.\n\nThe theoretical analysis and convergence guarantees for this mini-batch two-time-scale SGDA-RR are provided under appropriate assumptions, with step sizes chosen such that \\( \\alpha = \\beta / r \\) for some \\( r \\geq 14 \\kappa_2^2 \\), where \\( \\kappa_2 \\) is a condition number related to the problem structure. This extension allows the algorithm to leverage mini-batch gradients while retaining the benefits of two-time-scale updates in nonconvex minimax optimization."}
{"question_id": "27642536b6affc6438fc0e5a5b2ce6b2d5208309", "paper_id": "egu/esd/14-1261-2023", "generation": "The study applies to both nearshore (transitional and shallow water) and offshore (deep-water) environments, but with specific limitations. The numerical test cases focus on a limited range of the depth-relative dimensionless parameter kh from 0.5 to 2.0, which corresponds to transitional and shallow water wave conditions relevant to nearshore and surfzone dynamics. Deep-water conditions with kh > \u03c0 are acknowledged but omitted from the present study because, under such conditions, the mixing would completely sweep away the warmer water from the analyzed fluid domain within the selected time frame, making the case unsuitable for the current analysis.\n\nFurthermore, the modeling framework is designed to be applicable to both deep-water and shallow-water conditions, as indicated by references to previous work and the potential for modification to cover pseudo-random ocean waves in large periodic domains for offshore conditions. The fully nonlinear wavemaker model used allows for accurate representation of wave kinematics in both intermediate and deep waters, including nonlinear wave\u2013wave interactions and solitary wave propagation, which extend beyond weakly nonlinear approaches typically limited to nearshore environments.\n\nTherefore, while the primary numerical results and parameter ranges focus on nearshore and surfzone-relevant conditions, the methodology and modeling framework are applicable to offshore environments as well, with the possibility of extension to open-ocean hydrodynamics and random sea states."}
{"question_id": "add8f70fda4a981fbac7e3f41f938eeecd3ccd4d", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The first kind of rule in Section 4 is called \"Markovian\" because the calculation of the next state probability in the random walk depends only on the current state probability, without considering previously visited edges. This means that the random walk satisfies the Markov property, where the next step is determined solely by the present state, making the constraints dependent only on the current step's predicates and temporal relations between the query interval and each body interval."}
{"question_id": "7110b14e5ab532a6273415a059f6808204376ee6", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "The sequential neural process (SNP) setting is not included as a comparison because it is originally designed for supervised learning of a regression function over time rather than for forecasting. The work extends SNP to realize a meta-version of the sequential latent variable model (SLVM) formulation as a counterpart to be compared with the presented meta-SLVM, but the original SNP formulation does not directly address the forecasting task targeted in this study."}
{"question_id": "5c790742a803e76547d117cf4a77434d1737b5b1", "paper_id": "egu/esurf/11-849-2023", "generation": "Besides temperature and precipitation, several other factors could influence the distribution of glaciers in a given area:\n\n1. **Valley Geometry and Topography:** The shape and narrowing of valleys can induce ice elevation feedbacks, where glaciers thicken and extend as they advance into narrow valley sections, increasing accumulation area and promoting further growth. This effect is pronounced in certain valley geometries, such as the Elwha valley, and can significantly affect glacier extent.\n\n2. **Precipitation Type:** The form of precipitation (snow versus rain) affects glacier mass balance. Rain does not contribute to glacier accumulation, so the proportion of precipitation falling as snow versus rain influences glacier growth.\n\n3. **Cloudiness:** Variations in cloud cover modulate solar radiation reaching the glacier surface. Differences in cloudiness between valleys can alter local air temperatures and thus glacier melt rates. For example, less cloudiness on the leeward side of a mountain range can increase solar radiation and enhance melting.\n\n4. **Humidity and Lapse Rate:** Spatial patterns in humidity can influence glacier mass balance by affecting the lapse rate. Lower humidity and descending airflow on the leeward side can increase the lapse rate, potentially enhancing melt rates.\n\n5. **Melt Efficiency:** Differences in the efficiency of melting, potentially due to local climatic or topographic factors, can limit glacier growth. Even slight increases in melt efficiency can significantly reduce glacier extent.\n\n6. **Spatial Variability in Ablation Drivers:** Factors such as topographic shading and aspect-related melting introduce spatial variability in ablation, influencing glacier distribution beyond the effects of temperature and precipitation alone.\n\nThese factors interact with local topography and climate to produce complex spatial variability in glacier sensitivity and extent."}
{"question_id": "130985a6f0c94e81204c5a5014faa6017dc2a328", "paper_id": "egu/esd/14-1261-2023", "generation": "The problem under investigation pertains to genuinely nonlinear wave dynamics rather than being limited to a weakly nonlinear context. The study extends previous analyses based on weakly nonlinear theory by employing a fully nonlinear wavemaker model that admits higher-order nonlinearities without an upper limit. This fully nonlinear model captures amplitude dispersion, nonlinear wave\u2013wave interactions, and solitary wave propagation, which go beyond the applicability of weakly nonlinear approaches.\n\nComparisons between weakly nonlinear and fully nonlinear (higher-order) solutions reveal that discrepancies increase with wave steepness and wave height, particularly in subsurface and near-bottom velocity fields. The fully nonlinear model predicts enhanced subsurface streaming and more intensive mass transport near the bottom and surface layers, with velocity differences reaching up to 40% for higher wave heights. These nonlinear effects significantly influence wave-induced vertical mixing and the evolution of the temperature field, effects that are underestimated or neglected in weakly nonlinear models.\n\nTherefore, the investigation addresses strong nonlinearity in wave-induced vertical mixing, demonstrating that fully nonlinear modeling is necessary for accurate prediction and interpretation of wave-driven mass transport and mixing processes."}
{"question_id": "cf91a671c7d2248b716cf143ff64e032fed4681e", "paper_id": "nlpeer/ARR-22/27046f6633d116b03e48eb94976921b46a86a976ddd2199ef76c90820a95e0d85406daa5ebc9d7b12359157d20f3bd0025e1cbc2c126bb2506f5e83e93f435b5", "generation": "The layer normalization approximation incurs the most significant performance reduction among the approximation components in THE-X. Specifically, it causes an average performance drop of 1.08% on the GLUE benchmark tasks. In token-level tasks such as named entity recognition (NER), the layer norm approximation leads to a reduction in F1 score by approximately 1.9%, indicating a notable impact on tasks requiring detailed attention patterns. The approximation error from layer normalization tends to accumulate with deeper transformer layers, further affecting performance. Overall, while the layer norm approximation is critical for enabling homomorphic encryption compatibility, it remains the primary source of performance degradation across different task types in THE-X."}
{"question_id": "cfbd6962220a29ddfda999443b628e02ebd2d79b", "paper_id": "egu/esurf/11-849-2023", "generation": "The model and analysis presented do not explicitly consider the effect of slope orientation or aspect, such as the presence of more north-facing slopes in the Elwha area compared to the Quinault area, on solar radiation absorption and glacier distribution. The glacier flowline model calculates mass balance based on spatially variable precipitation and summer mean temperature, with temperature varying as a function of elevation and sea level temperature, but does not incorporate slope aspect or differential solar radiation due to orientation.\n\nHowever, the discussion acknowledges that cloudiness and precipitation gradients, influenced by orographic effects and valley geometry, affect glacier mass balance and extent. It is also noted that if the Elwha valley is less cloudy and less humid, it could experience higher local air temperatures and enhanced glacier melt due to increased solar radiation. The model assumes uniform precipitation type (all snow) and does not discriminate between different surface processes such as cloudiness or sublimation that could vary spatially.\n\nFurthermore, the conclusions suggest that differences in melt efficiency, which could be influenced by factors like solar radiation absorption related to slope orientation, might be significant in limiting glacier growth in the Elwha valley. This implies that spatial variability in melting, potentially related to solar radiation differences, could influence glacier extent, but this factor was not explicitly modeled.\n\nIn summary, while slope orientation and its effect on solar radiation absorption could influence glacier distribution, this effect was not directly incorporated or quantified in the model. The primary controls identified on glacier extent differences between the Elwha and Quinault valleys are precipitation gradients and valley geometry, with melt efficiency differences suggested as a possible additional factor for future investigation."}
{"question_id": "95d0f3eec5444caddab3df7e45aa31db81cabef8", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The operator \\( M_i,CM_i \\in \\{0,1\\}^{|E| \\times |E|} \\) related to step \\( i \\) under corresponding Markovian constraints \\( CM_i = \\{P_i, TR_{i,l+1}\\} \\) represents the adjacency matrix of the knowledge graph under these Markovian constraints. Specifically, for every pair of entities \\( e_x, e_y \\in E \\), the entry \\( (M_i,CM_i)_{x,y} \\) is defined as the maximum over all facts \\( F \\in F_{y,x} \\) of the product of filtering operators for the predicate \\( P_i \\) and the temporal relation \\( TR_{i,l+1} \\), i.e.,\n\n\\[\n(M_i,CM_i)_{x,y} = \\max_{F \\in F_{y,x}} f_{P_i}(F) \\cdot f_{TR_{i,l+1}}(F, (e_s, r, ?, I))\n\\]\n\nwhere \\( F_{y,x} \\) denotes the set of facts from entity \\( e_y \\) to entity \\( e_x \\). The entries are set to 1 if the constraints are satisfied and 0 otherwise. This operator enables the identification of all paths between any pair of entities that satisfy the Markovian constraints at step \\( i \\) during the constrained random walk process."}
{"question_id": "9505c56639fea265e46601d12575e9d9715b9e7a", "paper_id": "openreview/ICLR-2023-conf/7C9aRX2nBf2", "generation": "The variational approximation of the latent variable \\( c \\) given the query and support sets \\( D_s^j \\) and \\( x_q^{0:T} \\) is modeled as the posterior density\n\n\\[\nq_\\zeta(c \\mid D_s^j \\cup x_q^{0:T}),\n\\]\n\nwhich shares the same meta set-embedding model as the prior \\( p_\\zeta(c \\mid D_s^j) \\) but is conditioned on the augmented set formed by combining the support set \\( D_s^j \\) with the query series \\( x_q^{0:T} \\). This approximation is parameterized by a feed-forward embedding meta-model \\( \\zeta \\) that encodes the combined set to produce a Gaussian distribution\n\n\\[\nq_\\zeta(c \\mid D_s^j \\cup x_q^{0:T}) \\sim \\mathcal{N}(\\mu_c, \\sigma_c^2),\n\\]\n\nwhere the mean \\(\\mu_c\\) and variance \\(\\sigma_c^2\\) are obtained via separate linear layers applied to the aggregated embedding of the augmented set."}
{"question_id": "85afe8245083d99893657bc1eeadbcefa12dbf59", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "\"1 dB in PSNR\" refers to an improvement of one decibel in Peak Signal-to-Noise Ratio, which is a common metric used to measure the quality of image compression. An increase of 1 dB in PSNR indicates a higher fidelity or lower distortion in the compressed image compared to the original, reflecting better compression performance."}
{"question_id": "d250649b5c73368021f92321b3d59f4c1d3c762f", "paper_id": "openreview/ICLR-2022-conf/iulEMLYh1uR", "generation": "The msec/example values in Figures 1 and 2 were computed using 64 TPU-V3 hardware."}
{"question_id": "317ee5566b85c3b36699add3f268020579e8b718", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The drawbacks of using statistical learning methods such as StreamLearner and TLogic are as follows:\n\n1. They estimate rule confidence by simply counting the number of rule groundings and body groundings in the graph, which leads to independent rule learning that ignores interactions between different rules from the same positive example. This means the confidence of some rules might be enhanced while that of others diminished without considering their interdependencies.\n\n2. These methods cannot handle the similarity between different rules. For example, a reliable rule should imply a high confidence for another similar rule with the same predicates but slightly different temporal patterns. However, statistical methods may assign low confidence to such infrequent but similar rules.\n\n3. Their temporal constraints are built on timestamps rather than intervals and are fixed during learning, which impairs their ability to learn flexible temporal constraints.\n\n4. The performance of these timestamp-based methods on interval-based temporal knowledge graphs (tKGs) is not demonstrated, and temporal relations between intervals are more complex than those of timestamps.\n\n5. The restricted manner in which random walks are performed in these methods impairs the quality of learned rules, limiting their effectiveness in capturing temporal logical rules."}
{"question_id": "876d1ffd9379695a117eb81936e7bb2b0ffb1e9d", "paper_id": "openreview/NeurIPS-2022-dabt/TscdNx8udf5", "generation": "The baseline algorithms, including PID, MPC, and EMPC, were tuned by varying either the weights or the horizon, or both, until closed-loop trajectories with little to no overshoot and fast response times were obtained. This tuning was performed by experts on the environment to achieve optimal control performance."}
{"question_id": "916923a8909ab06632f575b7f36db3ac70642419", "paper_id": "egu/esd/14-1261-2023", "generation": "The molecular diffusivity \u03ba_m is given as 1.4 \u00d7 10^(-7) m\u00b2/s. The wave-induced diffusivity \u03ba_v(z) is calculated by the formula:\n\n\\[\n\\kappa_v(z) = \\alpha a^3 k \\sigma \\frac{\\sinh^2(k(z+h)) \\cosh(k(z+h))}{\\sinh^3(kh)}\n\\]\n\nwhere \u03b1 = 0.002 is a dimensionless coefficient, a is the wave amplitude, k is the wave number, \u03c3 is the angular frequency, h is the water depth, and z is the vertical coordinate.\n\nSince \u03ba_v depends on wave parameters and varies with depth, its magnitude can be significantly larger than the molecular diffusivity \u03ba_m, especially under mechanically generated waves with non-negligible amplitude. The molecular diffusivity \u03ba_m is a constant very small value, while \u03ba_v can increase substantially with wave amplitude and nonlinearity, indicating that wave-induced diffusivity \u03ba_v is generally much larger than molecular diffusivity \u03ba_m in the context of wave-induced vertical mixing."}
{"question_id": "006b4d78ff2835159d4e1f745a3f9c4f41fe8351", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The Markovian assumption in equation (6) is valid within the framework described, despite the current random walk step being determined by a given relation type and starting time that do not change during the walk. This is because the Markovian constraints considered involve only the current state probability and the immediate next step, without dependence on previously visited edges or states. Specifically, the random walk under Markovian constraints uses matrix operators (M_i,CM_i) that encode adjacency under these constraints, allowing the next state vector v_{i+1} to be computed solely from the current state vector v_i via multiplication by M_i,CM_i.\n\nThe relation type and starting time are fixed parameters of the query and serve as filtering conditions applied at each step, but they do not introduce dependence on the history of the walk. Non-Markovian constraints, which require consideration of previously visited edges or pairwise temporal relations between intervals, are handled separately by filtering the set of walks obtained under Markovian constraints. Thus, the Markovian assumption holds for the initial random walk process in equation (6), with subsequent filtering ensuring compliance with non-Markovian constraints.\n\nIn summary, the Markovian assumption is valid because the random walk step transition depends only on the current state and fixed query parameters, and not on the full history of the walk. Non-Markovian dependencies are enforced post hoc through filtering."}
{"question_id": "08ee038d964c18feafe50974403477b69a786d82", "paper_id": "openreview/ICLR-2023-conf/_X12NmQKvX", "generation": "The variables and functions in equations (8) to (13) have the following dimensions:\n\n- The parameter \\( p_{\\text{rec}} \\) for Recurrence is in \\(\\mathbb{R}^{|R|}\\), where \\(|R|\\) is the number of relations.\n- The parameter \\( p_{\\text{order}} \\) for TemporalOrder is in \\(\\mathbb{R}^{|R| \\times |R|}\\), representing probabilities for each pair of relations.\n- The parameters for RelationPairInterval include \\(\\mu_{\\text{pair}}, \\sigma_{\\text{pair}}, \\lambda_{\\text{pair}} \\in \\mathbb{R}^{|R| \\times |R|}\\), corresponding to Gaussian and exponential distribution parameters for each pair of relations.\n- The parameters for Duration, \\(\\mu_d, \\sigma_d\\), are in \\(\\mathbb{R}^{|R|}\\), representing Gaussian distribution parameters for each relation.\n\nThe scoring functions \\(\\phi_{\\text{rec}}, \\phi_{\\text{order}}, \\phi_{\\text{pair}}\\) and related probabilities \\(h\\) have dimensions consistent with these parameters:\n\n- \\(h_{\\text{rec}} \\in \\mathbb{R}^{|R|}\\),\n- \\(h_{\\text{order}} \\in \\mathbb{R}^{|R| \\times |R|}\\),\n- \\(h_{\\text{pair}} \\in \\mathbb{R}^{|R| \\times |R|}\\),\n- The learnable parameters \\(w, b\\) for these scoring functions have the same dimensions as the corresponding \\(h\\), i.e., \\(w, b \\in \\mathbb{R}^{|R|}\\) for Recurrence and \\(w, b \\in \\mathbb{R}^{|R| \\times |R|}\\) for TemporalOrder and RelationPairInterval.\n\nThus, the dimensions of variables and functions in equations (8)\u2013(13) are either \\(\\mathbb{R}^{|R|}\\) for single relations or \\(\\mathbb{R}^{|R| \\times |R|}\\) for pairs of relations."}
{"question_id": "ebd7cf5f3adbc674bd5ce88a563f3fea990873a9", "paper_id": "openreview/NeurIPS-2022-dabt/UrAYT2QwOX8", "generation": "The rationale for training on the first six months of data and validating on the last two months is based on the temporal splitting strategy commonly used in the fraud detection domain. This approach reflects the practical scenario where more recent data is considered more representative of the current data distribution when models are deployed in production. By training on earlier months and testing on later months, the evaluation better simulates real-world conditions where models must generalize to future, potentially shifting data distributions."}
{"question_id": "8192e96a224224e5fc15e03019d0ac65253d1492", "paper_id": "nlpeer/F1000-22/10-838", "generation": "The provided chunks do not contain any information regarding a \u201crange\u201d parameter. Therefore, no details about the purpose of a \u201crange\u201d parameter can be determined from the given content."}
{"question_id": "a9dce4895de5aac10ec1c0b1da92de90c47582f9", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The EGN and Meta-EGN models were pretrained for 4000 epochs. This is indicated by the training/validating dynamics shown in Figure 2, where the x-axis representing epochs extends up to 4000."}
{"question_id": "7603a58573eeaabda0e22ca42e407dd44c83bd3e", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "Action frames are syntactic and semantic structures that represent the core components of an action involving a verb and its arguments, such as the subject, direct object, and prepositional object. These components are mapped to semantic roles: the subject corresponds to the agent, the direct object to the theme, and the prepositional object to the goal. For frames involving prepositional phrases, separate frames are created for each preposition based on corpus statistics. Action frames capture the relations between pairs of arguments in a verb's usage and are used to model the physical implications of verbs by considering the relative physical knowledge entailed by the verb when applied to its arguments. Multiple frame relations can be associated with a single verb, reflecting different contexts or usages according to frame semantics."}
{"question_id": "8f0ed4f134911b593527e3793459b6d55faf5923", "paper_id": "openreview/NeurIPS-2022-dabt/-VyJim9UBxQ", "generation": "The copyright for the images used in the paper is held by the Reddit users who originally posted the photos. The dataset complies with the Reddit User Agreement and Reddit API terms of use, which state that user photos, text, and videos (\"User Content\") are owned by the users and not by Reddit. The dataset creators do not modify the original content and provide only the identifiers necessary to access the data, respecting the users' ownership rights."}
{"question_id": "53c8c64ae66712791ba2a355e4cc97a262c61acc", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "The provided text does not explicitly define the rate-distortion (R-D) function for probability distributions where the expectation is undefined, such as the Cauchy distribution. The definitions and theoretical results given assume the existence of finite expectations of distortion, as indicated by conditions such as the finiteness of \\( R(D) \\) for some distortion level \\( D \\) and the existence of a finite set \\( E \\subseteq Y \\) such that \\( \\mathbb{E}[\\min_{y \\in E} \\rho(X,y)] < \\infty \\) (Theorem A.2). These assumptions imply that the distortion metric \\( \\rho \\) and the source distribution \\( P_X \\) must be such that the expected distortion is finite.\n\nIn particular, the rate-distortion function \\( R(D) \\) is defined as the infimum of the mutual information \\( I(X;Y) \\) over all conditional distributions \\( Q_{Y|X} \\) satisfying the distortion constraint \\( \\mathbb{E}_{P_X Q_{Y|X}}[\\rho(X,Y)] \\leq D \\). This definition inherently requires the expectation of the distortion to be well-defined and finite.\n\nFor distributions like the Cauchy distribution, where the expectation of certain functions (including distortion) may be undefined or infinite, the standard rate-distortion function as defined cannot be directly applied without additional modifications or restrictions ensuring finiteness of the expected distortion.\n\nTherefore, within the scope of the provided material, the rate-distortion function is defined only for probability distributions and distortion metrics for which the expected distortion is finite and well-defined. No alternative definition or extension for distributions with undefined expectations, such as the Cauchy distribution, is provided."}
{"question_id": "6de0c620431f72ce5a6331d7dde1b8df91e24936", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "The criteria for determining which knowledge dimensions to annotate for each frame involve presenting crowd workers with a frame template (e.g., \"x threw y\") and asking them to rate the general relationship that the arguments of the frame exhibit with respect to all five knowledge dimensions considered in the study: size, weight, strength, rigidness, and speed. For each dimension, workers select from the options indicating whether the first argument is greater than, less than, approximately equal to, or has no general relation to the second argument along that attribute. Frames and attribute combinations that receive less than two-thirds agreement among workers or are selected by at least two-thirds of workers as having no general relation are removed from the usable dataset. Thus, the annotation is guided by human judgment of plausible and consistent relative physical relations along the five dimensions for each frame, with a threshold of inter-annotator agreement and exclusion of frames lacking a general relation."}
{"question_id": "****************************************", "paper_id": "openreview/ICLR-2022-conf/H4PmOqSZDY", "generation": "Yes, the proposed algorithms can be applied to other classes of probability distributions beyond the Gaussian distribution and the \"banana\" source. The work introduces new algorithms for upper and lower bounding the rate-distortion (R-D) function of a general, unknown memoryless source, which can be discrete, continuous, or neither. Experiments demonstrate the application of these algorithms not only on Gaussian sources but also on real-world data from particle physics and speech, which are more structured and complex than Gaussian noise. The upper bound algorithm uses neural networks to parameterize variational distributions and can incorporate tools such as normalizing flows for better expressiveness, allowing it to handle diverse data sources. The lower bound algorithm, while facing challenges with high-dimensional Gaussian sources, shows improved performance on structured real-world data, indicating its applicability beyond simple Gaussian models."}
{"question_id": "bc784ef2841eb98841522b97ab75da7a7106b99c", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The term \"fine-tuning timing for classical solver\" is not explicitly defined in the provided text. However, within the context of the Meta-EGN framework for combinatorial optimization, \"fine-tuning\" refers to performing one-step gradient descent on the model parameters at test time to adapt the pre-trained parameters \u03b8 to a specific test instance G\u2032, resulting in adapted parameters \u03b8_G\u2032. This fine-tuning step improves solution quality but incurs additional computational time during inference.\n\nIn contrast, classical solvers such as Gurobi do not involve such a fine-tuning process on learned parameters; their timing refers to the total runtime required to solve the optimization problem within a given time budget.\n\nTherefore, \"fine-tuning timing for classical solver\" would conceptually correspond to the additional computational time spent on adapting or optimizing parameters specific to an instance, which classical solvers do not perform. Instead, classical solvers' timing is solely the solver runtime. In the Meta-EGN context, fine-tuning timing is the extra time cost incurred by the one-step gradient update during testing, which can range from 1% to 30% of the time cost of other heuristic methods, as reported in the experiments.\n\nIn summary, \"fine-tuning timing for classical solver\" is not applicable because classical solvers do not perform fine-tuning; their timing is purely the solver execution time. Fine-tuning timing is a concept specific to learned models like Meta-EGN that adapt parameters at test time to improve solution quality."}
{"question_id": "9776571072ba250ab654c2a326bd48a527e61213", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The proposed framework, Meta-EGN, is designed as a general unsupervised learning method for combinatorial optimization (CO) problems, with a focus on achieving instance-wise good solutions through meta learning. While the current work demonstrates its effectiveness on benchmark CO problems such as max clique, vertex cover, and max independent set, the discussion on limitations and future work suggests potential extensions.\n\nSpecifically, Meta-EGN has been used to modify solutions of certain Monte Carlo (MC) algorithms like DGA and RGA in the maximum independent set (MIS) problem. The authors note that there exist other advanced MC algorithms (e.g., simulated annealing and parallel tempering) that could yield better results than DGA or RGA on random-regular graphs (RRGs). They propose the intuitive idea of learning Meta-EGN to further fine-tune these more advanced MC algorithms in the MIS problem on RRGs, indicating a direction for extending the framework to improve other heuristic or MC-based solvers.\n\nMoreover, the framework is motivated by the general goal of learning good initializations for future problem instances rather than direct solutions, which is a broadly applicable concept. The meta-learning approach adopted (MAML) is model-agnostic and can be adapted to various CO problems, as evidenced by its application to multiple synthetic and real-world graph datasets with varying sizes and distributions.\n\nIn summary, the framework can be extended to other applications or tasks within combinatorial optimization, particularly by adapting it to fine-tune more advanced MC algorithms or potentially other heuristic methods. The authors explicitly leave the research on modifying Meta-EGN to better handle CO problems requiring global assignments and to improve other advanced MC algorithms as future work."}
{"question_id": "92b04f60c27edb89dcdc8dcd575bcb9872f0e307", "paper_id": "nlpeer/PeerRead-ACL2017/818", "generation": "Yes, some incorrectly-classified actions and objects are ambiguous for humans. The error analysis highlights cases where the model fails due to underspecified frames or polysemy, which also cause disagreement among human annotators. For example, in one case, crowd workers provided conflicting examples regarding the relative weight of objects involved in the action \"stopped,\" illustrating complex underlying physics not modeled by the system. Another example involves polysemy in the phrase \"She caught the runner in first,\" where the correct interpretation depends on the sense of \"caught,\" leading to different physical relations. These instances indicate that the ambiguity in action senses and argument roles contributes to classification errors and reflects genuine uncertainty even for human annotators."}
{"question_id": "a7ad87d54def516b43292de78c758cf6107320f7", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "Yes, the proposed method can be implemented using projected gradient methods instead of mirror descent. The update rule for the weights \u03c9 in the algorithm uses mirror descent as a canonical generalization of Euclidean gradient descent on the probability simplex, but it is explicitly stated that other optimization methods, such as projected gradient descent, can also be used for this step."}
{"question_id": "d5b246ec5a8edcc34e88a24fb9bfd7d313572647", "paper_id": "openreview/ICLR-2022-conf/pjqqxepwoMy", "generation": "The target return \\( v^{tar}_t \\) in equation (1) is estimated by any value learning algorithm such as TD(0), Peng\u2019s Q(\u03bb), or other methods based on Bellman equations. In the implementation described, double Q-learning with dueling architecture is employed to compute \\( v^{tar}_t \\) due to its effectiveness and simplicity. Specifically, the target Q-value is computed as\n\n\\[\nQ^{target}_t = r_t + \\gamma Q(z_{t+1}, \\arg\\max_{a'} Q(z_{t+1}, a'; \\theta); \\theta^-),\n\\]\n\nwhere \\( r_t \\) is the reward at step \\( t \\), \\( \\gamma \\) is the discount factor, \\( \\theta \\) denotes the parameters of the Q network (MLP decoder), and \\( \\theta^- \\) denotes the parameters of the target Q network. The latent vector \\( z \\) used here is obtained from the posterior encoder with oracle observation as input. The first term of the VLOG loss function is then given by the mean square error between this target \\( v^{tar}_t \\) and the output of the Q network."}
{"question_id": "1be49494b9a1c964df99b3dabe0af0bfdc970713", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "Yes, the methodology can be applied to multi-task learning. The approach treats each training instance as a separate but related task, learning a good initialization that can be quickly fine-tuned for each individual instance. This is conceptually aligned with the principles of meta learning, which aims to achieve fast adaptation to new tasks by learning from historical tasks. Specifically, the method uses a meta-learning framework (MAML) to optimize neural networks towards instance-wise good solutions, viewing each instance as a distinct task and thus naturally fitting into a multi-task learning paradigm. This allows the model to generalize well across different instances and distributions, demonstrating robustness and adaptability characteristic of multi-task learning frameworks."}
{"question_id": "0544f3fb3619a15d1f6076086707d91cea93b334", "paper_id": "openreview/NeurIPS-2022-dabt/wPEXGTzZJt", "generation": "The authors selected the four datasets because, to their knowledge, these are the only publicly available datasets specifically designed for the task of visual search in natural scenes. Each dataset comprises curated search images, target objects, and anonymized participants\u2019 scanpaths, allowing for a systematic comparison of visual search models. The datasets differ in key characteristics such as the number of participants per image, number of images, target presentation, presence of distractors, color information, and image resolution, which provides a range of conditions for evaluation. Additionally, the authors performed extensive preprocessing to bring these datasets into a common format and criteria, enabling fair comparison across models despite variations in experimental design. This approach facilitates a unified evaluation framework and serves as a reference point for future incorporation of new datasets and models."}
{"question_id": "fe2486dcff37fd819cb07981aa6a1e026c1f52e5", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The proposed method, Target-Aware Weighted Training (TAWT), demonstrates clear improvements over state-of-the-art (SOTA) methods in cross-task learning. Empirical results show that TAWT enhances the performance of BERT in both pre-training and joint training paradigms across four sequence tagging tasks in natural language processing (PoS tagging, chunking, predicate detection, and named entity recognition). Specifically, TAWT achieves an average absolute improvement of 3.1% over baseline models.\n\nCompared to normalized joint training, which is a strong baseline inspired by the final task weights learned by TAWT, the weighted normalized joint training variant of TAWT consistently yields higher performance. For example, in named entity recognition (NER), weighted normalized joint training achieves 79.20% compared to 77.80% for normalized joint training. This improvement is statistically significant with p-values less than 0.001.\n\nTAWT also outperforms single-task learning and standard joint training methods. The method is effective especially when the target data size is limited or when the ratio between source and target training sizes is smaller, indicating that TAWT is more beneficial in scenarios where the base model's performance is weaker.\n\nFurthermore, TAWT is computationally efficient, requires minimal hyperparameter tuning, and can be easily integrated as a plugin into existing cross-task learning frameworks without significant overhead. It is the first weighted training algorithm for cross-task learning with theoretical guarantees, providing a principled approach to assign adaptive weights on source tasks or samples by minimizing a representation-based task distance.\n\nIn summary, TAWT surpasses current advanced methods in cross-task learning by improving sample efficiency, enhancing performance across multiple tasks, and offering theoretical and practical advantages over existing weighting schemes and multi-task learning heuristics."}
{"question_id": "530a4fe0ab0d3f801dab10132e0e5791d29d2051", "paper_id": "nlpeer/ARR-22/12ea2851de6445bf343d642c6ccc8640bf7a6c28b5f20ba123d6d58f02790c7c7f8ab696e6a6b4e45bfad28bd4bc0419bdcf6358693404dd0c14d202b291503b", "generation": "The results in Table 1 with decoder downsampling are obtained without the two-step decoding method. It is stated that increasing the downsampling rate from 3 to 5 degrades translation quality for all architectures, and employing the two-step decoder matches the decoding speed of subword models but results in much worse overall translation quality. This implies that the Table 1 results reflect models without the two-step decoder, as the two-step decoder leads to poorer translation quality not reflected in those results."}
{"question_id": "748b3e8ce8fe5bb6fa899f962211e41d18c30cae", "paper_id": "openreview/NeurIPS-2022-dabt/wPEXGTzZJt", "generation": "The bounds presented in Table 1 refer to the performance limits established by baseline models used as references for evaluating visual search models. The lower bound is defined by the uniform and center bias models, which represent minimal predictive performance: the uniform model assumes fixations are uniformly and independently distributed over the image, while the center bias model accounts for the human tendency to fixate near the center of images, modeled via a Gaussian Kernel Density Estimate (GKDE) over fixations from a training dataset. The upper bound is given by the Gold Standard model, which predicts fixations of each participant based on a GKDE over all fixations from other participants on the same image, representing the best achievable prediction given human variability. These bounds provide a reference frame for assessing the relative performance of evaluated visual search models."}
{"question_id": "b4885c9ebb178f8daa578cef0c857a1de41d8d54", "paper_id": "nlpeer/ARR-22/43e8458544e18304f6aa2c1aededf974877f088dd2ccd7d53ef71eb59e54567c0572f715ecdcbaf04c39206950c3f5628198e61ebc5ec09fa1ba0a559102f923", "generation": "The location of NLP conferences significantly influences the diversity of participation by encouraging attendance from researchers in the region surrounding the host country. Conferences held in different locations promote local participation, increasing the diversity of attendees. This effect is particularly pronounced in lower-income countries, where researchers show a stronger preference for attending nearby conferences. \n\nEmpirical analysis shows that the number of short-distance trips to conferences is higher than would be expected if venues were chosen randomly, while long-distance trips are reduced. This localized attendance pattern leads to a 27.21% reduction in carbon emissions from travel. Furthermore, conferences held in diverse locations attract a more geographically varied audience, as evidenced by higher diversity indices for events like LREC and RANLP, which achieve high diversity with relatively low travel distances.\n\nHowever, the diversity gain depends on the conference being advertised to and attended by the local regional community; simply holding conferences in different locations does not guarantee increased diversity if the majority of attendees come from distant regions. Additionally, wealthier countries tend to have researchers who travel longer distances, while lower-income countries show a preference for local events.\n\nOverall, distributing conferences globally fosters greater participation diversity by reducing travel distances for local researchers and encouraging attendance from a broader range of countries, thereby enhancing the geographic diversity of NLP research participation."}
{"question_id": "33ac8263606098fca8bcdc4746cd3f4235387b26", "paper_id": "openreview/ICLR-2022-conf/0jP2n0YFmKG", "generation": "The new method, Graph Parallelism, demonstrates favorable scaling efficiency and performance compared to existing distributed training methods such as data parallelism and model parallelism (pipeline parallelism). Specifically, combining graph parallelism with 32-way data parallelism on 256 GPUs achieves a scaling efficiency of 47% for the largest model, which is competitive given the model size and number of GPUs. Pure data parallel training with 32 GPUs attains 75% scaling efficiency for the smallest model, but this efficiency decreases with larger models and more GPUs due to network communication and load imbalance.\n\nIn terms of raw computational performance, graph parallelism outperforms pipeline parallelism for the models considered, as pipeline parallelism struggles with load balancing due to varying graph sizes and structures. Graph parallelism evenly distributes nodes, edges, and triplets across GPUs, mitigating load imbalance and communication overhead. Additionally, combining graph and pipeline parallelism shows complementary benefits, suggesting potential for further performance improvements.\n\nOverall, graph parallelism enables training of extremely large GNN models (up to nearly a billion parameters) with reasonably good scaling efficiency and higher sustained FLOPs compared to pipeline parallelism, indicating that it runs faster and scales better than existing model parallel methods and is promising when combined with data parallelism for large-scale distributed training."}
{"question_id": "413aa7a24c99874e0aae31b569348cc6c4e39b14", "paper_id": "nlpeer/F1000-22/10-170", "generation": "The recommendations based on the views expressed on coordination, preparation, and decision-makers are as follows:\n\n1. Engage the Right Experts: Pandemic management should involve relevant public health professionals, including infectious disease epidemiologists, health policy and systems experts, medical anthropologists, health economists, health communication experts, laboratory scientists, and clinicians. This science-based professional response is essential to ensure adequate preparedness and effective decision-making.\n\n2. Improve Coordination: Enhance both vertical and horizontal coordination among government departments and between government and non-governmental actors. Coordination should include multisectoral collaboration involving religious leaders, cultural activists, the private sector, non-governmental organizations, political parties, community groups, and individuals. Decentralization and coordination at district and sub-district levels are critical for ramping up the response.\n\n3. Transparent and Contextual Communication: Ensure data and decision transparency, provide correct and culturally appropriate information through trusted community messengers, and tailor messages by scientifically oriented social and behavior change communication experts.\n\n4. Strengthen Regulation and Accountability: Implement punitive actions against corruption and malpractice, dissolve syndicates within the health sector, and regulate the private sector for cost and quality control.\n\n5. Adequate Preparation and Support for Service Providers: Provide proper training, treatment guidelines, personal protective equipment (PPE), and workplace security to healthcare workers. Address logistical needs such as food, lodging, and transport, and ensure timely salary payments and motivational packages to maintain healthcare worker morale.\n\n6. Policy and Structural Reforms: Increase budgetary allocation and efficiency in the health sector, establish a dedicated public health career track, and adopt targeted policy approaches to address preexisting constraints such as budget shortages, low-quality services, and centralized care.\n\n7. Avoid Premature or Poorly Coordinated Decisions: Decisions such as lockdown imposition or termination should be based on epidemiological evidence and coordinated with relevant stakeholders to avoid unintended consequences, such as disease spread due to inadequate patient transport or premature relaxation of restrictions for cultural events.\n\nThese recommendations aim to improve pandemic preparedness, management, and public trust in the health system."}
{"question_id": "c5bafca1e41f1bfed6bf6063b321e6bb5102b171", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The poorer performance of the CLIP-ViL model compared to other models is attributed to its use of the image-text matching (ITM) pre-training objective, whereas models like X-VLM and ViLT are fine-tuned specifically for image-text retrieval tasks. This difference in training objectives results in CLIP-ViL achieving lower results, especially when the number of candidates increases (10 & 12 case), as the ITM objective may be less effective for the association task in WinoGAViL compared to the contrastive pre-training objective used by CLIP or the fine-tuning strategies of other models."}
{"question_id": "ddbf5cd6168ece49006281d9def64503e3610f0f", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The provided excerpts do not explicitly state that the representation \\(\\phi\\) must be a vector under assumption b or any related assumptions. The notation \\(\\phi \\in \\Phi\\) is used consistently, where \\(\\Phi\\) is a function class or representation space, but there is no explicit requirement that \\(\\phi\\) be a vector. Instead, \\(\\phi\\) is treated as an element of a function class or representation space, which may be more general than a vector.\n\nFor example, in Proposition A.1 and the theoretical guarantees, \\(\\phi\\) is considered as a representation in \\(\\Phi\\), and the analysis involves losses \\(L_t^\\star(\\phi)\\) and covering numbers of \\(\\Phi\\), which are function classes. The proofs and lemmas involve norms and distances defined on \\(\\Phi\\), but these do not imply that \\(\\phi\\) must be a vector; rather, \\(\\phi\\) can be a function or mapping.\n\nTherefore, it is not necessary for \\(\\phi\\) to be a vector given assumption b; \\(\\phi\\) is more generally an element of a function class or representation space \\(\\Phi\\)."}
{"question_id": "8f304b893f44b97b98ba8df1ea43ce0fcb657b87", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The expression in question involves sums of terms related to losses and empirical processes indexed by functions and parameters, such as\n\n\\[\n\\sum_{t=1}^T \\omega_t \\cdot \\frac{1}{n_t} \\sum_{i=1}^{n_t} \\left[ L_t(\\phi, f_t) - \\ell(f_t \\circ \\phi(x_{ti}), y_{ti}) + \\ell(\\bar{f}_{\\omega t} \\circ \\bar{\\phi}_\\omega(x_{ti}), y_{ti}) - L_t(\\bar{\\phi}_\\omega, \\bar{f}_{\\omega t}) \\right].\n\\]\n\nThis expression is used to define a function \\( G(\\{z_{ti}\\}) \\) which is a supremum over \\(\\phi \\in \\Phi\\), \\(\\{f_t\\} \\subset \\mathcal{F}\\), and weights \\(\\omega\\).\n\nThere is no explicit statement that this sum is always positive. However, the analysis treats this quantity as a nonnegative measure of deviation or complexity, which is standard in empirical process theory and learning theory. The key points supporting the positivity or nonnegativity are:\n\n1. **Loss functions are bounded and nonnegative:** The loss \\(\\ell(\\cdot, \\cdot)\\) is bounded in \\([0,1]\\), ensuring that differences involving losses are bounded and well-defined.\n\n2. **Supremum over function classes:** The supremum over \\(\\phi\\) and \\(\\{f_t\\}\\) is taken, which typically yields a nonnegative quantity since it measures the maximal deviation or discrepancy.\n\n3. **Use of norms and distances:** In the proof of Lemma A.1 and related arguments, distances such as \\(d_2\\) norms are used, which are inherently nonnegative.\n\n4. **Application of concentration inequalities:** McDiarmid\u2019s inequality and symmetrization arguments are applied to \\(G(\\{z_{ti}\\})\\), which requires bounded differences and implicitly assumes the quantity is nonnegative or at least bounded below.\n\nRegarding taking a power \\(1/p\\) of such sums, this is justified because the sums are treated as norms or semi-norms in function spaces (e.g., \\(L^p\\) norms or empirical \\(L^p\\) norms). Since norms are by definition nonnegative, taking the \\(1/p\\)-th power is well-defined and preserves the metric properties.\n\nIn summary, while the expression is not explicitly stated to be always positive, it is constructed and interpreted as a nonnegative quantity (e.g., a supremum of deviations or a norm-like measure). This nonnegativity justifies taking powers such as \\(1/p\\) in the analysis."}
{"question_id": "21f851f7058a46d8f9904d493b82811edf3aa8f3", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The provided text does not mention or discuss any connections between the proposed Target-Aware Weighted Training (TAWT) algorithm and existing research on learning data weighting using multi-arm bandits. The focus of the work is on adaptively assigning weights to source tasks or samples in cross-task learning by minimizing a representation-based task distance between source and target tasks. The weighting scheme is derived through a theoretically principled approach involving mirror descent updates on the probability simplex, with no reference to multi-arm bandit methods or related frameworks. Therefore, no explicit connections to multi-arm bandit-based data weighting approaches are established or discussed in this work."}
{"question_id": "3684e20e39c14e7d52c12084515e178aa0789584", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "Associations that are solvable by AI should be kept in the framework to reduce dependence on a specific AI model and to ensure the naturalness and diversity of the data. The framework prioritizes the \"solvable-by-humans\" score for data selection rather than excluding instances based on AI model performance. This approach avoids biasing the dataset toward only adversarial or artificially difficult examples tailored to fool a particular model. By including associations that AI can solve, the dataset maintains a broader range of natural and meaningful associations that reflect human commonsense reasoning, which is essential for developing models with better general association abilities."}
{"question_id": "3b1176248b0cfc5fb6e1786bb4007f98aa2ac210", "paper_id": "openreview/NeurIPS-2022-dabt/UrAYT2QwOX8", "generation": "The first and second disparities are induced through controlling the generative model sampling, depending on the group and label, respectively. Specifically, group size disparity is created by varying the group-wise frequencies in the dataset, such as having different sizes for age groups in the protected attribute. Prevalence disparity is generated by conditioning the class probability on the protected groups, resulting in different fraud rates across groups. These disparities are implemented by manipulating the sampling process of the generative model to reflect the desired group size and label prevalence differences."}
{"question_id": "caa94f24704e76df0ceac395ee650a45f7a174e1", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The most popular and advanced methods in cross-task learning are pre-training and joint training paradigms. Pre-training involves first training the representation part on the source data and then fine-tuning the entire model on the target data. Joint training simultaneously uses both source and target data to train a shared representation model along with task-specific functions for both source and target tasks. These paradigms are widely adopted and can be enhanced by weighted training algorithms such as Target-Aware Weighted Training (TAWT), which assigns adaptive weights to source tasks or samples to improve sample efficiency and performance."}
{"question_id": "26c5a6b5fdd3b1d0b2d97c1550126b503f144eed", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The chance performance reported in Table 4 corresponds to the random expected Jaccard index values for different numbers of candidates, which are 38% for 5 candidates, 34% for 6 candidates, 24% for 10 candidates, and 17% for 12 candidates."}
{"question_id": "b79e294382978a8a9cebe595211529bcb653e0f2", "paper_id": "nlpeer/COLING2020/1886", "generation": "The topics for n-grams are chosen by assigning each n-gram to a category based on the Empath lexicon, which includes a list of topics (e.g., Politics, War). Each n-gram is mapped to one or more topics according to its semantic content (e.g., \"missile\" and \"army\" are assigned to the topic War). To select only the n-grams relevant for one of the target classes (fake or real news), the difference between the importance values for the real class (R_v) and the fake class (F_v) is computed for each n-gram. Then, the mean (\u03bc) and standard deviation (\u03c3) of these differences are calculated, and only n-grams whose difference exceeds \u03bc + \u03c3 are retained. This procedure ensures that the chosen n-grams are significantly relevant to either the fake or real class based on their activation values in the model."}
{"question_id": "1874144ac78d10d99982bc7f6446545ac56a4805", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "Further analysis of model performance on different association types revealed that models perform best on associations where the cue is visually salient in the image, with accuracy around 75%, but their performance significantly degrades on visually non-salient associations, dropping to about 36%. This suggests that models struggle with associations requiring common sense reasoning beyond direct visual detection. A fine-grained categorization of association types was conducted, dividing instances into six categories: visually salient, visually non-salient, concept related, activity, counting, and colors. The model (CLIP ViT-B/32) showed highest accuracy in visually salient and colors categories, moderate performance in concept related and activity categories, and poor performance in visually non-salient and counting categories. This analysis indicates a lack of common sense reasoning capabilities in models for non-visually salient associations. Additionally, the annotated data with these categories was released for future research to further investigate these challenges."}
{"question_id": "207945518935728931a4b020daa416e8fc8f1cda", "paper_id": "openreview/ICLR-2023-conf/-ENYHCE8zBp", "generation": "The relaxations in Table 2 are based on previously established principles and derivations. Specifically, the loss function relaxations for the maximum clique (MC), minimum vertex covering (MVC), and maximum independent set (MIS) problems follow the relaxation principles and detailed derivations presented in Karalias & Loukas (2020) and Wang et al. (2022). The training loss design and relaxation methods are explicitly stated to follow these prior works, indicating that the relaxations are not newly derived in this paper but rather build upon and apply existing relaxation frameworks."}
{"question_id": "04e9ce2f786d7603574645eafe3bfe6e1a603190", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "The choice of mirror descent in the proposed Target-Aware Weighted Training (TAWT) method is not strictly necessary but is motivated by its suitability for optimization on the probability simplex. Specifically, mirror descent is used in the update rule for the weights \u03c9 (Equation 2.8) because it is a canonical generalization of Euclidean gradient descent to gradient descent on the probability simplex, which is the domain of the weights \u03c9 (Beck & Teboulle, 2003). The update rule adjusts the weights along directions where the target task and source tasks are more similar, using a weighted dissimilarity measure between gradients.\n\nHowever, the text explicitly notes that other optimization methods, such as projected gradient descent, can also be used in place of mirror descent. This indicates that mirror descent is chosen for its natural fit and intuitive interpretation in this context but is not an indispensable component of the algorithm. Thus, the method can be implemented with alternative optimization techniques for updating the weights on the simplex."}
{"question_id": "9baa6e51bac226403b63b8bef97ea58737e9f14c", "paper_id": "openreview/NeurIPS-2022-dabt/aJtVdI251Vv", "generation": "The primary factor that makes the 10 & 12 candidate task more difficult than the 5 & 6 candidate task for models, despite humans achieving similar performance on both, is the increased number of candidates (distractors) which raises the difficulty level for models but not for humans. Models experience a significant performance drop when the number of candidates increases, indicating that they struggle to handle the larger candidate set. In contrast, human accuracy remains robust and unaffected by the increased number of candidates, demonstrating human resilience to the higher difficulty level. Additionally, fine-tuning improves model performance by 7% only in the 10 & 12 candidate cases, suggesting that models can exploit supervised data more effectively in harder cases with lower random chance of success. This implies that the increased number of distractors reduces the likelihood of random success and requires more complex reasoning or discrimination, which models find challenging but humans manage well."}
{"question_id": "da72c5aada0a4b421a7919d5ef2739ddb658ae5e", "paper_id": "nlpeer/ARR-22/12ea2851de6445bf343d642c6ccc8640bf7a6c28b5f20ba123d6d58f02790c7c7f8ab696e6a6b4e45bfad28bd4bc0419bdcf6358693404dd0c14d202b291503b", "generation": "The results in Table 2 were generated using the IWSLT 2017 datasets with a training data size of around 200k sentences for each language pair. Specifically, the data statistics for training, validation, and test sets are as follows (from Table 4):\n\n- English-Arabic (en-ar): 232k training sentences, 1.3k validation sentences, 1.2k test sentences.\n- English-German (en-de): 206k training sentences, 1.3k validation sentences, 1.1k test sentences.\n- English-French (en-fr): 232k training sentences, 1.3k validation sentences, 1.2k test sentences.\n\nThus, approximately 200k parallel sentence pairs were used for training, and about 1.2k to 1.3k sentences were used for validation and testing for each language pair."}
{"question_id": "c44a73f20a295acb499abf61c0f0b96e4080d9ba", "paper_id": "openreview/ICLR-2022-conf/ltM1RMZntpu", "generation": "Yes, there is a \\(\\forall\\) quantifier before equation (3.5) in Definition 3.2 (Transferability). Specifically, the definition states that a representation \\(\\phi \\in \\Phi\\) is \\((\\rho, C_{\\rho})\\)-transferable from \\(\\omega\\)-weighted source tasks to the target task if there exist \\(\\rho > 0\\), \\(C_{\\rho} > 0\\) such that for **any** \\(\\bar{\\phi}_{\\omega} \\in \\bar{\\Phi}_{\\omega}\\), the inequality\n\n\\[\nL_0^{\\star}(\\phi) - L_0^{\\star}(\\bar{\\phi}_{\\omega}) \\leq C_{\\rho} \\left( \\sum_{t=1}^T \\omega_t \\left[ L_t^{\\star}(\\phi) - L_t^{\\star}(\\bar{\\phi}_{\\omega}) \\right] \\right)^{1/\\rho}\n\\]\n\nholds. The phrase \"for any \\(\\bar{\\phi}_{\\omega} \\in \\bar{\\Phi}_{\\omega}\\)\" explicitly indicates a universal quantification over \\(\\bar{\\phi}_{\\omega}\\)."}
{"question_id": "ba8b96d10b44463b1ec163db65a29b6145f8729a", "paper_id": "openreview/ICLR-2023-conf/ySCL-NG_I3", "generation": "The learning curves for the Harmonic Molecular Representation (HMR) model during training on the rigid protein docking task demonstrate progressive improvement in key performance metrics over 80 epochs. Specifically, the total loss decreases steadily, indicating effective optimization of the model parameters. Concurrently, the F-score, Area Under the Curve (AUC), and Average Precision (AP) metrics all show consistent increases, reflecting enhanced predictive accuracy and robustness in binding site prediction.\n\nThe model selection is based on the best validation AP score, which suggests that the training process successfully balances fitting the training data while maintaining generalization to unseen validation data. The use of a cosine annealing learning rate scheduler and Adam optimizer with a learning rate of 5 \u00d7 10\u207b\u2074 contributes to stable convergence.\n\nOverall, the learning curves indicate that the HMR model effectively learns to propagate and encode molecular surface features through harmonic message passing, resulting in improved performance on the protein docking task as training progresses."}
{"question_id": "69d95e16f2f308754136f8e2245592ca5497ff5f", "paper_id": "openreview/ICLR-2023-conf/xYlJRpzZtsY", "generation": "In the ROSCOE training process, the model is trained using triplets consisting of reference reasoning steps, positive hypothesis reasoning steps, and hard-negative hypothesis reasoning steps. Specifically, for contrastive learning, the pairs used are:\n\n- Positive pairs: the context and reference reasoning steps (denoted as (s, r))\n- Hard-negative pairs: the context and perturbed reference steps (denoted as (s, h))\n\nThe training objective minimizes cross-entropy with in-batch negatives by maximizing the similarity of the positive pairs (context and reference steps) and minimizing the similarity of the hard-negative pairs (context and perturbed reference steps)."}
{"question_id": "911fa2b76fba7e5ae58fb8322c5940c42acbd848", "paper_id": "openreview/ICLR-2023-conf/xYlJRpzZtsY", "generation": "The proposed taxonomy of generic reasoning errors is comprehensive as it covers a broad spectrum of error types encountered in step-by-step reasoning generated by large language models (LLMs). It includes nine distinct error categories that address both overall reasoning chain quality and fine-grained step-level inconsistencies. These error types encompass grammar, factuality, hallucination, redundancy, repetition, missing steps, coherency, commonsense, and arithmetic errors. The taxonomy was developed through manual preliminary analysis of LLM reasoning errors across multiple human-judged datasets and logical inference and commonsense reasoning tasks. It was further validated by constructing diagnostic datasets with synthetic perturbations and collecting human annotations from expert judges on model-generated reasoning chains. The taxonomy aligns with evaluation perspectives used in human annotations, which include detailed step-level error labels and overall quality assessments. Although the authors acknowledge that the taxonomy may not cover all possible reasoning errors, it is designed to be generic, applicable to natural language rationales, and considers alignment with input context and generated explanations. This design enables the taxonomy to provide fine-grained, interpretable metrics that improve upon baseline evaluation methods and demonstrate robustness across diverse reasoning tasks, including logical and arithmetic reasoning."}
{"question_id": "dd594c4d0897a3c1259ae3d2d2d23859fbb96f04", "paper_id": "nlpeer/F1000-22/10-637", "generation": "The provided information does not mention or describe the purpose of a saliva sample. Therefore, no conclusion about the purpose of a saliva sample can be drawn from the given content."}
