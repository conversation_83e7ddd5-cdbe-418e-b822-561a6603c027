import logging
import time
from pathlib import Path
import pandas as pd
import requests
from bs4 import BeautifulSoup
from tqdm.auto import tqdm
from tqdm.contrib.logging import logging_redirect_tqdm
import re
import fitz

from core import logging

logger = logging.get_logger()

DATA_PATH = Path("data/data")
QA_FILE = DATA_PATH / "qa.jsonl"
PAPERS_FILE = DATA_PATH / "papers.jsonl"
SECONDS_BETWEEN_REQUESTS = 1

ACL_CRAWL_URLS = [
  "https://aclanthology.org/volumes/P17-1/", # PeerRead-ACL2017
  "https://aclanthology.org/volumes/2022.acl-long/", # ARR-22
  "https://aclanthology.org/volumes/2022.acl-short/", # ARR-22
  "https://aclanthology.org/volumes/2022.findings-acl/", # ARR-22
  "https://aclanthology.org/volumes/2022.naacl-main/", # ARR-22
  "https://aclanthology.org/volumes/2022.findings-naacl/", # ARR-22
  "https://aclanthology.org/volumes/2020.coling-main/", # COLING2020
  "https://aclanthology.org/volumes/K16-1/", # PeerRead-CONLL2016
]

def get_paper_id_title_map() -> dict:
  paper_id_title_map = {}
  for data in pd.read_json(PAPERS_FILE, lines=True).itertuples():
    if data.type == 'title':
      paper_id_title_map[data.paper_id] = data.content
  return paper_id_title_map
      
def crawl_acl_anthology() -> dict:
  title_pdf_url_map = {}
  for url in ACL_CRAWL_URLS:
    logger.info(f"Crawling ACL Anthology URL: {url}")
    response = requests.get(url)
    soup = BeautifulSoup(response.content, 'html.parser')
    for paper in soup.find_all("p", {"class": "d-sm-flex align-items-stretch"}):
      url = paper.find("a", {"class": "badge badge-primary align-middle mr-1", "title": "Open PDF"})['href']
      title = paper.find_all("a", {"class": "align-middle"})[-1].text.strip()
      title_pdf_url_map[title.lower().replace("’", "'")] = url
  return title_pdf_url_map
  
def download_paper_from_f1000_research(paper_id: str, title: str) -> None:
  """
  Downloads a paper from F1000 Research given its paper ID and title.
  Example URL: https://f1000research.com/articles/10-72/v3/pdf
  """
  output_path_pdf = DATA_PATH / paper_id / "paper.pdf"
  output_path_pdf.parent.mkdir(parents=True, exist_ok=True)
  original_pdf_path = DATA_PATH / paper_id / "original_paper.pdf"

  def find_paper_version(title: str) -> int:
    version_regex = r"\[Version (\d+);"
    version = re.search(version_regex, title).group(1)    
    return version
  
  version = find_paper_version(title)
  pdf_url = f"https://f1000research.com/articles/{paper_id.split('/')[-1]}/v{version}/pdf"
  
  logger.info(f"Downloading original paper {paper_id} from F1000 Research")
  
  # download the PDF
  r = requests.get(pdf_url)
  with open(original_pdf_path, "wb") as f:
    f.write(r.content)
    
  # remove review comments from the PDF
  logger.info(f"Removing review comments from paper {paper_id}")
  document = fitz.open(original_pdf_path)
  review_start_page = None
  review_texts = [
    "Current Peer Review Status:", 
    "Open Peer Review",
    f"Version {version}", 
    "Reviewer Report"
  ]
  for page in document:
    page_text = page.get_text()
    is_review_page = all(review_text in page_text for review_text in review_texts)
    if is_review_page:
      review_start_page = page.number
      logger.info(f"Found review start page: {review_start_page}")
      break
  if review_start_page is None:
    logger.error(f"Could not find review start page in {original_pdf_path}")
    raise ValueError(f"Could not find review start page in {original_pdf_path}")
  
  # remove review pages from the PDF
  new_document = fitz.open()
  for page in document:
    if page.number < review_start_page:
      new_document.insert_pdf(document, from_page=page.number, to_page=page.number)
  new_document.save(output_path_pdf)
  new_document.close()
  
  return
    
def main():
  qa_data = pd.read_json(QA_FILE, lines=True)

  # filter out papers that are not from nlpeer
  qa_data = qa_data[qa_data.paper_id.str.startswith("nlpeer")]
  paper_ids = qa_data.paper_id.unique()
  logger.info(f"Found {len(paper_ids)} papers to download")

  paper_id_title_map = get_paper_id_title_map()
  acl_download_urls = crawl_acl_anthology()
  
  for paper_id in tqdm(paper_ids, ncols=80, desc="Downloading PDFs"):
    # create the output path
    output_path_pdf = DATA_PATH / paper_id / "paper.pdf"
    output_path_pdf.parent.mkdir(parents=True, exist_ok=True)  
    
    # download the PDF
    logger.info(f"Downloading paper {paper_id}")
    
    title = paper_id_title_map.get(paper_id, None)
    if not title:
      logger.error(f"Title not found for paper {paper_id}")
      raise ValueError(f"Title not found for paper {paper_id}")
    
    journal = paper_id.split("/")[1]
    
    if journal == "F1000-22":
      # F1000 Research papers are not available in the ACL Anthology
      download_paper_from_f1000_research(paper_id, title)
    else:
      # Download the PDF from ACL Anthology
      pdf_url = acl_download_urls.get(title.lower(), None)
      if not pdf_url:
        logger.error(f"URL not found for paper {paper_id} with title '{title}'")
        raise ValueError(f"URL not found for paper {paper_id} with title '{title}'")
      
      # download the PDF
      r = requests.get(pdf_url)
      with open(output_path_pdf, "wb") as f:
        f.write(r.content)
      
    # wait a bit before the next request
    time.sleep(SECONDS_BETWEEN_REQUESTS)

if __name__ == "__main__":
  with logging_redirect_tqdm():
    main()
