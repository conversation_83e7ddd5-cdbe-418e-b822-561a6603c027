import logging
import time
from pathlib import Path
import pandas as pd
import requests
from tqdm.auto import tqdm
from tqdm.contrib.logging import logging_redirect_tqdm

from core import logging

logger = logging.get_logger()

DATA_PATH = Path("data/data")
QA_FILE = DATA_PATH / "qa.jsonl"
SECONDS_BETWEEN_REQUESTS = 1

def main():
  qa_data = pd.read_json(QA_FILE, lines=True)

  # filter out papers that are not from OpenReview
  qa_data = qa_data[qa_data.paper_id.str.startswith("egu")]
  paper_ids = qa_data.paper_id.unique()

  for paper_id in tqdm(paper_ids, ncols=80, desc="Downloading PDFs"):
    _, journal, paper_id = paper_id.split("/")
    volume, page, year = paper_id.split("-")

    # create the output path
    output_path_pdf = DATA_PATH / "egu" / journal / paper_id / "paper.pdf"
    output_path_pdf.parent.mkdir(parents=True, exist_ok=True)
    # download the PDF
    egu_url = f"https://{journal}.copernicus.org/articles/{volume}/{page}/{year}/{journal}-{paper_id}.pdf"
    logger.info(f"Downloading {egu_url}")
    r = requests.get(egu_url)
    with open(output_path_pdf, "wb") as f:
      f.write(r.content)
    # wait a bit before the next request
    time.sleep(SECONDS_BETWEEN_REQUESTS)

if __name__ == "__main__":
  with logging_redirect_tqdm():
    main()
