from pathlib import Path
import pandas as pd
import dill as pickle

from core import logging
logger = logging.get_logger()

DATA_PATH = Path("data/data")
QA_FILE = DATA_PATH / "qa.jsonl"
QA_AUGMENTED_FILE = DATA_PATH / "qa-augmented-answers.jsonl"

ALL_PAPER_IDS = pd.read_json(QA_FILE, lines=True).paper_id.unique().tolist()

def get_qa_data():
  qa_data = pd.read_json(QA_FILE, lines=True)
  return qa_data

def get_qa_augmented_data():
  qa_data = pd.read_json(QA_AUGMENTED_FILE, lines=True)
  return qa_data

def get_all_paper_ids():
  return ALL_PAPER_IDS

def validate_paper_id(paper_id: str):
  if paper_id not in ALL_PAPER_IDS:
    raise ValueError(f"Paper ID {paper_id} not found in the dataset.")
  return True

def get_free_from_available_paper_ids():
  qa_data = pd.read_json(QA_FILE, lines=True)
  qa_data = qa_data[qa_data["answer_free_form"].notnull()]
  return qa_data["paper_id"].unique()

def get_answer_generation_questions():
  qa_data = get_qa_data()
  qa_augmented = get_qa_augmented_data()
  # print(len(qa_augmented[qa_augmented["augmented_answer_free_form"] != "nan"]))
  qa_data = qa_data.merge(qa_augmented[['question_id', 'augmented_answer_free_form']], on='question_id', how='left')
  qa_data = qa_data[qa_data["answer_free_form"].notnull() & qa_data["raw_answer_evidence"].notnull()]
  
  return qa_data

def get_pdf_path(paper_id: str) -> Path:
  validate_paper_id(paper_id)
  paper_path = DATA_PATH / paper_id / "paper.pdf"
  if not paper_path.exists():
    raise ValueError(f"PDF for paper ID {paper_id} does not exist.")
  return paper_path.absolute()

def get_paper_dir(paper_id: str) -> Path:
  validate_paper_id(paper_id)
  paper_dir = DATA_PATH / paper_id
  if not paper_dir.exists():
    raise ValueError(f"Directory for paper ID {paper_id} does not exist.")
  return paper_dir

def save_raw_parsed_data(paper_id: str, data):
  validate_paper_id(paper_id)
  file_path = DATA_PATH / paper_id / "raw_parsed_data.pkl"
  file_path.parent.mkdir(parents=True, exist_ok=True)
  with open(file_path, "wb") as file:
    pickle.dump(data, file)

def load_raw_parsed_data(paper_id: str):
  validate_paper_id(paper_id)
  file_path = DATA_PATH / paper_id / "raw_parsed_data.pkl"
  if not file_path.exists():
    logger.warning(f"Raw parsed data for paper ID {paper_id} does not exist.")
    return None
  with open(file_path, "rb") as file:
    return pickle.load(file)

def is_raw_parsed_data_exists(paper_id: str) -> bool:
  validate_paper_id(paper_id)
  file_path = DATA_PATH / paper_id / "raw_parsed_data.pkl"
  return file_path.exists()

def save_chunked_data(paper_id: str, chunks):
  validate_paper_id(paper_id)
  file_path = DATA_PATH / paper_id / "chunks.pkl"
  file_path.parent.mkdir(parents=True, exist_ok=True)
  with open(file_path, "wb") as file:
    pickle.dump(chunks, file)
  logger.info(f"Saved chunked data for paper {paper_id}")
  
def load_chunked_data(paper_id: str):
  validate_paper_id(paper_id)
  file_path = DATA_PATH / paper_id / "chunks.pkl"
  if not file_path.exists():
    logger.warning(f"Chunked data for paper ID {paper_id} does not exist.")
    return None
  with open(file_path, "rb") as file:
    return pickle.load(file)

def is_chunked_data_exists(paper_id: str) -> bool:
  validate_paper_id(paper_id)
  file_path = DATA_PATH / paper_id / "chunks.pkl"
  return file_path.exists()

if __name__ == "__main__":
  print(get_answer_generation_questions()[["answer_free_form", "augmented_answer_free_form"]].head())