import config from '@/config/env';
import { UploadResponse } from '@/interfaces/interfaces';

class ApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = config.API_BASE_URL;
  }

  private async request(endpoint: string, options: RequestInit = {}): Promise<Response> {
    const url = `${this.baseUrl}${endpoint}`;

    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // Remove Content-Type for FormData requests
    if (options.body instanceof FormData) {
      delete (defaultOptions.headers as any)['Content-Type'];
    }

    return fetch(url, defaultOptions);
  }

  // Paper/PDF related endpoints

  async getUploadUrl(fileName: string): Promise<UploadResponse> {
    const response = await this.request(`/getUploadPaperUrl/${encodeURIComponent(fileName)}`);
    
    if (!response.ok) {
      throw new Error('Failed to get upload URL');
    }

    return response.json();
  }

  async notifyPaperUploaded(paperId: string): Promise<{ message: string }> {
    const response = await this.request(`/notifyPaperUploaded/${paperId}`, {
      method: 'POST',
    });

    if (!response.ok) {
      throw new Error('Failed to notify upload completion');
    }

    return response.json();
  }

  async getPaperStatus(paperId: string): Promise<any> {
    const response = await this.request(`/papers/${paperId}`);
    
    if (!response.ok) {
      throw new Error('Failed to get paper status');
    }

    return response.json();
  }

  async deletePaper(paperId: string): Promise<{ message: string }> {
    const response = await this.request(`/papers/${paperId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error('Failed to delete PDF');
    }

    return response.json();
  }

  async getPDFViewUrl(paperId: string): Promise<string> {
    const response = await this.request(`/papers/${paperId}/view-url`);

    if (!response.ok) {
      throw new Error('Failed to get PDF view URL');
    }

    const data = await response.json();
    return data.view_url;
  }

  // Chat related endpoints
  async sendChatMessage(message: string, paperIds: string[], traceId: string, signal?: AbortSignal): Promise<Response> {
    const requestBody = {
      message,
      paper_ids: paperIds,
      trace_id: traceId,
    };

    console.log('Sending chat request:', requestBody);
    console.log('Request body JSON:', JSON.stringify(requestBody));

    const requestOptions = {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/plain',
      },
      signal,
    };

    console.log('Request options:', requestOptions);

    const response = await this.request('/chat', requestOptions);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    return response;
  }

  // Utility method to upload file to S3 using presigned URL
  async uploadToS3(uploadUrl: string, file: File): Promise<Response> {
    const response = await fetch(uploadUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': 'application/pdf',
      },
      mode: 'cors',
    });

    if (!response.ok) {
      throw new Error('Failed to upload file to S3');
    }

    return response;
  }
}

export const apiService = new ApiService();
export default apiService;
