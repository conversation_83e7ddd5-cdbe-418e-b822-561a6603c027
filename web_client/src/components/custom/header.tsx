import { ThemeToggle } from "./theme-toggle";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Hash } from "lucide-react";
import { cn } from "@/lib/utils";

interface HeaderProps {
  onTogglePDFSidebar?: () => void;
  onToggleChunksSidebar?: () => void;
  hasChunks?: boolean;
}

export const Header = ({ onTogglePDFSidebar, onToggleChunksSidebar, hasChunks }: HeaderProps) => {
  return (
    <>
      <header className="flex items-center justify-between px-2 sm:px-4 py-2 bg-background text-black dark:text-white w-full">
        <div className="flex items-center space-x-1 sm:space-x-2">
          {onTogglePDFSidebar && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onTogglePDFSidebar}
              className="h-10 w-12 border"
            >
              <FileText style={{ width: '20px', height: '24px' }} />
            </Button>
          )}
        </div>
        <div className="flex items-center space-x-1 sm:space-x-2">
          {onToggleChunksSidebar && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggleChunksSidebar}
              className={cn(
                "h-10 w-12 border",
                hasChunks ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20" : ""
              )}
              disabled={!hasChunks}
              title={hasChunks ? "Show relevance chunks" : "No chunks available"}
            >
              <Hash style={{ width: '20px', height: '24px' }} />
            </Button>
          )}
          <ThemeToggle />
        </div>
      </header>
    </>
  );
};