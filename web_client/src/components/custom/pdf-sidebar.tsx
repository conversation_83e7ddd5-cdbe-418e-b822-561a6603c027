import { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Upload, FileText, X, Trash2, Loader2, CheckCircle, AlertCircle, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { PDF } from '@/interfaces/interfaces';
import apiService from '@/services/api';

interface PDFSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  selectedPDFs: string[];
  onPDFSelectionChange: (pdfIds: string[]) => void;
}

export function PDFSidebar({ isOpen, onClose, selectedPDFs, onPDFSelectionChange }: PDFSidebarProps) {
  const [pdfs, setPDFs] = useState<PDF[]>(() => {
    // Initialize state from localStorage
    const savedPDFs = localStorage.getItem('uploaded-pdfs');
    if (savedPDFs) {
      try {
        return JSON.parse(savedPDFs).map((pdf: any) => ({
          ...pdf,
          uploadedAt: new Date(pdf.uploadedAt)
        }));
      } catch (error) {
        console.error('Error loading PDFs from localStorage:', error);
        return [];
      }
    }
    return [];
  });
  const [isUploading, setIsUploading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Mark as initialized after first render
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  // Save PDFs to localStorage whenever pdfs state changes (but not on initial load)
  useEffect(() => {
    if (isInitialized) {
      console.log('Saving PDFs to localStorage:', pdfs);
      localStorage.setItem('uploaded-pdfs', JSON.stringify(pdfs));
    }
  }, [pdfs, isInitialized]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !file.name.endsWith('.pdf')) {
      alert('Please select a PDF file');
      return;
    }

    setIsUploading(true);

    try {
      // Get presigned URL from backend
      console.log('Getting presigned URL for S3 upload...');
      const uploadData = await apiService.getUploadUrl(file.name);

      // Create PDF entry with uploading status
      const newPDF: PDF = {
        id: uploadData.paper_id,
        name: file.name,
        status: 'uploading',
        uploadedAt: new Date()
      };

      setPDFs(prev => [...prev, newPDF]);

      // Upload file directly to S3
      console.log('Uploading file to S3...');
      await apiService.uploadToS3(uploadData.upload_url, file);
      console.log('S3 upload successful');

      // Notify backend that upload is complete
      console.log('Notifying backend of upload completion...');
      await apiService.notifyPaperUploaded(uploadData.paper_id);

      // Update PDF status to processing
      setPDFs(prev => prev.map(pdf =>
        pdf.id === uploadData.paper_id
          ? { ...pdf, status: 'processing' }
          : pdf
      ));

      // Poll for processing completion
      pollPDFStatus(uploadData.paper_id);

    } catch (error) {
      console.error('Upload failed:', error);
      setPDFs(prev => prev.map(pdf =>
        pdf.name === file.name
          ? { ...pdf, status: 'error' }
          : pdf
      ));

      let errorMessage = 'Upload failed. ';
      if (error instanceof Error && error.message.includes('CORS')) {
        errorMessage += 'CORS error - please configure S3 bucket CORS policy.';
      } else {
        errorMessage += `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      }
      alert(errorMessage);
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const pollPDFStatus = async (pdfId: string) => {
    const maxAttempts = 60; // 5 minutes with 5-second intervals
    let attempts = 0;
    
    const poll = async () => {
      try {
        const paperData = await apiService.getPaperStatus(pdfId);
        const status = paperData.status;
        
        setPDFs(prev => prev.map(pdf => 
          pdf.id === pdfId 
            ? { ...pdf, status: status === 'processed' ? 'ready' : status === 'error' ? 'error' : 'processing' }
            : pdf
        ));
        
        if (status === 'processed' || status === 'error' || attempts >= maxAttempts) {
          return;
        }
        
        attempts++;
        setTimeout(poll, 5000); // Poll every 5 seconds
      } catch (error) {
        console.error('Polling error:', error);
        setPDFs(prev => prev.map(pdf => 
          pdf.id === pdfId 
            ? { ...pdf, status: 'error' }
            : pdf
        ));
      }
    };
    
    setTimeout(poll, 5000); // Start polling after 5 seconds
  };

  const handlePDFSelection = (pdfId: string) => {
    const isSelected = selectedPDFs.includes(pdfId);
    if (isSelected) {
      // Deselect the current PDF
      onPDFSelectionChange([]);
    } else {
      // Select only this PDF (replace any previous selection)
      onPDFSelectionChange([pdfId]);
    }
  };

  const handleDeletePDF = async (e: React.MouseEvent, pdfId: string) => {
    e.stopPropagation();

    try {
      await apiService.deletePaper(pdfId);
      setPDFs(prev => prev.filter(pdf => pdf.id !== pdfId));
      onPDFSelectionChange(selectedPDFs.filter(id => id !== pdfId));
    } catch (error) {
      console.error('Delete error:', error);
      alert('Failed to delete PDF');
    }
  };

  const handleViewPDF = async (e: React.MouseEvent, pdfId: string) => {
    e.stopPropagation();

    try {
      const pdfUrl = await apiService.getPDFViewUrl(pdfId);
      window.open(pdfUrl, '_blank');
    } catch (error) {
      console.error('Failed to open PDF:', error);
      alert('Failed to open PDF for viewing');
    }
  };

  const getStatusIcon = (status: PDF['status']) => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'ready':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: PDF['status']) => {
    switch (status) {
      case 'uploading':
        return 'Uploading...';
      case 'processing':
        return 'Processing...';
      case 'ready':
        return 'Ready';
      case 'error':
        return 'Error';
      default:
        return '';
    }
  };

  return (
    <div
      className={cn(
        "fixed inset-y-0 left-0 w-[400px] bg-background border-r transform transition-transform duration-200 ease-in-out z-50",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}
    >
      <div className="flex flex-col h-full p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">PDF Documents</h2>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept=".pdf"
          onChange={handleFileUpload}
          className="hidden"
        />

        <Button
          onClick={() => fileInputRef.current?.click()}
          className="mb-4 flex items-center gap-2"
          variant="outline"
          disabled={isUploading}
        >
          {isUploading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Upload className="h-4 w-4" />
          )}
          {isUploading ? 'Uploading...' : 'Upload PDF'}
        </Button>
        <ScrollArea className="w-full flex-1 max-w-full overflow-hidden">
          <div className="w-full space-y-2">
            {pdfs.map((pdf) => (
              <div className="group flex w-full" key={pdf.id}>
                <Button
                  variant={selectedPDFs.includes(pdf.id) ? "secondary" : "ghost"}
                  className="flex justify-start h-auto py-3 w-full"
                  onClick={() => handlePDFSelection(pdf.id)}
                  disabled={pdf.status !== 'ready'}
                >
                  <div className="flex-1 gap-2">
                    <div className="flex items-center gap-2 min-w-0 gap-2">
                      {getStatusIcon(pdf.status)}
                      <div className="min-w-0 text-left">
                        <p className="text-sm font-medium max-w-[240px] truncate" title={pdf.name}>
                          {pdf.name}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {getStatusText(pdf.status)}
                        </p>
                      </div>
                    </div>
                  </div>

                <div className="min-w-10 gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={(e) => handleViewPDF(e, pdf.id)}
                  disabled={pdf.status !== 'ready'}
                  title="View PDF"
                >
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={(e) => handleDeletePDF(e, pdf.id)}
                  title="Delete PDF"
                >
                  <Trash2 className="h-4 w-4 text-destructive" />
                </Button>
              </div>
              </Button>

            </div>
          ))}
          {pdfs.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">No PDFs uploaded yet</p>
              <p className="text-xs">Upload a PDF to get started</p>
            </div>
          )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}
