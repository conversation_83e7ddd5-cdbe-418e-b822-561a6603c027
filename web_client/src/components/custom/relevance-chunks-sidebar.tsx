import { Button } from '@/components/ui/button';
import { X, FileText, Hash } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { RelevanceChunk } from '@/interfaces/interfaces';
import { useState } from 'react';

interface RelevanceChunksSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  chunks: RelevanceChunk[];
  query?: string;
}

export function RelevanceChunksSidebar({ isOpen, onClose, chunks, query }: RelevanceChunksSidebarProps) {
  const [selectedChunk, setSelectedChunk] = useState<RelevanceChunk | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const formatSimilarityScore = (score: number) => {
    return (score * 100).toFixed(1) + '%';
  };



  const handleChunkClick = (chunk: RelevanceChunk) => {
    setSelectedChunk(chunk);
    setIsModalOpen(true);
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600 dark:text-green-400';
    if (score >= 0.6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 0.8) return 'bg-green-50 dark:bg-green-900/20';
    if (score >= 0.6) return 'bg-yellow-50 dark:bg-yellow-900/20';
    return 'bg-red-50 dark:bg-red-900/20';
  };

  return (
    <div
      className={cn(
        "fixed inset-y-0 right-0 w-[400px] bg-background border-l transform transition-transform duration-200 ease-in-out z-50",
        isOpen ? "translate-x-0" : "translate-x-full"
      )}
    >
      <div className="flex flex-col h-full p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Relevance Chunks</h2>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {chunks.length > 0 && (
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-900/20 rounded-lg">
            <p className="text-sm text-gray-700 dark:text-gray-300">
              Found {chunks.length} relevant chunk{chunks.length > 1 ? 's' : ''}
            </p>
          </div>
        )}

        <div className="w-full flex-1 max-w-full overflow-y-auto scrollbar-hide">
          <div className="w-full space-y-3 pr-2">
            {chunks.map((chunk, index) => (
              <div
                key={chunk.id}
                className={cn(
                  "p-4 rounded-lg border transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer",
                  getScoreBgColor(chunk.similarity_score)
                )}
                onClick={() => handleChunkClick(chunk)}
              >
                {/* Header with similarity score */}
                <div className="flex justify-between items-start mb-2">
                  <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Chunk {index + 1}
                    </span>
                  </div>
                  <div className={cn(
                    "px-2 py-1 rounded text-xs font-medium",
                    getScoreColor(chunk.similarity_score)
                  )}>
                    {formatSimilarityScore(chunk.similarity_score)}
                  </div>
                </div>

                {/* Heading context */}
                {chunk.heading_context && chunk.heading_context.length > 0 && (
                  <div className="mb-2">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                      Context:
                    </div>
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {chunk.heading_context.join(' > ')}
                    </div>
                  </div>
                )}

                {/* Content preview */}
                <div className="mb-2">
                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                    Content:
                  </div>
                  <div className="text-sm text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap line-clamp-[10]">
                    {chunk.content}
                  </div>
                </div>

                {/* Paper Name */}
                <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                  <FileText className="h-3 w-3" />
                  <span className="truncate" title={chunk.paper_name}>
                    {chunk.paper_name}
                  </span>
                </div>
              </div>
            ))}
            
            {chunks.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-sm">No relevant chunks found</p>
                <p className="text-xs">Try asking a question to see relevant content</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Full Content Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent
          className="max-w-4xl max-h-[85vh] flex flex-col"
          onOpenAutoFocus={(e) => e.preventDefault()}
        >
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Hash className="h-5 w-5" />
              Chunk Details
              {selectedChunk && (
                <span className={cn(
                  "ml-auto px-2 py-1 rounded text-sm font-medium",
                  getScoreColor(selectedChunk.similarity_score)
                )}>
                  {formatSimilarityScore(selectedChunk.similarity_score)}
                </span>
              )}
            </DialogTitle>
          </DialogHeader>

          {selectedChunk && (
            <div className="flex-1 min-h-0 flex flex-col space-y-4 overflow-hidden">
              {/* Heading Context */}
              {selectedChunk.heading_context && selectedChunk.heading_context.length > 0 && (
                <div className="flex-shrink-0">
                  <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                    Document Context:
                  </h4>
                  <div className="text-sm text-gray-800 dark:text-gray-200 bg-gray-50 dark:bg-gray-800 p-3 rounded">
                    {selectedChunk.heading_context.join(' > ')}
                  </div>
                </div>
              )}

              {/* Full Content - This is the scrollable section */}
              <div className="flex-1 min-h-0 flex flex-col">
                <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 flex-shrink-0">
                  Full Content:
                </h4>
                <div className="flex-1 min-h-0 bg-gray-50 dark:bg-gray-800 rounded overflow-hidden">
                  <div className="h-full w-full overflow-y-auto scrollbar-hide">
                    <div className="text-sm text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap p-3">
                      {selectedChunk.content}
                    </div>
                  </div>
                </div>
              </div>

              {/* Paper Name */}
              <div className="flex-shrink-0">
                <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                  Source Document:
                </h4>
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded">
                  <FileText className="h-4 w-4" />
                  <span className="break-words">
                    {selectedChunk.paper_name}
                  </span>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
