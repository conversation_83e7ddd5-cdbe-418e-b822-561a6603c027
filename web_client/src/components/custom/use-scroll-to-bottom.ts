import { useEffect, useRef, RefObject } from "react";

export function useScrollToBottom<T extends HTMLElement>(isLoading?: boolean, messageCount?: number): [
  RefObject<T>,
  RefObject<T>,
] {
  const containerRef = useRef<T>(null);
  const endRef = useRef<T>(null);
  const userScrolledDuringLoadingRef = useRef(false);
  const userHasScrolledRef = useRef(false);
  const wasLoadingRef = useRef(false);
  const lastMessageCountRef = useRef(messageCount || 0);

  useEffect(() => {
    const container = containerRef.current;
    const end = endRef.current;

    if (container && end) {
      const currentMessageCount = messageCount || 0;
      if (currentMessageCount > lastMessageCountRef.current) {
        userHasScrolledRef.current = false;
        end.scrollIntoView({ behavior: "smooth", block: "nearest", inline: "nearest" });
      }
      lastMessageCountRef.current = currentMessageCount;

      // Reset user scroll flags when loading starts (new conversation/message)
      if (isLoading && !wasLoadingRef.current) {
        userScrolledDuringLoadingRef.current = false;
        // Reset the main scroll flag for new conversations
        userHasScrolledRef.current = false;
      }
      wasLoadingRef.current = isLoading || false;

      const handleUserScroll = () => {
        // Mark that user has scrolled (stops auto-scroll immediately)
        userHasScrolledRef.current = true;

        // Mark that user scrolled during loading
        if (isLoading) {
          userScrolledDuringLoadingRef.current = true;
        }
      };

      const observer = new MutationObserver(() => {
        // Auto-scroll logic:
        // 1. STOP auto-scroll immediately if user has scrolled at any point
        // 2. Only auto-scroll if user hasn't scrolled
        const shouldAutoScroll = !userHasScrolledRef.current;

        if (shouldAutoScroll) {
          end.scrollIntoView({ behavior: "smooth", block: "nearest", inline: "nearest" });
        }
      });

      // Listen for various scroll events
      container.addEventListener('scroll', handleUserScroll, { passive: true });
      container.addEventListener('wheel', handleUserScroll, { passive: true });
      container.addEventListener('touchmove', handleUserScroll, { passive: true });

      observer.observe(container, {
        childList: true,        
        subtree: true,          
        characterData: true,    
      });

      return () => {
        observer.disconnect();
        container.removeEventListener('scroll', handleUserScroll);
        container.removeEventListener('wheel', handleUserScroll);
        container.removeEventListener('touchmove', handleUserScroll);
      };
    }
  }, [isLoading, messageCount]);

  return [containerRef, endRef];
}