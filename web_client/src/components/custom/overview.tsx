import { motion } from 'framer-motion';
import { MessageCircle, FileText, Upload, AlertCircle } from 'lucide-react';

interface OverviewProps {
  hasSelectedDocument?: boolean;
}

export const Overview = ({ hasSelectedDocument = false }: OverviewProps) => {
  return (
    <>
    <motion.div
      key="overview"
      className="max-w-3xl mx-auto md:mt-20"
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.98 }}
      transition={{ delay: 0.75 }}
    >
      <div className="rounded-xl p-6 flex flex-col gap-8 leading-relaxed text-center max-w-xl">
        {!hasSelectedDocument ? (
          <>
            <p className="flex flex-row justify-center gap-4 items-center">
              <Upload size={44} className="text-blue-500"/>
              <span>+</span>
              <FileText size={44} className="text-orange-500"/>
              <span>+</span>
              <MessageCircle size={44} className="text-purple-500"/>
            </p>
            <p>
              Welcome to <strong>PDF Chat Assistant</strong><br />
              Upload and select a PDF document to start chatting with AI.<br />
              <span className="text-sm text-muted-foreground">
                Click the sidebar button to upload and select a PDF document!
              </span>
            </p>
          </>
        ) : (
          <>
            <p className="flex flex-row justify-center gap-4 items-center">
              <MessageCircle size={44} className="text-green-500"/>
            </p>
            <p>
              <strong>Ready to Chat!</strong><br />
              You have selected a document. Start asking questions below.<br />
              <span className="text-sm text-muted-foreground">
                Type your question in the input field below to get started!
              </span>
            </p>
          </>
        )}
      </div>
    </motion.div>
    </>
  );
};
