export interface message{
    content:string;
    role:string;
    id:string;
}

export interface PDF {
    id: string;
    name: string;
    status: 'uploading' | 'processing' | 'ready' | 'error';
    uploadedAt: Date;
}

export interface UploadResponse {
    paper_id: string;
    upload_url: string;
}

export interface RelevanceChunk {
    id: string;
    paper_id: string;
    paper_name: string;
    content: string;
    heading_context: string[];
    similarity_score: number;
}

export interface ChunksResponse {
    chunks: RelevanceChunk[];
}