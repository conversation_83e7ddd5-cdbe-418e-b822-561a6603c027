from bs4 import BeautifulSoup
import requests
import json
import threading
import tqdm
import random
import os

from args_parser import get_argument
from logger import get_logger

PROXY_FILE = "temp/free_proxy_list.json"
PROXY_LIST = []
CHECK_TIME_OUT_SECONDS = 8
CHECK_RETRY_COUNT = 3
CHECK_URL = "https://httpbin.org/ip"
ALLOWED_SCHEMES = ["http", "https", "socks4", "socks5"]

logger = get_logger()

def setup_folders():
  if not os.path.exists("temp"):
    os.makedirs("temp")

def check_proxy(proxy):
  for _ in range(CHECK_RETRY_COUNT):
    try:
      response = requests.get(CHECK_URL, proxies={"http": proxy, "https": proxy}, timeout=CHECK_TIME_OUT_SECONDS)
      if response.status_code == 200:
        return True
    except requests.RequestException as e:
      pass
  return False

def filter_inactive_proxies(proxy_list):
  active_proxies = []
  semaphore = threading.Semaphore(64)

  progress = tqdm.tqdm(total=len(proxy_list), desc="Checking Proxies", unit="proxy")
  progress.display()
  
  def check_and_add(proxy, progress):
    with semaphore:
      if check_proxy(proxy):
        active_proxies.append(proxy)
      progress.update(1)
      
  threads = []
  for proxy in proxy_list:
    thread = threading.Thread(target=check_and_add, args=(proxy, progress,))
    threads.append(thread)
  for thread in threads:
    thread.start()
  for thread in threads:
    thread.join()
    
  progress.close()

  return active_proxies

def www_localhost():
  return [None]

def www_proxy_list_download():
  urls = [
    'https://www.proxy-list.download/api/v2/get?l=en&t=http',
    'https://www.proxy-list.download/api/v2/get?l=en&t=https'
  ]
  proxies = []
  for url in urls:
    response = requests.get(url)
    if response.status_code == 200:
      data = response.json()
      for proxy in data["LISTA"]:
        proxy = f"{proxy['IP']}:{proxy['PORT']}"
        if proxy not in proxies:
          proxies.append(proxy)
  return proxies

def www_free_proxy_list_net():
  urls = [
    "https://www.free-proxy-list.net/",
    "https://www.us-proxy.org/",
    "https://free-proxy-list.net/uk-proxy.html",
    "https://www.sslproxies.org/",
    "https://free-proxy-list.net/anonymous-proxy.html"
  ]
  proxies = []
  
  for url in urls:
    response = requests.get(url)
    soup = BeautifulSoup(response.text, "html.parser")
    table = soup.find("table")
    for row in table.find("tbody").find_all("tr"):
      ip = row.find_all("td")[0].text
      port = row.find_all("td")[1].text
      proxy = f"{ip}:{port}"
      if proxy not in proxies:
        proxies.append(proxy)
      
  return proxies

def www_proxyscrap_com():
  url = "https://api.proxyscrape.com/v4/free-proxy-list/get?request=display_proxies&protocol=http&proxy_format=protocolipport&format=text&timeout=20000"
  response = requests.get(url)
  if response.status_code == 200:
    data = response.text
    proxies = data.split("\n")
    return [_.strip() for _ in proxies if _]
  return []

def get_saved_proxies():
  try:
    with open(PROXY_FILE, "r") as file:
      return json.load(file)
  except FileNotFoundError:
    return []

def save_proxies_to_file(proxy_list):
  with open(PROXY_FILE, "w") as file:
    json.dump(proxy_list, file)

def get_random_proxy():
  if not PROXY_LIST:
    raise ValueError("No active proxies available.")
  return random.choice(PROXY_LIST)

def _init():
  global PROXY_LIST
  
  setup_folders()
  getters = [
    get_saved_proxies,
    www_localhost,
    www_proxy_list_download, 
    www_free_proxy_list_net,
    www_proxyscrap_com
  ]
  if get_argument("no_proxy_fetch"):
    logger.info("Skipped proxy fetch")
    getters = [get_saved_proxies]

  proxies = []
  for f in getters:
    for proxy in f():
      if proxy not in proxies:
        proxies.append(proxy)

  logger.info(f"Found {len(proxies)} free proxies")
  
  if get_argument("no_proxy_check"):
    logger.info("Skipped proxy check")
  else:
    proxies = filter_inactive_proxies(proxies)

  save_proxies_to_file(proxies)
  PROXY_LIST = proxies
  PROXY_LIST.append(None) # localhost, no proxy
  logger.info(f"Active proxies: {len(PROXY_LIST)}")

_init()


  
