import argparse

argparser = argparse.ArgumentParser(description="Proxy Checker")
argparser.add_argument("--no_proxy_fetch", action="store_true", help="Skip proxy fetch")
argparser.add_argument("--no_proxy_check", action="store_true", help="Skip proxy check")
argparser.add_argument("-log_level", type=str, default="INFO", help="Set log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)")
args = argparser.parse_args().__dict__

def get_argument(key):
  if key in args:
    return args[key]
  else:
    raise ValueError(f"Parameter {key} not found")