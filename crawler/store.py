import json
import os

from logger import get_logger

FIELDS = [
  'paperId', 
  'corpusId', 
  'externalIds', 
  'url', 
  'title', 
  'abstract', 
  'venue', 
  'publicationVenue', 
  'year', 
  'referenceCount', 
  'citationCount', 
  'influentialCitationCount', 
  'isOpenAccess', 
  'openAccessPdf', 
  'fieldsOfStudy', 
  's2FieldsOfStudy', 
  'publicationTypes', 
  'publicationDate', 
  'journal', 
  'citationStyles', 
  'authors', 
  'citations', 
  'references', 
  'embedding', 
  'tldr'
]

DATA_DIR = "data/"
PAPER_PERFIX = "paper-"

logger = get_logger()

def get_paper_fields():
  return FIELDS

def get_paper_file_path(id):
  return os.path.join(DATA_DIR, f"{PAPER_PERFIX}{id}.json")

def save_paper_data(data):
  for field in FIELDS:
    if field not in data:
      raise ValueError(f"Field '{field}' is missing in the data.")
  
  id = data.get("paperId")
  file_path = get_paper_file_path(id)
  with open(file_path, "w") as file:
    json.dump(data, file)
  logger.info(f"Paper id {id} saved")

def is_paper_data_exists(id):
  file_path = get_paper_file_path(id)
  return os.path.exists(file_path)

def load_paper_data(id):
  file_path = get_paper_file_path(id)
  with open(file_path, "r") as file:
    try:
      data = json.load(file)
    except json.JSONDecodeError:
      raise ValueError(f"Failed to decode JSON from file: {file_path}")
    
  for field in FIELDS:
    if field not in data:
      raise ValueError(f"Field '{field}' is missing in the loaded data.")
  
  return data

def get_crawled_paper_ids():
  if not os.path.exists(DATA_DIR):
    return []
  
  files = os.listdir(DATA_DIR)
  paper_ids = []
  for file in files:
    if file.startswith(PAPER_PERFIX) and file.endswith(".json"):
      paper_id = file[len(PAPER_PERFIX):-len(".json")]
      paper_ids.append(paper_id)
  
  return paper_ids


    