import requests
import threading
import random
import time

from logger import get_logger
from free_proxy import get_random_proxy
from store import get_paper_fields, save_paper_data, load_paper_data, is_paper_data_exists, get_crawled_paper_ids

BATCH_SIZE = 1
CRAWL_RETRY_COUNT = 8
CRAWL_TIMEOUT_SECONDS = 16
CRAWL_THREAD_COUNT = 16
BASE_URL = "https://api.semanticscholar.org/"

logger = get_logger()

def crawl_paper_data(paper_id: str) -> None:
  if is_paper_data_exists(paper_id):
    logger.info(f"Paper {paper_id} already crawled")
    return
    
  for i in range(CRAWL_RETRY_COUNT):
    try:
      url = f"{BASE_URL}/graph/v1/paper/{paper_id}"
      proxy = get_random_proxy()
      logger.info(f"Crawling paper id {paper_id} with proxy {proxy} (attempt {i + 1})")
      response = requests.get(
        url, 
        params={"fields": ",".join(get_paper_fields())},
        proxies={
          "http": proxy,
          "https": proxy
        },
        timeout=CRAWL_TIMEOUT_SECONDS
      )
      if response.status_code == 200:
        save_paper_data(response.json())
        logger.info(f"Paper id {paper_id} crawled successfully")
        return
      if response.status_code == 429:
        time.sleep(5)
    except requests.RequestException as e:
      pass
  logger.error(f"Failed to crawl paper {paper_id}")

def main():
  semaphore = threading.Semaphore(CRAWL_THREAD_COUNT)
  logger.info("Getting paper ids to crawl")
  to_crawl = set()
  for id in get_crawled_paper_ids():
    data = load_paper_data(id)
    if not data["fieldsOfStudy"] or "Computer Science" not in data["fieldsOfStudy"]:
      continue
    for citation in data["citations"]:
      to_crawl.add(citation['paperId'])
    for reference in data["references"]:
      to_crawl.add(reference['paperId'])

  to_crawl = random.sample(list(to_crawl), k=min(len(to_crawl), BATCH_SIZE))
  logger.info(f"Selected {len(to_crawl)} papers to crawl")
  def crawl_paper(id):
    with semaphore:
      crawl_paper_data(id)
  threads = []
  
  
  for i in range(0, len(to_crawl), 250):
    batch = to_crawl[i:i+250]
    threads = []
    for id in batch:
      thread = threading.Thread(target=crawl_paper, args=(id,))
      threads.append(thread)
    for thread in threads:
      thread.start()
    for thread in threads:
      thread.join()

if __name__ == "__main__":
  main()
  