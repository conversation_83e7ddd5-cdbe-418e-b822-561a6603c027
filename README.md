# PDF Chat Assistant

A comprehensive AI-powered system for chatting with PDF documents using RAG (Retrieval-Augmented Generation) architecture.

## Architecture Overview

The system consists of three main components:
- **Core Module**: Document processing, embeddings, and AI services
- **Web Server**: FastAPI backend with streaming chat capabilities
- **Web Client**: React-based frontend with real-time chat interface

## Prerequisites

- Python 3.10+
- Node.js 18+
- OpenAI API key
- Qdrant vector database

## Environment Setup

1. **<PERSON><PERSON> and navigate to the project:**
   ```bash
   git clone <repository-url>
   cd thesis
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration:
   # OPENAI_API_KEY=your_openai_api_key
   # OPENAI_MODEL=gpt-4
   # OPENAI_TEXT_EMBEDDING_MODEL=text-embedding-3-small
   # QDRANT_URL=http://localhost:6333
   ```

3. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Install Node.js dependencies:**
   ```bash
   cd web_client
   npm install
   cd ..
   ```

## Development Server

### 1. Start Qdrant Vector Database
```bash
# Using Docker
docker run -p 6333:6333 qdrant/qdrant
```

### 2. Start Backend Server
```bash
# From project root
python3.10 -m web_server.start
```
The backend will be available at `http://localhost:8000`

### 3. Start Frontend Development Server
```bash
# In a new terminal
cd web_client
npm run dev
```
The frontend will be available at `http://localhost:5173`

## Running Experiments

### 1. Document Processing Pipeline
```bash
# Parse PDF documents
python3.10 -m parser.main

# Generate embeddings and store in vector database
python3.10 -m core.embedding_pipeline
```

### 2. RAG Evaluation Experiments
```bash
# Run RAG workflow on QA dataset
python3.10 -m experiment.rag_workflow

# Evaluate results using multiple metrics
python3.10 -m experiment.evaluate_results
```

### 3. Chunk Augmentation Experiments
```bash
# Generate augmented chunks with entities and summaries
python3.10 -m experiment.chunk_augmentation

# Test different chunking strategies
python3.10 -m experiment.chunking_strategies
```

### 4. Custom Experiments
```bash
# Run specific experiment modules
python3.10 -m experiment.<experiment_name>
```

## Key Features

### Backend (FastAPI)
- **Streaming Chat**: Real-time response streaming with typing effects
- **Document Management**: PDF upload, processing, and storage
- **Vector Search**: Semantic similarity search using Qdrant
- **AI Integration**: OpenAI GPT models with scientific prompting
- **Error Handling**: Retry logic and graceful error recovery

### Frontend (React + TypeScript)
- **Real-time Chat**: Streaming responses with typing indicators
- **Document Selection**: Single PDF selection for focused queries
- **Relevance Chunks**: Sidebar showing source chunks with similarity scores
- **Markdown Support**: Full markdown rendering including tables
- **Theme Support**: Dark/light mode with instant switching
- **Responsive Design**: Mobile-friendly interface

### Core Processing
- **Document Parsing**: Extract structured content from PDFs
- **Chunking Strategies**: Multiple approaches with overlap handling
- **Embedding Generation**: OpenAI text embeddings for semantic search
- **Answer Generation**: Scientific question-answering with context

## API Endpoints

- `POST /chat` - Streaming chat with document context
- `POST /upload` - Upload PDF documents
- `GET /papers` - List uploaded documents
- `DELETE /papers/{id}` - Delete documents
- `GET /findSimilarChunks` - Search for relevant chunks

## Development Tips

1. **Hot Reload**: Both frontend and backend support hot reloading
2. **Debugging**: Check browser console and server logs for issues
3. **Database**: Use Qdrant dashboard at `http://localhost:6333/dashboard`
4. **API Testing**: Use FastAPI docs at `http://localhost:8000/docs`

## Troubleshooting

### Common Issues
- **Port conflicts**: Change ports in configuration files
- **API key errors**: Verify OpenAI API key in .env file
- **Database connection**: Ensure Qdrant is running and accessible
- **Module imports**: Run commands from project root directory

### Performance Optimization
- **Chunk size**: Adjust chunking parameters for better retrieval
- **Top-k results**: Modify similarity search limits
- **Streaming speed**: Adjust character delay in chat handler

## Project Structure
```
thesis/
├── core/                 # Core processing modules
├── web_server/          # FastAPI backend
├── web_client/          # React frontend
├── experiment/          # Research experiments
├── parser/              # Document processing
├── data/               # Datasets and storage
└── README.md           # This file
```

## Contributing

1. Follow TypeScript/Python type hints
2. Use existing code patterns and conventions
3. Test changes with both development server and experiments
4. Update documentation for new features

For detailed implementation details, refer to the individual module documentation and code comments.
