import logging
import boto3
import hashlib
from dataclasses import dataclass
from dataclasses_json import dataclass_json

logger = logging.getLogger()
logger.setLevel(logging.DEBUG)

@dataclass_json
@dataclass
class S3Access:
  access_key: str
  secret_key: str
  bucket: str

@dataclass_json
@dataclass
class InputEvent:
  s3_access: S3Access
  uploaded_file_key: str
  file_storage_folder: str

@dataclass_json
@dataclass
class OutputBody:
  file_sha256: str
  file_existed: bool

@dataclass_json
@dataclass
class OutputEvent:
  status_code: int
  body: OutputBody | None
  message: str

class InputError(Exception):
  pass

def parse_input_event(event) -> InputEvent:
  try:
    return InputEvent(
      s3_access=S3Access(
        access_key=event["s3_access"]["access_key"],
        secret_key=event["s3_access"]["secret_key"],
        bucket=event["s3_access"]["bucket"]
      ),
      uploaded_file_key=event["uploaded_file_key"],
      file_storage_folder=event["file_storage_folder"]
    )
  except KeyError as e:
    logger.error(f"Missing key in input event: {e}")
    raise InputError(f"Missing key in input event: {e}")

def check_file_exists(session: boto3.Session, bucket: str, key: str) -> bool:
  s3 = session.client("s3")
  try:
    s3.head_object(Bucket=bucket, Key=key)
    return True
  except Exception:
    return False

def download_file(session: boto3.Session, bucket: str, key: str) -> bytes:
  if not check_file_exists(session, bucket, key):
    logger.error(f"File {key} does not exist")
    raise InputError(f"File {key} does not exist")
  s3 = session.client("s3")
  response = s3.get_object(Bucket=bucket, Key=key)
  return response["Body"].read()

def upload_file(session: boto3.Session, bucket: str, key: str, data: bytes):
  s3 = session.client("s3")
  s3.put_object(Bucket=bucket, Key=key, Body=data)

def delete_file(session: boto3.Session, bucket: str, key: str):
  s3 = session.client("s3")
  s3.delete_object(Bucket=bucket, Key=key)

def lambda_handler(event, context):
  try:
    input_event = parse_input_event(event)
    logger.info("Received post upload process request for file %s", input_event.uploaded_file_key)
    
    logger.info("Downloading file from S3")
    session = boto3.Session(
      aws_access_key_id=input_event.s3_access.access_key,
      aws_secret_access_key=input_event.s3_access.secret_key
    )
    file_data = download_file(session, input_event.s3_access.bucket, input_event.uploaded_file_key)

    logger.info("Computing SHA256")
    sha256 = hashlib.sha256(file_data).hexdigest()
    file_name = f"{sha256}.pdf"
    
    logger.info("Checking if file already exists")
    target_key = f"{input_event.file_storage_folder}/{file_name}"
    file_existed = check_file_exists(session, input_event.s3_access.bucket, target_key)
    
    if not file_existed:
      logger.info("File does not exist, Uploading file to S3")
      upload_file(session, input_event.s3_access.bucket, target_key, file_data)
    else:
      logger.info("File already exists, skipping upload")
    
    logger.info("Deleting temp file on S3")
    delete_file(session, input_event.s3_access.bucket, input_event.uploaded_file_key)
    
    response = OutputEvent(
      status_code=200,
      body=OutputBody(
        file_sha256=sha256,
        file_existed=file_existed
      ),
      message="File processed successfully"
    )
  except InputError as e:
    response = OutputEvent(
      status_code=400,
      body=None,
      message=str(e)
    )
  except Exception as e:
    response = OutputEvent(
      status_code=500,
      body=None,
      message=str(e)
    )
  return response.to_dict()
