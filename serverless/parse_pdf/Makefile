.PHONY: build

build:
	mkdir -p app/assets/model
	mkdir -p app/assets/mupdf-layer
	wget https://huggingface.co/hantian/yolo-doclaynet/resolve/main/yolov12s-doclaynet.pt?download=true -O app/assets/yolov12s-doclaynet.pt
	wget https://github.com/rwv/mupdf-aws-lambda-layer/releases/download/v1.1.1/mupdf-layer.zip -O app/assets/mupdf-layer.zip
	unzip -o app/assets/mupdf-layer.zip -d app/assets/mupdf-layer
	sam build --use-container

deploy:
	sam deploy --guided