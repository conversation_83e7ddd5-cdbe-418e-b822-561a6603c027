import logging
import boto3

logger = logging.getLogger("pdfTextExtract")
logger.setLevel(logging.DEBUG)
  
def download_file_from_s3(session: boto3.Session, bucket: str, key: str) -> bytes:
  try:
    client = session.client('s3')
    logger.info(f"Downloading file from S3: {bucket}/{key}")
    response = client.get_object(Bucket=bucket, Key=key)
    logger.info(f"Downloaded file from S3: {bucket}/{key}")
    return response['Body'].read()
  except Exception as e:
    raise Exception(f"Failed to download file from S3: {str(e)}")

def upload_file_to_s3(session: boto3.Session, bucket: str, key: str, data: bytes):
  try:
    logger.info(f"Uploading file to S3: {bucket}/{key}")
    client = session.client('s3')
    client.put_object(Bucket=bucket, Key=key, Body=data)
    logger.info(f"Uploaded file to S3: {bucket}/{key}")
  except Exception as e:
    raise Exception(f"Failed to upload file to S3: {str(e)}")

def is_file_exists_in_s3(session: boto3.Session, bucket: str, key: str) -> bool:
  try:
    client = session.client('s3')
    client.head_object(Bucket=bucket, Key=key)
    return True
  except Exception:
    return False
