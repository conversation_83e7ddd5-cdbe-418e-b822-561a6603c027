FROM public.ecr.aws/lambda/python:3.12-arm64

# Install dependencies
COPY requirements.txt ./
COPY requirements_no-deps.txt ./

RUN python3.12 -m pip install --upgrade pip
RUN python3.12 -m pip install --no-deps -r requirements_no-deps.txt -t .
RUN python3.12 -m pip install -r requirements.txt -t .

# Copy assets
COPY assets/mupdf-layer/ /opt/
COPY assets/yolov12s-doclaynet.pt /opt/models/

# Copy application code
COPY lambda_function.py ./
COPY layout_model/ ./layout_model/
COPY processor/ ./processor/
COPY utils/ ./utils/

CMD ["lambda_function.lambda_handler"]
