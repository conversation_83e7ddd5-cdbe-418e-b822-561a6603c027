import boto3
from dataclasses import dataclass
from dataclasses_json import dataclass_json
from typing import List

from processor.parser import extract_text_from_pdf, Document
from processor.openai_service import OpenAIService
from processor.chunker import chunk_paper, DocumentChunk
from utils.utils import (
  logger,
  download_file_from_s3,
  upload_file_to_s3,
  is_file_exists_in_s3
)

@dataclass_json
@dataclass
class S3Access:
  access_key: str
  secret_key: str
  bucket: str

@dataclass_json
@dataclass
class OpenAIAccess:
  api_key: str
  model: str
  text_embedding_model: str

@dataclass_json
@dataclass
class EventData:
  s3_access: S3Access
  openai_access: OpenAIAccess
  file_key: str
  output_key: str

@dataclass_json
@dataclass
class Error:
  __typename__: str
  message: str

  def __init__(self, exception: Exception):
    self.__typename__ = type(exception).__name__
    self.message = str(exception)

@dataclass_json
@dataclass
class Response:
  statusCode: int
  message: str = ""
  error: Error | None = None

@dataclass_json
@dataclass
class Output:
  document: Document
  chunks: List[DocumentChunk]

def extract_event_data(event):
  try:
    return EventData(
      s3_access=S3Access(
        access_key=event["s3_access"]["access_key"],
        secret_key=event["s3_access"]["secret_key"],
        bucket=event["s3_access"]["bucket"]
      ),
      openai_access=OpenAIAccess(
        api_key=event["openai_access"]["api_key"],
        model=event["openai_access"]["model"],
        text_embedding_model=event["openai_access"]["text_embedding_model"]
      ),
      file_key=event["file_key"],
      output_key=event["file_key"][:-4] + ".json"
    )
  except KeyError as e:
    error_message = f"Missing or incorrect key in event data: {str(e)}"
    logger.error(error_message)
    raise ValueError(error_message)

def lambda_handler(event, context):
  try:
    event_data = extract_event_data(event)
    session = boto3.Session(
      aws_access_key_id=event_data.s3_access.access_key,
      aws_secret_access_key=event_data.s3_access.secret_key
    )
    openai_service = OpenAIService(
      api_key=event_data.openai_access.api_key,
      model=event_data.openai_access.model,
      text_embedding_model=event_data.openai_access.text_embedding_model
    )
    if is_file_exists_in_s3(session, event_data.s3_access.bucket, event_data.output_key):
      logger.info(f"File already processed, skipping")
      response = Response(
        statusCode=200,
        message=f'File already processed'
      )
      return response.to_dict()
    
    input_file_bytes = download_file_from_s3(session, event_data.s3_access.bucket, event_data.file_key)
    extracted_document = extract_text_from_pdf(openai_service, input_file_bytes)
    logger.info(f"Extracted text for {len(extracted_document.pages)} pages")
    chunks = chunk_paper(extracted_document, openai_service)
    output = Output(extracted_document, chunks)
    raw_json = output.to_json()
    upload_file_to_s3(session, event_data.s3_access.bucket, event_data.output_key, raw_json.encode("utf-8"))
    
    response = Response(
      statusCode=200,
      message=f'Text extracted successfully'
    )
  except Exception as e:
    logger.error(f"An error occurred: {str(e)}")
    response = Response(
      statusCode=500,
      error=Error(e)
    )

  return response.to_dict()
  