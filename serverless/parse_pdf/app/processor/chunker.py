from dataclasses import dataclass
from typing import List, Optional
import tiktoken
tiktoken_encoder = tiktoken.get_encoding("cl100k_base")

from utils.utils import logger
from processor.parser import Document, Block
from processor.openai_service import OpenAIService

# Configuration constants
DEFAULT_MAX_TOKENS_PER_CHUNK = 512
DEFAULT_MIN_TOKENS_PER_CHUNK = 100
DEFAULT_OVERLAP_TOKENS = 100
DEFAULT_SMALL_CHUNK_THRESHOLD = 0.3

HEADING_LABELS = ["HEADING_0", "HEADING_1", "HEADING_2", "HEADING_3", "HEADING_4", "HEADING_5"]
CONTENT_LABELS = ["PARAGRAPH", "ABSTRACT", "FIGURE_CAPTION", "TABLE", "TABLE_CAPTION", "CODE", "EQUATION", "PAPER_INFO"]
FILTERED_LABELS = ["PAGE-HEADER", "PAGE-FOOTER", "FOOTNOTE", "OTHER"]

@dataclass
class DocumentChunk:
  content: str
  token_count: int
  blocks: List[Block]
  heading_context: List[str] 
  introduce: List[str] = None
  refer: List[str] = None
  summary: str = None
  embedding: List[float] = None

@dataclass
class HeadingNode:
  text: str
  level: int
  children: List['HeadingNode']
  content_blocks: List[Block]

def count_tokens(text: str) -> int:
  if not text or not text.strip():
    return 0
  
  tokens = tiktoken_encoder.encode(text)
  return len(tokens)

def get_heading_level(label: str) -> Optional[int]:
  if label.startswith("HEADING_"):
    try:
      return int(label.split("_")[1])
    except (IndexError, ValueError):
      return None
  return None

def build_heading_hierarchy(text_blocks: List[Block]) -> List[HeadingNode]:
  root_nodes = []
  heading_stack = []  # Stack to track current heading hierarchy

  for block in text_blocks:
    heading_level = get_heading_level(block.label)

    if heading_level is not None:
      new_node = HeadingNode(
        text=block.text.strip(),
        level=heading_level,
        children=[],
        content_blocks=[]
      )

      while heading_stack and heading_stack[-1].level >= heading_level:
        heading_stack.pop()

      if heading_stack:
        heading_stack[-1].children.append(new_node)
      else:
        root_nodes.append(new_node)

      heading_stack.append(new_node)

    else:
      if heading_stack:
        heading_stack[-1].content_blocks.append(block)
      else:
        if not root_nodes or root_nodes[-1].text != "":
          virtual_root = HeadingNode(
            text="",
            level=-1,
            children=[],
            content_blocks=[]
          )
          root_nodes.append(virtual_root)
        root_nodes[-1].content_blocks.append(block)

  return root_nodes

def calculate_overlap_size(max_tokens: int, overlap_tokens: int = None) -> int:
  if overlap_tokens is not None:
    return min(overlap_tokens, max_tokens // 2)  # Cap at half chunk size
  else:
    return 0

def find_overlap_blocks(
  blocks: List[Block],
  target_overlap_tokens: int
) -> List[Block]:
  if not blocks or target_overlap_tokens <= 0:
    return [], 0

  overlap_blocks = []
  overlap_tokens = 0

  for block in reversed(blocks):
    block_tokens = count_tokens(block.text)

    # If adding this block would exceed target significantly, stop
    if overlap_tokens > 0 and (overlap_tokens + block_tokens) > target_overlap_tokens * 1.5:
      break

    overlap_blocks.insert(0, block)
    overlap_tokens += block_tokens
    if overlap_tokens >= target_overlap_tokens:
      break

  return overlap_blocks

def can_merge_chunks(chunk1: DocumentChunk, chunk2: DocumentChunk) -> bool:
  return (chunk1.heading_context == chunk2.heading_context and chunk1.token_count + chunk2.token_count < DEFAULT_MAX_TOKENS_PER_CHUNK)

def merge_small_chunks(
  chunks: List[DocumentChunk],
  max_tokens: int,
  small_chunk_threshold: float = DEFAULT_SMALL_CHUNK_THRESHOLD
) -> List[DocumentChunk]:
  if not chunks:
    return chunks

  threshold_tokens = int(max_tokens * small_chunk_threshold)
  merged_chunks = []

  for chunk in chunks:
    # Check if this chunk is small and can be merged with the previous one
    if (chunk.token_count < threshold_tokens and merged_chunks and can_merge_chunks(merged_chunks[-1], chunk)):
      previous_chunk = merged_chunks[-1]
      merged_chunk = merge_two_chunks(previous_chunk, chunk)
      merged_chunks[-1] = merged_chunk

    else:
      merged_chunks.append(chunk)

  return merged_chunks
 
def merge_two_chunks(
  chunk1: DocumentChunk,
  chunk2: DocumentChunk,
) -> DocumentChunk:
  combined_content = chunk1.content + "\n\n" + chunk2.content
  combined_tokens = chunk1.token_count + chunk2.token_count
  combined_blocks = chunk1.blocks + chunk2.blocks

  return DocumentChunk(
    content=combined_content,
    token_count=combined_tokens,
    heading_context=chunk1.heading_context.copy(), 
    blocks=combined_blocks
  )

def chunk_content_blocks(
  content_blocks: List[Block],
  heading_context: List[str],
  max_tokens: int = DEFAULT_MAX_TOKENS_PER_CHUNK,
  min_tokens: int = DEFAULT_MIN_TOKENS_PER_CHUNK,
  overlap_tokens: int = None,
  merge_small_chunks_enabled: bool = True
) -> List[DocumentChunk]:
  if not content_blocks:
    return []

  # Calculate overlap size
  target_overlap_tokens = calculate_overlap_size(max_tokens, overlap_tokens)

  chunks = []
  current_chunk_blocks = []
  current_chunk_tokens = 0
  chunk_counter = 0
  previous_chunk_blocks = []  # For overlap calculation

  for block in content_blocks:
    block_tokens = count_tokens(block.text)

    if current_chunk_blocks and (current_chunk_tokens + block_tokens > max_tokens):
      if current_chunk_tokens >= min_tokens or not chunks:
        chunk_content = "\n\n".join([b.text.strip() for b in current_chunk_blocks if b.text.strip()])
        if chunk_content.strip(): 
          chunks.append(DocumentChunk(
            content=chunk_content,
            token_count=current_chunk_tokens,
            heading_context=heading_context.copy(),
            blocks=current_chunk_blocks.copy()
          ))
          chunk_counter += 1

          previous_chunk_blocks = current_chunk_blocks.copy()

      if target_overlap_tokens > 0 and previous_chunk_blocks:
        overlap_blocks = find_overlap_blocks(
            previous_chunk_blocks, target_overlap_tokens
        )
        current_chunk_blocks = overlap_blocks + [block]
        current_chunk_tokens = sum(count_tokens(b.text) for b in overlap_blocks) + block_tokens

        if overlap_blocks:
          pass
      else:
        current_chunk_blocks = [block]
        current_chunk_tokens = block_tokens
    else:
      current_chunk_blocks.append(block)
      current_chunk_tokens += block_tokens

  # Handle remaining blocks
  if current_chunk_blocks:
    chunk_content = "\n\n".join([b.text.strip() for b in current_chunk_blocks if b.text.strip()])
    if chunk_content.strip():
      if target_overlap_tokens > 0 and previous_chunk_blocks and chunks:
        overlap_blocks = find_overlap_blocks(
          previous_chunk_blocks, target_overlap_tokens
        )

      chunks.append(DocumentChunk(
        content=chunk_content,
        token_count=current_chunk_tokens,
        heading_context=heading_context.copy(),
        blocks=current_chunk_blocks.copy()
      ))

  if target_overlap_tokens > 0:
    for i in range(1, len(chunks)):
      previous_chunk = chunks[i-1]

      # Find actual overlap between consecutive chunks
      overlap_blocks = find_overlap_blocks(
        previous_chunk.blocks, target_overlap_tokens
      )

  # Merge small chunks if enabled
  if merge_small_chunks_enabled and chunks:
    chunks = merge_small_chunks(chunks, max_tokens)

  return chunks

def calculate_node_total_tokens(node: HeadingNode) -> int:
  total_tokens = 0

  for block in node.content_blocks:
    total_tokens += count_tokens(block.text)

  for child in node.children:
    total_tokens += calculate_node_total_tokens(child)

  return total_tokens

def collect_all_node_blocks(node: HeadingNode) -> List[Block]:
  all_blocks = node.content_blocks
  for child in node.children:
    all_blocks.extend(collect_all_node_blocks(child))

  return all_blocks

def recursive_chunk_heading_node(
  node: HeadingNode,
  heading_context: List[str],
  max_tokens: int = DEFAULT_MAX_TOKENS_PER_CHUNK,
  min_tokens: int = DEFAULT_MIN_TOKENS_PER_CHUNK,
  overlap_tokens: int = None,
  overlap_ratio: float = None,
  merge_small_chunks_enabled: bool = True
) -> List[DocumentChunk]:
  chunks = []

  # Update heading context with current node (if it has text)
  current_context = heading_context.copy()
  if node.text.strip():
      current_context.append(node.text.strip())

  # Calculate total tokens for this node (content + all children)
  total_node_tokens = calculate_node_total_tokens(node)

  # If the entire node fits perfectly in one chunk, create a single chunk
  if total_node_tokens <= max_tokens and total_node_tokens >= min_tokens:
    all_blocks = collect_all_node_blocks(node)
    if all_blocks:
      chunk_content = "\n\n".join([b.text.strip() for b in all_blocks if b.text.strip()])
      if chunk_content.strip():
        chunk = DocumentChunk(
          content=chunk_content,
          token_count=total_node_tokens,
          heading_context=current_context.copy(),
          blocks=all_blocks.copy()
        )
        chunks.append(chunk)
    return chunks

  # Otherwise, process content and children separately (original behavior)
  # Chunk the content blocks under this heading
  if node.content_blocks:
    content_chunks = chunk_content_blocks(
      node.content_blocks,
      current_context,
      max_tokens,
      min_tokens,
      overlap_tokens,
      merge_small_chunks_enabled
    )
    chunks.extend(content_chunks)

  # Recursively process children
  for child in node.children:
    child_chunks = recursive_chunk_heading_node(
      child,
      current_context,
      max_tokens,
      min_tokens,
      overlap_tokens,
      overlap_ratio,
      merge_small_chunks_enabled
    )
    chunks.extend(child_chunks)

  return chunks

def chunk_document_hierarchically(
  text_blocks: List[Block],
  max_tokens: int = DEFAULT_MAX_TOKENS_PER_CHUNK,
  min_tokens: int = DEFAULT_MIN_TOKENS_PER_CHUNK,
  overlap_tokens: int = None,
  overlap_ratio: float = None,
  merge_small_chunks_enabled: bool = True
) -> List[DocumentChunk]:
  if not text_blocks:
    return []

  heading_nodes = build_heading_hierarchy(text_blocks)

  all_chunks = []
  for root_node in heading_nodes:
    node_chunks = recursive_chunk_heading_node(
      root_node,
      [],
      max_tokens,
      min_tokens,
      overlap_tokens,
      overlap_ratio,
      merge_small_chunks_enabled
    )
    all_chunks.extend(node_chunks)

  return all_chunks

def _get_chunk_text(chunk: DocumentChunk) -> str:
  context_text = ""
  for i, context in enumerate(chunk.heading_context):
    context_text += f"{'#' * (i + 1)} {context}\n\n"
  
  return context_text + chunk.content

def _generate_embeddings(chunks: List[DocumentChunk], openai_service: OpenAIService):
  texts = [_get_chunk_text(chunk) for chunk in chunks]
  embeddings = openai_service.generate_text_embeddings(texts)
  for i, embedding in enumerate(embeddings):
    chunks[i].embedding = embedding

def chunk_paper(
  doc: Document,
  openai_service: OpenAIService,
  max_tokens: int = DEFAULT_MAX_TOKENS_PER_CHUNK,
  min_tokens: int = DEFAULT_MIN_TOKENS_PER_CHUNK,
  overlap_tokens: int = DEFAULT_OVERLAP_TOKENS,
  overlap_ratio: float = None,
  merge_small_chunks_enabled: bool = True
):
  blocks: List[Block] = []
  for page in doc.pages:
    blocks.extend(page.blocks)
    
  logger.info(f"Chunking paper with {len(blocks)} blocks")

  filtered_blocks = [
    block for block in blocks
    if block.label not in FILTERED_LABELS
  ]

  chunks = chunk_document_hierarchically(
    filtered_blocks,
    max_tokens,
    min_tokens,
    overlap_tokens,
    overlap_ratio,
    merge_small_chunks_enabled
  )
  
  _generate_embeddings(chunks, openai_service)
  
  logger.info(f"Created {len(chunks)} chunks")
  
  return chunks

