import os
from openai import OpenAI
from utils.utils import logger

from processor.prompts import PROMPT_HEADINGS_CLASSIFICATION


class OpenAIService:
  def __init__(self, api_key: str, model: str, text_embedding_model: str):
    self.api_key = api_key
    self.model = model
    self.text_embedding_model = text_embedding_model
    self.client = OpenAI(api_key=api_key)
  
  def call_text_response_once(self, input_text, temperature=0.5):
    logger.info(f"Calling OpenAI API with model {self.model} and temperature {temperature}")
    input_text = input_text.strip()
    return self.client.responses.create(
      model=self.model,
      input=input_text.strip(),
      temperature=temperature,
    )

  def _parse_list(self, str):
    index = 1
    result = []
    for item in str.split("\n"):
      item = item.strip()
      if item.startswith(f"{index}: "):
        item = item[len(f"{index}: "):].strip()
        result.append(item)
        index += 1
      
    return result

  def classify_headings(self, input: list[str], temperature=0):
    try:
      prompt_input = "\n".join([f"{i}: {text}" for i, text in enumerate(input, 1)])
      prompt = PROMPT_HEADINGS_CLASSIFICATION.build_prompt(input=prompt_input)
      response = self.call_text_response_once(prompt, temperature=temperature)
      output = response.output[0].content[0].text
      output = self._parse_list(output)
      if len(output) != len(input):
        logger.error(f"Output length does not match input length, expected {len(input)}, got {len(output)}")
        print(f"Input:\n {prompt_input}")
        print(f"Output:\n {output}")
      return output
    except Exception as e:
      logger.error(f"Error in classify_headings: {e}")
      raise e
    
  def generate_text_embeddings(self, input: str | list[str]):
    logger.info(f"Calling OpenAI API to generate text embeddings with model {self.text_embedding_model}")
    is_str = False
    if type(input) == str:
      input = [input]
      is_str = True
    response = self.client.embeddings.create(
      model=self.text_embedding_model,
      input=input,
    )
    if is_str:
      return response.data[0].embedding
    else: 
      return [item.embedding for item in response.data]

