from dataclasses import dataclass

PROMPT_DIR = "processor/prompts/"

@dataclass
class Prompt:  
  file: str = ""
  params: str = ""
  prompt_data: str = None

  def load_prompt(self) -> str:
    try:
      with open(self.file, "r") as file:
        prompt = file.read()
      return prompt
    except FileNotFoundError:
      raise ValueError(f"Prompt file not found: {self.file}")
    
  def __post_init__(self):
    self.file = PROMPT_DIR + self.file + ".txt"
    self.prompt_data = self.load_prompt()
    
  def build_prompt(self, **kwargs) -> str:
    prompt = self.prompt_data
    params_list = self.params.split(",")
    
    for key in kwargs:
      if key not in params_list:
        raise ValueError(f"Unknown parameter: {key}")
    
    for param in params_list:
      if param not in kwargs:
        raise ValueError(f"Missing parameter: {param}")
      prompt = prompt.replace(f"<{param}>", kwargs[param])
    
    return prompt

PROMPT_HEADINGS_CLASSIFICATION = Prompt(
  file="headings_classification",
  params="input"
)