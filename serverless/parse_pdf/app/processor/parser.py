from PIL import ImageDraw
from dataclasses import dataclass
from dataclasses_json import dataclass_json
import fitz

from utils.utils import logger
from layout_model import layout_model
from processor.openai_service import OpenAIService

@dataclass_json
@dataclass
class BoundingBox:
  x0: float
  y0: float
  x1: float
  y1: float

  def normalize(self, image_width: int, image_height: int) -> "BoundingBox":
    return BoundingBox(self.x0 / image_width, self.y0 / image_height, self.x1 / image_width, self.y1 / image_height)

  def denormalize(self, image_width: int, image_height: int) -> "BoundingBox":
    return BoundingBox(self.x0 * image_width, self.y0 * image_height, self.x1 * image_width, self.y1 * image_height)
  
  @property
  def as_tuple(self):
    return (self.x0, self.y0, self.x1, self.y1)

  @property
  def area(self):
    return (self.x1 - self.x0) * (self.y1 - self.y0)
  
  def merge(self, other: "BoundingBox"):
    return BoundingBox(min(self.x0, other.x0), min(self.y0, other.y0), max(self.x1, other.x1), max(self.y1, other.y1))
  
  def intersection(self, other: "BoundingBox"):
    x0 = max(self.x0, other.x0)
    y0 = max(self.y0, other.y0)
    x1 = min(self.x1, other.x1)
    y1 = min(self.y1, other.y1)
    if x0 > x1 or y0 > y1:
      return None
    return BoundingBox(x0, y0, x1, y1)
  
  def intersection_area(self, other: "BoundingBox"):
    intersection = self.intersection(other)
    if intersection is None:
      return 0
    return intersection.area
  
  @staticmethod
  def from_layout_box(layout_box: layout_model.LayoutBox):
    return BoundingBox(layout_box.x0, layout_box.y0, layout_box.x1, layout_box.y1)
  
  @staticmethod
  def from_tuple(tuple):
    return BoundingBox(tuple[0], tuple[1], tuple[2], tuple[3])
  
@dataclass_json
@dataclass
class Word:
  text: str
  bbox: BoundingBox
  
  def __init__(self, text: str, bbox: BoundingBox):
    self.text = text.strip()
    self.bbox = bbox

@dataclass_json
@dataclass
class Line:
  words: list[Word]
  bbox: BoundingBox

  def __init__(self, words: list[Word]):
    self.words = words
    if len(words) == 0:
      raise ValueError("Line must contain at least one Word")
    self.bbox = words[0].bbox
    for word in words[1:]:
      self.bbox = self.bbox.merge(word.bbox)
    
  @property
  def text(self): 
    return " ".join([word.text for word in self.words])

@dataclass_json
@dataclass
class Text:
  lines: list[Line]
  bbox: BoundingBox
  
  def __init__(self, lines: list[Line]):
    self.lines = lines
    if len(lines) == 0:
      raise ValueError("Text must contain at least one Line")
    self.bbox = lines[0].bbox
    for line in lines[1:]:
      self.bbox = self.bbox.merge(line.bbox)
    
  @property
  def text(self):
    return " ".join([line.text for line in self.lines])

@dataclass_json
@dataclass
class Block:
  texts: list[Text]
  label: str
  bbox: BoundingBox
  
  def __init__(self, texts: list[Text], label: layout_model.Label, bbox: BoundingBox | None = None):
    self.texts = texts
    self.label = label.name
    if len(texts) == 0 and label != layout_model.Label.PICTURE:
      raise ValueError("Block except PICTURE must contain at least one Text")
    if bbox is not None:
      self.bbox = bbox
    else:
      self.bbox = texts[0].bbox
      for text in texts[1:]:
        self.bbox = self.bbox.merge(text.bbox)
    
  @property
  def text(self):
    return "\n".join([text.text for text in self.texts])

@dataclass_json
@dataclass
class Page:
  blocks: list[Block]
  
@dataclass_json
@dataclass
class Document:
  pages: list[Page]

def _average(numbers):
  return sum(numbers) / len(numbers)

def _group_contiguous_lines_to_texts(lines_with_pdf_block_number_with_index: list[tuple[Line, int, int]]) -> list[tuple[Text, float]]:
  results = []
  current_group = []
  for line_with_pdf_block_number_with_index in lines_with_pdf_block_number_with_index:
    _, pdf_block_number, _ = line_with_pdf_block_number_with_index
    if len(current_group) == 0 or pdf_block_number == current_group[0][1]:
      current_group.append(line_with_pdf_block_number_with_index)
    else:
      results.append((Text([line[0] for line in current_group]), _average([line[2] for line in current_group])))
      current_group = [line_with_pdf_block_number_with_index]
  if len(current_group) > 0:
    results.append((Text([line[0] for line in current_group]), _average([line[2] for line in current_group])))
  return results

def _extract_text_from_pdf_page(page: fitz.Page, page_layout: layout_model.LayoutPage):
  # get all text blocks
  image = layout_model._pixmap_to_image(page.get_pixmap()) # type: ignore
  
  # get all line blocks from pdf
  lines_with_pdf_block_number = []
  for pdf_block in page.get_text("dict")["blocks"]: # type: ignore
    if pdf_block["type"] != 0: # ignore non-text blocks
      continue
    for pdf_line in pdf_block["lines"]:
      words = []
      for pdf_span in pdf_line["spans"]:
        if pdf_span["text"].strip() == "": # ignore empty words
          continue
        word = Word(pdf_span["text"], BoundingBox.from_tuple(pdf_span["bbox"]).normalize(image.width, image.height))
        words.append(word)
      if len(words) == 0:
        continue
      lines_with_pdf_block_number.append((Line(words), pdf_block["number"]))
      
  # map line blocks to detected layout boxes
  # heuristic: choose the box with the highest (intersection area) * (box confidence)
  box_lines = [[] for _ in page_layout.layout_boxes]
  unassigned_lines = []
  for i, line_with_pdf_block_number in enumerate(lines_with_pdf_block_number):
    line = line_with_pdf_block_number[0]
    best_box = None
    best_score = 0
    for box in page_layout.layout_boxes:
      if box.label == layout_model.Label.PICTURE:
        continue
      score = line.bbox.intersection_area(BoundingBox.from_layout_box(box)) * box.confidence
      if score > best_score:
        best_box = box
        best_score = score
    if best_box is not None:
      box_lines[page_layout.layout_boxes.index(best_box)].append(i)
    else:
      unassigned_lines.append(i)
    
  # merge assigned lines to get blocks
  # estimate the block number by the average of line indexes
  blocks_with_estimated_number = []
  for layout_box, line_indexes in zip(page_layout.layout_boxes, box_lines):
    if len(line_indexes) == 0:
      continue
    texts = _group_contiguous_lines_to_texts([lines_with_pdf_block_number[i] + (i,) for i in line_indexes])
    blocks_with_estimated_number.append((Block([text[0] for text in texts], layout_box.label), _average(line_indexes)))  

  # for unassigned lines, also group contiguous lines within a pdf_block together
  # then consider them as a block with label "TEXT"
  unassigned_texts = _group_contiguous_lines_to_texts([lines_with_pdf_block_number[i] + (i,) for i in unassigned_lines])
  blocks_with_estimated_number.extend([(Block([text[0]], layout_model.Label.TEXT), text[1]) for text in unassigned_texts])
  
  # sort blocks by estimated number and add picture blocks
  blocks_with_estimated_number.sort(key=lambda x: x[1])
  blocks = [block[0] for block in blocks_with_estimated_number]
  for box in page_layout.layout_boxes:
    if box.label != layout_model.Label.PICTURE:
      continue
    blocks.append(Block([], box.label, BoundingBox.from_layout_box(box)))

  return Page(blocks)

def _visualize(doc: fitz.Document, extraction_result: Document, pages: list[int] = []):
  if len(pages) == 0:
    pages = list(range(doc.page_count))
  for page_num in pages:
    page = doc.load_page(page_num)
    image = layout_model._pixmap_to_image(page.get_pixmap()) # type: ignore
    draw = ImageDraw.Draw(image)
    for i, block in enumerate(extraction_result.pages[page_num].blocks):
      draw.rectangle(block.bbox.denormalize(image.width, image.height).as_tuple, outline="red")
      draw.text((block.bbox.x0 * image.width, block.bbox.y0 * image.height - 10), f"{block.label} - {i}", fill="red")
    image.show()

class PdfFileException(Exception):
  pass

class PasswordProtectedPdfException(PdfFileException):
  pass

class InvalidPdfException(PdfFileException):
  pass

def _open_pdf(pdf_file_bytes: bytes) -> fitz.Document:
  try:
    logger.info(f"Checking PDF file")
    if not pdf_file_bytes.startswith(b"%PDF-"):
      raise InvalidPdfException("Not a PDF file")
    doc = fitz.open(stream=pdf_file_bytes)
    if doc.needs_pass:
      raise PasswordProtectedPdfException("Password protected file")
    # try to load all pages
    for page in doc.pages():
      page.get_text()
    return doc
  except PdfFileException:
    raise
  except Exception as e:
    raise InvalidPdfException(f"Failed to open PDF file: {str(e)}")

def _classify_headings(openai_service: OpenAIService, doc: Document) -> Document:
  headings = []
  for page in doc.pages:
    for block in page.blocks:
      if block.label == layout_model.Label.TITLE.name or block.label == layout_model.Label.SECTION_HEADER.name:
        headings.append(block.text)
  
  if len(headings) == 0:
    return doc
  
  levels = openai_service.classify_headings(headings)
  index = 0
  for page in doc.pages:
    for block in page.blocks:
      if block.label == layout_model.Label.TITLE.name or block.label == layout_model.Label.SECTION_HEADER.name:
        block.label = f"HEADING_{levels[index]}"
        index += 1
        assert index <= len(levels), "Number of headings does not match"
  assert index == len(levels), "Number of headings does not match"
  return doc

def extract_text_from_pdf(openai_service: OpenAIService, pdf_file_bytes: bytes) -> Document:
  document = _open_pdf(pdf_file_bytes)
  layout_prediction = layout_model.predict_layout(document)
  pages = []
  for pdf_page, layout in zip(list(document.pages()), layout_prediction.layout_pages):
    pages.append(_extract_text_from_pdf_page(pdf_page, layout))
    logger.info(f"Page {pdf_page.number} processed with {len(pages[-1].blocks)} blocks")
  result = Document(pages)
  result = _classify_headings(openai_service, result)
  logger.info(f"Document parsed successfully with {len(result.pages)} pages")
  # _visualize(document, Document(pages))
  return result

