from dataclasses import dataclass
from dataclasses_json import dataclass_json
from PIL import Image
import fitz
from ultralytics import YOL<PERSON>
from ultralytics.engine.results import Results
from enum import IntEnum

from utils.utils import logger

MODEL_DIR = "/opt/models/yolov12s-doclaynet.pt"
IOU = 0.3
BATCH_SIZE = 4
DPI = 200

model = YOLO(MODEL_DIR, task="detect")

class Label(IntEnum):
  CAPTION = 0
  FOOTNOTE = 1
  FORMULA = 2
  LIST_ITEM = 3
  PAGE_FOOTER = 4
  PAGE_HEADER = 5
  PICTURE = 6
  SECTION_HEADER = 7
  TABLE = 8
  TEXT = 9
  TITLE = 10

@dataclass_json
@dataclass
class LayoutBox:
  x0: float
  y0: float
  x1: float
  y1: float
  label: Label
  confidence: float

@dataclass_json
@dataclass
class LayoutPage:
  layout_boxes: list[LayoutBox]
  model_output: Results
  @staticmethod
  def from_model_output(output: Results) -> "LayoutPage":
    boxes = []
    for label_num, box, confidence in zip(output.boxes.cls, output.boxes.xyxyn, output.boxes.conf): # type: ignore
      x0, y0, x1, y1 = box.numpy()
      label = Label(label_num.item())
      boxes.append(LayoutBox(float(x0), float(y0), float(x1), float(y1), label, float(confidence)))
    return LayoutPage(boxes, output)

@dataclass_json
@dataclass
class LayoutPrediction:
  layout_pages: list[LayoutPage]

def _pixmap_to_image(pixmap: fitz.Pixmap) -> Image.Image:
  image = Image.frombytes('RGB', (pixmap.width, pixmap.height), pixmap.samples)
  return image

def predict_layout(document: fitz.Document) -> LayoutPrediction:
  logger.info("Converting document to images (page count: %d)", document.page_count)
  page_images = [_pixmap_to_image(page.get_pixmap(dpi=DPI)) for page in document.pages()]
  
  logger.info("Predicting page layout")
  page_results = []
  for i in range(0, len(page_images), BATCH_SIZE):
    images = page_images[i:i+BATCH_SIZE]
    model_output = model.predict(images, verbose=False, device="cpu", iou=IOU)
    batch_result = [LayoutPage.from_model_output(_) for _ in model_output]
    for page_num, page in enumerate(batch_result, i):
      logger.info(f"Page {page_num}: {page.model_output.verbose()}")
    page_results.extend(batch_result)
    
  return LayoutPrediction(page_results)
