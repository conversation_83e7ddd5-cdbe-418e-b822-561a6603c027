import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware

from core import logger
from web_server.controller import file_manager, query_handler, chat_handler

app = FastAPI()

# Add validation error handler
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    logger.error(f"Validation error for {request.method} {request.url}: {exc.errors()}")
    # Convert bytes to string for JSON serialization
    body_str = exc.body.decode('utf-8') if isinstance(exc.body, bytes) else str(exc.body)
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors(), "body": body_str}
    )

# Add traditional CORS middleware as backup
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
    expose_headers=["*"], # Expose all headers
)

# Allow CORS from anywhere - Custom CORS middleware
@app.middleware("http")
async def cors_middleware(request: Request, call_next):
  # Handle preflight requests
  if request.method == "OPTIONS":
      return Response(
          content="",
          headers={
              "Access-Control-Allow-Origin": "*",
              "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH",
              "Access-Control-Allow-Headers": "*",
              "Access-Control-Allow-Credentials": "true",
              "Access-Control-Max-Age": "86400",
          }
      )

  response = await call_next(request)

  # Add CORS headers to all responses
  response.headers["Access-Control-Allow-Origin"] = "*"
  response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH"
  response.headers["Access-Control-Allow-Headers"] = "*"
  response.headers["Access-Control-Allow-Credentials"] = "true"
  response.headers["Access-Control-Expose-Headers"] = "*"

  return response

@app.get("/")
def read_root():
  return {"message": "Hello, world!"}

@app.get("/papers/{paper_id}")
def get_paper_info(paper_id: str):
  return file_manager.get_paper_info(paper_id)

@app.get("/getUploadPaperUrl/{file_name}")
def get_upload_url(file_name: str):
  return file_manager.generate_upload_paper_url(file_name)

@app.post("/notifyPaperUploaded/{file_id}")
def process_uploaded_paper(file_id: str):
  return file_manager.process_uploaded_paper(file_id)

@app.get("/findSimilarChunks")
def find_similar_chunks(query: str, paper_ids: str, top_k: int = 10):
  paper_ids = paper_ids.split(",")
  return query_handler.find_similar_chunks(query, paper_ids, top_k)

@app.delete("/papers/{paper_id}")
def delete_paper(paper_id: str):
  return file_manager.delete_paper(paper_id)

@app.get("/papers/{paper_id}/view-url")
def get_paper_view_url(paper_id: str):
  return file_manager.get_paper_view_url(paper_id)

@app.post("/chat")
async def chat_stream(request: chat_handler.ChatRequest):
  return await chat_handler.chat_stream(request)

def main():
  uvicorn.run(app, host="0.0.0.0", port=8000)

if __name__ == "__main__":
  main()
