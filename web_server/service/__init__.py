from .s3_service import S3Service
from .qdrant_service import QdrantService
from .lambda_service import LambdaService

s3_service = S3Service()
qdrant_service = QdrantService()
lambda_service = LambdaService()

def delete_chunks_by_paper_id(paper_id: str):
  from qdrant_client.http.models import Filter, FieldCondition, MatchValue
  filter_condition = Filter(
    must=[
      FieldCondition(
        key="paper_id",
        match=MatchValue(value=paper_id)
      )
    ]
  )
  return qdrant_service.delete_by_filter("chunk", filter_condition)
