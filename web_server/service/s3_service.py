import boto3

from core import config, logger

class S3Service:
  def __init__(self):
    self.access_key = config.getenv("AWS_ACCESS_KEY_ID")
    self.secret_key = config.getenv("AWS_SECRET_ACCESS_KEY")
    self.region = config.getenv("AWS_REGION")
    self.bucket = config.getenv("S3_BUCKET")
    self.session = boto3.Session(
      aws_access_key_id=self.access_key,
      aws_secret_access_key=self.secret_key,
      region_name=self.region
    )
    self.s3client = self.session.client("s3")
    self._check_auth()

  def _check_auth(self):
    try:
      logger.info("Checking S3 authentication")
      self.s3client.list_buckets()
    except Exception as e:
      logger.error(f"Failed to connect to S3: {e}")
      raise e

  def upload_file_bytes(self, key: str, data: bytes):
    logger.info(f"Uploading file to S3: {key}")
    self.s3client.put_object(
      Bucket=self.bucket,
      Key=key,
      Body=data
    )
  
  def download_file_bytes(self, key: str) -> bytes:
    response = self.s3client.get_object(
      Bucket=self.bucket,
      Key=key
    )
    return response["Body"].read()

  def get_presigned_url_for_upload(self, key: str, expiration=3600) -> str:
    return self.s3client.generate_presigned_url(
      "put_object",
      Params={
        "Bucket": self.bucket,
        "Key": key,
        "ContentType": "application/pdf",
      },
      ExpiresIn=expiration
    )
    
  def get_presigned_url_for_download(self, key: str, expiration=3600) -> str:
    return self.s3client.generate_presigned_url(
      "get_object",
      Params={"Bucket": self.bucket, "Key": key},
      ExpiresIn=expiration
    )
