from qdrant_client import QdrantClient

from core import config, logger

COLLECTIONS = [
  {
    "collection_name": "paper",
    "vectors_config": {
      "size": 1,
      "distance": "Dot"
    }
  },
  {
    "collection_name": "chunk",
    "vectors_config": {
      "size": 1536,
      "distance": "Cosine"
    }
  }
]

INDEXES = [
  {
    "collection_name": "chunk",
    "field_name": "paper_id",
    "field_type": "uuid"
  }
]

class QdrantService:
  def __init__(self):
    self.url = config.getenv("QDRANT_URL")
    self.api_key = config.getenv("QDRANT_API_KEY")
    logger.info(f"Connecting to Qdrant at {self.url}")
    self.client = QdrantClient(url=self.url, api_key=self.api_key)
    logger.info("Connected to Qdrant")
    self._init_collections()
  
  def _get_collections(self):
    return self.client.get_collections()
  
  def _init_collections(self):
    all_collections = self._get_collections().collections
    for collection in COLLECTIONS:
      if collection["collection_name"] not in [_.name for _ in all_collections]:
        logger.info(f"Creating collection {collection['collection_name']}")
        self.client.recreate_collection(**collection)
    
    for index in INDEXES:
      logger.info(f"Creating index {index['field_name']} for collection {index['collection_name']}")
      self.client.create_payload_index(**index)
    
  
  def upsert(self, collection_name: str, points):
    self.client.upsert(collection_name, points)
  
  def retrieve(self, collection_name: str, ids):
    return self.client.retrieve(collection_name, ids)
  
  def delete(self, collection_name: str, ids):
    self.client.delete(collection_name, ids)
  
  def search(self, collection_name: str, query_vector, query_filter=None, search_params=None, limit=10):
    return self.client.search(collection_name, query_vector, query_filter=query_filter, search_params=search_params, limit=limit)

  def delete_points(self, collection_name: str, point_ids):
    from qdrant_client.http.models import PointIdsList
    self.client.delete(collection_name, PointIdsList(points=point_ids))

  def delete_by_filter(self, collection_name: str, filter_condition):
    self.client.delete(collection_name, filter_condition)
  