import asyncio
import j<PERSON>
from typing import List, Optional
from pydantic import BaseModel
from fastapi.responses import StreamingResponse
from core import logger, openai_service, prompts
from web_server.controller import query_handler
from web_server.model import Paper
from fastapi.responses import StreamingResponse

# Chat request model
class ChatRequest(BaseModel):
    message: str
    paper_ids: Optional[List[str]] = []
    trace_id: Optional[str] = None

    class Config:
        # Allow extra fields to be ignored
        extra = "ignore"

async def chat_stream(request: ChatRequest):
  async def generate_response():
    chunks_data = []
    context = ""

    if request.paper_ids:
      try:
        chunks_with_scores = query_handler.find_similar_chunks_with_scores(
          request.message,
          request.paper_ids,
          top_k=5
        )

        # Prepare chunks data for client
        for chunk, score in chunks_with_scores:
          # Get paper name for display
          paper_name = chunk.paper_id  # fallback to ID
          try:
            paper = Paper.from_id(chunk.paper_id)
            paper_name = paper.name
          except Exception as e:
            logger.warning(f"Could not fetch paper name for {chunk.paper_id}: {e}")

          chunks_data.append({
            "id": chunk.id,
            "paper_id": chunk.paper_id,
            "paper_name": paper_name,
            "content": chunk.content,
            "heading_context": chunk.heading_context,
            "similarity_score": score
          })

        # Format context for answer generation prompt
        context_chunks = []
        for i, (chunk, score) in enumerate(chunks_with_scores, 1):
          context_chunks.append(f"Chunk {i}:\n{chunk.content}")

        context = "\n\n".join(context_chunks)
        logger.info(f"Retrieved {len(chunks_with_scores)} relevant chunks for query")
      except Exception as e:
        logger.error(f"Error retrieving chunks: {e}")

    # Send chunks data first as JSON
    chunks_json = json.dumps({"chunks": chunks_data})
    yield f"[CHUNKS]{chunks_json}[/CHUNKS]\n"

    # Generate AI response using the answer generation prompt
    if context:
        try:
            # Build the answer generation prompt
            answer_prompt = prompts.PROMPT_ANSWER_GENERATION.build_prompt(
                question=request.message,
                context=context
            )

            logger.info(f"Built answer generation prompt for question: '{request.message[:50]}...'")
            logger.debug(f"Context length: {len(context)} characters")

            # Get AI response
            logger.info("Generating AI response using answer generation prompt")
            response_text = openai_service.generate_chat_completion(answer_prompt, temperature=0.5)

            logger.info(f"Generated AI response with length: {len(response_text)} characters")

        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            # Fallback to simple response if AI generation fails
            response_text = f"""I found relevant information in the document, but encountered an error generating a response. Here's the raw context:

{context}

**Your question:** {request.message}
"""
    else:
        response_text = f"""I don't have any PDF documents selected to provide context.

**Your question:** {request.message}

Please upload and select some PDFs to get relevant information for your questions.
"""

    # Stream each character with typing effect
    for c in response_text:
      yield ("[EL]" if c == "\n" else c) + "\n"
      await asyncio.sleep(0.03)  # Slower for better typing effect
    yield "[END]" + "\n"
    
  return StreamingResponse(
    generate_response(),
    media_type="text/plain",
    headers={
      "Cache-Control": "no-cache",
      "Connection": "keep-alive",
    }
  )
