import threading
import json

from fastapi import HTT<PERSON>Exception

from core import logger
from web_server.model import Paper, PaperStatus, Chunk
from web_server.service import s3_service
from web_server.controller import lambda_caller

TEMP_UPLOAD_DIR = "temp-upload"
PAPER_DIR = "papers"

def generate_upload_paper_url(file_name: str, expiration=3600) -> str:
  try:
    if not file_name.endswith(".pdf"):
      raise HTTPException(status_code=400, detail="Only PDF files are supported")
    paper = Paper(file_name)
    paper.status = PaperStatus.UPLOADING
    paper.save()
    key = f"{TEMP_UPLOAD_DIR}/{paper.id}"
    return {
      "paper_id": paper.to_dict()['id'],
      "upload_url": s3_service.get_presigned_url_for_upload(key, expiration)
    }
  except Exception as e:
    raise HTTPException(status_code=500, detail=str(e))

def process_uploaded_paper(file_id: str):
  try:
    paper = Paper.from_id(file_id)
    if paper.status != PaperStatus.UPLOADING:
      raise HTTPException(status_code=400, detail="Paper status is not uploading")
    
    response = lambda_caller.invoke_post_upload_process_lambda(f"{TEMP_UPLOAD_DIR}/{paper.id}", PAPER_DIR)
    if response.status_code != 200:
      raise HTTPException(status_code=response.status_code, detail=response.message)
    paper.checksum = response.body.file_sha256
    paper.status = PaperStatus.UPLOADED
    paper.save()
    
    start_index_paper_process(paper, response.body.file_existed)
    
    return {"message": "Paper uploaded successfully and indexing is in progress"}
    
  except ValueError as e:
    raise HTTPException(status_code=400, detail=str(e))
  except HTTPException as e:
    raise e

def get_paper_info(paper_id: str):
  try:
    paper = Paper.from_id(paper_id)
    return paper.to_dict()
  except ValueError as e:
    raise HTTPException(status_code=400, detail=str(e))

def index_paper(paper: Paper, file_existed: bool):
  logger.info(f"Indexing paper {paper.id}")
  paper.status = PaperStatus.PROCESSING
  paper.save()
  result = lambda_caller.invoke_parse_pdf_lambda(f"{PAPER_DIR}/{paper.checksum}.pdf")
  if result.statusCode != 200:
    logger.error(f"Error parsing paper {paper.id}: {result.error.message}")
    paper.status = PaperStatus.ERROR
    paper.save()
    return
  
  data = s3_service.download_file_bytes(f"{PAPER_DIR}/{paper.checksum}.json")
  data = json.loads(data.decode("utf-8"))
  for chunk in data["chunks"]:
    chunk = Chunk(
      paper_id=paper.id,
      content=chunk["content"],
      heading_context=chunk["heading_context"],
      embedding=chunk["embedding"]
    )
    chunk.save()
  
  logger.info(f"Paper {paper.id} parsed successfully")
  paper.status = PaperStatus.PROCESSED
  paper.save()

def start_index_paper_process(paper: Paper, file_existed: bool):
  thread = threading.Thread(target=index_paper, args=(paper, file_existed))
  thread.start()




def delete_paper(paper_id: str):
  try:
    paper = Paper.from_id(paper_id)
    # Delete chunks from vector database
    from web_server.service import delete_chunks_by_paper_id
    delete_chunks_by_paper_id(paper_id)

    # Delete paper record
    paper.delete()

    return {"message": "Paper deleted successfully"}
  except ValueError as e:
    raise HTTPException(status_code=400, detail=str(e))
  except Exception as e:
    raise HTTPException(status_code=500, detail=str(e))

def get_paper_view_url(paper_id: str):
  try:
    paper = Paper.from_id(paper_id)

    # Check if paper is ready for viewing
    if paper.status != PaperStatus.PROCESSED:
      raise HTTPException(status_code=400, detail="Paper is not ready for viewing")

    # Generate S3 key for the PDF file
    pdf_key = f"{PAPER_DIR}/{paper.checksum}.pdf"

    # Generate presigned URL for viewing (valid for 1 hour)
    view_url = s3_service.get_presigned_url_for_download(pdf_key, expiration=3600)

    return {
      "paper_id": paper_id,
      "view_url": view_url,
      "expires_in": 3600
    }
  except ValueError as e:
    raise HTTPException(status_code=400, detail=str(e))
  except Exception as e:
    raise HTTPException(status_code=500, detail=str(e))