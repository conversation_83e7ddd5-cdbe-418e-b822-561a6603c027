from dataclasses import dataclass
from dataclasses_json import dataclass_json

from core import logger, config
from web_server.service import lambda_service, s3_service

POST_UPLOAD_FUNCTION_NAME = "post-upload-process"
PARSE_PDF_FUNCTION_NAME = "parse-pdf"

@dataclass_json
@dataclass
class PostUploadLambdaOutputBody:
  file_sha256: str
  file_existed: bool

@dataclass_json
@dataclass
class PostUploadLambdaOutputEvent:
  status_code: int
  body: PostUploadLambdaOutputBody | None
  message: str
  
@dataclass_json
@dataclass
class Error:
  __typename__: str
  message: str
  
@dataclass_json
@dataclass
class ParsePdfLambdaOutputEvent:
  statusCode: int
  message: str = ""
  error: Error | None = None

def invoke_post_upload_process_lambda(uploaded_file_key: str, file_storage_folder: str) -> PostUploadLambdaOutputEvent:
  try:
    logger.info(f"Invoking post upload process lambda for file {uploaded_file_key}")
    response = lambda_service.invoke(POST_UPLOAD_FUNCTION_NAME, {
        "s3_access": {
          "access_key": s3_service.access_key,
          "secret_key": s3_service.secret_key,
          "bucket": s3_service.bucket
        },
        "uploaded_file_key": uploaded_file_key,
        "file_storage_folder": file_storage_folder
      })
    return PostUploadLambdaOutputEvent.from_dict(response)
  except Exception as e:
    logger.error(f"Error invoking post upload process lambda: {e}")
    raise e

def invoke_parse_pdf_lambda(file_key: str) -> ParsePdfLambdaOutputEvent:
  try:
    logger.info(f"Invoking parse pdf lambda for file {file_key}")
    response = lambda_service.invoke(PARSE_PDF_FUNCTION_NAME, {
        "s3_access": {
          "access_key": s3_service.access_key,
          "secret_key": s3_service.secret_key,
          "bucket": s3_service.bucket
        },
        "openai_access": {
          "api_key": config.getenv("OPENAI_API_KEY"),
          "model": config.getenv("OPENAI_MODEL"),
          "text_embedding_model": config.getenv("OPENAI_TEXT_EMBEDDING_MODEL")
        },
        "file_key": file_key
    })
    return ParsePdfLambdaOutputEvent.from_dict(response)
  except Exception as e:
    logger.error(f"Error invoking parse pdf lambda: {e}")
    raise e
