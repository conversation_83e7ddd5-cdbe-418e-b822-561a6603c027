from typing import List, <PERSON><PERSON>

from core import openai_service, logger
from web_server.model import Chunk

def find_similar_chunks(query: str, paper_ids: List[str] = None, top_k: int = 10):
  query_embedding = openai_service.generate_text_embeddings(query)
  print(paper_ids)
  return Chunk.similarity_search(query_embedding, paper_ids, top_k)

def find_similar_chunks_with_scores(query: str, paper_ids: List[str] = None, top_k: int = 10) -> List[Tuple[Chunk, float]]:
  query_embedding = openai_service.generate_text_embeddings(query)
  print(paper_ids)
  return Chunk.similarity_search_with_scores(query_embedding, paper_ids, top_k)

