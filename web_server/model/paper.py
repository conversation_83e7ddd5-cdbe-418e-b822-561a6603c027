import uuid
import datetime
from dataclasses import dataclass
from enum import Enum
from qdrant_client.http.models import PointStruct, VectorStruct

from web_server.service import qdrant_service

COLLECTION_NAME = "paper"

class PaperStatus(Enum):
  UPLOADING = "uploading"
  UPLOADED = "uploaded"
  PROCESSING = "processing"
  PROCESSED = "processed"
  ERROR = "error"
  
@dataclass
class Paper:
  id: str
  name: str
  checksum: str
  status: PaperStatus
  created_at: datetime.datetime
  updated_at: datetime.datetime
  
  def __init__(self, name: str):
    self.id = uuid.uuid4().__str__()
    self.name = name
    self.checksum = ""
    self.status = PaperStatus.UPLOADING
    self.created_at = datetime.datetime.now()
    self.updated_at = datetime.datetime.now()
  
  def to_dict(self):
    return {
      "id": self.id,
      "name": self.name,
      "checksum": self.checksum,
      "status": self.status.value,
      "created_at": self.created_at.isoformat(),
      "updated_at": self.updated_at.isoformat()
    }
  
  def from_dict(data):
    paper = Paper(data["name"])
    paper.id = data["id"]
    paper.checksum = data["checksum"]
    paper.status = PaperStatus(data["status"])
    paper.created_at = datetime.datetime.fromisoformat(data["created_at"])
    paper.updated_at = datetime.datetime.fromisoformat(data["updated_at"])
    return paper
    
  def save(self):
    self.updated_at = datetime.datetime.now()
    data = self.to_dict()
    point = PointStruct(
      id=data["id"],
      vector=[0.0], # dummy
      payload=data
    )
    qdrant_service.upsert(COLLECTION_NAME, [point])
    
  @staticmethod
  def from_id(id: str):
    result = qdrant_service.retrieve(COLLECTION_NAME, [id])
    if not result:
      raise ValueError(f"Paper with id {id} not found")
    return Paper.from_dict(result[0].payload)

  def delete(self):
    qdrant_service.delete_points(COLLECTION_NAME, [self.id])
  
