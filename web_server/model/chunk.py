import uuid
import datetime
from dataclasses import dataclass
from enum import Enum
from qdrant_client.http.models import PointStruct, Filter, FieldCondition, MatchAny
from typing import List

from web_server.service import qdrant_service

COLLECTION_NAME = "chunk"

@dataclass
class Chunk:
  id: str
  paper_id: str
  content: str
  # blocks: List[Block]
  heading_context: List[str] 
  # introduce: List[str] = None
  # refer: List[str] = None
  # summary: str = None
  embedding: List[float] = None

  def __init__(self, paper_id: str, content: str, heading_context: List[str], embedding: List[float] = None):
    self.id = uuid.uuid4().__str__()
    self.paper_id = paper_id
    self.content = content
    self.heading_context = heading_context
    self.embedding = embedding
  
  def to_dict(self):
    return {
      "id": self.id,
      "paper_id": self.paper_id,
      "content": self.content,
      "heading_context": self.heading_context,
      "embedding": self.embedding
    }
    
  def from_dict(data):
    chunk = Chunk(data["paper_id"], data["content"], data["heading_context"], data["embedding"])
    chunk.id = data["id"]
    return chunk
  
  def save(self):
    data = self.to_dict()
    point = PointStruct(
      id=data["id"],
      vector=data["embedding"],
      payload=data
    )
    qdrant_service.upsert(COLLECTION_NAME, [point])
    
  @staticmethod
  def similarity_search(query_embedding: List[float], paper_ids: List[str] = None, top_k: int = 10):
    filter = Filter(must=FieldCondition(key="paper_id", match=MatchAny(any=paper_ids))) if paper_ids else None
    results = qdrant_service.search(COLLECTION_NAME, query_embedding, query_filter=filter, limit=top_k)
    return [Chunk.from_dict(result.payload) for result in results]

  @staticmethod
  def similarity_search_with_scores(query_embedding: List[float], paper_ids: List[str] = None, top_k: int = 10):
    filter = Filter(must=FieldCondition(key="paper_id", match=MatchAny(any=paper_ids))) if paper_ids else None
    results = qdrant_service.search(COLLECTION_NAME, query_embedding, query_filter=filter, limit=top_k)
    return [(Chunk.from_dict(result.payload), result.score) for result in results]
