import time
from dataclasses import dataclass, asdict
from typing import List
from tqdm import tqdm
from tqdm.contrib.logging import logging_redirect_tqdm

from core import logging, openai_service
from data import data_store
from processor.chunker import chunk_paper, DocumentChunk

logger = logging.get_logger()

DEFAULT_BATCH_SIZE = 100
DEFAULT_RATE_LIMIT_DELAY = 1.0
DEFAULT_MAX_RETRIES = 3
DEFAULT_RETRY_DELAY = 2.0

def prepare_chunk_for_embedding(chunk: DocumentChunk) -> str:
  """
  Context: hierarchical context (a > b > c)
  Content: chunk content
  """
  parts = []
  
  # Add hierarchical context if available
  if chunk.heading_context:
    context_text = " > ".join(chunk.heading_context)
    parts.append(f"Context: {context_text}")
  
  # Add the main content
  parts.append(f"Content: {chunk.content}")
  
  return "\n\n".join(parts)

def create_embeddings_batch(chunks: List[DocumentChunk], batch_size: int = DEFAULT_BATCH_SIZE) -> List[DocumentChunk]:
  processed_chunks = []
  
  # Process chunks in batches
  for i in range(0, len(chunks), batch_size):
    batch = chunks[i:i + batch_size]
    
    # Prepare texts for embedding
    texts = [prepare_chunk_for_embedding(chunk) for chunk in batch]
    
    # Get embeddings from OpenAI
    retries = 0
    while retries < DEFAULT_MAX_RETRIES:
      try:
        logger.info(f"Creating embeddings for batch {i//batch_size + 1} ({len(batch)} chunks)")
        embeddings = openai_service.generate_text_embeddings(texts)
        break
      except Exception as e:
        retries += 1
        if retries >= DEFAULT_MAX_RETRIES:
          logger.error(f"Failed to create embeddings after {DEFAULT_MAX_RETRIES} retries: {e}")
          raise e
        logger.warning(f"Embedding API call failed (attempt {retries}), retrying in {DEFAULT_RETRY_DELAY}s: {e}")
        time.sleep(DEFAULT_RETRY_DELAY)
    
    for chunk, embedding in zip(batch, embeddings):
      chunk.embedding = embedding
      processed_chunks.append(chunk)
    
    # Sleep to avoid rate limiting
    if i + batch_size < len(chunks):
      time.sleep(DEFAULT_RATE_LIMIT_DELAY)
  
  return processed_chunks

def embed_chunks(
  paper_id: str,
  force_regenerate: bool = False
) -> List[DocumentChunk]:
  logger.info(f"Generating embeddings for paper {paper_id}")
  chunks = data_store.load_chunked_data(paper_id)
  
  if not force_regenerate and all(chunk.embedding is not None for chunk in chunks):
    logger.info(f"Embeddings already exist for paper {paper_id}, loading from cache")
    return chunks
  
  processed_chunks = create_embeddings_batch(chunks)
  
  data_store.save_chunked_data(paper_id, processed_chunks)
  
  logger.info(f"Successfully created and saved {len(processed_chunks)} embeddings for paper {paper_id}")
  
  return processed_chunks

def embed_all_papers(
  force_regenerate: bool = False,
):
  paper_ids = data_store.get_free_from_available_paper_ids()
  
  logger.info(f"Found {len(paper_ids)} papers")
  
  for paper_id in tqdm(paper_ids, desc="Embedding papers"):
    try:
      embed_chunks(
        paper_id=paper_id,
        force_regenerate=force_regenerate
      )
    except Exception as e:
      logger.error(f"Failed to chunk and embed paper {paper_id}: {e}")

if __name__ == "__main__":
  with logging_redirect_tqdm([logger]):
    embed_all_papers(force_regenerate=False)
