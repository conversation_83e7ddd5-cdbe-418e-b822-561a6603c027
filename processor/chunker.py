import tiktoken
from dataclasses import dataclass
from typing import List, Optional
from tqdm import tqdm
from tqdm.contrib.logging import logging_redirect_tqdm

from core import logging
from data import data_store
from parser.parser import Document, Block, Text

tiktoken_encoder = tiktoken.get_encoding("cl100k_base")
logger = logging.get_logger()

# Configuration constants
MAX_TOKENS_PER_CHUNK = 512
MAX_OVERLAP_TOKENS = 128
MAX_OVERLAP_RATIO = 0.5 # not overlap above 0.5 * min(current_chunk, previous_chunk)
SMALL_CHUNK_THRESHOLD = 0.125 # chunks that is too small (typically last chunk in a section) will be merged with previous chunk

HEADING_LABELS = ["HEADING_0", "HEADING_1", "HEADING_2", "HEADING_3", "HEADING_4", "HEADING_5"]
CONTENT_LABELS = ["PARAGRAPH", "ABSTRACT", "FIGURE_CAPTION", "TAB<PERSON>", "TABLE_CAPTION", "CODE", "EQUATION", "PAPER_INFO"]
FILTERED_LABELS = ["PAGE-HEADER", "PAGE-FOOTER", "FOOTNOTE"]

@dataclass
class DocumentChunk:
  content: str
  token_count: int
  blocks: List[Block]
  heading_context: List[str]
  overlapped_text: str = ""
  embedding: List[float] = None
  introduce: List[str] = None
  refer: List[str] = None
  summary: str = None

@dataclass
class HeadingNode:
  text: str
  level: int
  children: List['HeadingNode']
  content_blocks: List[Block]

def count_tokens(text: str) -> int:
  if not text or not text.strip():
    return 0
  
  tokens = tiktoken_encoder.encode(text)
  return len(tokens)

def get_heading_level(label: str) -> Optional[int]:
  if label.startswith("HEADING_"):
    try:
      return int(label.split("_")[1])
    except (IndexError, ValueError):
      return None
  return None

def build_heading_hierarchy(text_blocks: List[Block]) -> List[HeadingNode]:
  root_nodes = []
  heading_stack = []  # Stack to track current heading hierarchy

  for block in text_blocks:
    heading_level = get_heading_level(block.label)

    if heading_level is not None:
      new_node = HeadingNode(
        text=block.text.strip(),
        level=heading_level,
        children=[],
        content_blocks=[]
      )

      while heading_stack and heading_stack[-1].level >= heading_level:
        heading_stack.pop()

      if heading_stack:
        heading_stack[-1].children.append(new_node)
      else:
        root_nodes.append(new_node)

      heading_stack.append(new_node)

    else:
      if heading_stack:
        heading_stack[-1].content_blocks.append(block)
      else:
        if not root_nodes or root_nodes[-1].text != "":
          virtual_root = HeadingNode(
            text="",
            level=-1,
            children=[],
            content_blocks=[]
          )
          root_nodes.append(virtual_root)
        root_nodes[-1].content_blocks.append(block)

  return root_nodes

def get_overlap_size(
  current_chunk_size: int,
  previous_chunk_size: int,
  overlap_tokens: int = MAX_OVERLAP_TOKENS, 
  overlap_ratio: float = MAX_OVERLAP_RATIO
) -> int:
  return min(min(current_chunk_size, previous_chunk_size) * overlap_ratio, overlap_tokens)

def get_overlap_text(max_tokens: int, chunk: DocumentChunk):
  overlap_text = ""
  overlap_tokens = 0
  for block in reversed(chunk.blocks):
    token_count = count_tokens(block.text)
    if overlap_tokens + token_count <= max_tokens:
      overlap_text = block.text + "\n\n" + overlap_text
      overlap_tokens += token_count
      continue
    
    for text in reversed(block.texts):
      token_count = count_tokens(text.text)
      if overlap_tokens + token_count <= max_tokens:
        overlap_text = text.text + "\n" + overlap_text
        overlap_tokens += token_count
        continue
      
      for line in reversed(text.lines):
        token_count = count_tokens(line.text)
        if overlap_tokens + token_count <= max_tokens:
          overlap_text = line.text + " " + overlap_text
          overlap_tokens += token_count
          continue
          
        for word in reversed(line.words):
          token_count = count_tokens(word.text)
          if overlap_tokens + token_count <= max_tokens:
            overlap_text = word.text + " " + overlap_text
            overlap_tokens += token_count
            continue
        
  return overlap_text

def perform_chunk_overlap(chunks: List[DocumentChunk]):
  if not chunks:
    return
  
  for i in range(len(chunks) - 1, 0, -1):
    previous_chunk = chunks[i-1]
    current_chunk = chunks[i]
    
    overlap_size = get_overlap_size(current_chunk.token_count, previous_chunk.token_count)
    overlap_text = get_overlap_text(overlap_size, previous_chunk)
    current_chunk.overlapped_text = overlap_text
    current_chunk.content = overlap_text + "\n\n" + current_chunk.content
    current_chunk.token_count += count_tokens(overlap_text)
  
def chunk_content_blocks(
  content_blocks: List[Block],
  heading_context: List[str]
) -> List[DocumentChunk]:
  if not content_blocks:
    return []

  chunks = []
  current_list = []
  current_tokens = 0
  current_content = ""
  
  for block in content_blocks:
    block_tokens_count = count_tokens(block.text)
    if current_tokens + block_tokens_count > MAX_TOKENS_PER_CHUNK:
      if current_list:
        chunks.append(DocumentChunk(
          content=current_content,
          token_count=current_tokens,
          heading_context=heading_context.copy(),
          blocks=current_list.copy()
        ))
        current_list = []
        current_tokens = 0
        current_content = ""
    current_list.append(block)
    current_tokens += block_tokens_count
    current_content += block.text + "\n\n"
  
  if current_list:
    if current_tokens < MAX_TOKENS_PER_CHUNK * SMALL_CHUNK_THRESHOLD and len(chunks) > 0:
      # Merge last small chunks with previous chunk if it's too small
      previous_chunk = chunks[-1]
      previous_chunk.content += "\n\n" + current_content
      previous_chunk.token_count += current_tokens
      previous_chunk.blocks.extend(current_list)
    else:
      # Otherwise, create a new chunk
      chunks.append(DocumentChunk(
        content=current_content,
        token_count=current_tokens,
        heading_context=heading_context.copy(),
        blocks=current_list.copy()
      ))
  
  perform_chunk_overlap(chunks)
  
  return chunks

def recursive_chunk_heading_node(node: HeadingNode, heading_context: List[str]) -> List[DocumentChunk]:
  chunks = []

  # Update heading context with current node
  current_context = heading_context.copy()
  if node.text.strip():
    current_context.append(node.text.strip())

  # Chunk the content blocks under this heading
  if node.content_blocks:
    content_chunks = chunk_content_blocks(node.content_blocks, current_context)
    chunks.extend(content_chunks)

  # Recursively process children
  for child in node.children:
    child_chunks = recursive_chunk_heading_node(child, current_context)
    chunks.extend(child_chunks)

  return chunks

def chunk_document_hierarchically(text_blocks: List[Block]) -> List[DocumentChunk]:
  if not text_blocks:
    return []

  heading_nodes = build_heading_hierarchy(text_blocks)

  all_chunks = []
  for root_node in heading_nodes:
    node_chunks = recursive_chunk_heading_node(root_node,[])
    all_chunks.extend(node_chunks)

  return all_chunks

def chunk_paper(paper_id: str):
  doc = data_store.load_raw_parsed_data(paper_id)
  blocks = []
  for page in doc.pages:
    blocks.extend(page.blocks)
    
  if not blocks:
    logger.warning(f"No parsed data found for paper {paper_id}")
    raise ValueError(f"No parsed data found for paper {paper_id}")

  filtered_blocks = [
    block for block in blocks
    if block.label not in FILTERED_LABELS
  ]

  logger.info(f"Chunking paper {paper_id}: {len(filtered_blocks)} blocks after filtering")

  chunks = chunk_document_hierarchically(filtered_blocks)

  logger.info(f"Created {len(chunks)} chunks for paper {paper_id}")
  
  return chunks
  
if __name__ == "__main__":
  paper_ids = [
    "openreview/ICLR-2023-conf/-CoNloheTs",
    "nlpeer/ARR-22/013b9bf63a6f68fd0c3ecc36f8cbe2ad5bc92ea3bfe5a9f6c15eb056ecc4f858718410182c3765b2dc2695ae29ba08fb5dea5fc495faf2bbb77205bc3f765fcd",
    "openreview/ICLR-2023-conf/T2Ncx_PN2K"
  ]
  for paper_id in paper_ids:
    chunks = chunk_paper(paper_id)
    data_store.save_chunked_data(paper_id, chunks)
  
  exit(0)
  with logging_redirect_tqdm([logger]):
    for paper_id in tqdm(data_store.get_free_from_available_paper_ids()):
      # if data_store.is_chunked_data_exists(paper_id):
      #   logger.info(f"Skipping paper {paper_id}, already chunked")
      #   continue
      logger.info(f"Chunking paper {paper_id}")
      chunks = chunk_paper(paper_id)
      data_store.save_chunked_data(paper_id, chunks)
    