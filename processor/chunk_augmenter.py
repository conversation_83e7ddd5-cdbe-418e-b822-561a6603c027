import json
from tqdm import tqdm
from tqdm.contrib.logging import logging_redirect_tqdm

from core import logging
from core.openai_service import call_text_response_once
from core.prompts import PROMPT_CHUNK_EXTRACTION
from core.utils import run_paralellism
from processor.chunker import DocumentChunk
from data import data_store

logger = logging.get_logger()

RETRY_COUNT = 3

def create_augmentation_prompt(chunk: DocumentChunk) -> str:
  context_str = ""
  if chunk.heading_context:
    context_str = " > ".join(chunk.heading_context)
  
  return PROMPT_CHUNK_EXTRACTION.build_prompt(chunk_context=context_str, chunk_content=chunk.content)

def parse_llm_output(text: str) -> dict:
  if text.startswith("```json"):
    text = text[7:-3].strip()
  
  try:
    data = json.loads(text)
  except json.JSONDecodeError:
    text = text.replace("\\", "\\\\")
    data = json.loads(text)
  
  if "introduced_elements" not in data or "referenced_elements" not in data or "summary" not in data:
    logger.error(f"Missing key in LLM output: {data}")
    raise ValueError("Missing key in LLM output")
  return data

def augment_single_chunk(chunk: DocumentChunk) -> DocumentChunk:
  for _ in range(RETRY_COUNT):
    try:
      if chunk.summary:
        return chunk
      prompt = create_augmentation_prompt(chunk)
      response = call_text_response_once(prompt, temperature=0.0)
      raw_response = response.output[0].content[0].text.strip()
      data = parse_llm_output(raw_response)
      chunk.introduce = data["introduced_elements"]
      chunk.refer = data["referenced_elements"]
      chunk.summary = data["summary"]
      return chunk
    except Exception as e:
      logger.error(f"Error augmenting chunk: {e}")
      # print(raw_response)
      # print(chunk)
      continue
  return chunk

if __name__ == "__main__":
  with logging_redirect_tqdm([]):
    paper_ids = data_store.get_free_from_available_paper_ids()
    logger.info(f"Found {len(paper_ids)} papers")

    for paper_id in tqdm(paper_ids):
      try:
        chunks = data_store.load_chunked_data(paper_id)
      except Exception as e:
        logger.warning(f"Failed to load chunks for paper {paper_id}: {e}")
        data_store.save_chunked_data(paper_id, [])
        continue
      logger.info(f"Augmenting {len(chunks)} chunks for paper {paper_id}")
      run_paralellism(augment_single_chunk, [(chunk,) for chunk in chunks], thread_count=16, batch_size=200)
      data_store.save_chunked_data(paper_id, chunks)
