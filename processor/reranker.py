import json
from typing import List

from core import logging, openai_service
from core.prompts import PROMPT_CHUNK_RERANKING
from processor.chunker import DocumentChunk

logger = logging.get_logger()

def create_reranking_prompt(question: str, chunks: List[DocumentChunk]) -> str:
  chunks_str = ""
  for i, chunk in enumerate(chunks):
    chunks_str += f"Chunk {i}: {chunk.summary}\n\n"
  
  return PROMPT_CHUNK_RERANKING.build_prompt(question=question, chunks=chunks_str)

def parse_reranking_output(output: str) -> List[int]:
  try:
    if output.startswith("```"):
      start_pos = output.find("[")
      end_pos = output.rfind("]")
      output = output[start_pos:end_pos+1].strip()
    return json.loads(output)
  except Exception as e:
    logger.error(f"Error parsing reranking output: {e}, {output}")
    raise e
  
def ensure_indexes_valid(indexes: List[int], length: int):
  for index in indexes:
    if index < 0 or index >= length:
      raise ValueError(f"Invalid index: {index}")
  if len(set(indexes)) != len(indexes):
    raise ValueError("Duplicated indexes")
  
def rerank_chunks(question: str, chunks: List[DocumentChunk]) -> List[DocumentChunk]:
  prompt = create_reranking_prompt(question, chunks)
  response = openai_service.call_text_response_once(prompt, temperature=0.0)
  output = response.output[0].content[0].text
  ranked_indexes = parse_reranking_output(output)

  ensure_indexes_valid(ranked_indexes, len(chunks))
  return [chunks[i] for i in ranked_indexes]

from data import data_store

if __name__ == "__main__":
  paper_id = data_store.get_free_from_available_paper_ids()[0]
  chunks = data_store.load_chunked_data(paper_id)

  for chunk in chunks:
    print(chunk.summary)
    print("===")
    print()
  chunks = chunks[:10]
  chunks = rerank_chunks("What is the proposed method?", chunks)
  