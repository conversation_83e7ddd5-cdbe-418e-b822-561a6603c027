import threading
from tqdm import tqdm
from tqdm.contrib.logging import logging_redirect_tqdm

def run_paralellism(func, args_list, thread_count=16, batch_size=256):
  with logging_redirect_tqdm():
    semaphore = threading.Semaphore(thread_count)
    progress = tqdm(total=len(args_list), desc=f"Running {func.__name__}")
    progress.display()
    
    def run_func(args):
      with semaphore:
        result = func(*args)
        progress.update(1)
        return result
    
    for i in range(0, len(args_list), batch_size):
      batch = args_list[i:i+batch_size]
      threads = []
      for args in batch:
        thread = threading.Thread(target=run_func, args=(args,))
        threads.append(thread)
      for thread in threads:
        thread.start()
      for thread in threads:
        thread.join()
    progress.close()
