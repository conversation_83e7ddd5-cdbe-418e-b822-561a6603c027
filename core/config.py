import os
import dotenv
import argparse

dotenv.load_dotenv()

argparser = argparse.ArgumentParser()
argparser.add_argument("-log_level", type=str, default="INFO", help="Set log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)")
args = argparser.parse_args().__dict__

def get_argument(key):
  if key in args:
    return args[key]
  else:
    raise ValueError(f"Parameter {key} not found")
  
def getenv(key):
  value = os.getenv(key)
  if value:
    return value
  else:
    raise ValueError(f"Environment variable '{key}' not found")