import logging
import time
import os
from colorama import Fore, Style, init

from core.config import get_argument

# Initialize colorama
init(autoreset=True)

LOG_DIR = "logs/"

LOGGER = None

log_file = os.path.join(LOG_DIR, f"app_{time.strftime('%Y%m%d_%H%M%S')}.log")

def get_log_level():
  log_level = get_argument("log_level").upper()
  if log_level not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
    raise ValueError(f"Invalid log level: {log_level}")
  return getattr(logging, log_level)

class ColoredFormatter(logging.Formatter):
  """Custom formatter to add colors to log levels."""
  def format(self, record):
    level_color = {
      "DEBUG": Fore.BLUE,
      "INFO": Fore.GREEN,
      "WARNING": Fore.YELLOW,
      "ERROR": Fore.RED,
      "CRITICAL": Fore.MAGENTA + Style.BRIGHT,
    }
    color = level_color.get(record.levelname, "")
    record.levelname = f"{color}{record.levelname}{Style.RESET_ALL}"
    return super().format(record)

def get_logger():
  if LOGGER:
    return LOGGER
  raise ValueError("Logger not configured")

def setup():
  global LOGGER
  if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)
  
  logger = logging.getLogger("thesis")
  level = get_log_level()
  logger.setLevel(level)

  ch = logging.StreamHandler()
  formatter = ColoredFormatter('[%(asctime)s - %(levelname)s] %(message)s (%(filename)s:%(lineno)d)')
  ch.setFormatter(formatter)

  fh = logging.FileHandler(log_file)
  fh.setLevel(level)
  fh.setFormatter(logging.Formatter('[%(asctime)s - %(levelname)s] %(message)s (%(filename)s:%(lineno)d)'))
  
  logger.addHandler(ch)
  logger.addHandler(fh)

  logger.info("Logger successfully configured")
  
  LOGGER = logger

setup()