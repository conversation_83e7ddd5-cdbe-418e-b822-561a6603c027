import os
import json
from time import sleep
from openai import OpenAI
from dotenv import load_dotenv

from core import prompts, logging

load_dotenv()
logger = logging.get_logger()

RETRY_DELAY = 2.0
MAX_RETRIES = 3
MODEL = os.getenv("OPENAI_MODEL")
TEXT_EMBEDDING_MODEL = os.getenv("OPENAI_TEXT_EMBEDDING_MODEL")

client = OpenAI(
  api_key=os.getenv("OPENAI_API_KEY"),
)

def call_text_response_once(input_text, temperature=0.5, model=MODEL):
  logger.info(f"Calling OpenAI API with model {model} and temperature {temperature}")
  input_text = input_text.strip()
  retries = 0
  while retries < MAX_RETRIES:
    try:
      response = client.responses.create(
        model=model,
        input=input_text.strip(),
        temperature=temperature,
      )
      return response
    except Exception as e:
      retries += 1
      if retries >= MAX_RETRIES:
        logger.error(f"Failed to call text response API after {MAX_RETRIES} retries: {e}")
        raise e
      sleep(RETRY_DELAY)

def generate_chat_completion(prompt, temperature=0.5, model=MODEL):
  logger.info(f"Calling OpenAI Chat Completions API with model {model} and temperature {temperature}")

  response = client.chat.completions.create(
    model=model,
    messages=[
      {"role": "user", "content": prompt},
      {"role": "user", "content": "Respond in markdown format if applicable, but not wrap inside code blocks ```. Use tables when presenting structured data, comparisons, or lists with multiple attributes. Format tables with proper headers and alignment."}
    ],
    temperature=temperature,
    max_tokens=1024
  )

  return response.choices[0].message.content

def _parse_list(str):
  index = 1
  result = []
  for item in str.split("\n"):
    item = item.strip()
    if item.startswith(f"{index}: "):
      item = item[len(f"{index}: "):].strip()
      result.append(item)
      index += 1
    
  return result

def classify_headings(input: list[str], temperature=0, model=MODEL):
  try:
    prompt_input = "\n".join([f"{i}: {text}" for i, text in enumerate(input, 1)])
    prompt = prompts.PROMPT_HEADINGS_CLASSIFICATION.build_prompt(input=prompt_input)
    response = call_text_response_once(prompt, temperature=temperature, model=model)
    output = response.output[0].content[0].text
    output = _parse_list(output)
    if len(output) != len(input):
      logger.error(f"Output length does not match input length, expected {len(input)}, got {len(output)}")
      print(f"Input:\n {prompt_input}")
      print(f"Output:\n {output}")
    return output
  except Exception as e:
    logger.error(f"Error in classify_headings: {e}")
    raise e
  
def generate_text_embeddings(input: str | list[str], model=TEXT_EMBEDDING_MODEL):
  logger.info(f"Calling OpenAI API to generate text embeddings with model {model}")
  is_str = False
  if type(input) == str:
    input = [input]
    is_str = True
  response = client.embeddings.create(
    model=model,
    input=input,
  )
  if is_str:
    return response.data[0].embedding
  else: 
    return [item.embedding for item in response.data]

if __name__ == "__main__":
  print(len(generate_text_embeddings("Hello world")))
