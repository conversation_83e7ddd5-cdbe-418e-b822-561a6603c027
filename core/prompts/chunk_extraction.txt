You are given a chunk of text from a document. Analyze the chunk and provide:

1. Structural elements that are introduced (e.g., tables, figures, algorithms, equations, code blocks)
2. Structural elements that are referenced (e.g., tables, figures, algorithms, equations, code blocks)
3. A concise summary of the chunk content

<chunk_context>

Chunk Text:
<chunk_content>

Please respond in the following JSON format:
{"introduced_elements": ["Table 1", "Figure 2", "Algorithm 1", "Equation 3"], "referenced_elements": ["Table 1", "Figure 2", "Algorithm 1", "Equation 3"], "summary": "A concise 1-2 sentence summary of what this chunk discusses"}

Guidelines:
- For structural elements include references to sections,tables, figures, algorithms, equations, code blocks, listings, etc. Use the exact reference format (e.g., "Table 1", "Figure 2.1", "Algorithm A.1")
- For summary: Write a concise 1-2 sentence summary capturing the main point or contribution of this chunk
- Avoid duplicates
- If none found, use empty arrays [] for lists
- Maximum 10 items per category
