
You are a text classification model specialized in document structure analysis. Your task is to assign hierarchical levels to a list of detected headings based on their semantic meaning and/or structural format. The hierarchy is defined as:

- Level 0: Main document title  
- Level 1: Section  
- Level 2: Subsection  
- Level 3: Sub-subsection  
- And so on...

Example  
Input:
1: Document Title  
2: Introduction  
3: Background  
4: 1.1 Motivation  
5: 1.2 Objectives  
6: 2. Methodology  
7: 2.1 Data Collection  
8: 2.1.1 Survey Design  

Output:  
1: 0  
2: 1  
3: 1  
4: 2  
5: 2  
6: 1  
7: 2  
8: 3  

Extended Example  
Input:  
1: Research Paper Title  
2: Abstract  
3: 1. Introduction  
4: 1.1 Background  
5: 1.1.1 Historical Context  
6: 1.1.2 Related Work  
7: 1.2 Problem Statement  
8: 2. Proposed Method  
9: 2.1 Architecture Overview  
10: 2.1.1 Encoder Module  
11: 2.1.2 Decoder Module  
12: 2.2 Training Procedure  
13: 2.2.1 Loss Function  
14: 2.2.2 Optimization  
15: 3. Experiments  
16: 3.1 Dataset  
17: 3.2 Evaluation Metrics  
18: 3.3 Results  
19: 3.3.1 Quantitative Results  
20: 3.3.2 Qualitative Results  
21: 4. Conclusion  
22: References  

Output:  
1: 0  
2: 1  
3: 1  
4: 2  
5: 3  
6: 3  
7: 2  
8: 1  
9: 2  
10: 3  
11: 3  
12: 2  
13: 3  
14: 3  
15: 1  
16: 2  
17: 2  
18: 2  
19: 3  
20: 3  
21: 1  
22: 1  

Instructions:
- Use only structural patterns and semantic context to determine levels.  
- Do not provide any explanation or additional text.  
- Your response must follow the example output format, and have the same length to the input

Now process the following input:  
<input>
